openapi: 3.1.0
info:
  title: app-service
  description: ''
  license:
    name: ''
  version: 0.0.1
paths:
  merchant_user/login:
    post:
      tags:
      - 商户认证管理
      summary: 商户用户登录
      description: 商户用户登录接口，验证手机号和密码，返回访问令牌
      operationId: login
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MerchantUserLoginRequest'
        required: true
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResult_MerchantUserLoginResponse'
              example:
                code: 200
                data:
                  token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
                message: 登录成功
  merchant_user/merchants:
    get:
      tags:
      - 商户认证管理
      summary: 获取用户商户列表
      description: 获取当前登录用户关联的所有商户列表，包含商户基本信息、分类信息和用户角色
      operationId: get_user_merchant_list
      parameters:
      - name: include_disabled
        in: query
        description: 是否包含已禁用的商户，默认为false
        required: false
        schema:
          type: boolean
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResult_UserMerchantListResponse'
              example:
                code: 200
                data:
                  merchants:
                  - merchant_avatar: https://example.com/avatar.jpg
                    merchant_category:
                      category_id: 550e8400-e29b-41d4-a716-************
                      category_name: 餐饮服务
                    merchant_code: REST001
                    merchant_id: 123456
                    merchant_name: 张三餐厅
                    user_roles:
                    - role_code: merchant_admin
                      role_id: 550e8400-e29b-41d4-a716-************
                      role_name: 商户管理员
                  total_count: 1
                message: 获取成功
  merchant_user/profile:
    get:
      tags:
      - 商户认证管理
      summary: 获取当前用户个人信息
      description: 获取商户用户当前登录人的详细信息，包括用户基本信息、权限列表和角色信息
      operationId: get_current_user_profile
      responses:
        '200':
          description: 个人信息获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResult_MerchantUserProfileResponse'
  merchant_user/switch_merchant:
    post:
      tags:
      - 商户认证管理
      summary: 切换当前商户
      description: 用户切换到其有权限的另一个商户，更新会话信息并返回新的token
      operationId: switch_merchant
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SwitchMerchantRequest'
        required: true
      responses:
        '200':
          description: 切换成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResult_SwitchMerchantResponse'
              example:
                code: 200
                data:
                  merchant_name: 张三餐厅
                  token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
                message: 切换成功
components:
  schemas:
    ApiResult_MerchantUserLoginResponse:
      type: object
      description: 通用API响应结构体
      required:
      - code
      - message
      properties:
        code:
          type: integer
          format: int32
          description: 响应状态码
        message:
          type: string
          description: 响应消息
        data:
          type: object
          description: 商户用户登录成功返回信息
          required:
          - token
          properties:
            token:
              type: string
              description: 访问令牌，用于后续API请求的身份验证
          example:
            token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
        timestamp:
          type:
          - integer
          - 'null'
          format: int64
          description: 时间戳（可选）
      example:
        code: 200
        data: 具体数据内容
        message: 操作成功
        timestamp: 1684903200
    ApiResult_MerchantUserProfileResponse:
      type: object
      description: 通用API响应结构体
      required:
      - code
      - message
      properties:
        code:
          type: integer
          format: int32
          description: 响应状态码
        message:
          type: string
          description: 响应消息
        data:
          type: object
          description: 当前用户信息响应 - 获取商户用户当前登录人的详细信息
          required:
          - id
          - username
          - status
          - permissions
          - roles
          properties:
            id:
              type: string
              description: 用户ID
            username:
              type: string
              description: 用户名
            real_name:
              type:
              - string
              - 'null'
              description: 真实姓名
            phone:
              type:
              - string
              - 'null'
              description: 手机号
            email:
              type:
              - string
              - 'null'
              description: 邮箱
            avatar:
              type:
              - string
              - 'null'
              description: 头像
            gender:
              oneOf:
              - type: 'null'
              - $ref: '#/components/schemas/MerchantUserGender'
                description: 性别
            status:
              type: integer
              format: int32
              description: 账户状态
            last_login_date:
              type:
              - string
              - 'null'
              description: 最后登录时间
            last_login_ip:
              type:
              - string
              - 'null'
              description: 最后登录IP
            permissions:
              type: array
              items:
                type: string
              description: 权限列表
            roles:
              type: array
              items:
                $ref: '#/components/schemas/MerchantUserRoleInfo'
              description: 角色列表
          example:
            avatar: https://static.oywm.top/avatars/users/55/9b/fe7a8f81-0825-4c9f-accd-622103ecaf36.png
            email: null
            gender: null
            id: 2d317d33-e2ab-4ce4-b694-c69c1c112824
            last_login_date: 2025-07-28 11:15:32
            last_login_ip: *************
            permissions:
            - sys:permission:detail
            - permission_template:delete
            - sys:role:update
            phone: null
            real_name: null
            roles:
            - description: 系统超级管理员，拥有所有权限
              id: f1e6b32e-4471-4192-a709-36d76b92050a
              name: 超级管理员
              role_code: ADMIN
            status: 1
            username: admin
        timestamp:
          type:
          - integer
          - 'null'
          format: int64
          description: 时间戳（可选）
      example:
        code: 200
        data: 具体数据内容
        message: 操作成功
        timestamp: 1684903200
    ApiResult_String:
      type: object
      description: 通用API响应结构体
      required:
      - code
      - message
      properties:
        code:
          type: integer
          format: int32
          description: 响应状态码
        message:
          type: string
          description: 响应消息
        data:
          type: string
        timestamp:
          type:
          - integer
          - 'null'
          format: int64
          description: 时间戳（可选）
      example:
        code: 200
        data: 具体数据内容
        message: 操作成功
        timestamp: 1684903200
    ApiResult_SwitchMerchantResponse:
      type: object
      description: 通用API响应结构体
      required:
      - code
      - message
      properties:
        code:
          type: integer
          format: int32
          description: 响应状态码
        message:
          type: string
          description: 响应消息
        data:
          type: object
          description: 切换商户响应 - 切换当前商户后的响应信息
          required:
          - token
          - merchant_name
          properties:
            token:
              type: string
              description: 新的访问令牌，包含更新后的商户信息
            merchant_name:
              type: string
              description: 切换后的商户名称
          example:
            merchant_name: 张三餐厅
            token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
        timestamp:
          type:
          - integer
          - 'null'
          format: int64
          description: 时间戳（可选）
      example:
        code: 200
        data: 具体数据内容
        message: 操作成功
        timestamp: 1684903200
    ApiResult_UserMerchantListResponse:
      type: object
      description: 通用API响应结构体
      required:
      - code
      - message
      properties:
        code:
          type: integer
          format: int32
          description: 响应状态码
        message:
          type: string
          description: 响应消息
        data:
          type: object
          description: 用户商户列表响应 - 获取用户关联的所有商户列表
          required:
          - merchants
          - total_count
          properties:
            merchants:
              type: array
              items:
                $ref: '#/components/schemas/UserMerchantListItemResponse'
              description: 商户列表
            total_count:
              type: integer
              description: 商户总数
              minimum: 0
          example:
            merchants:
            - merchant_avatar: https://example.com/avatar.jpg
              merchant_category:
                category_id: 550e8400-e29b-41d4-a716-************
                category_name: 餐饮服务
              merchant_code: REST001
              merchant_id: 123456
              merchant_name: 张三餐厅
              user_roles:
              - role_code: merchant_admin
                role_id: 550e8400-e29b-41d4-a716-************
                role_name: 商户管理员
            total_count: 1
        timestamp:
          type:
          - integer
          - 'null'
          format: int64
          description: 时间戳（可选）
      example:
        code: 200
        data: 具体数据内容
        message: 操作成功
        timestamp: 1684903200
    MerchantCategoryInfo:
      type: object
      description: 商户分类信息 - 用于商户列表中显示商户分类
      required:
      - category_id
      - category_name
      properties:
        category_id:
          type: string
          format: uuid
          description: 分类ID
        category_name:
          type: string
          description: 分类名称
      example:
        category_id: 550e8400-e29b-41d4-a716-************
        category_name: 餐饮服务
    MerchantRoleInfo:
      type: object
      description: 商户角色信息 - 用于商户列表中显示用户在商户下的角色
      required:
      - role_id
      - role_name
      properties:
        role_id:
          type: string
          format: uuid
          description: 角色ID
        role_name:
          type: string
          description: 角色名称
        role_code:
          type:
          - string
          - 'null'
          description: 角色编码
      example:
        role_code: merchant_admin
        role_id: 550e8400-e29b-41d4-a716-************
        role_name: 商户管理员
    MerchantUserGender:
      type: string
      description: |-
        用户性别枚举

        定义用户的性别类型，包括男性、女性和未知
      enum:
      - Male
      - Female
      - Unknown
    MerchantUserLoginRequest:
      type: object
      description: 商户用户登录请求
      required:
      - phone
      - password
      properties:
        phone:
          type: string
          description: 手机号
        password:
          type: string
          description: 密码
      example:
        password: '123456'
        phone: '13800138000'
    MerchantUserLoginResponse:
      type: object
      description: 商户用户登录成功返回信息
      required:
      - token
      properties:
        token:
          type: string
          description: 访问令牌，用于后续API请求的身份验证
      example:
        token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
    MerchantUserProfileResponse:
      type: object
      description: 当前用户信息响应 - 获取商户用户当前登录人的详细信息
      required:
      - id
      - username
      - status
      - permissions
      - roles
      properties:
        id:
          type: string
          description: 用户ID
        username:
          type: string
          description: 用户名
        real_name:
          type:
          - string
          - 'null'
          description: 真实姓名
        phone:
          type:
          - string
          - 'null'
          description: 手机号
        email:
          type:
          - string
          - 'null'
          description: 邮箱
        avatar:
          type:
          - string
          - 'null'
          description: 头像
        gender:
          oneOf:
          - type: 'null'
          - $ref: '#/components/schemas/MerchantUserGender'
            description: 性别
        status:
          type: integer
          format: int32
          description: 账户状态
        last_login_date:
          type:
          - string
          - 'null'
          description: 最后登录时间
        last_login_ip:
          type:
          - string
          - 'null'
          description: 最后登录IP
        permissions:
          type: array
          items:
            type: string
          description: 权限列表
        roles:
          type: array
          items:
            $ref: '#/components/schemas/MerchantUserRoleInfo'
          description: 角色列表
      example:
        avatar: https://static.oywm.top/avatars/users/55/9b/fe7a8f81-0825-4c9f-accd-622103ecaf36.png
        email: null
        gender: null
        id: 2d317d33-e2ab-4ce4-b694-c69c1c112824
        last_login_date: 2025-07-28 11:15:32
        last_login_ip: *************
        permissions:
        - sys:permission:detail
        - permission_template:delete
        - sys:role:update
        phone: null
        real_name: null
        roles:
        - description: 系统超级管理员，拥有所有权限
          id: f1e6b32e-4471-4192-a709-36d76b92050a
          name: 超级管理员
          role_code: ADMIN
        status: 1
        username: admin
    MerchantUserRoleInfo:
      type: object
      description: 商户用户角色信息 - 用于当前用户信息中显示角色
      required:
      - id
      - name
      - role_code
      properties:
        id:
          type: string
          description: 角色ID
        name:
          type: string
          description: 角色名称
        role_code:
          type: string
          description: 角色编码
        description:
          type:
          - string
          - 'null'
          description: 角色描述
      example:
        description: 系统超级管理员，拥有所有权限
        id: f1e6b32e-4471-4192-a709-36d76b92050a
        name: 超级管理员
        role_code: ADMIN
    SwitchMerchantRequest:
      type: object
      description: 切换当前商户请求
      required:
      - merchant_id
      properties:
        merchant_id:
          type: integer
          format: int64
          description: 要切换到的商户ID
      example:
        merchant_id: 123456
    SwitchMerchantResponse:
      type: object
      description: 切换商户响应 - 切换当前商户后的响应信息
      required:
      - token
      - merchant_name
      properties:
        token:
          type: string
          description: 新的访问令牌，包含更新后的商户信息
        merchant_name:
          type: string
          description: 切换后的商户名称
      example:
        merchant_name: 张三餐厅
        token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
    UserMerchantListItemResponse:
      type: object
      description: 用户商户列表项 - 用户关联的商户详细信息
      required:
      - merchant_id
      - merchant_name
      - merchant_code
      - user_roles
      properties:
        merchant_id:
          type: integer
          format: int64
          description: 商户ID
        merchant_name:
          type: string
          description: 商户名称
        merchant_code:
          type: string
          description: 商户编码
        merchant_category:
          oneOf:
          - type: 'null'
          - $ref: '#/components/schemas/MerchantCategoryInfo'
            description: 商户分类信息（可能为空）
        merchant_avatar:
          type:
          - string
          - 'null'
          description: 商户头像logo（可能为空）
        user_roles:
          type: array
          items:
            $ref: '#/components/schemas/MerchantRoleInfo'
          description: 用户在该商户下的角色列表
      example:
        merchant_avatar: https://example.com/avatar.jpg
        merchant_category:
          category_id: 550e8400-e29b-41d4-a716-************
          category_name: 餐饮服务
        merchant_code: REST001
        merchant_id: 123456
        merchant_name: 张三餐厅
        user_roles:
        - role_code: merchant_admin
          role_id: 550e8400-e29b-41d4-a716-************
          role_name: 商户管理员
    UserMerchantListRequest:
      type: object
      description: 获取用户商户列表请求
      properties:
        include_disabled:
          type: boolean
          description: 是否包含已禁用的商户，默认为false（仅显示启用的商户）
      example:
        include_disabled: false
    UserMerchantListResponse:
      type: object
      description: 用户商户列表响应 - 获取用户关联的所有商户列表
      required:
      - merchants
      - total_count
      properties:
        merchants:
          type: array
          items:
            $ref: '#/components/schemas/UserMerchantListItemResponse'
          description: 商户列表
        total_count:
          type: integer
          description: 商户总数
          minimum: 0
      example:
        merchants:
        - merchant_avatar: https://example.com/avatar.jpg
          merchant_category:
            category_id: 550e8400-e29b-41d4-a716-************
            category_name: 餐饮服务
          merchant_code: REST001
          merchant_id: 123456
          merchant_name: 张三餐厅
          user_roles:
          - role_code: merchant_admin
            role_id: 550e8400-e29b-41d4-a716-************
            role_name: 商户管理员
        total_count: 1
tags:
- name: 商户认证管理
  description: 商户用户认证相关接口，包括登录、获取商户列表、切换商户等功能

# 微信登录接口文档

## 概述

本文档描述了微信登录相关的API接口，支持系统用户和商户用户的微信登录、绑定和解绑功能。

## 接口列表

### 1. 微信登录

**接口地址：** `POST /wechat/login`

**描述：** 通用微信登录接口，支持系统用户和商户用户

**请求参数：**
```json
{
    "code": "061Fwoml0VKPiX1uN5ol0jBqlq3Fwomk",
    "user_type": "merchant",
    "merchant_id": 1001
}
```

**参数说明：**
- `code`: 微信授权码（必填）
- `user_type`: 用户类型，可选值：`system`（系统用户）、`merchant`（商户用户）
- `merchant_id`: 商户ID，当`user_type`为`merchant`时必填

**响应示例：**
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "user_info": {
            "id": "550e8400-e29b-41d4-a716-446655440000",
            "username": "admin",
            "nickname": "张三",
            "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/...",
            "user_type": "system"
        },
        "expires_in": 28800
    }
}
```

### 2. 微信账号绑定

**接口地址：** `POST /wechat/bind`

**描述：** 将微信账号绑定到现有用户账号

**请求参数：**
```json
{
    "code": "061Fwoml0VKPiX1uN5ol0jBqlq3Fwomk",
    "username": "admin",
    "password": "123456",
    "user_type": "system",
    "merchant_id": null
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "绑定成功",
    "data": {
        "message": "微信账号绑定成功"
    }
}
```

### 3. 微信账号解绑

**接口地址：** `POST /wechat/unbind`

**描述：** 解绑当前用户的微信账号（需要登录）

**请求参数：**
```json
{
    "password": "123456"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "解绑成功",
    "data": {
        "message": "微信账号解绑成功"
    }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误 |
| 401 | 微信授权失败或用户未绑定 |
| 409 | 微信账号已被绑定 |
| 500 | 服务器内部错误 |

## 使用流程

### 系统用户微信登录流程
1. 前端调用微信登录获取`code`
2. 调用`/wechat/login`接口，`user_type`设为`system`
3. 如果返回401且提示用户未绑定，则需要先调用`/wechat/bind`进行绑定
4. 绑定成功后再次调用登录接口

### 商户用户微信登录流程
1. 前端调用微信登录获取`code`
2. 调用`/wechat/login`接口，`user_type`设为`merchant`，并提供`merchant_id`
3. 如果返回401且提示用户未绑定，则需要先调用`/wechat/bind`进行绑定
4. 绑定成功后再次调用登录接口

## 注意事项

1. 微信`code`只能使用一次，使用后会失效
2. 商户用户登录时必须提供`merchant_id`
3. 解绑接口需要用户已登录状态
4. 绑定时需要验证用户名密码的正确性

# Invoice-Book 实体关系文档

## 概述

本文档专注于描述 Invoice-Book 系统中实体之间的关系和连接字段，采用**关系优先**和**业务流程驱动**的方式组织内容。

## 系统架构关系图

```
┌─────────────────────────────────────────────────────────────────┐
│                        系统域 (System)                           │
│                                                                 │
│  Users ──1:N── UserRoles ──N:1── Roles                          │
│    │              │               │                             │
│    │              │               │                             │
│    └─────────────多对多───────────┘                             │
│                                  │                              │
│                                  │                              │
│                                  ▼                              │
│                              RolePermissions                     │
│                                  │                              │
│                                  │                              │
│                                  ▼                              │
│                             Permissions                         │
│                                  │                              │
│                                  │ (parent_id 自关联)           │
│                                  ▼                              │
│                             Permission Tree                     │
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  │ 权限模板关系  
                                  │ (业务逻辑关联)
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                      商户业务域 (Business)                        │
│                                                                 │
│  SystemPermissionTemplates ──1:N── MerchantAuthorizedPermissions │
│         │                              │                        │
│         │ (parent_id 自关联)              │                        │
│         ▼                              │                        │
│  Permission Template Tree              │                        │
│                                        │                        │
│                                        ▼                        │
│  Merchants ──1:N── MerchantAuthorizedPermissions                 │
│     │                                  │                        │
│     │                                  │                        │
│     │                                  ▼                        │
│     │                         MerchantRolePermissions            │
│     │                                  │                        │
│     │                                  │                        │
│     │                                  ▼                        │
│     ├─────1:N───── MerchantRoles ──────┘                        │
│     │                  │                                        │
│     │                  │                                        │
│     │                  ▼                                        │
│     │            MerchantUserRoles                              │
│     │                  │                                        │
│     │                  │                                        │
│     │                  ▼                                        │
│     └─────多对多─── MerchantUsers                               │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 一、关系类型分析

### 1.1 一对多关系 (One-to-Many)

#### 1.1.1 Merchants → MerchantRoles
**关系说明**: 一个商户可以有多个角色，一个角色只属于一个商户

**连接字段**:
- **外键**: `merchant_roles.merchant_id` (i64)
- **主键**: `merchants.id` (i64)
- **关系**: `MerchantRoles.merchant_id → Merchants.id`

**业务意义**: 商户角色的数据隔离，每个商户管理自己的角色体系

#### 1.1.2 Merchants → MerchantAuthorizedPermissions
**关系说明**: 一个商户可以被授权多个权限，一个授权权限只属于一个商户

**连接字段**:
- **外键**: `merchant_authorized_permissions.merchant_id` (i64)
- **主键**: `merchants.id` (i64)
- **关系**: `MerchantAuthorizedPermissions.merchant_id → Merchants.id`

**业务意义**: 商户权限的数据隔离，系统管理员控制商户可用功能

#### 1.1.3 SystemPermissionTemplates → MerchantAuthorizedPermissions
**关系说明**: 一个权限模板可以授权给多个商户，一个授权权限对应一个权限模板

**连接字段**:
- **外键**: `merchant_authorized_permissions.permission_template_id` (UUID)
- **主键**: `system_permission_templates.id` (UUID)
- **关系**: `MerchantAuthorizedPermissions.permission_template_id → SystemPermissionTemplates.id`

**业务意义**: 权限模板的具体授权实例，实现权限的标准化管理

#### 1.1.4 MerchantRoles → MerchantRolePermissions
**关系说明**: 一个角色可以拥有多个权限，一个权限分配记录只属于一个角色

**连接字段**:
- **外键**: `merchant_role_permissions.role_id` (UUID)
- **主键**: `merchant_roles.id` (UUID)
- **关系**: `MerchantRolePermissions.role_id → MerchantRoles.id`

#### 1.1.5 MerchantAuthorizedPermissions → MerchantRolePermissions
**关系说明**: 一个授权权限可以分配给多个角色，一个权限分配记录对应一个授权权限

**连接字段**:
- **外键**: `merchant_role_permissions.authorized_permission_id` (UUID)
- **主键**: `merchant_authorized_permissions.id` (UUID)
- **关系**: `MerchantRolePermissions.authorized_permission_id → MerchantAuthorizedPermissions.id`

**业务意义**: 角色权限分配的具体实现，只能分配已授权的权限

#### 1.1.6 Users → UserRoles (系统域)
**关系说明**: 一个系统用户可以拥有多个角色，一个用户角色记录只属于一个用户

**连接字段**:
- **外键**: `user_roles.user_id` (UUID)
- **主键**: `users.id` (UUID)
- **关系**: `UserRoles.user_id → Users.id`

#### 1.1.7 Roles → UserRoles (系统域)
**关系说明**: 一个系统角色可以分配给多个用户，一个用户角色记录对应一个角色

**连接字段**:
- **外键**: `user_roles.role_id` (UUID)
- **主键**: `roles.id` (UUID)
- **关系**: `UserRoles.role_id → Roles.id`

#### 1.1.8 Roles → RolePermissions (系统域)
**关系说明**: 一个系统角色可以拥有多个权限，一个角色权限记录对应一个角色

**连接字段**:
- **外键**: `role_permissions.role_id` (UUID)
- **主键**: `roles.id` (UUID)
- **关系**: `RolePermissions.role_id → Roles.id`

#### 1.1.9 Permissions → RolePermissions (系统域)
**关系说明**: 一个系统权限可以分配给多个角色，一个角色权限记录对应一个权限

**连接字段**:
- **外键**: `role_permissions.permission_id` (UUID)
- **主键**: `permissions.id` (UUID)
- **关系**: `RolePermissions.permission_id → Permissions.id`

### 1.2 多对多关系 (Many-to-Many)

#### 1.2.1 Users ↔ Roles (系统域)
**关系说明**: 系统用户与系统角色的多对多关系

**中间表**: `user_roles`
**连接字段**:
- `user_roles.user_id` (UUID) → `users.id` (UUID)
- `user_roles.role_id` (UUID) → `roles.id` (UUID)

**业务意义**: 系统管理员可以拥有多个角色，一个角色可以分配给多个管理员

#### 1.2.2 Roles ↔ Permissions (系统域)
**关系说明**: 系统角色与系统权限的多对多关系

**中间表**: `role_permissions`
**连接字段**:
- `role_permissions.role_id` (UUID) → `roles.id` (UUID)
- `role_permissions.permission_id` (UUID) → `permissions.id` (UUID)

**业务意义**: 系统角色可以拥有多个权限，一个权限可以分配给多个角色

#### 1.2.3 MerchantUsers ↔ MerchantRoles
**关系说明**: 商户用户与商户角色的多对多关系

**中间表**: `merchant_user_roles`
**连接字段**:
- `merchant_user_roles.user_id` (UUID) → `merchant_users.id` (UUID)
- `merchant_user_roles.role_id` (UUID) → `merchant_roles.id` (UUID)
- `merchant_user_roles.merchant_id` (i64) → `merchants.id` (i64)

**特殊字段**: `merchant_id` 确保数据隔离，用户角色关联必须在同一商户内

**业务意义**: 商户用户可以拥有多个角色，一个角色可以分配给多个用户

#### 1.2.4 MerchantUsers ↔ Merchants
**关系说明**: 商户用户与商户的多对多关系

**中间表**: `merchant_user_roles`
**连接字段**:
- `merchant_user_roles.user_id` (UUID) → `merchant_users.id` (UUID)
- `merchant_user_roles.merchant_id` (i64) → `merchants.id` (i64)

**业务意义**: 商户用户可以关联多个商户，一个商户可以有多个用户

#### 1.2.5 MerchantRoles ↔ MerchantAuthorizedPermissions
**关系说明**: 商户角色与商户授权权限的多对多关系

**中间表**: `merchant_role_permissions`
**连接字段**:
- `merchant_role_permissions.role_id` (UUID) → `merchant_roles.id` (UUID)
- `merchant_role_permissions.authorized_permission_id` (UUID) → `merchant_authorized_permissions.id` (UUID)
- `merchant_role_permissions.merchant_id` (i64) → `merchants.id` (i64)

**特殊字段**: `merchant_id` 确保数据隔离，角色权限分配必须在同一商户内

**业务意义**: 商户角色可以拥有多个权限，一个权限可以分配给多个角色

### 1.3 自关联关系 (Self-Referencing)

#### 1.3.1 Permissions 自关联 (系统域)
**关系说明**: 系统权限的树形结构

**连接字段**:
- **外键**: `permissions.parent_id` (UUID, nullable)
- **主键**: `permissions.id` (UUID)
- **关系**: `Permissions.parent_id → Permissions.id`

**业务意义**: 构建系统权限的层次结构，支持菜单树形显示

#### 1.3.2 SystemPermissionTemplates 自关联
**关系说明**: 权限模板的树形结构

**连接字段**:
- **外键**: `system_permission_templates.parent_id` (UUID, nullable)
- **主键**: `system_permission_templates.id` (UUID)
- **关系**: `SystemPermissionTemplates.parent_id → SystemPermissionTemplates.id`

**业务意义**: 构建权限模板的层次结构，为商户提供标准化的权限树

## 二、业务流程驱动的关系链路

### 2.1 系统管理员权限获取流程

```
1. 系统管理员登录
   Users (username/password) → 认证

2. 查询管理员角色
   Users.id → UserRoles.user_id → UserRoles.role_id → Roles.id

3. 查询角色权限
   Roles.id → RolePermissions.role_id → RolePermissions.permission_id → Permissions.id

4. 构建权限树
   Permissions.parent_id → Permissions.id (递归构建)

5. 权限验证
   用户权限集合 ∩ 操作所需权限 = 权限验证结果
```

**关键连接字段链路**:
```
Users.id → UserRoles.user_id
UserRoles.role_id → Roles.id
Roles.id → RolePermissions.role_id
RolePermissions.permission_id → Permissions.id
Permissions.parent_id → Permissions.id (自关联)
```

### 2.2 商户权限授权流程

```
1. 系统管理员选择权限模板
   SystemPermissionTemplates (权限模板选择)

2. 创建商户授权权限
   SystemPermissionTemplates.id → MerchantAuthorizedPermissions.permission_template_id
   Merchants.id → MerchantAuthorizedPermissions.merchant_id

3. 权限模板树形结构
   SystemPermissionTemplates.parent_id → SystemPermissionTemplates.id (递归)

4. 数据隔离验证
   MerchantAuthorizedPermissions.merchant_id = 目标商户ID
```

**关键连接字段链路**:
```
SystemPermissionTemplates.id → MerchantAuthorizedPermissions.permission_template_id
Merchants.id → MerchantAuthorizedPermissions.merchant_id
SystemPermissionTemplates.parent_id → SystemPermissionTemplates.id (自关联)
```

### 2.3 商户角色权限分配流程

```
1. 商户管理员选择角色
   MerchantRoles (角色选择，必须是同一商户)

2. 查询商户已授权权限
   Merchants.id → MerchantAuthorizedPermissions.merchant_id

3. 分配权限给角色
   MerchantRoles.id → MerchantRolePermissions.role_id
   MerchantAuthorizedPermissions.id → MerchantRolePermissions.authorized_permission_id
   Merchants.id → MerchantRolePermissions.merchant_id (数据隔离)

4. 权限范围验证
   只能分配已授权权限: MerchantAuthorizedPermissions.status = 0 (正常)
```

**关键连接字段链路**:
```
Merchants.id → MerchantRoles.merchant_id
MerchantRoles.id → MerchantRolePermissions.role_id
MerchantAuthorizedPermissions.id → MerchantRolePermissions.authorized_permission_id
Merchants.id → MerchantRolePermissions.merchant_id (数据隔离)
```

### 2.4 商户用户权限获取流程

```
1. 商户用户登录
   MerchantUsers (phone/password) → 认证

2. 查询用户角色关联
   MerchantUsers.id → MerchantUserRoles.user_id
   MerchantUserRoles.role_id → MerchantRoles.id
   MerchantUserRoles.merchant_id → Merchants.id (数据隔离)

3. 查询角色权限
   MerchantRoles.id → MerchantRolePermissions.role_id
   MerchantRolePermissions.authorized_permission_id → MerchantAuthorizedPermissions.id

4. 获取权限模板信息
   MerchantAuthorizedPermissions.permission_template_id → SystemPermissionTemplates.id

5. 构建权限树
   SystemPermissionTemplates.parent_id → SystemPermissionTemplates.id (递归)

6. 权限验证
   用户权限集合 ∩ 操作所需权限 = 权限验证结果
```

**关键连接字段链路**:
```
MerchantUsers.id → MerchantUserRoles.user_id
MerchantUserRoles.role_id → MerchantRoles.id
MerchantUserRoles.merchant_id → Merchants.id (数据隔离)
MerchantRoles.id → MerchantRolePermissions.role_id
MerchantRolePermissions.authorized_permission_id → MerchantAuthorizedPermissions.id
MerchantAuthorizedPermissions.permission_template_id → SystemPermissionTemplates.id
SystemPermissionTemplates.parent_id → SystemPermissionTemplates.id (自关联)
```

## 三、数据隔离策略

### 3.1 系统级隔离
- **系统域** (system schema) 与 **商户域** (business schema) 完全隔离
- 系统管理员无法直接操作商户业务数据
- 通过权限模板进行间接控制

### 3.2 商户级隔离
**关键隔离字段**: `merchant_id` (i64)

**隔离范围**:
- `merchants.id` → `merchant_roles.merchant_id`
- `merchants.id` → `merchant_authorized_permissions.merchant_id`
- `merchants.id` → `merchant_role_permissions.merchant_id`
- `merchants.id` → `merchant_user_roles.merchant_id`

**隔离规则**:
- 所有商户数据操作都必须带上 `merchant_id` 条件
- 跨商户的数据关联被物理阻断
- 角色、权限、用户都按商户隔离

### 3.3 用户级隔离
**个人数据范围**: `merchant_roles.data_scope = 2`

**隔离实现**:
- 用户只能查看自己创建的数据
- 通过 `created_by` 字段进行过滤
- 适用于订单、客户等业务数据

## 四、关系约束和验证

### 4.1 外键约束
```sql
-- 商户用户角色关联的多重约束
ALTER TABLE merchant_user_roles 
ADD CONSTRAINT fk_merchant_user_roles_user_id 
FOREIGN KEY (user_id) REFERENCES merchant_users(id);

ALTER TABLE merchant_user_roles 
ADD CONSTRAINT fk_merchant_user_roles_role_id 
FOREIGN KEY (role_id) REFERENCES merchant_roles(id);

ALTER TABLE merchant_user_roles 
ADD CONSTRAINT fk_merchant_user_roles_merchant_id 
FOREIGN KEY (merchant_id) REFERENCES merchants(id);
```

### 4.2 数据一致性验证
```sql
-- 确保用户角色关联在同一商户内
ALTER TABLE merchant_user_roles 
ADD CONSTRAINT ck_merchant_user_roles_same_merchant 
CHECK (
    merchant_id = (SELECT merchant_id FROM merchant_roles WHERE id = role_id)
);

-- 确保角色权限分配在同一商户内
ALTER TABLE merchant_role_permissions 
ADD CONSTRAINT ck_merchant_role_permissions_same_merchant 
CHECK (
    merchant_id = (SELECT merchant_id FROM merchant_roles WHERE id = role_id)
    AND 
    merchant_id = (SELECT merchant_id FROM merchant_authorized_permissions WHERE id = authorized_permission_id)
);
```

### 4.3 业务逻辑验证
- **权限分配验证**: 只能分配已授权权限
- **角色类型验证**: 管理员角色不能删除
- **数据范围验证**: 根据角色数据权限范围过滤数据
- **状态验证**: 禁用的权限不能分配，锁定的用户不能登录

## 五、性能优化建议

### 5.1 索引优化
```sql
-- 商户用户角色查询优化
CREATE INDEX idx_merchant_user_roles_user_merchant 
ON merchant_user_roles(user_id, merchant_id);

-- 角色权限查询优化
CREATE INDEX idx_merchant_role_permissions_role_merchant 
ON merchant_role_permissions(role_id, merchant_id);

-- 权限树查询优化
CREATE INDEX idx_permission_templates_parent_order 
ON system_permission_templates(parent_id, order_num);
```

### 5.2 查询优化
- 使用 `JOIN` 代替 `N+1` 查询
- 权限验证时使用 `EXISTS` 代替 `COUNT`
- 树形结构使用递归 CTE 或者预计算路径

### 5.3 缓存策略
- 用户权限集合缓存 (Redis Set)
- 权限树结构缓存 (Redis Hash)
- 角色权限映射缓存 (Redis Hash)

## 六、总结

### 6.1 核心关系特点
1. **分层架构**: 系统层 → 模板层 → 商户层 → 用户层
2. **数据隔离**: 通过 `merchant_id` 实现商户级隔离
3. **权限继承**: 用户 → 角色 → 权限 → 权限模板
4. **树形结构**: 权限和权限模板支持层次化管理

### 6.2 关键连接字段
- `merchant_id`: 商户级数据隔离的核心字段
- `permission_template_id`: 连接权限模板和授权权限
- `authorized_permission_id`: 连接授权权限和角色权限
- `parent_id`: 构建树形结构的自关联字段

### 6.3 业务流程要点
1. **权限模板** 是系统层向商户层的权限传递桥梁
2. **授权权限** 是商户实际可用权限的具体实例
3. **角色权限** 是权限分配的具体实现
4. **用户权限** 通过角色继承获得

这种设计实现了权限的精细化控制、数据的安全隔离，同时保持了系统的灵活性和可扩展性。 
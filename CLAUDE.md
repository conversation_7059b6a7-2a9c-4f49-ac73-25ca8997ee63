# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a multi-tenant B2B invoice management SaaS platform built with Rust and Axum framework. It's designed as a "online ordering, offline payment" system providing order management, inventory management, and financial reconciliation for merchants.

API Documentation: https://amd2g7rffe.apifox.cn/ (password: invoice-book)

## Common Development Commands

### Building and Running
```bash
# Build the entire workspace
cargo build

# Build in release mode
cargo build --release

# Run the main application service
cargo run --bin app-service

# Run in development mode with specific environment
APP_ENV=dev cargo run --bin app-service

# Generate OpenAPI documentation
cargo run --bin generate_docs

# Run code generation demo (shows lib-codegen capabilities)
cargo run --bin demo_new_architecture

# Run WeChat integration example
cargo run --example wechat_test -p lib-wechat

# Check code without building (faster)
cargo check
```

### Testing
```bash
# Run all tests in the workspace
cargo test

# Run tests with output displayed
cargo test -- --nocapture

# Run tests for a specific crate
cargo test -p lib-sharding
cargo test -p app-service

# Run benchmarks (available in lib-sharding)
cargo bench

# Run specific integration tests
cargo test integration_test

# Run tests for specific modules
cargo test test_name -- --exact
```

### Code Quality
```bash
# Format all code in the workspace
cargo fmt

# Check formatting without changes
cargo fmt -- --check

# Run clippy lints
cargo clippy

# Run clippy and treat warnings as errors
cargo clippy -- -D warnings

# Fix clippy suggestions automatically
cargo clippy --fix
```

### Database Setup
```bash
# Start database services
docker-compose up -d postgres redis

# Setup database schema (run SQL scripts in order)
docker exec -i postgres psql -U admin -d invoice_book < database/invoice-book.sql
docker exec -i postgres psql -U admin -d invoice_book < database/merchants.sql
docker exec -i postgres psql -U admin -d invoice_book < database/merchant_commission_rules.sql
docker exec -i postgres psql -U admin -d invoice_book < database/merchant_follower_commission.sql
docker exec -i postgres psql -U admin -d invoice_book < database/partition_strategy.sql
docker exec -i postgres psql -U admin -d invoice_book < database/rls-policies.sql
docker exec -i postgres psql -U admin -d invoice_book < database/add_role_code_to_system_roles.sql

# For production deployment
./script/migrate-server-db-fixed.sh
```

### Docker Services
```bash
# Start all services (includes nginx reverse proxy)
docker-compose up -d

# View logs for main application service
docker-compose logs -f app

# Build Docker images with enhanced script
./script/build-docker.sh

# Export local database for backup
./script/export-local-db.sh

# Database credentials: admin/123456 on localhost:5432/invoice_book
```

### Configuration
- Development config: `crates/services/app-service/config/app-dev.toml`
- Production config: `crates/services/app-service/config/app-prod.toml`
- Test config: `crates/services/app-service/config/app-test.toml`

**Configuration includes:**
- Database connections (PostgreSQL + Redis)
- JWT authentication settings (separate keys for system vs merchant tokens)
- WeChat Mini Program integration (AppID, AppSecret, API endpoints)
- Alibaba Cloud OSS storage settings
- Logging and monitoring configuration
- Server binding and CORS policies

## Architecture Overview

### Project Structure
```
crates/
├── libs/           # Shared libraries
│   ├── lib-auth/   # Authentication and authorization
│   ├── lib-cache/  # Redis caching layer
│   ├── lib-codegen/ # Code generation system for CRUD scaffolding
│   ├── lib-core/   # Core utilities and app framework
│   ├── lib-data/   # Database and data layer
│   ├── lib-job/    # Job scheduling
│   ├── lib-macros/ # Procedural macros and sharded entity support
│   ├── lib-sharding/ # SQL query rewriting and table partitioning
│   ├── lib-store/  # File storage (Alibaba Cloud OSS)
│   ├── lib-wechat/ # WeChat API integration and authentication
│   └── lib-web/    # Web framework utilities
└── services/
    └── app-service/ # Main application service
```

### Key Technologies
- **Web Framework**: Axum with Tower middleware
- **Database**: PostgreSQL with Sea-ORM (no migrations - uses raw SQL scripts)
- **Authentication**: JWT-based with custom middleware + WeChat OAuth integration
- **Caching**: Redis
- **File Storage**: Alibaba Cloud OSS (configured as S3-compatible)
- **WeChat Integration**: Complete WeChat Mini Program API integration with OAuth
- **API Documentation**: OpenAPI 3.0 with utoipa
- **Configuration**: TOML-based environment configs
- **Job Scheduling**: tokio-cron-scheduler for background tasks
- **Testing**: Standard Rust testing with Criterion benchmarks

### Multi-Tenant Architecture
- Data isolation by `merchant_id` with automatic schema creation per merchant
- Advanced table partitioning and sharding via `lib-sharding` with SQL query rewriting
- RBAC (Role-Based Access Control) with fine-grained permissions
- **Dual-level role system**:
  - **System roles**: Platform administration (system admins, operators) - manage merchants, platform settings
  - **Merchant roles**: Per-merchant custom roles (store admin, staff, inventory manager) - business operations within tenant
- Row-level security (RLS) policies for complete data isolation
- Automatic tenant provisioning with dedicated schemas and partitioned tables

### Plugin System
The application uses a plugin architecture:
- `DataPlugin`: Database connections and migrations
- `RedisPlugin`: Caching layer
- `WebPlugin`: HTTP server and routing
- `JobPlugin`: Background job scheduling
- `StoragePlugin`: File storage operations
- `WechatPlugin`: WeChat Mini Program API integration and OAuth authentication

### WeChat Integration
The platform includes comprehensive WeChat Mini Program integration through `lib-wechat`:

**Key Features:**
- WeChat OAuth authentication flow (login, token refresh)
- WeChat API client with automatic retry and error handling
- Support for WeChat Mini Program code-to-session exchange
- Account binding functionality for existing users
- Configurable endpoints for different WeChat services (mini-program, payment, messaging)

**Authentication Flow:**
- Users authenticate via WeChat authorization code
- System exchanges code for WeChat session and user information
- JWT tokens issued for API access
- Supports both new user registration and existing account binding

**Configuration:**
WeChat settings are configured in environment-specific TOML files with options for:
- Mini Program credentials (AppID, AppSecret)
- API endpoints and timeouts
- Error handling and retry policies

**Usage:**
- Public routes: `/wechat/login`, `/wechat/bind` (no authentication required)
- Run WeChat integration example: `cargo run --example wechat_test -p lib-wechat`

## Domain Structure

### System Level
- Users, Roles, Permissions
- Merchant management
- System-wide categories and tags

### Business Level
- Multi-tenant merchant operations
- Products, inventory, and categories
- Orders and financial records
- Customer management

### Key Business Rules
1. **Multi-tenant data isolation**: All business data partitioned by merchant_id
2. **Role-based permissions**: System admins vs merchant admins vs staff
3. **Inventory management**: FIFO (First-In-First-Out) stock rotation
4. **Financial separation**: Staff cannot view cost/profit data

## Development Notes

### Environment Configuration
- Use `APP_ENV` environment variable to switch between dev/test/prod
- Configuration files are merged: base config + environment-specific config
- Default development database: PostgreSQL on localhost:5432

### Authentication Flow
- JWT tokens for API authentication
- IP resolution middleware for request tracking
- Role-based access control at endpoint level
- Merchant-specific data access controls

### Database Schema
- **No Sea-ORM migrations**: Uses raw SQL scripts in `database/` directory
- Multi-tenant with automatic partitioning by merchant_id
- **Two main schemas**: `system` (platform data) and `business` (merchant data)
- Row-level security policies for tenant isolation
- Custom sharding system with SQL query rewriting (`lib-sharding`)
- Cascade deletion for merchant data cleanup

### API Documentation
- OpenAPI 3.0 specs generated automatically
- Documentation available at `/docs/openapi.json` and `/docs/openapi.yaml`
- Run `cargo run --bin generate_docs` to update docs

## Testing and Deployment

### Local Development Setup
1. Start services: `docker-compose up -d postgres redis`
2. **Setup database**: Run SQL scripts in sequence (see Database Setup section)
3. Run application: `APP_ENV=dev cargo run --bin app-service`
4. Generate docs: `cargo run --bin generate_docs`

### Testing Strategy
- **Unit tests**: Embedded in source files (standard Rust conventions)
- **Integration tests**: Comprehensive SQL rewriting tests in `lib-sharding/tests/`
- **Benchmarks**: Criterion performance tests in `lib-sharding`
- **Test running**: Use `cargo test -p <crate_name>` for specific crates
- **No CI/CD**: Manual testing and deployment currently

### Docker Deployment
- Production image builds with `cargo build --release`
- Multi-stage Dockerfile for optimized images
- Environment-specific configurations
- Health checks and logging configured

### Key Ports
- Application: 9002
- PostgreSQL: 5432
- Redis: 6379
- Nginx: 80/443

## Important Implementation Details

### Permission System
- System roles: Platform-level permissions (admins, operators)
- Merchant roles: Store-level permissions (custom roles per merchant)
- Staff restrictions: Cannot view cost prices or profit margins
- Cross-merchant employee support: One user can have roles in multiple merchants

### File Storage
- Alibaba Cloud OSS integration for file uploads
- S3-compatible API with custom endpoint configuration
- Presigned URLs for secure file access
- Automatic file cleanup on record deletion
- Custom domain support (static.oywm.top)

### Job Scheduling
- Cron-based job scheduling for maintenance tasks
- Background processing for data cleanup
- Configurable retention policies

## Code Structure and Patterns

### Controller Layer
- Public routes: No authentication required (e.g., login, health checks)
- Protected routes: JWT authentication middleware applied
- System controllers: Platform-level operations (users, merchants, roles)
- Business controllers: Merchant-specific operations (orders, products)

### Service Layer
- Service implementations in `service/` directory
- Business logic separated from controllers
- Data access through Sea-ORM entities
- Validation using `validator` crate

### Domain Structure
- **System domain**: Platform-level entities (users, roles, permissions)
- **Business domain**: Merchant-specific entities (orders, products, customers)
- **Common domain**: Shared utilities and storage operations
- DTOs for request/response data transfer
- VOs for view objects and API responses

### Authentication Architecture
- JWT-based authentication with access/refresh tokens
- IP resolution middleware for request tracking
- Multi-level authorization:
  - System-level permissions for platform operations
  - Merchant-level permissions for business operations
  - Row-level security for data isolation

### Database Design
- Multi-tenant architecture with merchant_id-based partitioning
- Separate schemas: `public` (system data), `business` (merchant data)
- Row-level security policies for tenant isolation
- Automatic table partitioning for large datasets
- Cascade deletion for merchant data cleanup

## Important Development Notes

### Database Migration Process
- **NO Sea-ORM migrations**: This project uses raw SQL scripts for schema management
- New schema changes go in `database/` directory as `.sql` files
- Test locally by executing SQL manually against Docker PostgreSQL
- Production deployment uses `./script/migrate-server-db-fixed.sh`

### Multi-Tenant Architecture Details
- All business data isolated by `merchant_id` with automatic table partitioning
- **Advanced Sharding System (`lib-sharding`)**:
  - SQL query rewriting and table name transformation at query time
  - AST-based SQL parsing with visitor pattern for query modification
  - Template-based table naming strategies (e.g., `orders` → `merchant_123_orders`)
  - Automatic schema creation per merchant for complete data isolation
  - Strategy pattern for different partitioning approaches (by merchant_id, time-based, etc.)
- Row-level security (RLS) policies enforce data access controls as secondary security layer
- Session context required for proper authorization and tenant resolution
- Comprehensive integration tests in `lib-sharding/tests/integration_test.rs` covering SQL rewriting scenarios

### Code Generation and Macros
- `lib-codegen`: Complete CRUD scaffolding generation from database tables
  - Run `cargo run --bin demo_new_architecture` to see code generation capabilities
  - Template-based system for controllers, services, and entities
- `lib-macros`: Procedural macros for boilerplate reduction
  - `#[sharded_entity]` macro for automatic table partitioning support
- `lib-sharding`: Automatic table partitioning and SQL rewriting
  - Active development tracked in `crates/libs/lib-sharding/TASK_LIST.md`
  - Comprehensive test suite in `tests/integration_test.rs`

### Development Workflow
1. Use `cargo check` for fast compilation feedback
2. Run `cargo clippy` before committing changes  
3. Test database changes manually before adding to scripts
4. Generate OpenAPI docs after API changes: `cargo run --bin generate_docs`
5. **Never use Sea-ORM migrations** - all schema changes via SQL scripts in `database/`
6. Use code generation for new CRUD operations: `cargo run --bin demo_new_architecture`
7. Test sharding functionality with: `cargo test -p lib-sharding`

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.

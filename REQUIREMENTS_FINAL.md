# 开单记账系统需求文档

## 📋 系统概述

**开单记账系统** 是一个多商户B2B管理SaaS平台，采用**线上开单、线下付款**的业务模式，为商户提供订单管理、商品管理、库存管理、财务对账等一体化管理解决方案。

## 🏗️ 技术架构

### 技术栈
- **前端**: UniApp (主要通过小程序实现)
- **后端**: Rust + Axum
- **数据库**: PostgreSQL
- **数据隔离**: 根据商户ID自动分表，各商户数据完全隔离

### 核心特性
- 多租户数据隔离架构
- 自动分表机制 (商品数据、账单数据等)
- 级联删除 (删除商户时清除所有相关数据)
- 定时清理 (可设置账单数据每年固定日期自动清零)

## 👥 用户角色权限体系

### 角色层级结构
```
系统层级
├── 系统超级管理员 (System Super Admin)
│   └── 系统角色 (System Roles) - 系统级角色分配
├── 普通用户 (Guest User) - 浏览展示
└── 商户 (Merchant Admin) - 店铺管理员
    └── 商户角色员工 (Merchant Role Staff) - 商户自定义角色的员工
```

### 双层角色体系说明

#### 系统级角色 (System Level Roles)
- **系统超级管理员**: 平台最高权限，负责平台管理、商户管理、系统配置
- **系统角色**: 系统超级管理员可以创建和分配的系统级角色
  - 可能包括：系统运营、客服管理员、数据分析师等
  - 由系统超级管理员定义权限范围
  - 主要用于平台运营管理

#### 商户级角色 (Merchant Level Roles)
- **商户**: 店铺管理员，拥有店铺全权管理
- **商户角色员工**: 商户自定义创建的角色，分配给员工
  - 商户可以自定义角色名称（如：销售经理、开单员、仓库管理员等）
  - 每个角色由商户分配具体的功能权限
  - 一个员工可以在多个商户下拥有不同角色(可以加入多个商户)

### 角色权限说明
- **系统超级管理员**: 平台管理，商户管理，系统配置，基础数据维护，系统角色分配
- **系统角色**: 由系统超级管理员分配的系统级权限，主要用于平台运营
- **普通用户**: 只能浏览商品展示，不能查看价格和下单
- **商户**: 店铺全权管理，可创建商户角色并分配给员工
- **商户角色员工**: 根据商户分配的角色权限，执行相应功能（开单、库存管理等）

### 权限分配机制
```
权限分配流程：
系统超级管理员 → 系统角色 (系统级权限分配)
系统超级管理员 → 商户 (菜单功能权限分配)
商户 → 商户角色 → 员工 (店铺功能权限分配)
```

### 员工多角色支持
- **跨商户角色**: 一个员工账户可以在多个商户下拥有不同角色
- **角色隔离**: 在不同商户下的数据和权限完全隔离
- **统一管理中心**: 员工有独立的管理中心，可以切换不同商户角色

## 📱 小程序使用流程

### 普通用户体验流程
```
小程序首页 → 系统推荐商品界面
├── 搜索平台所有商品
├── 分类筛选 (系统商品分类)
├── 标签筛选 (系统管理员维护)
├── 地理位置筛选
└── 进入店铺
    ├── 查看店铺信息
    ├── 查看店铺商品列表
    └── 无法查看价格 (纯展示用途)
    └── 无法下单购买 (仅浏览功能)
```

**重要限制**: 普通用户只能浏览商品信息，**不能查看价格，不能下单购买**

### 管理端登录机制
- **入口位置**: 分类界面管理端登录入口
- **访问权限**: 商户及角色员工
- **跳转逻辑**: 登录后直接跳转到对应管理端

### 角色员工开单业务流程

**下单权限**: 只有商户设定的**角色员工**才能在其所属店铺下单，普通用户无下单权限

**开单流程**:
```
角色员工选择商品规格数量 → 选择客户 → 自动生成订单 (含商家收款信息)
                    ↓
订单可以打折 → 生成订单海报 → 分享给客户 → 客户线下付款
                    ↓  
员工确认下单 → 商家后台生成待接单订单 → 商家确认收款 → 接单进入待出货状态
```

**核心特点**: 
- **线上开单，线下付款**，不涉及在线支付功能
- **权限限制**: 角色员工只能在其被授权的店铺内开单
- **数据隔离**: 员工只能看到自己开单的订单和客户数据

## 🏪 商户管理端功能

### 主要功能模块
```
商户管理端
├── 订单管理
├── 商品管理
├── 财务对账
├── 客户管理
├── 仓库管理
├── 商户管理 (仅系统超级管理员)
├── 门店设置
├── 角色管理
├── 商品批量导入模板
├── 打印设置
└── 当前账号
```

### 1. 订单管理

#### 1.1 来单信息展示
**功能描述**: 展示和管理订单信息

**显示信息**:
- **员工信息**: 姓名、电话、角色
- **商品信息**: 所属库区、位置编号、生产日期
- **价格信息**: 订单原价、成交价

#### 1.2 订单状态管理
**员工视角**:
- 待确认订单
- 已完成订单
- 支持时间区间查询

**商家视角**:
- 待接单订单
- 待出货订单
- 已完成订单
- 支持时间区间查询

**查询维度**:
- 自定义时间区间
- 订单状态筛选
- 员工筛选

### 2. 商品管理

#### 2.1 商品分类管理
**分类层级结构**:
```
系统商品分类 (二级分类，系统超级管理员维护)
└── 商户商品分类 (一级分类，商户自定义)
```

**功能特性**:
- 发布商品时可设置多个系统商品分类
- 支持分类层级管理

#### 2.2 商品发布管理
**功能描述**: 商户发布商品到平台

**发布配置**:
- 系统商品分类可设置多选
- 商品标签选择 (由系统管理员编辑维护)
- 游客可通过标签快速筛选商品

**智能预警设置** (在商品发布页面配置):
- **库存预警设置**: 设置商品数量不足预警阈值
- **过期预警设置**: 设置商品过期时间预警天数
- **滞销预警设置**: 设置商品滞销时间预警天数

**预警计算规则**:
```
库存预警: 商品数量 < 设定阈值
过期预警: 商品距离过期时间 = 保质期 - (当日 - 出厂时间) < 设定天数
滞销预警: 滞销天数 = 当日 - 入库时间 > 设定天数
```

#### 2.3 商品标签系统
- **管理权限**: 系统超级管理员编辑维护
- **使用场景**: 商家发布商品时选择标签，普通用户快速筛选

### 3. 财务对账

#### 3.1 整体看板
**角色权限差异**:
- **角色员工**: 只显示个人销售数据
- **商家管理员**: 显示店铺所有订单数据

#### 3.2 时间维度统计
**支持时间段**:
- 今日数据
- 近七日数据  
- 近15日数据
- 自定义具体时间段

#### 3.3 关键财务指标
**基础指标** (所有角色可查看):
- 总营业额
- 预计收入  
- 有效订单数量

**高级指标** (仅商家管理员可查看):
- 毛利润

**毛利润计算公式**:
```
毛利润 = 规格商品售价 - 规格商品出厂价 - 员工佣金
```

#### 3.4 账单详情功能
**功能包含**:
- 实时账单查看
- 历史数据查询
- 账单详情展示
- 订单概况统计

### 4. 客户管理

#### 4.1 客户列表权限控制
**权限控制**:
- **商户**: 查看全部开单员工的客户列表
- **角色员工**: 只能查看自己的客户列表

#### 4.2 客户分类管理
**管理权限**: 客户分类名称的编辑和新增由超级管理员统一设定

#### 4.3 客户档案信息
**基础信息**:
- 客户名字、电话
- 月销量、年销量

**购买记录**:
- 已购商品列表
- 支持自定义时间段查询相应内容
- 商品信息包含销售额、规格等详细信息

### 5. 仓库管理

#### 5.1 库区分类管理
**功能描述**:
- 商家可新建库区分类用于组织管理
- 库区分类主要用于商品规格选择时的分组
- 库区分类显示对应的库存商品汇总信息

#### 5.2 商品规格管理

##### 入库字段信息
- **基础信息**: 规格名称、价格、总库存、品牌、供应商、保质期
- **库存信息**: 入库数量、生产日期、位置编号、实际库存
- **所属库区**: 根据商品规格入库时选择对应库区分类
- **自动字段**: 
  - 入库时间 (填写入库时自动生成)
  - 入库人 (自动识别当前操作员工)

##### 权限控制
- **进货价字段**: 角色员工不可查看
- **查看权限**: 仅商家管理员可查看成本信息

#### 5.3 智能预警系统

**注意**: 预警规则设置已移至商品发布页面配置，此处仅显示预警结果

##### 过期预警显示
**预警提示**: "下列商品离过期时间不到 X 天" (X天数在商品发布时设置)

##### 滞销预警显示  
**预警提示**: "下列商品进库时间已经超过 X 天了" (X天数在商品发布时设置)

##### 库存预警显示
**预警提示**: "下列商品数量不足 X" (X数量在商品发布时设置)

#### 5.4 先进先出库存机制
**业务规则**:
- 客户购买商品时，优先减去较早出厂日期规格库存
- 例如: 有20241120、20241230两个出厂日期的库存
- 客户下单后，优先减去20241120规格对应的库存
- 确保库存周转遵循先进先出原则

### 6. 门店设置

#### 6.1 佣金规则配置
**基础佣金**:
```
开单奖励佣金 = 订单金额 × 佣金比例%
```

**阶梯佣金规则** (可添加多组规则):
- 满足年累计销售额达到指定金额 OR 客户数达到指定数量
- 满足其中一项条件即可享受对应佣金比例
- 支持多层级阶梯设置

### 7. 角色管理

#### 7.1 角色分类管理
**功能包含**:
- 角色分类列表展示
- 查看角色分类中的用户

#### 7.2 用户信息展示
**显示信息**:
- 用户名字、电话
- 月销量、年销量
- 上月收入、累计年收入
- 收入分红明细 (该角色的财务对账)

#### 7.3 角色权限配置
**管理功能**:
- 商户可自定义新增和编辑角色名称
- 分配对应权限给角色

### 8. 商品批量导入

#### 8.1 模板功能
- 提供标准导入模板下载
- 支持批量商品信息上传
- 数据格式验证

### 9. 打印设置

#### 9.1 打印机支持
- **打印机兼容**: 优先支持美团/饿了么同款小票打印机
- **接口选择**: 市面主流打印机接口
- **输出格式**: 标准小票格式

### 10. 当前账号
- 账号信息管理
- 权限查看
- 个人设置

## 🔧 系统超级管理员功能

### 1. 商户管理

#### 1.1 商户列表管理
- 查看分类商户列表
- 按商户分类筛选查看

#### 1.2 商户详细信息
**基本信息包含**:
- 经营有效期
- 月营业额、年营业额度
- 平台抽成比例
- 跟进人信息、跟进人佣金比例

#### 1.3 商户操作功能
- 添加新商户
- 临时关闭店铺
- 永久删除店铺 (数据库中关于商户的所有数据将被删除)

### 2. 基础数据管理

#### 2.1 分类管理 (对应增删改查)
- 商户分类管理
- 客户分类管理
- 系统商品标签分类管理
- 系统商品分类管理

## 🔐 权限设计详细说明

### 数据可见性权限控制
| 角色 | 订单数据 | 财务数据 | 客户数据 | 库存数据 | 成本信息 |
|------|----------|----------|----------|----------|----------|
| 系统超级管理员 | 全平台 | 全平台概览 | 全平台 | 全平台概览 | 全平台 |
| 系统角色 | 根据分配权限 | 根据分配权限 | 根据分配权限 | 根据分配权限 | 根据分配权限 |
| 商户 | 本店铺全部 | 本店铺全部 | 本店铺全部 | 本店铺全部 | 可查看 |
| 商户角色员工 | 根据角色权限 | 根据角色权限 | 根据角色权限 | 根据角色权限 | 不可查看 |

### 商户角色员工特殊限制
- 不能查看规格入库字段中的**进货价**
- 不能查看财务看板中的**毛利润**
- 权限颗粒度需精确控制，由商户自定义分配

### 系统角色权限设计
- **系统角色权限范围**: 由系统超级管理员定义和分配
- **常见系统角色**: 
  - 系统运营：商户管理、数据统计
  - 客服管理员：问题处理、用户支持
  - 数据分析师：平台数据分析、报表查看
- **权限可配置**: 系统超级管理员可以灵活配置系统角色的具体权限

### 员工管理中心设计
- 一个员工账户可以成为多个商户的员工
- 员工有独立的管理中心界面
- 需要合理的账户设计机制

### 多租户数据隔离
- 所有业务数据按 `merchant_id` 进行租户隔离
- 角色权限控制访问范围
- 确保数据安全和隐私保护

## 📊 数据管理策略

### 自动分表机制
- 商品数据按 merchant_id 分表
- 账单数据按 merchant_id 分表
- 确保各商户数据完全隔离

### 数据生命周期管理
- 商户删除商品 → 数据库物理删除
- 系统管理员删除商户 → 级联删除所有相关数据
- 支持设置账单数据每年固定日期自动清零

### 数据库设计重点

#### 核心表结构
```sql
-- 多租户基础
merchants (商户表)
users (用户表) 
system_roles (系统角色表)
merchant_roles (商户角色表)
permissions (权限表)
system_role_permissions (系统角色权限关联表)
merchant_role_permissions (商户角色权限关联表)
user_system_roles (用户系统角色关联表)
user_merchant_roles (用户商户角色关联表)

-- 业务核心
products (商品表)
product_categories (商品分类)
product_specs (商品规格)
inventory (库存表)
orders (订单表)
order_items (订单明细)
customers (客户表)

-- 财务相关
financial_records (财务记录)
commission_rules (佣金规则)

-- 预警设置
alert_settings (预警设置表)

-- 系统配置
system_categories (系统分类)
system_tags (系统标签)
```

## 🚀 开发优先级建议

### Phase 1: 基础架构 (优先级: ✅ 已完成)
1. ✅ 多租户数据分表架构设计
2. ✅ 细粒度RBAC权限系统  
3. ✅ 用户角色管理体系
4. ✅ 商户基础管理和跟进人佣金系统

### Phase 2: 核心业务 (优先级: 🔥🔥🔥 当前重点)
1. **商品管理模块** (下一步重点开发)
   - 商品分类体系 (系统分类 + 商户分类)
   - 商品基础信息管理 (CRUD + 状态管理)  
   - 商品规格管理 (多规格支持 + 预警设置集成)
   - 商品标签系统和发布管理
2. 订单管理模块 (线上开单流程)
3. 基础库存管理 (与商品规格集成)

### Phase 3: 高级功能 (优先级: 🔥🔥)
1. 财务对账系统 (权限隔离)
2. 客户管理系统
3. 仓库预警系统 (基于商品预警配置)

### Phase 4: 扩展功能 (优先级: 🔥)
1. 小程序前端界面
2. 打印集成
3. 数据导入导出
4. 高级报表和分析

### 📋 当前开发建议

**立即开始**: 商品管理模块开发
**原因**: 
- 基础架构已完善，可直接开发业务功能
- 商品是整个B2B平台的核心基础
- 订单管理、库存管理都依赖商品管理
- 预警设置已从仓库管理整合到商品发布，逻辑更清晰

**开发顺序**:
1. 商品分类管理 → 2. 商品基础管理 → 3. 商品规格+预警 → 4. 商品发布管理

## 📝 补充说明

### 特殊业务规则
1. **所有角色员工**，不能查看规格入库字段中的进货价、以及看板中的毛利润
2. **注意角色权限的颗粒度**，需要精确控制每个功能的访问权限
3. **角色员工管理中心**，因为按账户设计，一个员工可以成为多个商户的员工
4. **账户设计**需要支持多商户关联的复杂场景
5. **下单权限限制**，普通用户不能下单，只有商户角色员工才能在其店铺下单

### 系统特色功能
- **线上开单，线下付款**的独特业务模式
- **先进先出库存管理**的智能算法  
- **多维度预警系统**的智能提醒（预警设置集成在商品发布页面）
- **阶梯佣金规则**的灵活配置
- **细粒度权限控制**的安全保障
- **商品规格集成库区管理**的创新设计 




开单记账
构建思路
打造一个多商户的开单记账管理软件。
涉及到商户管理，订单管理，商品管理，仓库管理，客户管理，数据管理等功能。
系统角色权限可设置超级管理员，
超级管理员可以给商户分配菜单功能权限。
商户可以在得到系统分配的菜单功能权限基础上，
给员工分配其店铺所拥有的功能权限。
前端使用uniapp，
各商户之间数据完全隔离
数据库设计方面，数据库采用pgsql。
商品数据，账单数据等，可根据商户ID自动分表。
系统超级管理员删除商户后，数据库中关于商户的数据也删除。
商户在小程序删除商品，数据库商品数据也删除。
可设置商户账单数据每年固定日期自动清零。

使用流程：前端最主要通过小程序实现，用户分为普通用户、商家、商家自己设置的角色员工。
进去小程序，展示的是系统商品分类界面，看到的是系统所有分类商品图片及简介。也可以进入特定商户的店铺，查看商户分类商品图片及介绍。但不能查看商品价格以及下单购买。
分类界面有管理端登录入口，商户及工作人员以此进入管理端。
商家及商家角色员工登录商家管理端，角色员工只能按分配的权限使用商家端功能，权限由商家分配。



修改重点
1. 使用流程
.关于用户：
普通用户登录小程序，进入系统推荐的商品界面，可以搜索平台所有商品，也可以分类筛选或者按标签、地理位置等条件筛选商品。
可以经商品进入所属店铺，进入店铺后，可以查看店铺信息，和店铺商品列表，不能查看商品价格，单纯做展示用（见下图示例界面）。




●关于角色员工
普通人不能在小程序下单，商家设定的角色员工才能在他的店铺下单
角色员工下单流程：
员工选择对应商品规格数量----选择客户----自动生成带商家收款信息的订单---订单可以打折----生成的订单海报，可以下载分享给客户线下付款---客户付款后员工点击下单---同时，商家后台生成待接单的订单---商家收到款后，点击接单进入待出货状态。整个过程是线上开单，线下付款，不涉及到支付问题。


员工的订单管理界面，最主要显示待确认订单和已完成订单等订单状态。
和商家的一样，可以按时间区间查询。




●关于商家端：
商家及其添加的角色进入小程序，直接跳转到商家管理端，商家管理端由订单管理、商品管理、财务对账、客户管理、仓库管理、商户管理、门店设置、商品批量导入模板、打印设置、当前账号组成。（参考下面示例界面）。






●门店设置
门店设置界面如下图
关键点:开单奖励佣金是按订单金额*（）%
可添加多组规则
比如：
要满足年累计销售额达到（）或者客户数达到（）佣金比例为（）
满足其中一项即可






●商户管理
商户管理这个功能针对系统管理员
进入界面可以查看分类商户列表，系统管理员可以添加商户。
可以查看商户基本信息、经营有效期、月营业额、年营业额、平台抽成、跟进人、跟进人佣金比例。可临时关闭店铺、也可永久删除店铺，删除店铺时，数据库关于商户所有数据都将清除。
同时，系统的商品分类、商品标签分类、客户分类、商户分类等，也在这个界面添加，分类设置界面如下图



●打印设置
打印机接口选择现在市面上的，支持美团或者饿了么那种同款小票打印机，这样可选择多。




●商品批量导入



●当前账号





●订单管理：
来单显示点单员工信息（名字、电话、角色）。以及商品所属库区、位置编号、生产日期，订单原价、成交价等。（见下面示例小程序界面）
可按时间区间查询订单明细






----商品管理
商品分类：商品有两种分类，一种是系统商品分类，一种是商户商品分类。系统商品分类是二级分类，商户商品分类为一级分类。商户发布商品时，系统商品分类可设置多选。
商品标签：商品标签由系统超级管理员编辑，商家在发布商品时，可以选择给产品打上标签，方便游客快速筛选到商品。





----财务对账
财务对账整体看板这里可以查看今日、近七日、近15日、以及具体时间的营业总额、预计收入、有效订单、毛利润（如果是开单员工，无法查看这项）等
毛利润计算方法:毛利润=规格商品售价—规格商品出厂价——员工佣金

可查看实时账单和历史账单数据、账单详情、订单概况等
（注意：开单员工显示的是自己的销售数据，商家管理员显示的是店铺所的订单数据）







●仓库管理：
商家可新建库区分类，分类显示库区商品---点击对应商品---来到商品规格列表界面。
在商品规格里面，添加一些入库字段
(规格名称、价格、总库存、品牌、供应商、保质期、入库数量、生产日期、位置编号、入库时间（入库时间在填写入库时，自动生成）、入库人（自动识别）、实际库存）
重点说明：关于库存预警、过期预警、滞销预警说明：
库存预警，提示：下列商品数量不足（）。（）数量由商家设置。
过期预警，提示：下列商品离过期时间不到（）天。（）天数由商家设置。
过期预警计算方法：商品离过期时间=保质期—（当日—出厂时间），以天为单位。
滞销预警：提示：下列商品进库时间已经超过（）天了，（）天数由商家设置。
滞销预警计算方法：滞销天数=当日—入库时间
 
先进先出原则，客户购买商品时，优先减去较早出厂日期的规格库存。
（比如有20241120，20241230两个出厂日期，
客户下单后，先减去20241120的规格对应的库存，做到先进先出）。








----客户管理
小程序主界面展示的客户分类列表，以及列表里的客户信息。
（注意：开单员工显示的是自己的客户列表，商家管理员显示的是全部开单员工客户列表）
客户分类名称的编辑和新增，由系统超级管理员统一设定。
列表里的客户信息包括用户的名字、电话、月销量、年销量、已购商品列表。
已购商品列表支持自定义时间段查询相应内容，
已购商品列表商品信息包括总销售额、规格1销售额、规格2销售额..........





----角色管理
小程序主界面展示的角色分类列表，以及列表里的角色用户信息。
商家可自定义新增和编辑角色名称，并分配对应权限给角色。
角色用户信息包括用户的名字、电话、月销量、年销量、上月收入、累计年收入以及收入分红明细，分红明细就是该角色的财务对账

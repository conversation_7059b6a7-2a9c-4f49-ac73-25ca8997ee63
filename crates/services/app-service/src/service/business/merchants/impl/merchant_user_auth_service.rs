use crate::domain::business::merchants::dto::merchant_user_request::MerchantUserLoginRequest;
use crate::domain::business::merchants::entities::merchant_roles::role_code;
use crate::domain::business::merchants::entities::merchant_users::ActiveModel;
use crate::domain::business::merchants::vo::merchant_user_vo::{
    MerchantUserLoginResponse, MerchantUserProfileResponse,
};
use crate::repository::{
    MerchantRolePermissionsRepository, MerchantUserMerchantsRepository, MerchantUsersRepository,
    MerchantsRepository, SystemPermissionTemplatesRepository,
};
use crate::utils::password;
use chrono::Local;
use lib_auth::config::SecurityConfig;
use lib_auth::middleware::merchant_session::{MerchantAuthSession, MerchantUserType};
use lib_core::app::app::App;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_core::config::ConfigRegistry;
use lib_core::BusinessError;
use lib_core::DateTimeUtils;
use lib_data::active_value_ext::{ActiveValueExt, ActiveValueNullableExt};
use lib_macros::Service;
use sea_orm::ActiveEnum;
use sea_orm::{ActiveModelTrait, DatabaseConnection, Set};
use tracing::{error, info, warn};

#[derive(Clone, Service)]
pub struct MerchantUserAuthService {
    #[inject(component)]
    db: DatabaseConnection,
    #[inject(component)]
    merchant_users_repository: MerchantUsersRepository,
    #[inject(component)]
    merchant_user_merchants_repository: MerchantUserMerchantsRepository,
    #[inject(component)]
    merchants_repository: MerchantsRepository,
    #[inject(component)]
    merchant_role_permissions_repository: MerchantRolePermissionsRepository,
    #[inject(component)]
    system_permission_templates_repository: SystemPermissionTemplatesRepository,
    #[inject(config)]
    security_config: SecurityConfig,
}
impl MerchantUserAuthService {
    pub async fn login(
        &self,
        req: MerchantUserLoginRequest,
        client_ip: String,
    ) -> Result<MerchantUserLoginResponse, BusinessError> {
        info!("商户用户登录请求: {}，IP: {}", req.phone, client_ip);

        // 根据手机号查询用户基本信息
        let user = self
            .merchant_users_repository
            .find_user_login_info(&req.phone, &self.db)
            .await
            .map_err(|e| {
                error!("查询用户登录信息失败: {}, 手机号: {}", e, req.phone);
                BusinessError::new(500, "系统服务异常，请稍后重试".to_string())
            })?
            .ok_or_else(|| {
                warn!("登录失败：用户不存在, 手机号: {}", req.phone);
                BusinessError::new(400, "用户账号或密码错误".to_string())
            })?;

        // 验证密码
        let password_is_valid =
            password::verify_password(&req.password, &user.password).map_err(|e| {
                error!("密码验证失败: {}", e);
                BusinessError::new(500, "账号或密码错误".to_string())
            })?;

        if !password_is_valid {
            warn!("登录失败：密码错误, 手机号: {}", req.phone);
            return Err(BusinessError::new(400, "账号或密码错误".to_string()));
        }

        // 检查用户状态
        if !user.can_login() {
            warn!(
                "登录失败：用户状态异常, 手机号: {}, 状态: {}",
                req.phone,
                user.get_status_desc()
            );
            return Err(BusinessError::new(
                400,
                format!("账号状态异常：{}", user.get_status_desc()),
            ));
        }

        // 查询用户关联的商户ID列表
        let merchant_ids = self
            .merchant_user_merchants_repository
            .get_user_merchant_ids(user.id, &self.db)
            .await
            .map_err(|e| {
                error!("查询用户关联商户失败: {}, 用户ID: {}", e, user.id);
                BusinessError::new(500, "查询用户关联商户失败".to_string())
            })?;

        // 检查是否有关联的商户
        if merchant_ids.is_empty() {
            warn!("登录失败：用户未关联商户, 手机号: {}", req.phone);
            return Err(BusinessError::new(
                400,
                "用户未关联任何商户，无法登录".to_string(),
            ));
        }

        // 获取用户的默认商户ID（使用表字段）
        let current_merchant_id = match user.default_merchant_id {
            Some(default_id) if merchant_ids.contains(&default_id) => default_id,
            Some(default_id) => {
                warn!(
                    "用户 {} 的默认商户ID {} 不在关联商户列表中，使用第一个商户 {}",
                    user.phone, default_id, merchant_ids[0]
                );
                merchant_ids[0]
            }
            None => {
                warn!(
                    "用户 {} 没有设置默认商户ID，使用第一个商户 {}",
                    user.phone, merchant_ids[0]
                );
                merchant_ids[0]
            }
        };

        // 查询用户在当前商户的权限信息
        let (role_codes, permission_codes) = self
            .merchant_user_merchants_repository
            .get_user_merchant_permissions(user.id, current_merchant_id, &self.db)
            .await
            .map_err(|e| {
                error!(
                    "查询用户权限失败: {}, 用户ID: {}, 商户ID: {}",
                    e, user.id, current_merchant_id
                );
                BusinessError::new(500, "查询用户权限失败".to_string())
            })?;

        // 根据角色编码判断用户类型
        let user_type = determine_user_type(&role_codes);

        // 运行时获取特定配置
        let security_config = App::global()
            .get_config::<SecurityConfig>()
            .map_err(|e| BusinessError::new(500, format!("获取配置失败: {}", e)))?;

        // 创建商户认证Session并生成JWT token
        let token = MerchantAuthSession::create_session(
            user.id,
            user.username.clone(),
            user_type.clone(),
            merchant_ids.clone(),
            current_merchant_id,
            role_codes.clone(),
            permission_codes,
            &security_config,
        )
        .await
        .map_err(|e| {
            error!("生成认证令牌失败: {}, 用户ID: {}", e, user.id);
            BusinessError::new(500, "生成认证令牌失败".to_string())
        })?;

        // 更新用户的最后登录信息
        let mut user_active_model: ActiveModel = user.clone().into();
        user_active_model.last_login_ip = Set(Some(client_ip));
        user_active_model.last_login_date = Set(Some(Local::now().into()));
        user_active_model.updated_date = Set(Local::now().into());

        if let Err(update_err) = user_active_model.update(&self.db).await {
            // 登录信息更新失败不影响登录流程，只记录警告
            warn!("更新商户用户 {} 登录信息失败: {}", user.phone, update_err);
        }

        info!(
            "商户用户 {} 登录成功，当前商户: {}, 关联商户: {:?}, 用户类型: {:?}, 角色: {:?}",
            user.phone, current_merchant_id, merchant_ids, user_type, role_codes
        );

        let response = MerchantUserLoginResponse { token };

        Ok(response)
    }

    /// 获取用户商户列表
    /// 验证用户身份并返回用户关联的所有商户信息
    pub async fn get_user_merchant_list(
        &self,
        req: crate::domain::business::merchants::dto::merchant_user_request::UserMerchantListRequest,
        user_id: uuid::Uuid,
    ) -> Result<
        crate::domain::business::merchants::vo::merchant_user_vo::UserMerchantListResponse,
        BusinessError,
    > {
        info!(
            "获取用户商户列表请求: 用户ID: {}, 包含已禁用商户: {}",
            user_id, req.include_disabled
        );

        // 查询用户商户列表数据
        let mut merchant_data = self
            .merchant_user_merchants_repository
            .get_user_merchant_list(user_id, req.include_disabled, &self.db)
            .await
            .map_err(|e| {
                error!("查询用户商户列表失败: {}, 用户ID: {}", e, user_id);
                BusinessError::new(500, "查询用户商户列表失败".to_string())
            })?;

        // 对商户数据进行排序优化
        Self::sort_merchant_list(&mut merchant_data);

        // 转换为响应格式（使用From trait简化转换逻辑）
        let merchants = merchant_data
            .into_iter()
            .map(crate::domain::business::merchants::vo::merchant_user_vo::UserMerchantListItemResponse::from)
            .collect::<Vec<_>>();

        let total_count = merchants.len();

        info!(
            "用户 {} 的商户列表查询成功，共 {} 个商户",
            user_id, total_count
        );

        Ok(
            crate::domain::business::merchants::vo::merchant_user_vo::UserMerchantListResponse {
                merchants,
                total_count,
            },
        )
    }

    /// 对商户列表进行排序优化
    /// 1. 商户级排序：有管理员角色的商户排在前面
    /// 2. 角色级排序：每个商户内管理员角色排在前面
    fn sort_merchant_list(
        merchant_data: &mut Vec<(
            crate::domain::business::merchants::entities::merchants::Model,
            Option<crate::domain::business::merchants::entities::merchant_categories::Model>,
            Vec<crate::domain::business::merchants::entities::merchant_roles::Model>,
        )>,
    ) {
        use std::cmp::Reverse;

        let admin_role_code = role_code::SYSTEM_CUSTOM_ADMIN_ROLE;

        // 对每个商户内的角色进行排序：管理员角色排在前面
        for (_, _, roles) in merchant_data.iter_mut() {
            roles.sort_by_key(|role| {
                (
                    Reverse(role.role_code.as_deref() == Some(admin_role_code)),
                    role.role_name.clone(),
                )
            });
        }

        // 对商户进行排序：有管理员角色的商户排在前面
        merchant_data.sort_by_key(|(merchant, _, roles)| {
            (
                Reverse(
                    roles
                        .iter()
                        .any(|role| role.role_code.as_deref() == Some(admin_role_code)),
                ),
                merchant.merchant_name.clone(),
            )
        });
    }

    /// 切换当前商户
    /// 验证用户权限并切换到指定商户，更新用户会话信息
    pub async fn switch_merchant(
        &self,
        req: crate::domain::business::merchants::dto::merchant_user_request::SwitchMerchantRequest,
        current_session: lib_auth::middleware::merchant_session::MerchantAuthSession,
        current_token: &str,
    ) -> Result<
        crate::domain::business::merchants::vo::merchant_user_vo::SwitchMerchantResponse,
        BusinessError,
    > {
        info!(
            "切换商户请求: 用户ID: {}, 当前商户: {}, 目标商户ID: {}",
            current_session.user_id, current_session.current_merchant_id, req.merchant_id
        );

        // 1. 验证用户是否有权限访问目标商户（使用session中的merchant_ids）
        if !current_session.merchant_ids.contains(&req.merchant_id) {
            warn!(
                "用户 {} 尝试切换到无权限的商户 {}",
                current_session.user_id, req.merchant_id
            );
            return Err(BusinessError::new(400, "您没有权限访问该商户".to_string()));
        }

        // 2. 查询目标商户的基本信息
        let merchant_info = self
            .merchants_repository
            .find_by_id(req.merchant_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询商户信息失败: {}, 商户ID: {}", e, req.merchant_id);
                BusinessError::new(500, "查询商户信息失败".to_string())
            })?
            .ok_or_else(|| {
                warn!("商户不存在: {}", req.merchant_id);
                BusinessError::new(400, "商户不存在".to_string())
            })?;

        // 检查商户状态
        if !merchant_info.is_active() {
            return Err(BusinessError::new(
                400,
                format!(
                    "商户状态不正确，请选择其他商户 当前切换的商户状态为{}",
                    merchant_info.get_status_desc()
                ),
            ));
        }

        // 3. 查询用户在目标商户下的权限信息
        let (role_codes, permission_codes) = self
            .merchant_user_merchants_repository
            .get_user_merchant_permissions(current_session.user_id, req.merchant_id, &self.db)
            .await
            .map_err(|e| {
                error!(
                    "查询用户权限失败: {}, 用户ID: {}, 商户ID: {}",
                    e, current_session.user_id, req.merchant_id
                );
                BusinessError::new(500, "查询用户权限失败".to_string())
            })?;

        // 根据角色编码判断用户类型
        let user_type = determine_user_type(&role_codes);

        // 4. 使用switch_merchant_with_permissions方法切换商户
        let _new_token = MerchantAuthSession::switch_merchant_with_permissions(
            current_token,
            merchant_info.id,
            role_codes,
            permission_codes,
            &self.security_config,
            user_type,
        )
        .await
        .map_err(|e| {
            error!(
                "切换商户失败: {}, 用户ID: {}, 商户ID: {}",
                e, current_session.user_id, req.merchant_id
            );
            BusinessError::new(500, "切换商户失败".to_string())
        })?;

        info!(
            "用户 {} 成功切换到商户: {} ({})",
            current_session.username, merchant_info.merchant_name, req.merchant_id
        );

        // 5. 构建响应
        Ok(
            crate::domain::business::merchants::vo::merchant_user_vo::SwitchMerchantResponse {
                merchant_name: merchant_info.merchant_name,
            },
        )
    }

    /// 获取当前登录用户的个人信息
    /// 根据会话信息查询用户详细信息、权限和角色
    /// 使用单一职责原则，调用专门的repository方法
    pub async fn get_current_user_profile(
        &self,
        session: MerchantAuthSession,
    ) -> Result<MerchantUserProfileResponse, BusinessError> {
        info!("获取当前用户个人信息请求: 用户ID: {}", session.user_id);

        // 1. 根据用户ID查询用户基本信息
        let user = self
            .merchant_users_repository
            .find_by_id(session.user_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询用户基本信息失败: {}, 用户ID: {}", e, session.user_id);
                BusinessError::new(500, "查询用户信息失败".to_string())
            })?
            .ok_or_else(|| {
                warn!("用户不存在: {}", session.user_id);
                BusinessError::new(404, "用户不存在".to_string())
            })?;

        // 2. 查询商户基本信息及分类信息（使用专门的MerchantsRepository）
        let merchant_with_category = self
            .merchants_repository
            .find_by_id_with_category(session.current_merchant_id, &self.db)
            .await
            .map_err(|e| {
                error!(
                    "查询商户及分类信息失败: {}, 商户ID: {}",
                    e, session.current_merchant_id
                );
                BusinessError::new(500, "查询商户信息失败".to_string())
            })?
            .ok_or_else(|| {
                warn!("商户不存在: {}", session.current_merchant_id);
                BusinessError::new(404, "商户不存在".to_string())
            })?;

        // 3. 查询用户在当前商户下的角色信息（包含完整的角色详情）
        let mut user_roles = self
            .merchant_user_merchants_repository
            .get_user_merchant_roles_with_details(
                session.user_id,
                session.current_merchant_id,
                &self.db,
            )
            .await
            .map_err(|e| {
                error!(
                    "查询用户角色信息失败: {}, 用户ID: {}, 商户ID: {}",
                    e, session.user_id, session.current_merchant_id
                );
                BusinessError::new(500, "查询用户角色失败".to_string())
            })?;

        // 对角色进行排序：管理员角色排在前面
        Self::sort_user_roles(&mut user_roles);

        // 提取角色ID用于权限查询
        let role_ids: Vec<uuid::Uuid> = user_roles.iter().map(|role| role.id).collect();

        // 4. 查询权限模板ID（使用专门的MerchantRolePermissionsRepository）
        let template_ids = self
            .merchant_role_permissions_repository
            .find_permission_template_ids_by_role_ids(role_ids, &self.db)
            .await
            .map_err(|e| {
                error!(
                    "查询权限模板ID失败: {}, 用户ID: {}, 商户ID: {}",
                    e, session.user_id, session.current_merchant_id
                );
                BusinessError::new(500, "查询权限模板失败".to_string())
            })?;

        // 5. 查询权限编码（使用专门的SystemPermissionTemplatesRepository）
        let current_permission_codes = self
            .system_permission_templates_repository
            .find_permission_codes_by_template_ids(template_ids, &self.db)
            .await
            .map_err(|e| {
                error!(
                    "查询权限编码失败: {}, 用户ID: {}, 商户ID: {}",
                    e, session.user_id, session.current_merchant_id
                );
                BusinessError::new(500, "查询权限编码失败".to_string())
            })?;

        // 6. 构建当前商户信息（包含完整的角色信息）
        let current_merchant = crate::domain::business::merchants::vo::merchant_user_vo::UserMerchantListItemResponse::from((
            merchant_with_category.0,
            merchant_with_category.1,
            user_roles, // 传入实际的角色信息
        ));

        // 7. 格式化时间
        let last_login_date = user
            .last_login_date
            .map(|dt| DateTimeUtils::format_datetime(&dt.with_timezone(&chrono::Local)));
        // 8. 构建响应数据（使用最新的权限信息）
        let response = MerchantUserProfileResponse {
            id: user.id.to_string(),
            username: user.username,
            real_name: Some(user.real_name),
            phone: Some(user.phone),
            email: user.email,
            avatar: user.avatar,
            gender: user.gender,
            status: user.status.to_value(),
            last_login_date,
            last_login_ip: user.last_login_ip,
            current_merchant,
            permissions: current_permission_codes, // 使用最新查询的权限
        };

        info!(
            "用户 {} 个人信息获取成功，当前商户: {}",
            session.user_id, session.current_merchant_id
        );

        Ok(response)
    }

    /// 修改当前登录用户的密码
    /// 验证旧密码并更新为新密码
    pub async fn change_password(
        &self,
        req: crate::domain::business::merchants::dto::merchant_user_request::ChangePasswordRequest,
        session: MerchantAuthSession,
    ) -> Result<(), BusinessError> {
        info!("修改密码请求: 用户ID: {}", session.user_id);

        // 1. 查询用户当前信息（包含密码哈希）
        let user = self
            .merchant_users_repository
            .find_by_id(session.user_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询用户信息失败: {}, 用户ID: {}", e, session.user_id);
                BusinessError::new(500, "查询用户信息失败".to_string())
            })?
            .ok_or_else(|| {
                warn!("用户不存在: {}", session.user_id);
                BusinessError::new(404, "用户不存在".to_string())
            })?;

        // 2. 验证旧密码是否正确
        let old_password_valid = password::verify_password(&req.old_password, &user.password)
            .map_err(|e| {
                error!("验证旧密码失败: {}, 用户ID: {}", e, session.user_id);
                BusinessError::new(500, "密码验证失败".to_string())
            })?;

        if !old_password_valid {
            warn!("修改密码失败：旧密码错误, 用户ID: {}", session.user_id);
            return Err(BusinessError::new(400, "旧密码不正确".to_string()));
        }

        // 3. 检查新密码是否与旧密码相同
        if req.old_password == req.new_password {
            warn!(
                "修改密码失败：新密码与旧密码相同, 用户ID: {}",
                session.user_id
            );
            return Err(BusinessError::new(
                400,
                "新密码不能与旧密码相同".to_string(),
            ));
        }

        // 4. 加密新密码
        let new_password_hash = password::hash_password(&req.new_password).map_err(|e| {
            error!("新密码加密失败: {}, 用户ID: {}", e, session.user_id);
            BusinessError::new(500, "密码加密失败".to_string())
        })?;

        // 5. 更新数据库中的密码
        let mut user_active_model: ActiveModel = user.into();
        user_active_model.password = Set(new_password_hash);
        user_active_model.updated_date = Set(Local::now().into());

        user_active_model.update(&self.db).await.map_err(|e| {
            error!("更新用户密码失败: {}, 用户ID: {}", e, session.user_id);
            BusinessError::new(500, "更新密码失败".to_string())
        })?;

        // 6. 记录操作日志
        info!(
            "用户 {} ({}) 密码修改成功",
            session.username, session.user_id
        );

        Ok(())
    }

    /// 修改当前登录用户的个人信息
    /// 更新用户的基本信息，不包括密码、身份证号、真实姓名、状态等敏感字段
    pub async fn update_profile(
        &self,
        req: crate::domain::business::merchants::dto::merchant_user_request::UpdateProfileRequest,
        session: MerchantAuthSession,
    ) -> Result<(), BusinessError> {
        info!("修改个人信息请求: 用户ID: {}", session.user_id);

        // 1. 验证用户是否存在
        let user = self
            .merchant_users_repository
            .find_by_id(session.user_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询用户信息失败: {}, 用户ID: {}", e, session.user_id);
                BusinessError::new(500, "查询用户信息失败".to_string())
            })?
            .ok_or_else(|| {
                warn!("用户不存在: {}", session.user_id);
                BusinessError::new(404, "用户不存在".to_string())
            })?;

        // 2. 验证手机号是否已被其他用户使用（如果要修改手机号）
        if let Some(ref new_phone) = req.phone {
            let phone_exists = self
                .merchant_users_repository
                .exists_by_phone(new_phone, Some(session.user_id), &self.db)
                .await
                .map_err(|e| {
                    error!("检查手机号唯一性失败: {}, 手机号: {}", e, new_phone);
                    BusinessError::new(500, "验证手机号失败".to_string())
                })?;

            if phone_exists {
                warn!("手机号已被使用: {}", new_phone);
                return Err(BusinessError::new(
                    400,
                    "手机号已被其他用户使用".to_string(),
                ));
            }
        }

        // 3. 验证邮箱格式和唯一性（如果要修改邮箱）
        if let Some(ref new_email) = req.email {
            let email_exists = self
                .merchant_users_repository
                .exists_by_email(new_email, Some(session.user_id), &self.db)
                .await
                .map_err(|e| {
                    error!("检查邮箱唯一性失败: {}, 邮箱: {}", e, new_email);
                    BusinessError::new(500, "验证邮箱失败".to_string())
                })?;

            if email_exists {
                warn!("邮箱已被使用: {}", new_email);
                return Err(BusinessError::new(400, "邮箱已被其他用户使用".to_string()));
            }
        }

        // 4. 验证默认商户ID是否有效（如果要修改默认商户）
        if let Some(default_merchant_id) = req.default_merchant_id {
            // 检查用户是否有该商户的权限
            let has_merchant_access = self
                .merchant_user_merchants_repository
                .user_belongs_to_merchant(session.user_id, default_merchant_id, &self.db)
                .await
                .map_err(|e| {
                    error!(
                        "检查用户商户权限失败: {}, 用户ID: {}, 商户ID: {}",
                        e, session.user_id, default_merchant_id
                    );
                    BusinessError::new(500, "验证默认商户失败".to_string())
                })?;

            if !has_merchant_access {
                warn!(
                    "用户 {} 尝试设置无权限的默认商户: {}",
                    session.user_id, default_merchant_id
                );
                return Err(BusinessError::new(400, "您没有权限访问该商户".to_string()));
            }

            // 检查商户是否处于正常状态
            let merchant_info = self
                .merchants_repository
                .find_by_id(default_merchant_id, &self.db)
                .await
                .map_err(|e| {
                    error!("查询商户信息失败: {}, 商户ID: {}", e, default_merchant_id);
                    BusinessError::new(500, "查询商户信息失败".to_string())
                })?
                .ok_or_else(|| {
                    warn!("商户不存在: {}", default_merchant_id);
                    BusinessError::new(400, "商户不存在".to_string())
                })?;

            if !merchant_info.is_active() {
                return Err(BusinessError::new(
                    400,
                    format!(
                        "商户状态异常，无法设置为默认商户：{}",
                        merchant_info.get_status_desc()
                    ),
                ));
            }
        }

        // 5. 更新数据库中的用户信息
        let mut user_active_model: ActiveModel = user.into();

        // 使用扩展方法简化字段更新
        user_active_model.username.set_if_some(req.username);
        user_active_model.phone.set_if_some(req.phone);
        user_active_model.email.set_if_some_nullable(req.email);
        user_active_model.avatar.set_if_some_nullable(req.avatar);
        user_active_model.remark.set_if_some_nullable(req.remark);
        user_active_model.default_merchant_id.set_if_some_nullable(req.default_merchant_id);

        // 性别字段的特殊处理
        if let Some(gender) = req.gender {
            let gender_enum = crate::domain::business::merchants::entities::merchant_users::MerchantUserGender::from_i32(gender)
                .map_err(|e| {
                    warn!("性别值解析失败: {}, 用户ID: {}", e, session.user_id);
                    BusinessError::new(400, e)
                })?;
            user_active_model.gender = Set(Some(gender_enum));
        }

        // 更新时间戳
        user_active_model.updated_date = Set(Local::now().into());

        // 执行更新
        self.merchant_users_repository
            .update(user_active_model, &self.db)
            .await
            .map_err(|e| {
                error!("更新用户个人信息失败: {}, 用户ID: {}", e, session.user_id);
                BusinessError::new(500, "更新个人信息失败".to_string())
            })?;

        // 6. 记录操作日志
        info!(
            "用户 {} ({}) 个人信息更新成功",
            session.username, session.user_id
        );

        Ok(())
    }

    /// 主动登出
    /// 清理用户会话状态，注销当前登录
    pub async fn logout(
        &self,
        session: MerchantAuthSession,
        token: &str,
    ) -> Result<(), BusinessError> {
        info!("用户主动登出: 用户ID: {}", session.user_id);

        // 1. 清理Redis中的会话状态
        MerchantAuthSession::destroy_session(token, &self.security_config)
            .await
            .map_err(|e| {
                error!("清理用户会话失败: {}, 用户ID: {}", e, session.user_id);
                BusinessError::new(500, format!("登出失败: {}", e))
            })?;

        // 2. 记录登出日志
        info!("用户 {} ({}) 登出成功", session.username, session.user_id);

        Ok(())
    }

    fn sort_user_roles(
        roles: &mut Vec<crate::domain::business::merchants::entities::merchant_roles::Model>,
    ) {
        use std::cmp::Reverse;

        let admin_role_code = role_code::SYSTEM_CUSTOM_ADMIN_ROLE;

        // 对角色进行排序：管理员角色排在前面，其他角色按名称排序
        roles.sort_by_key(|role| {
            (
                Reverse(role.role_code.as_deref() == Some(admin_role_code)),
                role.role_name.clone(),
            )
        });
    }
}

/// 根据角色编码确定用户类型
pub fn determine_user_type(role_codes: &[String]) -> MerchantUserType {
    // 检查是否有管理员角色
    if role_codes.contains(&role_code::SYSTEM_CUSTOM_ADMIN_ROLE.to_string()) {
        MerchantUserType::MerchantAdmin
    } else {
        // 没有管理员角色，则为普通员工
        MerchantUserType::MerchantEmployee
    }
}

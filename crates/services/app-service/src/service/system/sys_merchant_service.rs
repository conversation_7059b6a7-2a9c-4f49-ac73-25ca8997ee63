use crate::constants::*;
use crate::domain::business::merchant_user_vo::FollowerUserResponse;
use crate::domain::business::merchants::entities::merchant_follower_commission::{
    ActiveModel as CommissionActiveModel, CommissionType,
};
use crate::domain::business::merchants::entities::merchants::{ActiveModel, MerchantStatus};
use crate::domain::business::merchants::entities::prelude::Merchants;
use crate::domain::system::dto::sys_merchant_request::{
    SysMerchantBatchDeleteRequest, SysMerchantCreateRequest, SysMerchantFollowerCommissionRequest,
    SysMerchantListRequest, SysMerchantPageRequest, SysMerchantPlatformCommissionRequest,
    SysMerchantStatusRequest, SysMerchantUpdateRequest,
};
use crate::domain::system::vo::merchant_vo::{
    SysMerchantBasicResponse, SysMerchantDetailResponse, SysMerchantListResponse,
    SysMerchantLocationResponse, SysMerchantSelectItem, SysMerchantStatsResponse,
};
use crate::repository::MerchantFollowerCommissionRepository;
use crate::repository::{
    MerchantUserRolesRepository, MerchantUsersRepository, MerchantsRepository,
};
use crate::service::system::SysMerchantRoleService;
use crate::utils::commission_utils::CommissionUtils;
use crate::utils::datetime::DateTimeUtils;
use lib_core::BusinessError;
use lib_core::app::plugin::service::Service as ServiceTrait;
use lib_core::response::PageData;
use lib_macros::Service;
use sea_orm::{ActiveEnum, DatabaseConnection, Set, TransactionTrait};
use tracing::{error, info, log};
use uuid::Uuid;

/// 商户管理服务实现
#[derive(Clone, Service)]
pub struct SysMerchantService {
    #[inject(component)]
    db: DatabaseConnection,
    #[inject(component)]
    merchants_repository: MerchantsRepository,
    #[inject(component)]
    merchant_follower_commission_repository: MerchantFollowerCommissionRepository,
    #[inject(component)]
    sys_merchant_role_service: SysMerchantRoleService,
    #[inject(component)]
    sys_merchant_user_repository: MerchantUsersRepository,
    #[inject(component)]
    merchant_user_roles_repository: MerchantUserRolesRepository,
}

impl SysMerchantService {
    /// 分页查询商户列表
    pub async fn page_merchants(
        &self,
        req: SysMerchantPageRequest,
    ) -> Result<PageData<SysMerchantListResponse>, BusinessError> {
        info!("分页查询商户，参数: {:?}", req);

        // 使用Repository进行分页查询
        let page_data = self
            .merchants_repository
            .page_with_categories(&req, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("分页查询商户失败: {}", e)))?;

        // 转换为响应对象
        let items: Vec<SysMerchantListResponse> = page_data
            .items
            .into_iter()
            .map(|(merchant, category)| {
                let mut response = SysMerchantListResponse::from(merchant);
                if let Some(category) = category {
                    response.category_name = Some(category.category_name);
                }
                response
            })
            .collect();

        let final_page_data =
            PageData::new(items, page_data.total, page_data.page, page_data.page_size);

        info!(
            "分页查询商户成功，返回数据: {} 条",
            final_page_data.items.len()
        );

        Ok(final_page_data)
    }

    /// 根据ID查询商户详情
    pub async fn get_merchant_by_id(
        &self,
        merchant_id: i64,
    ) -> Result<SysMerchantDetailResponse, BusinessError> {
        info!("查询商户详情，ID: {}", merchant_id);

        let merchant_detail = self
            .merchants_repository
            .find_by_id_with_category(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "商户不存在".to_string()))?;

        let (merchant, merchant_categories) = merchant_detail;

        // 使用新的From trait转换
        let mut detail = SysMerchantDetailResponse::from(merchant);
        match merchant_categories {
            None => {
                detail.category_name = None;
            }
            Some(merchant_categories) => {
                detail.category_name = Some(merchant_categories.category_name);
            }
        };

        Ok(detail)
    }

    /// 创建商户
    pub async fn create_merchant(
        &self,
        req: SysMerchantCreateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("创建商户，请求: {:?}，操作人: {}", req, operator_id);

        let txn = self.db.begin().await.map_err(|e| {
            error!("开启事务失败: {}", e);
            BusinessError::new(500, "创建商户失败！".to_string())
        })?;

        // 检查商户名称是否已存在
        let name_exists = self
            .merchants_repository
            .exists_by_name(&req.merchant_name, None, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查商户名称失败: {}", e)))?;
        if name_exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "商户名称已存在".to_string(),
            ));
        }

        // 检查商户编码是否已存在
        let code_exists = self
            .merchants_repository
            .exists_by_code(&req.merchant_code, None, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查商户编码失败: {}", e)))?;
        if code_exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "商户编码已存在".to_string(),
            ));
        }
        // 使用新的转换方法
        let new_merchant = req
            .to_active_model(Some(operator_id))
            .map_err(|_| BusinessError::new(INTERNAL_ERROR, "转换商户数据失败".to_string()))?;
        let merchant_insert_result = self
            .merchants_repository
            .create(new_merchant, &txn)
            .await
            .map_err(|db_err| {
                log::error!("创建商户失败: {}", db_err);
                BusinessError::new(DATABASE_ERROR, format!("创建商户失败: {}", db_err))
            })?;

        let merchant_id = merchant_insert_result.last_insert_id;

        // 创建商户管理员角色
        let _role_id = self
            .sys_merchant_role_service
            .create_merchant_admin_role(merchant_id, operator_id, &txn)
            .await?;

        txn.commit().await.map_err(|e| {
            error!("提交事务失败: {}", e);
            BusinessError::new(500, "创建商户失败！".to_string())
        })?;

        info!("商户创建成功");
        Ok("商户创建成功".to_string())
    }

    /// 更新商户
    pub async fn update_merchant(
        &self,
        merchant_id: i64,
        req: SysMerchantUpdateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "更新商户，ID: {}，请求: {:?}，操作人: {}",
            merchant_id, req, operator_id
        );

        let merchant = self
            .merchants_repository
            .find_by_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "商户不存在".to_string()))?;

        // 检查商户名称是否已存在（排除当前商户）
        let name_exists = self
            .merchants_repository
            .exists_by_name(&req.merchant_name, Some(merchant_id), &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查商户名称失败: {}", e)))?;
        if name_exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "商户名称已存在".to_string(),
            ));
        }

        // 检查商户编码是否已存在（排除当前商户）
        let code_exists = self
            .merchants_repository
            .exists_by_code(&req.merchant_code, Some(merchant_id), &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查商户编码失败: {}", e)))?;
        if code_exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "商户编码已存在".to_string(),
            ));
        }

        // 使用新的转换方法
        let existing_active: ActiveModel = merchant.into();
        let updated_merchant = req
            .update_active_model(existing_active, Some(operator_id))
            .map_err(|_| BusinessError::new(INTERNAL_ERROR, "转换商户数据失败".to_string()))?;

        self.merchants_repository
            .update(updated_merchant, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("更新商户失败: {}", e)))?;

        info!("商户更新成功，ID: {}", merchant_id);
        Ok("商户更新成功".to_string())
    }

    /// 删除商户
    pub async fn delete_merchant(
        &self,
        merchant_id: i64,
        _operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("删除商户，ID: {}，操作人: {}", merchant_id, _operator_id);

        let _merchant = self
            .merchants_repository
            .find_by_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "商户不存在".to_string()))?;

        self.merchants_repository
            .delete_by_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除商户失败: {}", e)))?;

        info!("商户删除成功，ID: {}", merchant_id);
        Ok("商户删除成功".to_string())
    }

    /// 批量删除商户
    pub async fn batch_delete_merchants(
        &self,
        req: SysMerchantBatchDeleteRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "批量删除商户，IDs: {:?}，操作人: {}",
            req.merchant_ids, operator_id
        );

        if req.merchant_ids.is_empty() {
            return Err(BusinessError::new(
                INVALID_PARAMETER,
                "请选择要删除的商户".to_string(),
            ));
        }

        let delete_count = self
            .merchants_repository
            .delete_by_ids(req.merchant_ids.clone(), &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("批量删除商户失败: {}", e)))?;

        info!("批量删除商户成功，删除数量: {}", delete_count);
        Ok(format!("成功删除{}个商户", delete_count))
    }

    /// 切换商户状态
    pub async fn change_merchant_status(
        &self,
        merchant_id: i64,
        req: SysMerchantStatusRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "切换商户状态，ID: {}，状态: {}，操作人: {}",
            merchant_id, req.status, operator_id
        );

        let status = MerchantStatus::try_from_value(&req.status)
            .map_err(|_| BusinessError::new(INVALID_PARAMETER, "无效的商户状态".to_string()))?;

        let merchant = self
            .merchants_repository
            .find_by_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "商户不存在".to_string()))?;

        let mut merchant_active: ActiveModel = merchant.into();
        merchant_active.status = Set(status);
        merchant_active.updated_date = Set(DateTimeUtils::now_local().fixed_offset());
        merchant_active.updated_by = Set(Some(operator_id));

        self.merchants_repository
            .update(merchant_active, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("更新商户状态失败: {}", e)))?;

        let status_text = Merchants::get_status_desc(status);
        info!(
            "商户状态切换成功，ID: {}，状态: {}",
            merchant_id, status_text
        );
        Ok(format!("商户状态已切换为{}", status_text))
    }

    /// 设置商户平台佣金
    pub async fn set_merchant_platform_commission(
        &self,
        merchant_id: i64,
        req: SysMerchantPlatformCommissionRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "设置商户平台佣金，ID: {}，平台佣金: {}，操作人: {}",
            merchant_id, req.platform_commission_rate, operator_id
        );

        // 查询商户信息
        let merchant = self
            .merchants_repository
            .find_by_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "商户不存在".to_string()))?;

        // 解析平台佣金比例
        let platform_rate =
            CommissionUtils::parse_platform_commission(&req.platform_commission_rate).map_err(
                |e| BusinessError::new(INVALID_PARAMETER, format!("平台佣金比例转换失败: {}", e)),
            )?;

        // 更新商户平台佣金
        let mut merchant_active: ActiveModel = merchant.into();
        merchant_active.platform_commission_rate = Set(Some(platform_rate));
        merchant_active.updated_date = Set(DateTimeUtils::now_local().fixed_offset());
        merchant_active.updated_by = Set(Some(operator_id));

        self.merchants_repository
            .update(merchant_active, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("更新商户平台佣金失败: {}", e))
            })?;

        info!(
            "商户平台佣金设置成功，ID: {}，比例: {}",
            merchant_id, platform_rate
        );
        Ok("商户平台佣金设置成功".to_string())
    }

    /// 设置商户跟进人佣金
    ///
    /// 业务规则：一个商户只能有一个跟进人
    /// - 如果商户已有跟进人，会更新为新的跟进人
    /// - 如果商户没有跟进人，会创建新的跟进人记录
    pub async fn set_merchant_follower_commission(
        &self,
        merchant_id: i64,
        req: SysMerchantFollowerCommissionRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "设置商户跟进人佣金，商户ID: {}，跟进人ID: {}，佣金类型: {}，佣金值: {}，操作人: {}",
            merchant_id,
            req.follower_id,
            req.follower_commission_type,
            req.follower_commission_value,
            operator_id
        );
        // 查询用户是否存在
        let _ = &self
            .sys_merchant_user_repository
            .find_by_id(req.follower_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询跟进人失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "跟进人不存在".to_string()))?;

        // 校验用户是否属于该商户
        let user_belongs_to_merchant = self
            .merchant_user_roles_repository
            .user_belongs_to_merchant(req.follower_id, merchant_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("校验用户商户关联失败: {}", e))
            })?;

        if !user_belongs_to_merchant {
            return Err(BusinessError::new(
                INVALID_PARAMETER,
                "该用户不属于此商户，无法设置跟进人佣金".to_string(),
            ));
        }

        // 解析佣金值
        let commission_value = CommissionUtils::parse_follower_commission(
            &req.follower_commission_value,
            req.follower_commission_type,
        )
        .map_err(|e| {
            BusinessError::new(INVALID_PARAMETER, format!("跟进人佣金值转换失败: {}", e))
        })?;

        // 转换佣金类型
        let commission_type_enum = CommissionType::try_from_value(&req.follower_commission_type)
            .map_err(|_| BusinessError::new(INVALID_PARAMETER, "无效的佣金类型".to_string()))?;

        let now = DateTimeUtils::now_local().fixed_offset();

        // 查找商户当前的跟进人记录（一个商户只能有一个跟进人）
        let existing_follower = self
            .merchant_follower_commission_repository
            .find_current_follower_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询商户当前跟进人记录失败: {}", e))
            })?;

        // 如果没有现有跟进人记录，直接创建新记录并返回
        if existing_follower.is_none() {
            let commission_active: CommissionActiveModel = (
                &req,
                merchant_id,
                commission_type_enum,
                commission_value,
                now,
            )
                .into();

            self.merchant_follower_commission_repository
                .create(commission_active, &self.db)
                .await
                .map_err(|e| {
                    BusinessError::new(DATABASE_ERROR, format!("创建商户跟进人佣金配置失败: {}", e))
                })?;

            info!(
                "商户跟进人佣金配置创建成功，商户ID: {}，跟进人ID: {}",
                merchant_id, req.follower_id
            );
            return Ok("商户跟进人佣金设置成功".to_string());
        }

        // 有现有跟进人记录，更新为新的跟进人
        let existing = existing_follower.unwrap();
        let mut commission_active: CommissionActiveModel = existing.into();
        commission_active.user_id = Set(req.follower_id); // 更新为新的跟进人ID
        commission_active.commission_type = Set(commission_type_enum);
        commission_active.commission_value = Set(commission_value);
        commission_active.updated_date = Set(now);
        commission_active.remark = Set(req.follower_commission_remark);

        self.merchant_follower_commission_repository
            .update(commission_active, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("更新商户跟进人佣金配置失败: {}", e))
            })?;

        info!(
            "商户跟进人佣金配置更新成功，商户ID: {}，新跟进人ID: {}",
            merchant_id, req.follower_id
        );

        Ok("商户跟进人佣金设置成功".to_string())
    }

    /// 获取商户选择项列表
    pub async fn get_merchant_select_items(
        &self,
    ) -> Result<Vec<SysMerchantSelectItem>, BusinessError> {
        info!("获取商户选择项列表");

        let merchants = self
            .merchants_repository
            .find_active_select_items(&self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户列表失败: {}", e)))?;

        let select_items = merchants
            .into_iter()
            .map(|merchant| SysMerchantSelectItem {
                id: merchant.id,
                merchant_name: merchant.merchant_name,
                merchant_code: merchant.merchant_code,
                category_name: None, // TODO: 从关联表获取分类名称
                status: merchant.status.to_value(),
            })
            .collect();

        Ok(select_items)
    }

    /// 获取商户基础信息列表
    pub async fn get_merchant_basic_list(
        &self,
        req: SysMerchantListRequest,
    ) -> Result<Vec<SysMerchantBasicResponse>, BusinessError> {
        info!("获取商户基础信息列表");

        let merchants = self
            .merchants_repository
            .find_active_basic_list(req.merchant_name, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户列表失败: {}", e)))?;

        let basic_list = merchants
            .into_iter()
            .map(|merchant| SysMerchantBasicResponse::from(merchant))
            .collect();

        Ok(basic_list)
    }

    /// 获取商户统计信息
    pub async fn get_merchant_stats(&self) -> Result<SysMerchantStatsResponse, BusinessError> {
        info!("获取商户统计信息");

        // 总商户数
        let total_count = self
            .merchants_repository
            .count_total(&self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户总数失败: {}", e)))?
            as i32;

        // 正常营业商户数
        let active_count = self
            .merchants_repository
            .count_active(&self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询正常营业商户数失败: {}", e))
            })? as i32;

        // 临时关闭商户数
        let suspended_count = self
            .merchants_repository
            .count_suspended(&self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询临时关闭商户数失败: {}", e))
            })? as i32;

        // 永久关闭商户数
        let closed_count = self
            .merchants_repository
            .count_closed(&self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询永久关闭商户数失败: {}", e))
            })? as i32;

        // TODO: 实现按分类统计的功能
        let by_category = Vec::new();

        let stats = SysMerchantStatsResponse {
            total_count,
            active_count,
            inactive_count: suspended_count + closed_count, // 将临时关闭和永久关闭合并为非活跃状态
            by_category,
        };

        Ok(stats)
    }

    /// 获取商户地理位置信息
    pub async fn get_merchant_location(
        &self,
        merchant_id: i64,
    ) -> Result<SysMerchantLocationResponse, BusinessError> {
        info!("获取商户地理位置信息，ID: {}", merchant_id);

        let merchant = self
            .merchants_repository
            .find_by_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "商户不存在".to_string()))?;

        let location = SysMerchantLocationResponse {
            merchant_id: merchant.id,
            merchant_name: merchant.merchant_name,
            address: merchant.address,
            // location: Location::from_string(merchant.location)
            location: None, // TODO: 从location字段解析经纬度
        };

        Ok(location)
    }

    /// 获取商户跟进人信息
    pub async fn get_merchant_followers_by_merchant_id(
        &self,
        merchant_id: i64,
    ) -> Result<FollowerUserResponse, BusinessError> {
        info!("获取商户跟进人信息，ID: {}", merchant_id);

        // 使用新的关联查询方法获取跟进人详细信息
        let follower_info = self
            .merchant_follower_commission_repository
            .find_follower_user_info_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询商户跟进人信息失败: {}", e))
            })?;

        match follower_info {
            Some(info) => {
                info!(
                    "成功获取商户 {} 的跟进人信息: {}",
                    merchant_id, info.username
                );
                Ok(info.to_response())
            }
            None => {
                info!("商户 {} 未找到跟进人信息", merchant_id);
                Err(BusinessError::new(
                    RESOURCE_NOT_FOUND,
                    "商户跟进人不存在".to_string(),
                ))
            }
        }
    }
}

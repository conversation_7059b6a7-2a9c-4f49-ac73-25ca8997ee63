use crate::constants::*;
use crate::repository::{
    MerchantAuthorizedPermissionRepository, SystemPermissionTemplatesRepository,
};
use crate::utils::datetime::DateTimeUtils;
use lib_core::BusinessError;
use lib_core::app::plugin::service::Service as ServiceTrait;
use lib_core::response::PageData;
use lib_macros::Service;
use sea_orm::{DatabaseConnection, Set, TransactionTrait};
use std::collections::HashMap;
use tracing::{error, info, warn};
use uuid::Uuid;

use crate::domain::business::merchants::entities::system_permission_templates::ActiveModel;
use crate::domain::system::dto::sys_permission_template_request::{
    SysPermissionTemplateBatchDeleteRequest, SysPermissionTemplateCreateRequest,
    SysPermissionTemplatePageRequest, SysPermissionTemplateStatusRequest,
    SysPermissionTemplateUpdateRequest,
};
use crate::domain::system::vo::permission_template_vo::{
    SystemPermissionTemplateDetailResponse, SystemPermissionTemplateListResponse,
    SystemPermissionTemplateSelectItem, SystemPermissionTemplateStatsResponse,
    SystemPermissionTemplateTreeNode,
};

/// 系统权限模板管理服务实现
#[derive(Service, Clone)]
pub struct SysPermissionTemplateService {
    #[inject(component)]
    db: DatabaseConnection,
    #[inject(component)]
    system_permission_templates_repository: SystemPermissionTemplatesRepository,
    #[inject(component)]
    merchant_authorized_permission_repository: MerchantAuthorizedPermissionRepository,
}

impl SysPermissionTemplateService {
    /// 构建树形结构
    fn build_tree(
        nodes: Vec<SystemPermissionTemplateListResponse>,
        parent_id: Option<Uuid>,
    ) -> Vec<SystemPermissionTemplateTreeNode> {
        let mut tree_nodes = Vec::new();

        for node in nodes.iter() {
            if node.parent_id != parent_id {
                continue;
            }
            let mut tree_node = SystemPermissionTemplateTreeNode {
                id: node.id,
                label: node.permission_name.clone(),
                permission_code: node.permission_code.clone(),
                parent_id: node.parent_id,
                permission_type: node.permission_type,
                visible: node.visible,
                icon: node.icon.clone(),
                order_num: node.order_num,
                children: Vec::new(),
            };

            // 递归构建子节点
            tree_node.children = Self::build_tree(nodes.clone(), Some(node.id));
            tree_nodes.push(tree_node);
        }

        // 按order_num排序
        tree_nodes.sort_by(|a, b| a.order_num.cmp(&b.order_num));
        tree_nodes
    }

    /// 批量获取父权限名称 - 避免循环中的数据库操作
    pub async fn batch_get_parent_names(
        &self,
        parent_ids: Vec<Uuid>,
    ) -> Result<HashMap<Uuid, String>, BusinessError> {
        if parent_ids.is_empty() {
            return Ok(HashMap::new());
        }

        let parents = self
            .system_permission_templates_repository
            .find_by_ids(parent_ids, &self.db)
            .await
            .map_err(|e| {
                error!("批量查询父权限失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询父权限失败".to_string())
            })?;

        let parent_map: HashMap<Uuid, String> = parents
            .into_iter()
            .map(|p| (p.id, p.permission_name))
            .collect();

        Ok(parent_map)
    }
}

impl SysPermissionTemplateService {
    /// 分页查询权限模板列表
    pub async fn page_permission_templates(
        &self,
        req: SysPermissionTemplatePageRequest,
    ) -> Result<PageData<SystemPermissionTemplateListResponse>, BusinessError> {
        info!("分页查询权限模板，参数: {:?}", req);

        let page_data = self
            .system_permission_templates_repository
            .page_by_condition(&req, &self.db)
            .await
            .map_err(|e| {
                error!("分页查询权限模板失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "分页查询权限模板失败".to_string())
            })?;

        info!(
            "分页查询权限模板成功，返回数据: {} 条",
            page_data.items.len()
        );

        // 转换为响应对象
        let mut list: Vec<SystemPermissionTemplateListResponse> = page_data
            .items
            .into_iter()
            .map(|template| SystemPermissionTemplateListResponse::from(template))
            .collect();

        // 批量查询父权限名称，避免循环中的数据库操作
        if !list.is_empty() {
            let parent_ids: Vec<Uuid> = list.iter().filter_map(|item| item.parent_id).collect();

            if !parent_ids.is_empty() {
                let parent_map = self.batch_get_parent_names(parent_ids).await?;

                for item in &mut list {
                    if let Some(parent_id) = item.parent_id {
                        item.parent_name = parent_map.get(&parent_id).cloned();
                    }
                }
            }
        }

        let result = PageData {
            items: list,
            total: page_data.total,
            page: page_data.page,
            page_size: page_data.page_size,
            total_pages: page_data.total_pages,
        };

        Ok(result)
    }

    /// 根据ID查询权限模板详情
    pub async fn get_permission_template_by_id(
        &self,
        template_id: Uuid,
    ) -> Result<SystemPermissionTemplateDetailResponse, BusinessError> {
        info!("查询权限模板详情，ID: {}", template_id);

        let template = self
            .system_permission_templates_repository
            .find_by_id(template_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询权限模板失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询权限模板失败".to_string())
            })?;

        let template = match template {
            Some(t) => t,
            None => {
                warn!("权限模板不存在: {}", template_id);
                return Err(BusinessError::new(
                    RESOURCE_NOT_FOUND,
                    "权限模板不存在".to_string(),
                ));
            }
        };

        let mut detail = SystemPermissionTemplateDetailResponse::from(template);

        // 查询父权限名称
        if let Some(parent_id) = detail.parent_id {
            let parent = self
                .system_permission_templates_repository
                .find_by_id(parent_id, &self.db)
                .await
                .map_err(|e| {
                    error!("查询父权限失败: {:?}", e);
                    BusinessError::new(DATABASE_ERROR, "查询父权限失败".to_string())
                })?;
            detail.parent_name = parent.map(|p| p.permission_name);
        }

        Ok(detail)
    }

    /// 创建权限模板
    pub async fn create_permission_template(
        &self,
        req: SysPermissionTemplateCreateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("创建权限模板，请求: {:?}，操作人: {:?}", req, operator_id);

        // 检查权限编码是否已存在
        let code_exists = self
            .system_permission_templates_repository
            .exists_by_permission_code(&req.permission_code, None, &self.db)
            .await
            .map_err(|e| {
                error!("检查权限编码失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "检查权限编码失败".to_string())
            })?;

        if code_exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "权限编码已存在".to_string(),
            ));
        }

        // 检查父权限是否存在
        if let Some(parent_id) = req.parent_id {
            let parent_exists = self
                .system_permission_templates_repository
                .exists_by_id(parent_id, &self.db)
                .await
                .map_err(|e| {
                    error!("查询父权限失败: {:?}", e);
                    BusinessError::new(DATABASE_ERROR, "查询父权限失败".to_string())
                })?;

            if !parent_exists {
                return Err(BusinessError::new(
                    RESOURCE_NOT_FOUND,
                    "父权限不存在".to_string(),
                ));
            }
        }

        let new_template = req.to_active_model(Some(operator_id));

        self.system_permission_templates_repository
            .create(new_template, &self.db)
            .await
            .map_err(|e| {
                error!("创建权限模板失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "创建权限模板失败".to_string())
            })?;

        info!("权限模板创建成功");
        Ok("权限模板创建成功".to_string())
    }

    /// 更新权限模板
    pub async fn update_permission_template(
        &self,
        template_id: Uuid,
        req: SysPermissionTemplateUpdateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "更新权限模板，ID: {}，请求: {:?}，操作人: {:?}",
            template_id, req, operator_id
        );

        let template = self
            .system_permission_templates_repository
            .find_by_id(template_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询权限模板失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询权限模板失败".to_string())
            })?;

        let template = match template {
            Some(t) => t,
            None => {
                warn!("权限模板不存在: {}", template_id);
                return Err(BusinessError::new(
                    RESOURCE_NOT_FOUND,
                    "权限模板不存在".to_string(),
                ));
            }
        };

        // 检查权限编码是否已存在（排除当前模板）
        let code_exists = self
            .system_permission_templates_repository
            .exists_by_permission_code(&req.permission_code, Some(template_id), &self.db)
            .await
            .map_err(|e| {
                error!("检查权限编码失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "检查权限编码失败".to_string())
            })?;

        if code_exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "权限编码已存在".to_string(),
            ));
        }

        // 检查是否将自己设置为父权限
        if let Some(parent_id) = req.parent_id {
            if parent_id == template_id {
                return Err(BusinessError::new(
                    INVALID_PARAMETER,
                    "不能将自己设置为父权限".to_string(),
                ));
            }

            // 检查父权限是否存在
            let parent_exists = self
                .system_permission_templates_repository
                .exists_by_id(parent_id, &self.db)
                .await
                .map_err(|e| {
                    error!("查询父权限失败: {:?}", e);
                    BusinessError::new(DATABASE_ERROR, "查询父权限失败".to_string())
                })?;

            if !parent_exists {
                return Err(BusinessError::new(
                    RESOURCE_NOT_FOUND,
                    "父权限不存在".to_string(),
                ));
            }
        }

        let existing_active: ActiveModel = template.into();
        let updated_template = req.update_active_model(existing_active, Some(operator_id));

        self.system_permission_templates_repository
            .update(updated_template, &self.db)
            .await
            .map_err(|e| {
                error!("更新权限模板失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "更新权限模板失败".to_string())
            })?;

        info!("权限模板更新成功，ID: {}", template_id);
        Ok("权限模板更新成功".to_string())
    }

    /// 删除权限模板
    pub async fn delete_permission_template(
        &self,
        template_id: Uuid,
        _operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "删除权限模板，ID: {}，操作人: {:?}",
            template_id, _operator_id
        );

        let template_exists = self
            .system_permission_templates_repository
            .exists_by_id(template_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询权限模板失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询权限模板失败".to_string())
            })?;

        if !template_exists {
            warn!("权限模板不存在: {}", template_id);
            return Err(BusinessError::new(
                RESOURCE_NOT_FOUND,
                "权限模板不存在".to_string(),
            ));
        }

        // 检查是否有子权限
        let has_children = self
            .system_permission_templates_repository
            .has_children(template_id, &self.db)
            .await
            .map_err(|e| {
                error!("检查子权限失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "检查子权限失败".to_string())
            })?;

        if has_children {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "存在子权限，不能删除".to_string(),
            ));
        }

        // 检查是否被商户使用
        let usage_count = self
            .merchant_authorized_permission_repository
            .count_by_template_id(template_id, &self.db)
            .await
            .map_err(|e| {
                error!("检查权限使用情况失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "检查权限使用情况失败".to_string())
            })?;

        if usage_count > 0 {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "权限模板已被商户使用，不能删除".to_string(),
            ));
        }

        self.system_permission_templates_repository
            .delete_by_id(template_id, &self.db)
            .await
            .map_err(|e| {
                error!("删除权限模板失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "删除权限模板失败".to_string())
            })?;

        info!("权限模板删除成功，ID: {}", template_id);
        Ok("权限模板删除成功".to_string())
    }

    /// 批量删除权限模板
    pub async fn batch_delete_permission_templates(
        &self,
        req: SysPermissionTemplateBatchDeleteRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "批量删除权限模板，IDs: {:?}，操作人: {:?}",
            req.template_ids, operator_id
        );

        if req.template_ids.is_empty() {
            return Err(BusinessError::new(
                INVALID_PARAMETER,
                "请选择要删除的权限模板".to_string(),
            ));
        }

        // 开启事务
        let txn = self.db.begin().await.map_err(|e| {
            error!("开启事务失败: {:?}", e);
            BusinessError::new(DATABASE_ERROR, "开启事务失败".to_string())
        })?;

        // 批量检查子权限
        let children_count = self
            .system_permission_templates_repository
            .count_by_parent_ids(req.template_ids.clone(), &txn)
            .await
            .map_err(|e| {
                error!("批量检查子权限失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "检查子权限失败".to_string())
            })?;

        if children_count > 0 {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "部分权限模板存在子权限，无法删除".to_string(),
            ));
        }

        // 批量检查商户使用情况
        let mut total_usage_count = 0;
        for template_id in &req.template_ids {
            let usage_count = self
                .merchant_authorized_permission_repository
                .count_by_template_id(*template_id, &txn)
                .await
                .map_err(|e| {
                    error!("检查权限使用情况失败: {:?}", e);
                    BusinessError::new(DATABASE_ERROR, "检查权限使用情况失败".to_string())
                })?;
            total_usage_count += usage_count;
        }

        if total_usage_count > 0 {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "部分权限模板已被商户使用，无法删除".to_string(),
            ));
        }

        // 批量删除权限模板
        let delete_count = self
            .system_permission_templates_repository
            .delete_by_ids(req.template_ids.clone(), &txn)
            .await
            .map_err(|e| {
                error!("批量删除权限模板失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "批量删除权限模板失败".to_string())
            })?;

        txn.commit().await.map_err(|e| {
            error!("提交事务失败: {:?}", e);
            BusinessError::new(DATABASE_ERROR, "提交事务失败".to_string())
        })?;

        info!("批量删除权限模板成功，删除数量: {}", delete_count);
        Ok(format!("成功删除{}个权限模板", delete_count))
    }

    /// 切换权限模板状态
    pub async fn change_permission_template_status(
        &self,
        template_id: Uuid,
        req: SysPermissionTemplateStatusRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "切换权限模板状态，ID: {}，状态: {}，操作人: {:?}",
            template_id, req.visible, operator_id
        );

        let template = self
            .system_permission_templates_repository
            .find_by_id(template_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询权限模板失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询权限模板失败".to_string())
            })?;

        let template = match template {
            Some(t) => t,
            None => {
                warn!("权限模板不存在: {}", template_id);
                return Err(BusinessError::new(
                    RESOURCE_NOT_FOUND,
                    "权限模板不存在".to_string(),
                ));
            }
        };

        let mut template_active: ActiveModel = template.into();
        template_active.visible = Set(Some(req.visible));
        template_active.updated_date = Set(DateTimeUtils::now_local().fixed_offset());
        template_active.updated_by = Set(Some(operator_id));

        self.system_permission_templates_repository
            .update(template_active, &self.db)
            .await
            .map_err(|e| {
                error!("更新权限模板状态失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "更新权限模板状态失败".to_string())
            })?;

        let status_text = if req.visible == 0 { "显示" } else { "隐藏" };
        info!(
            "权限模板状态切换成功，ID: {}，状态: {}",
            template_id, status_text
        );
        Ok(format!("权限模板已切换为{}", status_text))
    }

    /// 获取权限模板树形结构
    pub async fn get_permission_template_tree(
        &self,
    ) -> Result<Vec<SystemPermissionTemplateTreeNode>, BusinessError> {
        info!("获取权限模板树形结构");

        let templates = self
            .system_permission_templates_repository
            .find_all(&self.db)
            .await
            .map_err(|e| {
                error!("查询权限模板列表失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询权限模板列表失败".to_string())
            })?;

        let template_list: Vec<SystemPermissionTemplateListResponse> = templates
            .into_iter()
            .map(|template| SystemPermissionTemplateListResponse::from(template))
            .collect();

        let tree = Self::build_tree(template_list, None);
        Ok(tree)
    }

    /// 获取权限模板选择项
    pub async fn get_permission_template_select_items(
        &self,
    ) -> Result<Vec<SystemPermissionTemplateSelectItem>, BusinessError> {
        info!("获取权限模板选择项");

        let templates = self
            .system_permission_templates_repository
            .find_visible(&self.db)
            .await
            .map_err(|e| {
                error!("查询权限模板列表失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询权限模板列表失败".to_string())
            })?;

        let select_items = templates
            .into_iter()
            .map(|template| SystemPermissionTemplateSelectItem::from(template))
            .collect();

        Ok(select_items)
    }

    /// 获取权限模板统计信息
    pub async fn get_permission_template_stats(
        &self,
    ) -> Result<SystemPermissionTemplateStatsResponse, BusinessError> {
        info!("获取权限模板统计信息");

        // 查询总数
        let total_count = self
            .system_permission_templates_repository
            .count_all(&self.db)
            .await
            .map_err(|e| {
                error!("查询权限模板总数失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询权限模板总数失败".to_string())
            })? as i32;

        // 按类型统计
        let directory_count = self
            .system_permission_templates_repository
            .count_by_permission_type(1, &self.db)
            .await
            .map_err(|e| {
                error!("查询目录数量失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询目录数量失败".to_string())
            })? as i32;

        let menu_count = self
            .system_permission_templates_repository
            .count_by_permission_type(2, &self.db)
            .await
            .map_err(|e| {
                error!("查询菜单数量失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询菜单数量失败".to_string())
            })? as i32;

        let button_count = self
            .system_permission_templates_repository
            .count_by_permission_type(3, &self.db)
            .await
            .map_err(|e| {
                error!("查询按钮数量失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询按钮数量失败".to_string())
            })? as i32;

        // 按状态统计
        let visible_count = self
            .system_permission_templates_repository
            .count_visible(&self.db)
            .await
            .map_err(|e| {
                error!("查询显示数量失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询显示数量失败".to_string())
            })? as i32;

        let hidden_count = self
            .system_permission_templates_repository
            .count_hidden(&self.db)
            .await
            .map_err(|e| {
                error!("查询隐藏数量失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询隐藏数量失败".to_string())
            })? as i32;

        let stats = SystemPermissionTemplateStatsResponse {
            total_count,
            directory_count,
            menu_count,
            button_count,
            visible_count,
            hidden_count,
        };

        Ok(stats)
    }

    /// 根据父ID获取子权限列表
    pub async fn get_children_by_parent(
        &self,
        parent_id: Option<Uuid>,
    ) -> Result<Vec<SystemPermissionTemplateListResponse>, BusinessError> {
        info!("获取子权限列表，父ID: {:?}", parent_id);

        let templates = self
            .system_permission_templates_repository
            .find_by_parent_id(parent_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询子权限列表失败: {:?}", e);
                BusinessError::new(DATABASE_ERROR, "查询子权限列表失败".to_string())
            })?;

        let list = templates
            .into_iter()
            .map(|template| SystemPermissionTemplateListResponse::from(template))
            .collect();

        Ok(list)
    }
}

use crate::constants::*;
use crate::domain::business::merchants::entities::merchant_role_permissions::Entity as MerchantRolePermission;
use crate::domain::business::merchants::entities::merchant_roles::{
    ActiveModel, MerchantRoleDataScope, MerchantRoleStatus, MerchantRoleType,
};

use crate::domain::system::dto::sys_merchant_role_request::{
    SysMerchantRoleBatchDeleteRequest, SysMerchantRoleCreateRequest, SysMerchantRolePageRequest,
    SysMerchantRolePermissionRequest, SysMerchantRoleStatusRequest, SysMerchantRoleUpdateRequest,
};
use crate::domain::system::vo::merchant_role_permissions_vo::{
    AvailablePermissionTreeNode, AvailablePermissionTreeResponse, RolePermissionTreeDetailResponse,
    RolePermissionTreeNode, get_role_type_desc,
};
use crate::domain::system::vo::merchant_roles_vo::{
    MerchantRoleBasicResponse, MerchantRoleDetailResponse, MerchantRoleListResponse,
    MerchantRoleSelectItem, MerchantRoleStatsResponse,
};
use lib_core::app::plugin::service::Service as ServiceTrait;

use crate::domain::business::merchants::entities::prelude::MerchantRoles;
use crate::repository::{
    MerchantAuthorizedPermissionRepository, MerchantRoleRepository, MerchantsRepository,
    SystemPermissionTemplatesRepository,
};
use crate::service::system::SysMerchantAuthorizedPermissionService;
use crate::utils::datetime::DateTimeUtils;
use lib_core::BusinessError;
use lib_core::app::app::App;
use lib_core::app::plugin::ComponentRegistry;
use lib_core::response::PageData;

use lib_macros::Service;
use sea_orm::{ActiveEnum, ConnectionTrait, DatabaseConnection, Set, TransactionTrait};
use tracing::{error, info, warn};
use uuid::Uuid;

/// 商户角色管理服务实现
#[derive(Clone, Service)]
pub struct SysMerchantRoleService {
    #[inject(component)]
    db: DatabaseConnection,
    #[inject(component)]
    merchant_role_repository: MerchantRoleRepository,
    #[inject(component)]
    merchant_authorized_permission_repository: MerchantAuthorizedPermissionRepository,
    #[inject(component)]
    merchants_repository: MerchantsRepository,
    #[inject(component)]
    system_permission_templates_repository: SystemPermissionTemplatesRepository,
}

impl SysMerchantRoleService {
    /// 生成角色编码
    pub async fn generate_role_code(&self, merchant_id: i64) -> Result<String, BusinessError> {
        self.merchant_role_repository
            .generate_role_code(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("生成角色编码失败: {}", e)))
    }
}

impl SysMerchantRoleService {
    /// 分页查询商户角色
    pub async fn page_merchant_roles(
        &self,
        req: SysMerchantRolePageRequest,
    ) -> Result<PageData<MerchantRoleListResponse>, BusinessError> {
        info!("分页查询商户角色，请求: {:?}", req);

        // 使用 repository 进行分页查询
        let page_data = self
            .merchant_role_repository
            .page_by_condition(&req, &self.db)
            .await
            .map_err(|e| {
                let error_msg = format!("分页查询商户角色失败: {}", e);
                error!("分页查询商户角色失败，请求: {:?}，错误: {}", req, e);
                BusinessError::new(DATABASE_ERROR, error_msg)
            })?;

        // 转换为响应格式
        let response_items = page_data
            .items
            .into_iter()
            .map(|role| MerchantRoleListResponse::from(role))
            .collect();

        let result = PageData::new(
            response_items,
            page_data.total,
            page_data.page,
            page_data.page_size,
        );

        info!(
            "分页查询商户角色成功，总数: {}，页码: {}，页大小: {}",
            result.total, result.page, result.page_size
        );

        Ok(result)
    }

    /// 根据ID查询商户角色详情
    pub async fn get_merchant_role_by_id(
        &self,
        role_id: Uuid,
    ) -> Result<MerchantRoleDetailResponse, BusinessError> {
        info!("查询商户角色详情，ID: {}", role_id);

        let role = self
            .merchant_role_repository
            .find_by_id(role_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户角色失败: {}", e)))?
            .ok_or_else(|| {
                warn!("商户角色不存在: {}", role_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户角色不存在".to_string())
            })?;

        let detail = MerchantRoleDetailResponse::from(role);
        Ok(detail)
    }

    /// 创建商户角色
    pub async fn create_merchant_role(
        &self,
        req: SysMerchantRoleCreateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("创建商户角色，请求: {:?}，操作人: {}", req, operator_id);

        // 检查角色名称是否已存在
        let name_exists = self
            .merchant_role_repository
            .exists_by_name(req.merchant_id, &req.role_name, None, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查角色名称失败: {}", e)))?;
        if name_exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "角色名称已存在".to_string(),
            ));
        }

        // 生成或检查角色编码
        let role_code = if let Some(ref code) = req.role_code {
            // 检查角色编码是否已存在
            let code_exists = self
                .merchant_role_repository
                .exists_by_code(req.merchant_id, code, None, &self.db)
                .await
                .map_err(|e| {
                    BusinessError::new(DATABASE_ERROR, format!("检查角色编码失败: {}", e))
                })?;
            if code_exists {
                return Err(BusinessError::new(
                    RESOURCE_ALREADY_EXISTS,
                    "角色编码已存在".to_string(),
                ));
            }
            Some(code.clone())
        } else if req.role_type == 2 {
            // 自定义角色自动生成编码
            Some(self.generate_role_code(req.merchant_id).await?)
        } else {
            None
        };

        let new_role = ActiveModel {
            id: Set(Uuid::now_v7()),
            merchant_id: Set(req.merchant_id),
            role_code: Set(role_code),
            role_name: Set(req.role_name),
            role_type: Set(MerchantRoleType::try_from_value(&req.role_type)
                .map_err(|_| BusinessError::new(400, "无效的角色类型"))?),
            is_default: Set(req.is_default),
            data_scope: Set(MerchantRoleDataScope::try_from_value(&req.data_scope)
                .map_err(|_| BusinessError::new(400, "无效的数据范围"))?),
            role_description: Set(req.role_description),
            status: Set(MerchantRoleStatus::try_from_value(&req.status)
                .map_err(|_| BusinessError::new(400, "无效的角色状态"))?),
            created_date: Set(DateTimeUtils::now_local().fixed_offset()),
            updated_date: Set(DateTimeUtils::now_local().fixed_offset()),
            created_by: Set(Some(operator_id)),
            updated_by: Set(Some(operator_id)),
            remark: Set(req.remark),
        };

        self.merchant_role_repository
            .create(new_role, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("创建商户角色失败: {}", e)))?;

        info!("商户角色创建成功");
        Ok("商户角色创建成功".to_string())
    }

    /// 更新商户角色
    pub async fn update_merchant_role(
        &self,
        role_id: Uuid,
        req: SysMerchantRoleUpdateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "更新商户角色，ID: {}，请求: {:?}，操作人: {}",
            role_id, req, operator_id
        );

        let role = self
            .merchant_role_repository
            .find_by_id(role_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户角色失败: {}", e)))?
            .ok_or_else(|| {
                warn!("商户角色不存在: {}", role_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户角色不存在".to_string())
            })?;

        // 检查角色名称是否已存在（排除当前角色）
        let name_exists = self
            .merchant_role_repository
            .exists_by_name(role.merchant_id, &req.role_name, Some(role_id), &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查角色名称失败: {}", e)))?;
        if name_exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "角色名称已存在".to_string(),
            ));
        }

        // 检查角色编码是否已存在（排除当前角色）
        if let Some(ref code) = req.role_code {
            let code_exists = self
                .merchant_role_repository
                .exists_by_code(role.merchant_id, code, Some(role_id), &self.db)
                .await
                .map_err(|e| {
                    BusinessError::new(DATABASE_ERROR, format!("检查角色编码失败: {}", e))
                })?;
            if code_exists {
                return Err(BusinessError::new(
                    RESOURCE_ALREADY_EXISTS,
                    "角色编码已存在".to_string(),
                ));
            }
        }

        let updated_role = req.update_active_model(role.into(), Some(operator_id));

        self.merchant_role_repository
            .update(updated_role, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("更新商户角色失败: {}", e)))?;

        info!("商户角色更新成功，ID: {}", role_id);
        Ok("商户角色更新成功".to_string())
    }

    /// 删除商户角色
    pub async fn delete_merchant_role(
        &self,
        role_id: Uuid,
        _operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("删除商户角色，ID: {}，操作人: {}", role_id, _operator_id);

        let role = self
            .merchant_role_repository
            .find_by_id(role_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户角色失败: {}", e)))?
            .ok_or_else(|| {
                warn!("商户角色不存在: {}", role_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户角色不存在".to_string())
            })?;

        // 检查是否为默认角色
        if role.is_default {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "默认角色不能删除".to_string(),
            ));
        }

        // 检查角色是否可以删除
        let deletable = self
            .merchant_role_repository
            .is_deletable(role_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("检查角色是否可删除失败: {}", e))
            })?;
        if !deletable {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "该角色已分配给用户，不能删除".to_string(),
            ));
        }

        // 开启事务删除角色及相关数据
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "开启事务失败".to_string()))?;

        // 删除角色权限关联
        self.merchant_role_repository
            .delete_role_permissions(role_id, &txn)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("删除角色权限关联失败: {}", e))
            })?;

        // 删除角色
        self.merchant_role_repository
            .delete_by_id(role_id, &txn)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除商户角色失败: {}", e)))?;

        txn.commit()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "提交事务失败".to_string()))?;

        info!("商户角色删除成功，ID: {}", role_id);
        Ok("商户角色删除成功".to_string())
    }

    /// 批量删除商户角色
    pub async fn batch_delete_merchant_roles(
        &self,
        req: SysMerchantRoleBatchDeleteRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "批量删除商户角色，IDs: {:?}，操作人: {}",
            req.role_ids, operator_id
        );

        if req.role_ids.is_empty() {
            return Err(BusinessError::new(
                INVALID_PARAMETER,
                "请选择要删除的角色".to_string(),
            ));
        }

        // 检查是否有默认角色
        let default_roles = self
            .merchant_role_repository
            .count_default_roles(req.role_ids.clone(), &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查默认角色失败: {}", e)))?;

        if default_roles > 0 {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "选择的角色中包含默认角色，不能删除".to_string(),
            ));
        }

        // 检查是否有角色已分配给用户
        let assigned_roles = self
            .merchant_role_repository
            .count_assigned_roles(req.role_ids.clone(), &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查角色分配失败: {}", e)))?;

        if assigned_roles > 0 {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "选择的角色中有已分配给用户的角色，不能删除".to_string(),
            ));
        }

        // 开启事务批量删除
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "开启事务失败".to_string()))?;

        // 删除角色权限关联
        self.merchant_role_repository
            .delete_role_permissions_by_role_ids(req.role_ids.clone(), &txn)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("删除角色权限关联失败: {}", e))
            })?;

        // 批量删除角色
        let delete_count = self
            .merchant_role_repository
            .delete_by_ids(req.role_ids.clone(), &txn)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("批量删除商户角色失败: {}", e))
            })?;

        txn.commit()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "提交事务失败".to_string()))?;

        info!("批量删除商户角色成功，删除数量: {}", delete_count);
        Ok(format!("成功删除{}个角色", delete_count))
    }

    /// 切换商户角色状态
    pub async fn change_merchant_role_status(
        &self,
        role_id: Uuid,
        req: SysMerchantRoleStatusRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "切换商户角色状态，ID: {}，状态: {}，操作人: {}",
            role_id, req.status, operator_id
        );

        let status = MerchantRoleStatus::try_from_value(&req.status)
            .map_err(|_| BusinessError::new(400, "无效的角色状态"))?;

        let role = self
            .merchant_role_repository
            .find_by_id(role_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户角色失败: {}", e)))?
            .ok_or_else(|| {
                warn!("商户角色不存在: {}", role_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户角色不存在".to_string())
            })?;

        let mut role_active: ActiveModel = role.into();
        role_active.status = Set(status);
        role_active.updated_date = Set(DateTimeUtils::now_local().fixed_offset());
        role_active.updated_by = Set(Some(operator_id));

        self.merchant_role_repository
            .update(role_active, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("更新商户角色状态失败: {}", e))
            })?;

        let status_text = MerchantRoles::get_status_desc(status);
        info!(
            "商户角色状态切换成功，ID: {}，状态: {}",
            role_id, status_text
        );
        Ok(format!("角色状态已切换为{}", status_text))
    }

    /// 为角色分配权限
    pub async fn assign_permissions_to_role(
        &self,
        role_id: Uuid,
        req: SysMerchantRolePermissionRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "为角色分配权限，角色ID: {}，权限: {:?}，操作人: {}",
            role_id, req.authorized_permission_ids, operator_id
        );

        // 检查角色是否存在
        let role = self
            .merchant_role_repository
            .find_by_id(role_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户角色失败: {}", e)))?
            .ok_or_else(|| {
                warn!("商户角色不存在: {}", role_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户角色不存在".to_string())
            })?;

        if req.authorized_permission_ids.is_empty() {
            return Err(BusinessError::new(
                INVALID_PARAMETER,
                "请选择要分配的权限".to_string(),
            ));
        }

        // 开启事务
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "开启事务失败".to_string()))?;

        // 删除现有的权限分配
        self.merchant_role_repository
            .delete_role_permissions(role_id, &txn)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("删除现有权限分配失败: {}", e))
            })?;

        // 查询商户授权权限，获取权限模板ID到商户授权权限ID的映射
        let merchant_auth_permissions = self
            .merchant_authorized_permission_repository
            .find_by_merchant_and_template_ids(
                role.merchant_id,
                req.authorized_permission_ids.clone(),
                &txn,
            )
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询商户授权权限失败: {}", e))
            })?;

        // 创建权限模板ID到商户授权权限ID的映射
        let template_to_auth_map: std::collections::HashMap<Uuid, Uuid> = merchant_auth_permissions
            .iter()
            .map(|auth| (auth.permission_template_id, auth.id))
            .collect();

        // 验证所有权限模板ID都有对应的商户授权权限
        for template_id in &req.authorized_permission_ids {
            if !template_to_auth_map.contains_key(template_id) {
                return Err(BusinessError::new(
                    INVALID_PARAMETER,
                    format!("权限模板 {} 未授权给该商户", template_id),
                ));
            }
        }

        let mut permissions: Vec<
            crate::domain::business::merchants::entities::merchant_role_permissions::ActiveModel,
        > = Vec::new();

        // 批量插入新的权限分配
        let now = DateTimeUtils::now_local().fixed_offset();
        for template_id in req.authorized_permission_ids {
            // 获取对应的商户授权权限ID
            let authorized_permission_id = template_to_auth_map.get(&template_id).unwrap();

            let role_permission = crate::domain::business::merchants::entities::merchant_role_permissions::ActiveModel {
                id: Set(Uuid::now_v7()),
                merchant_id: Set(role.merchant_id),
                role_id: Set(role_id),
                authorized_permission_id: Set(*authorized_permission_id), // 存储正确的商户授权权限ID
                created_date: Set(now),
                created_by: Set(Some(operator_id)),
                remark: Set(None),
            };
            permissions.push(role_permission);
        }
        self.merchant_role_repository
            .insert_role_permissions(permissions, &txn)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("分配权限失败: {}", e)))?;

        txn.commit()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "提交事务失败".to_string()))?;

        info!("角色权限分配成功，角色ID: {}", role_id);
        Ok("角色权限分配成功".to_string())
    }

    /// 移除角色权限
    pub async fn remove_permissions_from_role(
        &self,
        role_id: Uuid,
        req: SysMerchantRolePermissionRequest,
        _operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "移除角色权限，角色ID: {}，权限: {:?}，操作人: {}",
            role_id, req.authorized_permission_ids, _operator_id
        );

        // 检查角色是否存在
        let _role = self
            .merchant_role_repository
            .find_by_id(role_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户角色失败: {}", e)))?
            .ok_or_else(|| {
                warn!("商户角色不存在: {}", role_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户角色不存在".to_string())
            })?;

        if req.authorized_permission_ids.is_empty() {
            return Err(BusinessError::new(
                INVALID_PARAMETER,
                "请选择要移除的权限".to_string(),
            ));
        }

        // 删除指定的权限分配
        let delete_count = self
            .merchant_role_repository
            .delete_role_permissions_by_permission_ids(
                role_id,
                req.authorized_permission_ids,
                &self.db,
            )
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("移除角色权限失败: {}", e)))?;

        info!(
            "角色权限移除成功，角色ID: {}，移除数量: {}",
            role_id, delete_count
        );
        Ok(format!("成功移除{}个权限", delete_count))
    }

    /// 获取商户角色选择项
    pub async fn get_merchant_role_select_items(
        &self,
        merchant_id: i64,
    ) -> Result<Vec<MerchantRoleSelectItem>, BusinessError> {
        info!("获取商户角色选择项，商户ID: {}", merchant_id);

        let roles = self
            .merchant_role_repository
            .find_enabled_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询商户角色列表失败: {}", e))
            })?;

        let select_items = roles
            .into_iter()
            .map(|role| MerchantRoleSelectItem::from(role))
            .collect();

        Ok(select_items)
    }

    /// 获取商户角色基础信息列表
    pub async fn get_merchant_role_basic_list(
        &self,
        merchant_id: i64,
    ) -> Result<Vec<MerchantRoleBasicResponse>, BusinessError> {
        info!("获取商户角色基础信息列表，商户ID: {}", merchant_id);

        let roles = self
            .merchant_role_repository
            .find_enabled_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询商户角色列表失败: {}", e))
            })?;

        let basic_list = roles
            .into_iter()
            .map(|role| MerchantRoleBasicResponse::from(role))
            .collect();

        Ok(basic_list)
    }

    /// 获取商户角色统计信息
    pub async fn get_merchant_role_stats(
        &self,
        merchant_id: i64,
    ) -> Result<MerchantRoleStatsResponse, BusinessError> {
        info!("获取商户角色统计信息，商户ID: {}", merchant_id);

        // 查询总角色数
        let total_roles = self
            .merchant_role_repository
            .count_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询角色总数失败: {}", e)))?
            as i32;

        // 查询管理员角色数
        let admin_roles = self
            .merchant_role_repository
            .count_admin_roles(merchant_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询管理员角色数失败: {}", e))
            })? as i32;

        // 查询自定义角色数
        let custom_roles = self
            .merchant_role_repository
            .count_custom_roles(merchant_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询自定义角色数失败: {}", e))
            })? as i32;

        // 查询启用角色数
        let active_roles = self
            .merchant_role_repository
            .count_active_roles(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询启用角色数失败: {}", e)))?
            as i32;

        // 查询禁用角色数
        let disabled_roles = self
            .merchant_role_repository
            .count_disabled_roles(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询禁用角色数失败: {}", e)))?
            as i32;

        let stats = MerchantRoleStatsResponse {
            merchant_id,
            merchant_name: "".to_string(),
            total_roles,
            admin_roles,
            custom_roles,
            active_roles,
            disabled_roles,
        };

        Ok(stats)
    }

    /// 获取商户管理员角色
    pub async fn get_merchant_default_admin_role(
        &self,
        merchant_id: i64,
    ) -> Result<Option<Uuid>, BusinessError> {
        let role = self
            .merchant_role_repository
            .find_admin_role(merchant_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询管理员角色失败: {}", e))
            })?;

        Ok(role.map(|r| r.id))
    }

    /// 获取角色权限分配
    pub async fn get_role_permissions(
        &self,
        role_id: Uuid,
    ) -> Result<RolePermissionTreeDetailResponse, BusinessError> {
        info!("获取角色权限分配，角色ID: {}", role_id);

        // 查询角色信息
        let role = self
            .merchant_role_repository
            .find_by_id(role_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询角色失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "角色不存在".to_string()))?;

        // 查询商户信息
        let merchant = self
            .merchants_repository
            .find_by_id(role.merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户信息失败: {}", e)))?;

        // 查询角色已分配的权限关联
        let role_permissions = self
            .merchant_role_repository
            .find_role_permissions(role_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询角色权限失败: {}", e)))?;

        let assigned_permission_ids: Vec<Uuid> = role_permissions
            .into_iter()
            .map(|rp| rp.authorized_permission_id)
            .collect();

        // 查询商户的所有授权权限（包含权限模板信息）
        let authorized_permissions = self
            .merchant_authorized_permission_repository
            .find_active_by_merchant_id(role.merchant_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询商户授权权限失败: {}", e))
            })?;

        // 获取所有权限模板ID
        let template_ids: Vec<Uuid> = authorized_permissions
            .iter()
            .map(|ap| ap.permission_template_id)
            .collect();

        // 查询权限模板详细信息
        let permission_templates = self
            .system_permission_templates_repository
            .find_by_ids(template_ids, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询权限模板失败: {}", e)))?;

        // 统计权限数量
        let total_permissions = authorized_permissions.len() as i32;
        let assigned_permissions = assigned_permission_ids.len() as i32;

        // 构建授权权限映射
        let auth_map: std::collections::HashMap<Uuid, _> = authorized_permissions
            .into_iter()
            .map(|auth| (auth.permission_template_id, auth))
            .collect();

        // 使用VO中的构建方法创建权限树
        let root_nodes = RolePermissionTreeNode::build_permission_tree(
            permission_templates,
            auth_map,
            assigned_permission_ids.clone(),
        );

        let response = RolePermissionTreeDetailResponse::builder()
            .role_id(role.id)
            .role_name(role.role_name)
            .role_type(role.role_type.to_value())
            .role_type_desc(get_role_type_desc(Some(role.role_type.to_value())))
            .merchant_id(role.merchant_id)
            .merchant_name(merchant.map(|m| m.merchant_name).unwrap_or_default())
            .total_permissions(total_permissions)
            .assigned_permissions(assigned_permissions)
            .permission_tree(root_nodes)
            .build();

        Ok(response)
    }

    /// 获取角色可分配的权限列表
    pub async fn get_available_permissions_for_role(
        &self,
        role_id: Uuid,
    ) -> Result<AvailablePermissionTreeResponse, BusinessError> {
        info!("获取角色可分配权限，角色ID: {}", role_id);

        // 查询角色信息
        let role = self
            .merchant_role_repository
            .find_by_id(role_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询角色失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "角色不存在".to_string()))?;

        // 查询商户信息
        let merchant = self
            .merchants_repository
            .find_by_id(role.merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户信息失败: {}", e)))?;

        // 查询角色已分配的权限
        let role_permissions = self
            .merchant_role_repository
            .find_role_permissions(role_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询角色权限失败: {}", e)))?;

        let assigned_permission_ids: Vec<Uuid> = role_permissions
            .into_iter()
            .map(|rp| rp.authorized_permission_id)
            .collect();

        // 查询该商户的所有授权权限
        let authorized_permissions = self
            .merchant_authorized_permission_repository
            .find_by_merchant_id(role.merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询授权权限失败: {}", e)))?;

        // 获取所有权限模板ID
        let template_ids: Vec<Uuid> = authorized_permissions
            .iter()
            .map(|ap| ap.permission_template_id)
            .collect();

        // 查询权限模板详细信息
        let permission_templates = self
            .system_permission_templates_repository
            .find_by_ids(template_ids, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询权限模板失败: {}", e)))?;

        // 构建权限模板映射
        let template_map: std::collections::HashMap<Uuid, _> = permission_templates
            .into_iter()
            .map(|template| (template.id, template))
            .collect();

        // 构建授权权限映射
        let auth_map: std::collections::HashMap<Uuid, _> = authorized_permissions
            .into_iter()
            .map(|auth| (auth.permission_template_id, auth))
            .collect();

        // 统计权限数量（在移动之前）
        let total_authorized = auth_map.len() as i32;
        let already_assigned = assigned_permission_ids.len() as i32;

        // 使用VO中的构建方法创建权限树
        let root_nodes = AvailablePermissionTreeNode::build_available_permission_tree(
            template_map,
            auth_map,
            assigned_permission_ids,
        );
        let available_count = total_authorized - already_assigned;

        let response = AvailablePermissionTreeResponse::builder()
            .role_id(role.id)
            .role_name(role.role_name)
            .merchant_id(role.merchant_id)
            .merchant_name(merchant.map(|m| m.merchant_name).unwrap_or_default())
            .total_authorized(total_authorized)
            .already_assigned(already_assigned)
            .available_count(available_count)
            .permission_tree(root_nodes)
            .build();

        Ok(response)
    }

    /// 创建商户时自动创建管理员角色
    pub async fn create_merchant_admin_role<C>(
        &self,
        merchant_id: i64,
        operator_id: Uuid,
        db: &C,
    ) -> Result<Uuid, BusinessError>
    where
        C: ConnectionTrait,
    {
        // 检查是否已存在默认管理员角色
        let default_admin_role = self.get_merchant_default_admin_role(merchant_id).await?;
        if let Some(existing_role_id) = default_admin_role {
            info!("商户已存在默认管理员角色，跳过创建");
            return Ok(existing_role_id);
        }

        // 没有管理员角色，创建默认管理员角色
        let role_id = MerchantRoles::default_admin_role(operator_id, merchant_id, db)
            .await
            .map_err(|e| {
                error!("创建默认管理员角色失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "创建默认管理员角色失败".to_string())
            })?;

        info!("成功创建商户默认管理员角色，角色ID: {}", role_id);
        Ok(role_id)
    }
    /// 为商户管理员角色分配商户所拥有的全部权限
    pub async fn assign_all_merchant_permissions_to_admin_role<C>(
        &self,
        merchant_id: i64,
        role_id: Uuid,
        operator_id: Uuid,
        db: &C,
    ) -> Result<String, BusinessError>
    where
        C: ConnectionTrait,
    {
        let permission_service = App::global()
            .get_component::<SysMerchantAuthorizedPermissionService>()
            .unwrap();

        // 获取商户所拥有的授权权限id集合vec
        let authorized_permissions_ids = permission_service
            .get_authorized_permission_ids_by_merchant_id(merchant_id)
            .await
            .map_err(|e| BusinessError::new(500, e.to_string()))
            .unwrap_or(vec![]);

        // 准备向商户角色权限中间表插入数据
        let _ = MerchantRolePermission::create_from_role_id_and_authorized_permissions_ids(
            role_id,
            merchant_id,
            authorized_permissions_ids,
            operator_id,
            db,
        )
        .await
        .map_err(|e| BusinessError::new(500, e.to_string()));

        Ok("".to_string())
    }

    /// 【新增】同步商户管理员角色权限
    /// 当商户权限发生变化时，自动同步该商户的管理员角色权限
    pub async fn sync_merchant_admin_role_permissions(
        &self,
        merchant_id: i64,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "开始同步商户管理员角色权限，商户ID: {}，操作人: {}",
            merchant_id, operator_id
        );

        // 1. 查找商户的管理员角色
        let admin_role = self
            .merchant_role_repository
            .find_enabled_admin_role(merchant_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询商户管理员角色失败: {}", e))
            })?;

        if let Some(admin_role) = admin_role {
            // 2. 获取商户最新的所有授权权限ID
            let permission_service = App::global()
                .get_component::<SysMerchantAuthorizedPermissionService>()
                .unwrap();

            let authorized_permission_ids = permission_service
                .get_authorized_permission_ids_by_merchant_id(merchant_id)
                .await
                .map_err(|e| {
                    error!("获取商户授权权限失败: {}", e);
                    BusinessError::new(DATABASE_ERROR, "获取商户授权权限失败".to_string())
                })?;

            // 3. 开启事务更新管理员角色权限
            let txn = self
                .db
                .begin()
                .await
                .map_err(|_| BusinessError::new(DATABASE_ERROR, "开启事务失败".to_string()))?;

            // 4. 删除管理员角色现有的权限分配
            self.merchant_role_repository
                .delete_role_permissions(admin_role.id, &txn)
                .await
                .map_err(|e| {
                    BusinessError::new(DATABASE_ERROR, format!("删除现有权限分配失败: {}", e))
                })?;

            let authorized_permission_ids_len = authorized_permission_ids.len();

            // 5. 为管理员角色分配所有商户权限
            if !authorized_permission_ids.is_empty() {
                let mut permissions: Vec<
                    crate::domain::business::merchants::entities::merchant_role_permissions::ActiveModel,
                > = Vec::new();

                let now = DateTimeUtils::now_local().fixed_offset();
                for authorized_permission_id in authorized_permission_ids {
                    let role_permission = crate::domain::business::merchants::entities::merchant_role_permissions::ActiveModel {
                        id: Set(Uuid::now_v7()),
                        merchant_id: Set(merchant_id),
                        role_id: Set(admin_role.id),
                        authorized_permission_id: Set(authorized_permission_id),
                        created_date: Set(now),
                        created_by: Set(Some(operator_id)),
                        remark: Set(Some("系统自动同步管理员权限".to_string())),
                    };
                    permissions.push(role_permission);
                }

                self.merchant_role_repository
                    .insert_role_permissions(permissions, &txn)
                    .await
                    .map_err(|e| {
                        BusinessError::new(DATABASE_ERROR, format!("同步管理员权限失败: {}", e))
                    })?;
            }

            // 6. 提交事务
            txn.commit()
                .await
                .map_err(|_| BusinessError::new(DATABASE_ERROR, "提交事务失败".to_string()))?;

            info!(
                "商户管理员角色权限同步成功，商户ID: {}，角色ID: {}",
                merchant_id, admin_role.id
            );
            Ok(format!(
                "成功同步管理员角色权限，共{}个权限",
                authorized_permission_ids_len
            ))
        } else {
            warn!("商户{}未找到管理员角色，跳过权限同步", merchant_id);
            Ok("未找到管理员角色，跳过同步".to_string())
        }
    }

    /// 【新增】批量同步多个商户的管理员角色权限
    /// 用于批量操作时的权限同步
    pub async fn batch_sync_merchant_admin_role_permissions(
        &self,
        merchant_ids: Vec<i64>,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "开始批量同步商户管理员角色权限，商户数量: {}，操作人: {}",
            merchant_ids.len(),
            operator_id
        );

        if merchant_ids.is_empty() {
            return Ok("没有需要同步的商户".to_string());
        }

        // 1. 批量查询所有商户的管理员角色
        let admin_roles = self
            .merchant_role_repository
            .find_admin_roles_by_merchant_ids(merchant_ids.clone(), &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("批量查询商户管理员角色失败: {}", e))
            })?;

        if admin_roles.is_empty() {
            warn!("未找到任何管理员角色，跳过批量同步");
            return Ok("未找到任何管理员角色，跳过同步".to_string());
        }

        // 2. 批量查询所有商户的授权权限
        let all_merchant_permissions = self
            .merchant_authorized_permission_repository
            .find_by_merchant_ids(merchant_ids.clone(), &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("批量查询商户授权权限失败: {}", e))
            })?;

        // 3. 构建商户权限映射
        let mut merchant_permissions_map: std::collections::HashMap<i64, Vec<Uuid>> =
            std::collections::HashMap::new();
        for permission in all_merchant_permissions {
            merchant_permissions_map
                .entry(permission.merchant_id)
                .or_insert_with(Vec::new)
                .push(permission.id);
        }

        // 4. 构建角色ID列表
        let role_ids: Vec<Uuid> = admin_roles.iter().map(|role| role.id).collect();

        // 5. 开启事务进行批量操作
        let txn =
            self.db.begin().await.map_err(|_| {
                BusinessError::new(DATABASE_ERROR, "开启批量同步事务失败".to_string())
            })?;

        // 6. 批量删除所有管理员角色现有的权限分配
        let deleted_count = self
            .merchant_role_repository
            .delete_role_permissions_by_role_ids(role_ids.clone(), &txn)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("批量删除现有权限分配失败: {}", e))
            })?;

        info!("批量删除现有权限分配完成，删除数量: {}", deleted_count);

        // 7. 批量构建新的权限分配
        let mut all_permissions: Vec<
            crate::domain::business::merchants::entities::merchant_role_permissions::ActiveModel,
        > = Vec::new();
        let now = DateTimeUtils::now_local().fixed_offset();
        let mut total_permissions_count = 0;

        for admin_role in &admin_roles {
            if let Some(authorized_permission_ids) =
                merchant_permissions_map.get(&admin_role.merchant_id)
            {
                for &authorized_permission_id in authorized_permission_ids {
                    let role_permission = crate::domain::business::merchants::entities::merchant_role_permissions::ActiveModel {
                        id: Set(Uuid::now_v7()),
                        merchant_id: Set(admin_role.merchant_id),
                        role_id: Set(admin_role.id),
                        authorized_permission_id: Set(authorized_permission_id),
                        created_date: Set(now),
                        created_by: Set(Some(operator_id)),
                        remark: Set(Some("批量自动同步管理员权限".to_string())),
                    };
                    all_permissions.push(role_permission);
                    total_permissions_count += 1;
                }
            }
        }

        // 8. 批量插入新的权限分配
        if !all_permissions.is_empty() {
            self.merchant_role_repository
                .insert_role_permissions(all_permissions, &txn)
                .await
                .map_err(|e| {
                    BusinessError::new(DATABASE_ERROR, format!("批量插入权限分配失败: {}", e))
                })?;
        }

        // 9. 提交事务
        txn.commit()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "批量同步事务提交失败".to_string()))?;

        let synced_merchants_count = admin_roles.len();
        info!(
            "批量同步商户管理员角色权限完成，同步商户数: {}，总权限数: {}",
            synced_merchants_count, total_permissions_count
        );

        Ok(format!(
            "批量同步成功，共同步{}个商户的管理员权限，总计{}个权限",
            synced_merchants_count, total_permissions_count
        ))
    }
}

use crate::constants::*;
use crate::domain::business::merchants::entities::merchant_authorized_permissions::{
    ActiveModel, MerchantAuthorizedPermissionsStatus, Model,
};
use crate::domain::system::dto::sys_merchant_authorized_permission_request::{
    SysMerchantAuthorizedPermissionBatchDeleteRequest,
    SysMerchantAuthorizedPermissionCreateRequest, SysMerchantAuthorizedPermissionPageRequest,
    SysMerchantAuthorizedPermissionStatusRequest, SysMerchantAuthorizedPermissionUpdateRequest,
    SysMerchantPermissionBatchAuthorizeRequest, SysMerchantPermissionCopyRequest,
    SysMerchantPermissionRevokeRequest,
};
use crate::domain::system::vo::merchant_authorized_permissions_vo::{
    MerchantAuthorizedPermissionDetailResponse, MerchantAuthorizedPermissionListResponse,
    MerchantAuthorizedPermissionSelectItem, MerchantAuthorizedPermissionTreeNode,
    MerchantAuthorizedPermissionTreeResponse, MerchantPermissionStatsResponse,
};
use crate::domain::system::vo::permission_template_vo::{
    SystemPermissionTemplateSelectItem, SystemPermissionTemplateTreeNode,
};
use crate::repository::{MerchantAuthorizedPermissionRepository, MerchantRolePermissionsRepository, SystemPermissionTemplatesRepository};
use crate::service::system::SysMerchantRoleService;
use crate::utils::datetime::DateTimeUtils;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_core::response::PageData;
use lib_core::BusinessError;
use lib_macros::Service;
use sea_orm::{ActiveEnum, DatabaseConnection, Set, TransactionTrait};
use tracing::{info, warn};
use uuid::Uuid;

/// 商户授权权限管理服务实现
#[derive(Clone, Service)]
pub struct SysMerchantAuthorizedPermissionService {
    #[inject(component)]
    merchant_authorized_permission_repos: MerchantAuthorizedPermissionRepository,
    #[inject(component)]
    merchant_role_permissions_repos: MerchantRolePermissionsRepository,
    #[inject(component)]
    system_permission_templates_repos: SystemPermissionTemplatesRepository,
    #[inject(component)]
    merchant_role_service: SysMerchantRoleService,
    #[inject(component)]
    db: DatabaseConnection,
}

impl SysMerchantAuthorizedPermissionService {
    /// 分页查询商户授权权限列表
    pub async fn page_merchant_authorized_permissions(
        &self,
        req: SysMerchantAuthorizedPermissionPageRequest,
    ) -> Result<PageData<MerchantAuthorizedPermissionListResponse>, BusinessError> {
        info!("分页查询商户授权权限，参数: {:?}", req);

        // 使用Repository进行分页查询
        let page_data = self
            .merchant_authorized_permission_repos
            .page_by_condition(&req, &self.db)
                .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("分页查询商户授权权限失败: {}", e)))?;

        // 如果没有数据，直接返回空的响应结果
        if page_data.items.is_empty() {
            return Ok(PageData::new(
                Vec::new(),
                page_data.total,
                page_data.page,
                page_data.page_size,
            ));
        }

        // 批量获取权限模板信息，避免循环中的数据库查询
        let permission_template_ids: Vec<Uuid> = page_data
            .items
            .iter()
            .map(|item| item.permission_template_id)
            .collect();

        // 批量查询权限模板信息
        let permission_templates = self
            .merchant_authorized_permission_repos
            .find_templates_by_ids(permission_template_ids, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询权限模板失败: {}", e)))?;

        // 构建权限模板映射，提高查找效率
        let template_map: std::collections::HashMap<Uuid, _> = permission_templates
            .into_iter()
            .map(|template| (template.id, template))
            .collect();

        // 为每个授权权限添加权限模板信息
        let enhanced_items: Vec<MerchantAuthorizedPermissionListResponse> = page_data
            .items
            .into_iter()
            .map(|model| {
                let mut response = MerchantAuthorizedPermissionListResponse::from(model);
                if let Some(template) = template_map.get(&response.permission_template_id) {
                    response = response.with_permission_info(
                        Some(template.permission_name.clone()),
                        Some(template.permission_code.clone()),
                        template.permission_type,
                    );
                }
                response
            })
            .collect();

        // 构建最终的分页结果
        let final_page_data = PageData::new(
            enhanced_items,
            page_data.total,
            page_data.page,
            page_data.page_size,
        );

        Ok(final_page_data)
    }

    /// 根据ID查询商户授权权限详情
    pub async fn get_merchant_authorized_permission_by_id(
        &self,
        authorized_permission_id: Uuid,
    ) -> Result<MerchantAuthorizedPermissionDetailResponse, BusinessError> {
        info!("查询商户授权权限详情，ID: {}", authorized_permission_id);

        let authorized_permission = self
            .merchant_authorized_permission_repos
            .find_by_id(authorized_permission_id, &self.db)
                .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户授权权限失败: {}", e)))?
                .ok_or_else(|| {
                    warn!("商户授权权限不存在: {}", authorized_permission_id);
                    BusinessError::new(RESOURCE_NOT_FOUND, "商户授权权限不存在".to_string())
                })?;

        let mut detail =
            MerchantAuthorizedPermissionDetailResponse::from(authorized_permission.clone());

        // 查询权限模板信息
        if let Ok(templates) = self
            .merchant_authorized_permission_repos
            .find_templates_by_ids(vec![authorized_permission.permission_template_id], &self.db)
                .await
        {
            if let Some(template) = templates.first() {
            detail = detail.with_permission_info(
                    Some(template.permission_name.clone()),
                    Some(template.permission_code.clone()),
                template.permission_type,
                    template.description.clone(),
            );
            }
        }

        // TODO: 查询商户名称和用户姓名

        Ok(detail)
    }

    /// 创建商户授权权限
    pub async fn create_merchant_authorized_permission(
        &self,
        req: SysMerchantAuthorizedPermissionCreateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("创建商户授权权限，请求: {:?}，操作人: {}", req, operator_id);

        let merchant_id = req.merchant_id;

        // 检查权限模板是否存在
        let existing_templates = self
            .check_permission_templates_exist(req.permission_template_ids.clone())
            .await.map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        if existing_templates.len() != req.permission_template_ids.len() {
            return Err(BusinessError::new(
                INVALID_PARAMETER,
                "部分权限模板不存在".to_string(),
            ));
        }

        // 检查是否已授权相同权限
        let exists = self
            .merchant_authorized_permission_repos
            .exists_by_merchant_and_templates(
                req.merchant_id,
                req.permission_template_ids.clone(),
                &self.db,
            )
            .await.map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        if exists {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "商户已授权部分权限，请先撤销后重新授权".to_string(),
            ));
        }

        // 批量创建授权权限
        let models = req.to_active_models(operator_id).map_err(|_| {
            BusinessError::new(INVALID_PARAMETER, "构建授权权限数据失败".to_string())
        })?;

        self.merchant_authorized_permission_repos
            .insert_many(models, &self.db)
            .await.map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        info!("商户授权权限创建成功");

        // 【新增】自动同步商户管理员角色权限
        if let Err(e) = self
            .merchant_role_service
            .sync_merchant_admin_role_permissions(merchant_id, operator_id)
            .await
        {
            warn!("同步商户管理员权限失败: {}，但权限创建已成功", e);
        } else {
            info!("商户管理员权限同步成功");
        }

        Ok("商户权限授权成功".to_string())
    }

    /// 更新商户授权权限
    pub async fn update_merchant_authorized_permission(
        &self,
        authorized_permission_id: Uuid,
        req: SysMerchantAuthorizedPermissionUpdateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "更新商户授权权限，ID: {}，请求: {:?}，操作人: {}",
            authorized_permission_id, req, operator_id
        );

        let authorized_permission = self
            .merchant_authorized_permission_repos
            .find_by_id(authorized_permission_id, &self.db)
                .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户授权权限失败: {}", e)))?
                .ok_or_else(|| {
                    warn!("商户授权权限不存在: {}", authorized_permission_id);
                    BusinessError::new(RESOURCE_NOT_FOUND, "商户授权权限不存在".to_string())
                })?;

        let updated_permission = req
            .update_active_model(authorized_permission.into(), operator_id)
            .map_err(|_| BusinessError::new(INVALID_PARAMETER, "构建更新数据失败".to_string()))?;

        self.merchant_authorized_permission_repos
            .update(updated_permission, &self.db)
            .await.map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        info!("商户授权权限更新成功，ID: {}", authorized_permission_id);
        Ok("商户授权权限更新成功".to_string())
    }

    /// 删除商户授权权限
    pub async fn delete_merchant_authorized_permission(
        &self,
        authorized_permission_id: Uuid,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "删除商户授权权限，ID: {}，操作人: {}",
            authorized_permission_id, operator_id
        );

        let authorized_permission = self
            .merchant_authorized_permission_repos
            .find_by_id(authorized_permission_id, &self.db)
                .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户授权权限失败: {}", e)))?
                .ok_or_else(|| {
                    warn!("商户授权权限不存在: {}", authorized_permission_id);
                    BusinessError::new(RESOURCE_NOT_FOUND, "商户授权权限不存在".to_string())
                })?;

        // 检查权限是否可以删除
        let deletable = self
            .merchant_authorized_permission_repos
            .is_deletable(authorized_permission_id, &self.db)
            .await.map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;
        if !deletable {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "该权限已分配给角色，不能删除".to_string(),
            ));
        }

        // 开启事务删除权限及相关数据
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "开启事务失败".to_string()))?;

        // 删除角色权限关联
        self.merchant_role_permissions_repos
            .delete_by_authorized_permission_ids(vec![authorized_permission_id], &txn)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除角色权限关联失败: {}", e)))?;

        // 删除授权权限
        self.merchant_authorized_permission_repos
            .delete_by_id(authorized_permission_id, &txn)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除商户授权权限失败: {}", e)))?;

        txn.commit()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "提交事务失败".to_string()))?;

        info!("商户授权权限删除成功，ID: {}", authorized_permission_id);

        // 【新增】自动同步商户管理员角色权限
        if let Err(e) = self
            .merchant_role_service
            .sync_merchant_admin_role_permissions(authorized_permission.merchant_id, operator_id)
            .await
        {
            warn!("同步商户管理员权限失败: {}，但权限删除已成功", e);
        } else {
            info!("商户管理员权限同步成功");
        }

        Ok("商户授权权限删除成功".to_string())
    }

    /// 批量删除商户授权权限
    pub async fn batch_delete_merchant_authorized_permissions(
        &self,
        req: SysMerchantAuthorizedPermissionBatchDeleteRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "批量删除商户授权权限，IDs: {:?}，操作人: {}",
            req.authorized_permission_ids, operator_id
        );

        if req.authorized_permission_ids.is_empty() {
            return Err(BusinessError::new(
                INVALID_PARAMETER,
                "请选择要删除的授权权限".to_string(),
            ));
        }

        // 【新增】查询要删除的权限记录，获取涉及的商户ID
        let permissions_to_delete = self
            .merchant_authorized_permission_repos
            .find_by_ids(req.authorized_permission_ids.clone(), &self.db)
            .await.map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        let affected_merchant_ids: Vec<i64> = permissions_to_delete
            .iter()
            .map(|p| p.merchant_id)
            .collect::<std::collections::HashSet<_>>()
            .into_iter()
            .collect();

        // 检查是否有权限已分配给角色
        let assigned_permissions = self.merchant_role_permissions_repos
            .find_by_authorized_permission_ids(req.authorized_permission_ids.clone(), &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查权限分配失败: {}", e)))?
            .len();

        if assigned_permissions > 0 {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "选择的权限中有已分配给角色的权限，不能删除".to_string(),
            ));
        }

        // 开启事务批量删除
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "开启事务失败".to_string()))?;

        // 删除角色权限关联
        self.merchant_role_permissions_repos
            .delete_by_authorized_permission_ids(req.authorized_permission_ids.clone(), &txn)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除角色权限关联失败: {}", e)))?;

        // 批量删除授权权限
        let delete_count = self.merchant_authorized_permission_repos
            .delete_by_ids(req.authorized_permission_ids.clone(), &txn)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("批量删除商户授权权限失败: {}", e)))?;

        txn.commit()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "提交事务失败".to_string()))?;

        info!("批量删除商户授权权限成功，删除数量: {}", delete_count);

        // 【新增】批量同步影响商户的管理员角色权限
        if !affected_merchant_ids.is_empty() {
            if let Err(e) = self
                .merchant_role_service
                .batch_sync_merchant_admin_role_permissions(affected_merchant_ids, operator_id)
                .await
            {
                warn!("批量同步商户管理员权限失败: {}，但权限删除已成功", e);
            } else {
                info!("商户管理员权限批量同步成功");
            }
        }

        Ok(format!("成功删除{}个授权权限", delete_count))
    }

    /// 切换商户授权权限状态
    pub async fn change_merchant_authorized_permission_status(
        &self,
        authorized_permission_id: Uuid,
        req: SysMerchantAuthorizedPermissionStatusRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "切换商户授权权限状态，ID: {}，状态: {}，操作人: {}",
            authorized_permission_id, req.status, operator_id
        );
        let status_enum = MerchantAuthorizedPermissionsStatus::try_from_value(&req.status)
            .map_err(|_| BusinessError::new(400, "无效的状态值"))?;

        let authorized_permission = self
            .merchant_authorized_permission_repos
            .find_by_id(authorized_permission_id, &self.db)
                .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户授权权限失败: {}", e)))?
                .ok_or_else(|| {
                    warn!("商户授权权限不存在: {}", authorized_permission_id);
                    BusinessError::new(RESOURCE_NOT_FOUND, "商户授权权限不存在".to_string())
                })?;

        let merchant_id = authorized_permission.merchant_id;
        let mut permission_active: ActiveModel = authorized_permission.into();

        permission_active.status = Set(status_enum);
        permission_active.updated_by = Set(Some(operator_id));

        self.merchant_authorized_permission_repos
            .update(permission_active, &self.db)
            .await.map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        let status_text = Model::get_status_desc(status_enum);
        info!(
            "商户授权权限状态切换成功，ID: {}，状态: {}",
            authorized_permission_id, status_text
        );

        // 【新增】自动同步商户管理员角色权限
        if let Err(e) = self
            .merchant_role_service
            .sync_merchant_admin_role_permissions(merchant_id, operator_id)
            .await
        {
            warn!("同步商户管理员权限失败: {}，但状态切换已成功", e);
        } else {
            info!("商户管理员权限同步成功");
        }

        Ok(format!("授权权限状态已切换为{}", status_text))
    }

    /// 商户权限批量授权
    pub async fn batch_authorize_merchant_permissions(
        &self,
        req: SysMerchantPermissionBatchAuthorizeRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("商户权限批量授权，请求: {:?}，操作人: {}", req, operator_id);

        let merchant_id = req.merchant_id;

        // 检查权限模板是否存在
        let existing_templates = self
            .check_permission_templates_exist(req.permission_template_ids.clone())
            .await.map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        if existing_templates.len() != req.permission_template_ids.len() {
            return Err(BusinessError::new(
                INVALID_PARAMETER,
                "部分权限模板不存在".to_string(),
            ));
        }

        // 开启事务
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "开启事务失败".to_string()))?;

        // 如果覆盖现有权限，先删除已有权限
        if req.override_existing {
            self.merchant_authorized_permission_repos
                .delete_by_merchant_id(req.merchant_id, &txn)
                .await
                .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除现有权限失败: {}", e)))?;
        } else {
            // 检查是否已授权相同权限
            let existing_permissions = self.merchant_authorized_permission_repos
                .find_by_merchant_and_templates(req.merchant_id, req.permission_template_ids.clone(), &txn)
                .await
                .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查已授权权限失败: {}", e)))?;

            if !existing_permissions.is_empty() {
                return Err(BusinessError::new(
                    OPERATION_CONFLICT,
                    "商户已授权部分权限".to_string(),
                ));
            }
        }

        // 批量创建授权权限
        let models = req.to_active_models(operator_id).map_err(|_| {
            BusinessError::new(INVALID_PARAMETER, "构建授权权限数据失败".to_string())
        })?;

        self.merchant_authorized_permission_repos
            .insert_many(models, &txn)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("批量授权权限失败: {}", e)))?;

        txn.commit()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "提交事务失败".to_string()))?;

        info!("商户权限批量授权成功");

        // 【新增】自动同步商户管理员角色权限
        if let Err(e) = self
            .merchant_role_service
            .sync_merchant_admin_role_permissions(merchant_id, operator_id)
            .await
        {
            warn!("同步商户管理员权限失败: {}，但批量授权已成功", e);
        } else {
            info!("商户管理员权限同步成功");
        }

        Ok("商户权限批量授权成功".to_string())
    }

    /// 撤销商户权限
    pub async fn revoke_merchant_permissions(
        &self,
        req: SysMerchantPermissionRevokeRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("撤销商户权限，请求: {:?}，操作人: {}", req, operator_id);

        // 开启事务
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "开启事务失败".to_string()))?;

        // 查询要撤销的权限
        let permissions_to_revoke = self.merchant_authorized_permission_repos
            .find_by_merchant_and_templates(req.merchant_id, req.permission_template_ids.clone(), &txn)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询要撤销的权限失败: {}", e)))?;

        if permissions_to_revoke.is_empty() {
            return Err(BusinessError::new(
                RESOURCE_NOT_FOUND,
                "未找到要撤销的权限".to_string(),
            ));
        }

        let permission_ids: Vec<Uuid> = permissions_to_revoke.iter().map(|p| p.id).collect();

        // 删除角色权限关联
        self.merchant_role_permissions_repos
            .delete_by_authorized_permission_ids(permission_ids.clone(), &txn)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除角色权限关联失败: {}", e)))?;

        // 删除授权权限
        let revoked_count = self.merchant_authorized_permission_repos
            .delete_by_ids(permission_ids, &txn)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("撤销商户权限失败: {}", e)))?;

        txn.commit()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "提交事务失败".to_string()))?;

        info!("商户权限撤销成功，撤销数量: {}", revoked_count);

        // 【新增】自动同步商户管理员角色权限
        if let Err(e) = self
            .merchant_role_service
            .sync_merchant_admin_role_permissions(req.merchant_id, operator_id)
            .await
        {
            warn!("同步商户管理员权限失败: {}，但权限撤销已成功", e);
        } else {
            info!("商户管理员权限同步成功");
        }

        Ok(format!("成功撤销{}个权限", revoked_count))
    }

    /// 复制商户权限
    pub async fn copy_merchant_permissions(
        &self,
        req: SysMerchantPermissionCopyRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("复制商户权限，请求: {:?}，操作人: {}", req, operator_id);

        // 获取源商户的权限模板ID列表
        let source_permissions = self
            .merchant_authorized_permission_repos
            .find_effective_by_merchant_id(req.source_merchant_id, &self.db)
            .await.map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        if source_permissions.is_empty() {
            return Err(BusinessError::new(
                RESOURCE_NOT_FOUND,
                "源商户没有可复制的权限".to_string(),
            ));
        }

        let permission_template_ids: Vec<Uuid> = source_permissions
            .iter()
            .map(|p| p.permission_template_id)
            .collect();

        // 开启事务
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "开启事务失败".to_string()))?;

        let mut total_copied = 0;

        for target_merchant_id in req.target_merchant_ids.iter() {
            // 如果覆盖现有权限，先删除已有权限
            if req.override_existing {
                self.merchant_authorized_permission_repos
                    .delete_by_merchant_id(*target_merchant_id, &txn)
                    .await
                    .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除目标商户现有权限失败: {}", e)))?;
            } else {
                // 检查是否已授权相同权限
                let existing_permissions = self.merchant_authorized_permission_repos
                    .find_by_merchant_and_templates(*target_merchant_id, permission_template_ids.clone(), &txn)
                    .await
                    .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查目标商户已授权权限失败: {}", e)))?;

                if !existing_permissions.is_empty() {
                    warn!("目标商户{}已有部分权限，跳过", target_merchant_id);
                    continue;
                }
            }

            // 为目标商户创建权限
            let models = req
                .to_active_models_for_target(
                    *target_merchant_id,
                    permission_template_ids.clone(),
                    operator_id,
                )
                .map_err(|_| {
                    BusinessError::new(INVALID_PARAMETER, "构建目标商户权限数据失败".to_string())
                })?;

            self.merchant_authorized_permission_repos
                .insert_many(models, &txn)
                .await
                .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("为目标商户创建权限失败: {}", e)))?;

            total_copied += permission_template_ids.len();
        }

        txn.commit()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "提交事务失败".to_string()))?;

        info!("商户权限复制成功，总计复制: {} 个权限", total_copied);

        // 【新增】批量同步目标商户的管理员角色权限
        if !req.target_merchant_ids.is_empty() {
            if let Err(e) = self
                .merchant_role_service
                .batch_sync_merchant_admin_role_permissions(
                    req.target_merchant_ids.clone(),
                    operator_id,
                )
                .await
            {
                warn!("批量同步目标商户管理员权限失败: {}，但权限复制已成功", e);
            } else {
                info!("目标商户管理员权限批量同步成功");
            }
        }

        Ok(format!("商户权限复制成功，总计复制{}个权限", total_copied))
    }

    /// 获取商户授权权限选择项
    pub async fn get_merchant_authorized_permission_select_items(
        &self,
        merchant_id: i64,
    ) -> Result<Vec<MerchantAuthorizedPermissionSelectItem>, BusinessError> {
        info!("获取商户授权权限选择项，商户ID: {}", merchant_id);

        let authorized_permissions = self.merchant_authorized_permission_repos
            .find_with_templates_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户授权权限列表失败: {}", e)))?;

        let mut select_items = Vec::new();
        for (authorized_permission, permission_templates) in authorized_permissions {
            let mut item = MerchantAuthorizedPermissionSelectItem::from(authorized_permission);

            // 设置权限模板信息
            if let Some(template) = permission_templates.first() {
                item = item.with_permission_info(
                    Some(template.permission_name.clone()),
                    Some(template.permission_code.clone()),
                );
            }

            select_items.push(item);
        }

        Ok(select_items)
    }

    /// 获取商户权限统计信息
    pub async fn get_merchant_permission_stats(
        &self,
        merchant_id: i64,
    ) -> Result<MerchantPermissionStatsResponse, BusinessError> {
        info!("获取商户权限统计信息，商户ID: {}", merchant_id);

        // 查询系统总权限数量
        let total_permissions = self.system_permission_templates_repos
            .count_all(&self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询系统总权限数失败: {}", e)))?
            as i32;

        // 查询已授权权限数量
        let authorized_permissions = self
            .merchant_authorized_permission_repos
            .count_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("统计授权权限数量失败: {}", e)))? as i32;

        // 查询正常权限数量
        let active_permissions = self
            .merchant_authorized_permission_repos
            .count_active_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("统计有效权限数量失败: {}", e)))? as i32;

        let suspended_permissions = authorized_permissions - active_permissions;

        let stats = MerchantPermissionStatsResponse {
            merchant_id,
            merchant_name: "".to_string(), // TODO: 需要从商户表查询
            total_permissions,
            authorized_permissions,
            active_permissions,
            suspended_permissions,
        };

        Ok(stats)
    }

    /// 获取系统权限模板选择列表
    pub async fn get_permission_template_select_for_authorize(
        &self,
    ) -> Result<Vec<SystemPermissionTemplateSelectItem>, BusinessError> {
        info!("获取系统权限模板选择列表");

        let templates = self.system_permission_templates_repos
            .find_visible(&self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询系统权限模板失败: {}", e)))?;

        let select_items = templates
            .into_iter()
            .map(|template| SystemPermissionTemplateSelectItem {
                id: template.id,
                permission_name: template.permission_name,
                permission_code: template.permission_code,
                permission_type: template.permission_type.unwrap_or(2),
                parent_id: template.parent_id,
                visible: template.visible.unwrap_or(0),
            })
            .collect();

        Ok(select_items)
    }

    /// 获取系统权限模板树形选择列表
    pub async fn get_permission_template_tree_for_authorize(
        &self,
    ) -> Result<Vec<SystemPermissionTemplateTreeNode>, BusinessError> {
        info!("获取系统权限模板树形选择列表");

        let templates = self.system_permission_templates_repos
            .find_visible(&self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询系统权限模板失败: {}", e)))?;

        let tree = SystemPermissionTemplateTreeNode::build_permission_tree(templates);
        Ok(tree)
    }

    /// 检查商户是否已授权指定权限
    pub async fn check_merchant_permission_authorized(
        &self,
        merchant_id: i64,
        permission_template_id: Uuid,
    ) -> Result<bool, BusinessError> {
        let count = self.merchant_authorized_permission_repos
            .count_by_merchant_and_template(merchant_id, permission_template_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查商户权限授权状态失败: {}", e)))?;

        Ok(count > 0)
    }

    /// 检查权限是否可以删除
    pub async fn check_permission_deletable(
        &self,
        authorized_permission_id: Uuid,
    ) -> Result<bool, BusinessError> {
        // 检查是否有角色关联到这个权限
        let role_count = self.merchant_role_permissions_repos
            .count_by_authorized_permission_id(authorized_permission_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查权限关联角色失败: {}", e)))?;

        Ok(role_count == 0)
    }

    /// 获取商户所有有效权限模板ID列表
    pub async fn get_merchant_effective_permission_template_ids(
        &self,
        merchant_id: i64,
    ) -> Result<Vec<Uuid>, BusinessError> {
        let permissions = self.merchant_authorized_permission_repos
            .find_effective_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户有效权限失败: {}", e)))?;

        let template_ids = permissions
            .into_iter()
            .map(|p| p.permission_template_id)
            .collect();

        Ok(template_ids)
    }

    /// 批量检查权限模板是否存在
    pub async fn check_permission_templates_exist(
        &self,
        permission_template_ids: Vec<Uuid>,
    ) -> Result<Vec<Uuid>, BusinessError> {
        let existing_templates = self
            .merchant_authorized_permission_repos
            .find_templates_by_ids(permission_template_ids, &self.db)
            .await.map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        let existing_ids = existing_templates.into_iter().map(|t| t.id).collect();

        Ok(existing_ids)
    }

    /// 根据商户ID查询授权权限树形结构
    pub async fn get_merchant_authorized_permission_tree(
        &self,
        merchant_id: i64,
    ) -> Result<MerchantAuthorizedPermissionTreeResponse, BusinessError> {
        info!("查询商户授权权限树形结构，商户ID: {}", merchant_id);

        // 查询所有系统权限模板
        let all_templates = self.system_permission_templates_repos
            .find_visible(&self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询系统权限模板失败: {}", e)))?;

        // 查询商户已授权的权限
        let merchant_permissions = &self.merchant_authorized_permission_repos
            .find_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户授权权限失败: {}", e)))?;

        // 构建授权权限映射 (permission_template_id -> authorized_permission)
        let mut permission_map = std::collections::HashMap::new();
        for permission in merchant_permissions {
            permission_map.insert(permission.permission_template_id, permission);
        }

        // 构建树形节点
        let mut node_map = std::collections::HashMap::new();
        for template in all_templates {
            let authorized_permission = permission_map.get(&template.id);

            let node = MerchantAuthorizedPermissionTreeNode {
                id: template.id,
                authorized_permission_id: authorized_permission.map(|p| p.id),
                permission_name: template.permission_name,
                permission_code: template.permission_code,
                permission_type: template.permission_type.unwrap_or(2),
                permission_type_desc: Model::get_permission_type_desc(template.permission_type),
                parent_id: template.parent_id,
                is_authorized: authorized_permission.is_some(),
                status: authorized_permission.map(|p| p.status as i32),
                status_desc: authorized_permission.map(|p| Model::get_status_desc(p.status)),
                authorized_date: authorized_permission.map(|p| {
                    // 将 DateTimeWithTimeZone 转换为 DateTime<Local> 再格式化
                    let local_dt = chrono::DateTime::<chrono::Local>::from(
                        p.authorized_date.with_timezone(&chrono::Local),
                    );
                    DateTimeUtils::format_datetime(&local_dt)
                }),
                order_num: template.order_num.unwrap_or(0),
                children: Vec::new(),
                icon: template.icon.clone(),
            };
            node_map.insert(template.id, node);
        }

        // 构建树形结构
        let mut tree = Vec::new();
        let mut all_nodes: Vec<_> = node_map.into_iter().collect();
        all_nodes.sort_by(|a, b| a.1.order_num.cmp(&b.1.order_num));

        // 分离根节点和子节点
        let mut children_map: std::collections::HashMap<
            Uuid,
            Vec<MerchantAuthorizedPermissionTreeNode>,
        > = std::collections::HashMap::new();

        for (_, node) in all_nodes {
            if let Some(parent_id) = node.parent_id {
                children_map
                    .entry(parent_id)
                    .or_insert_with(Vec::new)
                    .push(node);
            } else {
                tree.push(node);
            }
        }

        // 递归设置子节点
        fn set_children(
            node: &mut MerchantAuthorizedPermissionTreeNode,
            children_map: &mut std::collections::HashMap<
                Uuid,
                Vec<MerchantAuthorizedPermissionTreeNode>,
            >,
        ) {
            if let Some(mut children) = children_map.remove(&node.id) {
                children.sort_by(|a, b| a.order_num.cmp(&b.order_num));
                for mut child in children {
                    set_children(&mut child, children_map);
                    node.children.push(child);
                }
            }
        }

        for mut root in &mut tree {
            set_children(&mut root, &mut children_map);
        }

        // TODO: 查询商户名称
        let merchant_name = format!("商户{}", merchant_id); // 临时使用

        let response = MerchantAuthorizedPermissionTreeResponse {
            merchant_id,
            merchant_name,
            tree,
        };

        Ok(response)
    }

    /// 根据商户ID查询授权权限ids集合
    pub async fn get_authorized_permission_ids_by_merchant_id(
        &self,
        merchant_id: i64,
    ) -> Result<Vec<Uuid>, BusinessError> {
        // 查询商户已授权的权限
        let authorized_permissions = self.merchant_authorized_permission_repos
            .find_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;
        
        let authorized_permissions_ids = authorized_permissions
            .iter()
            .map(|permission| permission.id)
            .collect();
        Ok(authorized_permissions_ids)
    }
}

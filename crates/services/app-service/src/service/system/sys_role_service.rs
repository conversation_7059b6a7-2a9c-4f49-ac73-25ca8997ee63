use crate::domain::system::dto::sys_role_request::{
    SysRoleBatchDeleteRequest, SysRoleCreateRequest, SysRolePageRequest, SysRolePermissionRequest,
    SysRoleUpdateRequest,
};
use crate::domain::system::entities::roles::ActiveModel;
use crate::domain::system::vo::{
    SysRoleDetailResponse, SysRoleListResponse, SysRolePermissionVo,
    SysRoleSimpleAndUserCountResponse, SysRoleUserVo,
};
use crate::repository::{SysRolePermissionRepository, SysRoleRepository, SysUserRoleRepository};
use chrono::Local;
use lib_core::BusinessError;
use lib_core::app::plugin::service::Service as ServiceTrait;
use lib_core::response::PageData;
use lib_macros::Service;
use sea_orm::{DatabaseConnection, DatabaseTransaction, Set, TransactionTrait};
use tracing::{error, info, warn};
use uuid::Uuid;

/// 系统角色管理服务实现
#[derive(Service, Clone)]
pub struct SysRoleService {
    #[inject(component)]
    db: DatabaseConnection,
    #[inject(component)]
    role_repository: SysRoleRepository,
    #[inject(component)]
    user_role_repository: SysUserRoleRepository,
    #[inject(component)]
    role_permission_repository: SysRolePermissionRepository,
}
pub const SYS_ROLE_ROLE_CODE: &str = "ADMIN";
/// 实现SysRoleService
impl SysRoleService {
    pub async fn page_roles(
        &self,
        req: SysRolePageRequest,
    ) -> Result<PageData<SysRoleListResponse>, BusinessError> {
        info!("分页查询角色列表: {:?}", req);

        // 使用Repository进行分页查询
        let page_data = self
            .role_repository
            .page_by_condition(&req, &self.db)
            .await
            .map_err(|e| {
                error!("分页查询角色失败: {}", e);
                BusinessError::new(500, "分页查询角色失败".to_string())
            })?;

        // 转换为VO
        let items: Vec<SysRoleListResponse> = page_data
            .items
            .into_iter()
            .map(|role| role.into())
            .collect();

        let mut page_data =
            PageData::new(items, page_data.total, page_data.page, page_data.page_size);

        // 批量获取角色统计信息
        let role_ids: Vec<Uuid> = page_data.items.iter().map(|role| role.id).collect();

        if role_ids.is_empty() {
            return Ok(page_data);
        }

        // 批量查询用户数量
        let user_counts = self
            .user_role_repository
            .batch_count_users_by_role_ids(role_ids.clone(), &self.db)
            .await
            .map_err(|e| {
                error!("批量查询角色用户数失败: {}", e);
                BusinessError::new(500, "查询角色用户数失败".to_string())
            })?;

        // 批量查询权限数量
        let permission_counts = self
            .role_repository
            .batch_count_permissions_by_role_ids(role_ids, &self.db)
            .await
            .map_err(|e| {
                error!("批量查询角色权限数失败: {}", e);
                BusinessError::new(500, "查询角色权限数失败".to_string())
            })?;

        // 构建最终结果
        let enhanced_roles: Vec<SysRoleListResponse> = page_data
            .items
            .into_iter()
            .map(|role| {
                let user_count = user_counts.get(&role.id).copied().unwrap_or(0);
                let permission_count = permission_counts.get(&role.id).copied().unwrap_or(0);
                role.with_counts(user_count, permission_count)
            })
            .collect();

        page_data.items = enhanced_roles;
        Ok(page_data)
    }

    pub async fn get_role_by_id(
        &self,
        role_id: Uuid,
    ) -> Result<SysRoleDetailResponse, BusinessError> {
        info!("查询角色详情: {}", role_id);

        let result = self
            .role_repository
            .find_role_with_users_and_permissions(role_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询角色详情失败: {}", e);
                BusinessError::new(500, "查询角色详情失败".to_string())
            })?;

        let (role, users, permissions) = match result {
            Some(data) => data,
            None => {
                warn!("角色不存在: {}", role_id);
                return Err(BusinessError::new(404, "角色不存在".to_string()));
            }
        };

        let permissions_vo: Vec<SysRolePermissionVo> = permissions
            .into_iter()
            .map(|perm| SysRolePermissionVo {
                id: perm.id,
                menu_name: perm.menu_name,
                perms: perm.perms,
                menu_type: perm.menu_type,
                parent_id: perm.parent_id,
            })
            .collect();

        let users_vo: Vec<SysRoleUserVo> = users
            .into_iter()
            .map(|user| SysRoleUserVo {
                id: user.id,
                username: user.username,
                real_name: user.real_name,
                email: user.email,
                status: user.status,
            })
            .collect();

        let role_detail = SysRoleDetailResponse {
            id: role.id,
            name: role.name,
            description: role.description,
            created_date: role.created_date.format("%Y-%m-%d %H:%M:%S").to_string(),
            updated_date: role.updated_date.format("%Y-%m-%d %H:%M:%S").to_string(),
            created_by: role.created_by,
            updated_by: role.updated_by,
            remark: role.remark,
            permissions: permissions_vo,
            users: users_vo,
        };

        Ok(role_detail)
    }

    pub async fn create_role(
        &self,
        req: SysRoleCreateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("创建角色: {}", req.name);

        let exists = self
            .role_repository
            .name_exists(&req.name, None, &self.db)
            .await
            .map_err(|e| {
                error!("检查角色名是否存在失败: {}", e);
                BusinessError::new(500, "检查角色名失败".to_string())
            })?;
        if exists {
            return Err(BusinessError::new(400, "角色名已存在".to_string()));
        }

        let txn = self.db.begin().await.map_err(|e| {
            error!("开启事务失败: {}", e);
            BusinessError::new(500, "创建角色失败".to_string())
        })?;

        let role_id = Uuid::now_v7();
        let now = Local::now();

        let role_active_model = ActiveModel {
            id: Set(role_id),
            name: Set(req.name.clone()),
            description: Set(req.description),
            created_date: Set(now.into()),
            updated_date: Set(now.into()),
            created_by: Set(Some(operator_id)),
            updated_by: Set(Some(operator_id)),
            remark: Set(req.remark),
            ..Default::default()
        };

        self.role_repository
            .create(role_active_model, &txn)
            .await
            .map_err(|e| {
                error!("插入角色记录失败: {}", e);
                BusinessError::new(500, "创建角色失败".to_string())
            })?;

        if let Some(permission_ids) = req.permission_ids {
            self.assign_role_permissions(&txn, role_id, &permission_ids, operator_id)
                .await
                .map_err(|e| {
                    error!("分配角色权限失败: {}", e);
                    BusinessError::new(500, "创建角色失败".to_string())
                })?;
        }

        txn.commit().await.map_err(|e| {
            error!("提交事务失败: {}", e);
            BusinessError::new(500, "创建角色失败".to_string())
        })?;

        info!("角色 {} 创建成功，ID: {}", req.name, role_id);
        Ok("角色创建成功".to_string())
    }

    pub async fn update_role(
        &self,
        role_id: Uuid,
        req: SysRoleUpdateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("更新角色: {}", role_id);

        let role = self
            .role_repository
            .find_by_id(role_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询角色失败: {}", e);
                BusinessError::new(500, "查询角色失败".to_string())
            })?
            .ok_or_else(|| BusinessError::new(404, "角色不存在".to_string()))?;

        let exists = self
            .role_repository
            .name_exists(&req.name, Some(role_id), &self.db)
            .await
            .map_err(|e| {
                error!("检查角色名是否存在失败: {}", e);
                BusinessError::new(500, "检查角色名失败".to_string())
            })?;
        if exists {
            return Err(BusinessError::new(400, "角色名已存在".to_string()));
        }

        let mut role_active_model: ActiveModel = role.into();
        role_active_model.name = Set(req.name);
        role_active_model.description = Set(req.description);
        role_active_model.remark = Set(req.remark);
        role_active_model.updated_date = Set(Local::now().into());
        role_active_model.updated_by = Set(Some(operator_id));

        self.role_repository
            .update(role_active_model, &self.db)
            .await
            .map_err(|e| {
                error!("更新角色失败: {}", e);
                BusinessError::new(500, "更新角色失败".to_string())
            })?;

        info!("角色 {} 更新成功", role_id);
        Ok("角色更新成功".to_string())
    }

    pub async fn delete_role(&self, role_id: Uuid) -> Result<String, BusinessError> {
        info!("删除角色: {}", role_id);

        let role = self
            .role_repository
            .find_by_id(role_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询角色失败: {}", e);
                BusinessError::new(500, "查询角色失败".to_string())
            })?
            .ok_or_else(|| BusinessError::new(404, "角色不存在".to_string()))?;

        let user_count = self
            .user_role_repository
            .count_by_role_id(role_id, &self.db)
            .await
            .map_err(|e| {
                error!("检查角色使用情况失败: {}", e);
                BusinessError::new(500, "检查角色使用情况失败".to_string())
            })?;

        if user_count > 0 {
            return Err(BusinessError::new(
                400,
                format!("角色正在被 {} 个用户使用，无法删除", user_count),
            ));
        }

        let txn = self.db.begin().await.map_err(|e| {
            error!("开启事务失败: {}", e);
            BusinessError::new(500, "删除角色失败".to_string())
        })?;

        self.clear_role_permissions(&txn, role_id)
            .await
            .map_err(|e| {
                error!("清除角色权限失败: {}", e);
                BusinessError::new(500, "删除角色失败".to_string())
            })?;

        self.role_repository
            .delete_by_id(role_id, &txn)
            .await
            .map_err(|e| {
                error!("删除角色记录失败: {}", e);
                BusinessError::new(500, "删除角色失败".to_string())
            })?;

        txn.commit().await.map_err(|e| {
            error!("提交事务失败: {}", e);
            BusinessError::new(500, "删除角色失败".to_string())
        })?;

        info!("角色 {} ({}) 删除成功", role.name, role_id);
        Ok("角色删除成功".to_string())
    }

    pub async fn batch_delete_roles(
        &self,
        req: SysRoleBatchDeleteRequest,
    ) -> Result<String, BusinessError> {
        info!("批量删除角色: {:?}", req.role_ids);

        for role_id in &req.role_ids {
            let user_count = self
                .user_role_repository
                .count_by_role_id(*role_id, &self.db)
                .await
                .map_err(|e| {
                    error!("检查角色使用情况失败: {}", e);
                    BusinessError::new(500, "检查角色使用情况失败".to_string())
                })?;

            if user_count > 0 {
                return Err(BusinessError::new(
                    400,
                    format!(
                        "角色 {} 正在被 {} 个用户使用，无法批量删除",
                        role_id, user_count
                    ),
                ));
            }
        }

        let txn = self.db.begin().await.map_err(|e| {
            error!("开启事务失败: {}", e);
            BusinessError::new(500, "批量删除角色失败".to_string())
        })?;

        let mut success_count = 0;
        let mut error_count = 0;

        for role_id in &req.role_ids {
            if let Err(err) = self.clear_role_permissions(&txn, *role_id).await {
                error!("清除角色 {} 权限失败: {}", role_id, err);
                error_count += 1;
                continue;
            }

            match self.role_repository.delete_by_id(*role_id, &txn).await {
                Ok(_) => {
                    success_count += 1;
                    info!("角色 {} 删除成功", role_id);
                }
                Err(err) => {
                    error!("删除角色 {} 失败: {}", role_id, err);
                    error_count += 1;
                }
            }
        }

        if error_count > 0 {
            if let Err(rollback_err) = txn.rollback().await {
                error!("事务回滚失败: {}", rollback_err);
            }
            return Err(BusinessError::new(
                500,
                format!(
                    "批量删除失败，成功: {}，失败: {}",
                    success_count, error_count
                ),
            ));
        }

        txn.commit().await.map_err(|e| {
            error!("提交事务失败: {}", e);
            BusinessError::new(500, "批量删除角色失败".to_string())
        })?;

        info!("批量删除角色成功，删除数量: {}", success_count);
        Ok(format!("批量删除成功，删除 {} 个角色", success_count))
    }

    pub async fn assign_permissions(
        &self,
        role_id: Uuid,
        req: SysRolePermissionRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("为角色 {} 分配权限: {:?}", role_id, req.permission_ids);

        let _role = self
            .role_repository
            .find_by_id(role_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询角色失败: {}", e);
                BusinessError::new(500, "查询角色失败".to_string())
            })?
            .ok_or_else(|| BusinessError::new(404, "角色不存在".to_string()))?;

        let txn = self.db.begin().await.map_err(|e| {
            error!("开启事务失败: {}", e);
            BusinessError::new(500, "分配权限失败".to_string())
        })?;

        self.clear_role_permissions(&txn, role_id)
            .await
            .map_err(|e| {
                error!("清除角色权限失败: {}", e);
                BusinessError::new(500, "分配权限失败".to_string())
            })?;

        self.assign_role_permissions(&txn, role_id, &req.permission_ids, operator_id)
            .await
            .map_err(|e| {
                error!("分配角色权限失败: {}", e);
                BusinessError::new(500, "分配权限失败".to_string())
            })?;

        txn.commit().await.map_err(|e| {
            error!("提交事务失败: {}", e);
            BusinessError::new(500, "分配权限失败".to_string())
        })?;

        info!("角色 {} 权限分配成功", role_id);
        Ok("权限分配成功".to_string())
    }

    pub async fn check_role_name_exists(
        &self,
        name: &str,
        exclude_role_id: Option<Uuid>,
    ) -> Result<bool, BusinessError> {
        self.role_repository
            .name_exists(name, exclude_role_id, &self.db)
            .await
            .map_err(|e| {
                error!("检查角色名是否存在失败: {}", e);
                BusinessError::new(500, "检查角色名失败".to_string())
            })
    }

    pub async fn get_all_roles_simple(
        &self,
    ) -> Result<Vec<SysRoleSimpleAndUserCountResponse>, BusinessError> {
        info!("获取所有角色简单信息");

        // 先查询所有角色基础信息
        let roles = self.role_repository.find_all(&self.db).await.map_err(|e| {
            error!("查询角色列表失败: {}", e);
            BusinessError::new(500, "查询角色列表失败".to_string())
        })?;

        info!("查询角色列表成功，共 {} 个角色", roles.len());

        // 如果没有角色，直接返回空列表
        if roles.is_empty() {
            return Ok(Vec::new());
        }

        // 提取角色ID列表，批量查询用户数量
        let role_ids: Vec<Uuid> = roles.iter().map(|role| role.id).collect();
        let user_counts = self
            .user_role_repository
            .batch_count_users_by_role_ids(role_ids, &self.db)
            .await
            .map_err(|e| {
                error!("批量查询角色用户数失败: {}", e);
                BusinessError::new(500, "查询角色用户数失败".to_string())
            })?;

        // 构建最终结果
        let role_list: Vec<SysRoleSimpleAndUserCountResponse> = roles
            .into_iter()
            .map(|role| {
                let user_count = user_counts.get(&role.id).copied().unwrap_or(0);
                SysRoleSimpleAndUserCountResponse {
                    id: role.id,
                    name: role.name,
                    user_count: user_count as i32,
                    description: role.description,
                }
            })
            .collect();

        Ok(role_list)
    }
}

impl SysRoleService {
    pub async fn clear_role_permissions(
        &self,
        txn: &DatabaseTransaction,
        role_id: Uuid,
    ) -> Result<(), sea_orm::DbErr> {
        self.role_permission_repository
            .delete_by_role_id(role_id, txn)
            .await
            .map_err(|e| {
                error!("清除角色权限失败: {}", e);
                sea_orm::DbErr::Custom(format!("清除角色权限失败: {}", e))
            })?;
        Ok(())
    }

    pub async fn assign_role_permissions(
        &self,
        txn: &DatabaseTransaction,
        role_id: Uuid,
        permission_ids: &[Uuid],
        operator_id: Uuid,
    ) -> Result<(), sea_orm::DbErr> {
        for permission_id in permission_ids {
            let role_permission = crate::domain::system::entities::role_permissions::ActiveModel {
                id: sea_orm::Set(Uuid::now_v7()),
                role_id: sea_orm::Set(role_id),
                permission_id: sea_orm::Set(*permission_id),
                created_date: sea_orm::Set(Local::now().into()),
                created_by: sea_orm::Set(Some(operator_id)),
                ..Default::default()
            };
            self.role_permission_repository
                .create(role_permission, txn)
                .await
                .map_err(|e| {
                    error!("插入角色权限关联失败: {}", e);
                    sea_orm::DbErr::Custom(format!("插入角色权限关联失败: {}", e))
                })?;
        }
        Ok(())
    }

    pub async fn count_users_by_role_id(&self, role_id: Uuid) -> Result<u64, sea_orm::DbErr> {
        self.user_role_repository
            .count_by_role_id(role_id, &self.db)
            .await
            .map_err(|e| {
                error!("统计角色用户数失败: {}", e);
                sea_orm::DbErr::Custom(format!("统计角色用户数失败: {}", e))
            })
    }

    pub async fn count_permissions_by_role_id(&self, role_id: Uuid) -> Result<u64, sea_orm::DbErr> {
        self.role_permission_repository
            .count_by_role_id(role_id, &self.db)
            .await
            .map_err(|e| {
                error!("统计角色权限数失败: {}", e);
                sea_orm::DbErr::Custom(format!("统计角色权限数失败: {}", e))
            })
    }
}

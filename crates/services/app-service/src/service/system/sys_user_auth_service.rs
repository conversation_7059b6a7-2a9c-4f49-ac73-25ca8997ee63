use crate::constants::*;
use crate::domain::system::dto::sys_user_request::{
    BindWechatRequest, RefreshTokenRequest, SysUserLoginRequest, SysUserWechatLoginRequest,
    UnbindWechatRequest, UpdateProfileRequest,
};
use crate::domain::system::entities::users::ActiveModel;
use crate::domain::system::vo::{RefreshTokenResponse, SysUserLoginResponse, UserProfileResponse};
use crate::repository::{SysPermissionRepository, SysUserRepository, SysUserRoleRepository};
use crate::service::system::sys_permission_service::ALL_PERMISSIONS_CODE;
use crate::service::system::sys_role_service::SYS_ROLE_ROLE_CODE;
use crate::utils;
use chrono::Local;
use lib_auth::config::SecurityConfig;
use lib_auth::middleware::sys_jwt::{AuthToken, RefreshToken};
use lib_auth::middleware::system_auth_middleware::{
    delete_refresh_token, get_refresh_token, store_refresh_token,
};
use lib_core::app::plugin::service::Service as ServiceTrait;
use lib_core::BusinessError;
use lib_macros::Service;
use lib_wechat::WechatService;
use sea_orm::{DatabaseConnection, Set};
use std::time::Instant;
use tracing::{info, warn};
use uuid::Uuid;

/// 系统用户认证服务实现
///
/// 基于数据库和Redis的标准认证实现
#[derive(Service, Clone)]
pub struct SysUserAuthService {
    #[inject(component)]
    db: DatabaseConnection,
    #[inject(config)]
    config: SecurityConfig,
    #[inject(component)]
    user_repository: SysUserRepository,
    #[inject(component)]
    user_role_repository: SysUserRoleRepository,
    #[inject(component)]
    permission_repository: SysPermissionRepository,
    #[inject(component)]
    wechat_service: WechatService,
}

impl SysUserAuthService {
    /// 用户登录
    ///
    /// # 参数
    /// * `req` - 登录请求信息
    /// * `client_ip` - 客户端IP地址
    ///
    /// # 返回
    /// 登录成功返回access_token和refresh_token
    pub async fn login(
        &self,
        req: SysUserLoginRequest,
        client_ip: String,
    ) -> Result<SysUserLoginResponse, BusinessError> {
        info!("用户登录请求: {}，IP: {}", req.username, client_ip);

        // 第一步：根据用户名查找用户（最轻量级查询）
        let user = self
            .user_repository
            .find_by_username(&req.username, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "查找用户失败".to_string()))?;

        let user = match user {
            Some(u) => u,
            None => {
                return Err(BusinessError::new(
                    USER_NOT_FOUND,
                    "账号或密码错误".to_string(),
                ));
            }
        };

        // 第二步：验证密码
        let password_is_valid = utils::verify_password(&req.password, &user.password)
            .map_err(|_e| BusinessError::new(INVALID_CREDENTIALS, "账号或密码错误".to_string()))?;

        if !password_is_valid {
            return Err(BusinessError::new(
                INVALID_CREDENTIALS,
                "账号或密码错误".to_string(),
            ));
        }

        // 第三步：检查用户状态
        if let Err(status_error) = user.can_login() {
            return Err(BusinessError::new(
                ACCOUNT_DISABLED,
                status_error.to_string(),
            ));
        }

        // 第四步：获取权限和角色信息（只有在前面验证都通过后才执行）
        let role_codes = self
            .user_role_repository
            .get_user_role_codes(user.id, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "获取用户角色失败".to_string()))?;

        // 第五步：获取用户权限代码
        let permission_codes = self.get_user_permission_codes(user.id, &role_codes).await?;

        // 第六步：生成access token
        let step6_start = Instant::now();
        let access_token = AuthToken::create_token(
            String::from(user.id.clone()),
            user.username.clone(),
            permission_codes,
            role_codes,
            &self.config,
        )
        .map_err(|_e| BusinessError::new(INTERNAL_ERROR, "生成access token失败".to_string()))?;
        let step6_duration = step6_start.elapsed();
        info!("步骤6-生成access token: 耗时 {:?}", step6_duration);

        // 第七步：解析access token以获取AuthToken实例，用于生成refresh token
        let auth_token = AuthToken::verify(self.config.system.jwt_secret(), &access_token)
            .map_err(|_e| BusinessError::new(INTERNAL_ERROR, "验证access token失败".to_string()))?;

        // 第八步：生成refresh token（只包含最小信息）
        let refresh_token = auth_token
            .create_refresh_token(&self.config)
            .map_err(|_e| {
                BusinessError::new(INTERNAL_ERROR, "生成refresh token失败".to_string())
            })?;

        // 第九步：将refresh token存储到Redis
        store_refresh_token(
            &user.id.to_string(),
            &refresh_token,
            self.config.system.jwt_refresh_token_exp_sec,
        )
        .await
        .map_err(|_e| BusinessError::new(INTERNAL_ERROR, "存储refresh token失败".to_string()))?;

        // 第十步：更新用户的最后登录信息
        let mut user_active_model: ActiveModel = user.clone().into();
        user_active_model.last_login_ip = sea_orm::Set(Some(client_ip));
        user_active_model.last_login_date = sea_orm::Set(Some(Local::now().into()));
        user_active_model.updated_date = sea_orm::Set(Local::now().into());

        if let Err(update_err) = self
            .user_repository
            .update(user_active_model, &self.db)
            .await
        {
            // 登录信息更新失败不影响登录流程，只记录警告
            warn!("更新用户 {} 登录信息失败: {}", user.username, update_err);
        }

        let response = SysUserLoginResponse::builder()
            .access_token(access_token)
            .refresh_token(refresh_token)
            .build();

        Ok(response)
    }

    async fn get_user_permission_codes(
        &self,
        user_id: Uuid,
        role_codes: &Vec<String>,
    ) -> Result<Vec<String>, BusinessError> {
        let permission_codes = if role_codes.contains(&SYS_ROLE_ROLE_CODE.to_string()) {
            info!("用户 id {} 拥有ADMIN角色，直接授予超级权限", user_id);
            vec![ALL_PERMISSIONS_CODE.to_string()]
        } else {
            self.permission_repository
                .get_user_permission_codes(user_id, &self.db)
                .await
                .map_err(|_e| BusinessError::new(DATABASE_ERROR, "获取用户权限失败".to_string()))?
        };
        Ok(permission_codes)
    }

    /// 用户注销
    ///
    /// # 参数
    /// * `current_user` - 当前认证用户
    ///
    /// # 返回
    /// 注销结果
    pub async fn logout(&self, current_user: AuthToken) -> Result<(), BusinessError> {
        let user_id = current_user.id;
        info!("用户注销请求: {}", user_id);

        // 删除Redis中的refresh token
        let deleted = delete_refresh_token(&user_id).await.map_err(|_e| {
            BusinessError::new(INTERNAL_ERROR, "删除refresh token失败".to_string())
        })?;

        if deleted > 0 {
            info!("删除用户 {} 的refresh token成功", user_id);
        } else {
            warn!("用户 {} 的refresh token不存在", user_id);
        }

        info!("用户 {} 注销成功", user_id);
        Ok(())
    }

    /// 刷新访问令牌
    ///
    /// # 参数
    /// * `req` - 刷新令牌请求
    ///
    /// # 返回
    /// 新的token对
    pub async fn refresh_token(
        &self,
        req: RefreshTokenRequest,
    ) -> Result<RefreshTokenResponse, BusinessError> {
        info!("收到token刷新请求");

        // 验证refresh token（使用RefreshToken::verify，只接受refresh类型）
        let refresh_token_data =
            RefreshToken::verify(self.config.system.jwt_secret(), &req.refresh_token).map_err(
                |_e| BusinessError::new(TOKEN_INVALID, "Refresh token验证失败".to_string()),
            )?;

        // 检查Redis中是否存在该用户的refresh token
        let stored_refresh_token =
            get_refresh_token(&refresh_token_data.id)
                .await
                .map_err(|_e| {
                    BusinessError::new(TOKEN_EXPIRED, "Refresh token已失效，请重新登录".to_string())
                })?;

        // 验证refresh token是否匹配
        if stored_refresh_token != req.refresh_token {
            warn!("Refresh token不匹配，用户ID: {}", refresh_token_data.id);
            return Err(BusinessError::new(
                TOKEN_EXPIRED,
                "Refresh token已失效，请重新登录".to_string(),
            ));
        }

        // 查询用户最新的权限和角色信息
        let user_id = Uuid::parse_str(&refresh_token_data.id)
            .map_err(|_e| BusinessError::new(INVALID_PARAMETER, "无效的用户ID".to_string()))?;

        let user = self
            .user_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "查询用户信息失败".to_string()))?;

        let user = match user {
            Some(u) => u,
            None => {
                return Err(BusinessError::new(USER_NOT_FOUND, "用户不存在".to_string()));
            }
        };

        // 检查用户状态
        if let Err(status_error) = user.can_login() {
            return Err(BusinessError::new(
                ACCOUNT_DISABLED,
                status_error.to_string(),
            ));
        }

        // 获取用户最新的角色信息
        let role_codes = self
            .user_role_repository
            .get_user_role_codes(user.id, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "获取用户角色失败".to_string()))?;

        // 获取用户最新的权限信息（如果是ADMIN角色，直接给超级权限）
        let permissions = self.get_user_permission_codes(user.id, &role_codes).await?;

        // 生成新的access token（使用最新的权限和角色）
        let new_access_token = AuthToken::create_token(
            user.id.to_string(),
            user.username.clone(),
            permissions,
            role_codes,
            &self.config,
        )
        .map_err(|_e| BusinessError::new(INTERNAL_ERROR, "生成新access token失败".to_string()))?;

        // 解析新的access token以获取AuthToken实例
        let new_auth_token = AuthToken::verify(self.config.system.jwt_secret(), &new_access_token)
            .map_err(|_e| {
                BusinessError::new(INTERNAL_ERROR, "验证新access token失败".to_string())
            })?;

        // 生成新的refresh token
        let new_refresh_token =
            new_auth_token
                .create_refresh_token(&self.config)
                .map_err(|_e| {
                    BusinessError::new(INTERNAL_ERROR, "生成新refresh token失败".to_string())
                })?;

        // 更新Redis中的refresh token
        store_refresh_token(
            &user.id.to_string(),
            &new_refresh_token,
            self.config.system.jwt_refresh_token_exp_sec,
        )
        .await
        .map_err(|_e| BusinessError::new(INTERNAL_ERROR, "存储新refresh token失败".to_string()))?;

        info!("Token手动刷新成功，用户: {}", user.username);

        let response = RefreshTokenResponse::builder()
            .access_token(new_access_token)
            .refresh_token(new_refresh_token)
            .expires_in(self.config.system.jwt_access_token_exp_sec)
            .build();

        Ok(response)
    }

    /// 修改密码
    ///
    /// # 参数
    /// * `user_id` - 用户ID
    /// * `old_password` - 旧密码
    /// * `new_password` - 新密码
    ///
    /// # 返回
    /// 修改结果
    pub async fn change_password(
        &self,
        user_id_str: String,
        old_password: String,
        new_password: String,
    ) -> Result<(), BusinessError> {
        info!("用户 {} 请求修改密码", user_id_str);

        let user_id = Uuid::parse_str(&user_id_str)
            .map_err(|_e| BusinessError::new(INVALID_PARAMETER, "无效的用户ID".to_string()))?;

        // 查询用户
        let user = self
            .user_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "数据库查询失败".to_string()))?;

        let user = match user {
            Some(u) => u,
            None => {
                return Err(BusinessError::new(USER_NOT_FOUND, "用户不存在".to_string()));
            }
        };

        // 验证旧密码
        let is_valid = utils::verify_password(&old_password, &user.password)
            .map_err(|_e| BusinessError::new(INTERNAL_ERROR, "密码验证失败".to_string()))?;

        if !is_valid {
            warn!("用户 {} 旧密码错误", user.username);
            return Err(BusinessError::new(
                OLD_PASSWORD_INCORRECT,
                "旧密码错误".to_string(),
            ));
        }

        // 检查新旧密码是否相同
        if old_password == new_password {
            warn!("用户 {} 旧密码和新密码相同", user_id_str);
            return Err(BusinessError::new(
                SAME_PASSWORD,
                "旧密码和新密码不能相同".to_string(),
            ));
        }

        // 加密新密码
        let hashed_new_password = utils::hash_password(&new_password)
            .map_err(|_e| BusinessError::new(INTERNAL_ERROR, "密码加密失败".to_string()))?;

        // 更新密码
        let mut user_active_model: ActiveModel = user.into();
        user_active_model.password = sea_orm::Set(hashed_new_password);
        user_active_model.updated_date = sea_orm::Set(Local::now().into());

        self.user_repository
            .update(user_active_model, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "密码更新失败".to_string()))?;

        info!("用户 {} 密码修改成功", user_id_str);
        Ok(())
    }

    /// 获取用户个人信息
    ///
    /// # 参数
    /// * `user_id` - 用户ID
    ///
    /// # 返回
    /// 用户详细信息包括权限和角色
    pub async fn get_user_profile(
        &self,
        user_id_str: String,
    ) -> Result<UserProfileResponse, BusinessError> {
        info!("获取用户个人信息请求: {}", user_id_str);

        let user_id = Uuid::parse_str(&user_id_str)
            .map_err(|_e| BusinessError::new(INVALID_PARAMETER, "无效的用户ID".to_string()))?;

        // 查询用户信息
        let user = self
            .user_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "查询用户信息失败".to_string()))?;

        let user = match user {
            Some(u) => u,
            None => {
                return Err(BusinessError::new(USER_NOT_FOUND, "用户不存在".to_string()));
            }
        };

        // 获取用户权限信息
        let permissions = self
            .permission_repository
            .get_user_permission_codes(user.id, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "获取用户权限失败".to_string()))?;

        // 获取用户角色信息
        let roles = self
            .user_role_repository
            .get_user_roles(user.id, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "获取用户角色失败".to_string()))?;

        info!("用户 {} 个人信息获取成功", user.username);

        let profile =
            UserProfileResponse::from_user_with_permissions_and_roles(user, permissions, roles);

        Ok(profile)
    }

    /// 修改个人信息
    ///
    /// # 参数
    /// * `user_id` - 用户ID
    /// * `req` - 修改个人信息请求
    ///
    /// # 返回
    /// 修改结果
    pub async fn update_profile(
        &self,
        user_id: String,
        req: UpdateProfileRequest,
    ) -> Result<(), BusinessError> {
        info!("用户 {} 请求更新个人信息", user_id);

        let user_uuid = Uuid::parse_str(&user_id)
            .map_err(|_e| BusinessError::new(INVALID_PARAMETER, "无效的用户ID".to_string()))?;

        // 查询用户
        let user = self
            .user_repository
            .find_by_id(user_uuid, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "数据库查询失败".to_string()))?;

        let user = match user {
            Some(u) => u,
            None => {
                return Err(BusinessError::new(USER_NOT_FOUND, "用户不存在".to_string()));
            }
        };

        // 更新用户信息
        let mut user_active_model: ActiveModel = user.into();
        user_active_model.real_name = Set(req.real_name);
        user_active_model.phone = Set(req.phone);
        user_active_model.email = Set(req.email);
        user_active_model.avatar = Set(req.avatar);
        user_active_model.gender = Set(req.gender);
        user_active_model.updated_date = Set(Local::now().into());

        self.user_repository
            .update(user_active_model, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "用户信息更新失败".to_string()))?;

        info!("用户 {} 个人信息更新成功", user_id);
        Ok(())
    }

    /// 系统用户微信登录
    ///
    /// # 参数
    /// * `req` - 微信登录请求信息
    /// * `client_ip` - 客户端IP地址
    ///
    /// # 返回
    /// 登录成功返回access_token和refresh_token
    pub async fn wechat_login(
        &self,
        req: SysUserWechatLoginRequest,
        client_ip: String,
    ) -> Result<SysUserLoginResponse, BusinessError> {
        info!("系统用户微信登录请求，IP: {}", client_ip);

        // 第一步：调用微信API获取用户openid
        let wechat_user_info = self.wechat_service.login(req.code).await.map_err(|e| {
            warn!("获取微信用户信息失败: {}", e);
            BusinessError::new(WECHAT_API_ERROR, "微信授权失败")
        })?;

        let openid = &wechat_user_info.openid;
        info!("微信登录获取到OpenID: {}", openid);

        // 第二步：根据微信OpenID查询系统用户
        let user = self
            .user_repository
            .find_by_wechat_openid(openid, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "查询用户失败".to_string()))?;

        let user = match user {
            Some(u) => u,
            None => {
                warn!("微信OpenID {} 未绑定系统用户", openid);
                return Err(BusinessError::new(
                    USER_NOT_FOUND,
                    "该微信账号未绑定系统用户，请先使用账号密码登录并绑定微信".to_string(),
                ));
            }
        };

        info!("找到绑定用户: {}", user.username);

        // 第三步：检查用户状态
        if let Err(status_error) = user.can_login() {
            return Err(BusinessError::new(
                ACCOUNT_DISABLED,
                status_error.to_string(),
            ));
        }

        // 第四步：获取用户权限和角色信息
        let role_codes = self
            .user_role_repository
            .get_user_role_codes(user.id, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "获取用户角色失败".to_string()))?;

        // 第五步：获取用户权限代码
        let permission_codes = self.get_user_permission_codes(user.id, &role_codes).await?;

        // 第六步：生成access token
        let access_token = AuthToken::create_token(
            user.id.to_string(),
            user.username.clone(),
            permission_codes,
            role_codes,
            &self.config,
        )
        .map_err(|_e| BusinessError::new(INTERNAL_ERROR, "生成access token失败".to_string()))?;

        // 第七步：解析access token以获取AuthToken实例，用于生成refresh token
        let auth_token = AuthToken::verify(self.config.system.jwt_secret(), &access_token)
            .map_err(|_e| BusinessError::new(INTERNAL_ERROR, "验证access token失败".to_string()))?;

        // 第八步：生成refresh token
        let refresh_token = auth_token
            .create_refresh_token(&self.config)
            .map_err(|_e| {
                BusinessError::new(INTERNAL_ERROR, "生成refresh token失败".to_string())
            })?;

        // 第九步：将refresh token存储到Redis
        store_refresh_token(
            &user.id.to_string(),
            &refresh_token,
            self.config.system.jwt_refresh_token_exp_sec,
        )
        .await
        .map_err(|_e| BusinessError::new(INTERNAL_ERROR, "存储refresh token失败".to_string()))?;

        // 第十步：更新用户的最后登录信息
        let mut user_active_model: ActiveModel = user.clone().into();
        user_active_model.last_login_ip = Set(Some(client_ip));
        user_active_model.last_login_date = Set(Some(Local::now().into()));
        user_active_model.updated_date = Set(Local::now().into());

        if let Err(update_err) = self
            .user_repository
            .update(user_active_model, &self.db)
            .await
        {
            // 登录信息更新失败不影响登录流程，只记录警告
            warn!("更新用户 {} 登录信息失败: {}", user.username, update_err);
        }

        let response = SysUserLoginResponse::builder()
            .access_token(access_token)
            .refresh_token(refresh_token)
            .build();

        info!("系统用户 {} 微信登录成功", user.username);
        Ok(response)
    }

    /// 绑定微信
    ///
    /// # 参数
    /// * `req` - 绑定微信请求（包含授权码、昵称和头像）
    /// * `user_id` - 当前用户ID
    ///
    /// # 返回
    /// 绑定结果
    pub async fn bind_wechat(
        &self,
        req: BindWechatRequest,
        user_id: Uuid,
    ) -> Result<(), BusinessError> {
        info!("系统用户 {} 请求绑定微信", user_id);

        // 第一步：调用微信API获取用户OpenID
        let wechat_user_info = self.wechat_service.login(req.code).await.map_err(|e| {
            warn!("获取微信用户信息失败: {}", e);
            BusinessError::new(WECHAT_API_ERROR, "微信授权失败，请重试")
        })?;

        let openid = &wechat_user_info.openid;
        info!("绑定微信获取到OpenID: {}", openid);

        // 第二步：检查该OpenID是否已被其他系统用户绑定
        if let Some(existing_user) = self
            .user_repository
            .find_by_wechat_openid(openid, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "查询用户失败".to_string()))?
        {
            if existing_user.id != user_id {
                warn!("微信OpenID {} 已被其他系统用户绑定: {}", openid, existing_user.username);
                return Err(BusinessError::new(
                    WECHAT_ALREADY_BOUND,
                    "该微信账号已绑定其他用户".to_string(),
                ));
            }
        }

        // 第三步：查询当前用户信息
        let user = self
            .user_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "查询用户失败".to_string()))?;

        let user = match user {
            Some(u) => u,
            None => {
                return Err(BusinessError::new(USER_NOT_FOUND, "用户不存在".to_string()));
            }
        };

        // 第四步：检查当前用户是否已绑定微信
        if user.wechat_openid.is_some() {
            warn!("用户 {} 已绑定微信", user.username);
            return Err(BusinessError::new(
                WECHAT_ALREADY_BOUND,
                "您已绑定微信账号，请先解绑后再重新绑定".to_string(),
            ));
        }

        // 第五步：更新用户的微信信息（OpenID、昵称、头像）
        let mut user_active_model: ActiveModel = user.into();
        user_active_model.wechat_openid = Set(Some(openid.clone()));
        user_active_model.wechat_nickname = Set(req.nickname);
        user_active_model.wechat_avatar = Set(req.avatar);
        user_active_model.updated_date = Set(Local::now().into());

        self.user_repository
            .update(user_active_model, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "绑定微信失败".to_string()))?;

        info!("系统用户 {} 微信绑定成功，OpenID: {}", user_id, openid);
        Ok(())
    }

    /// 解绑微信
    ///
    /// # 参数
    /// * `req` - 解绑微信请求
    /// * `user_id` - 当前用户ID
    ///
    /// # 返回
    /// 解绑结果
    pub async fn unbind_wechat(
        &self,
        req: UnbindWechatRequest,
        user_id: Uuid,
    ) -> Result<(), BusinessError> {
        info!("系统用户 {} 请求解绑微信", user_id);

        // 第一步：查询当前用户信息
        let user = self
            .user_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "查询用户失败".to_string()))?;

        let user = match user {
            Some(u) => u,
            None => {
                return Err(BusinessError::new(USER_NOT_FOUND, "用户不存在".to_string()));
            }
        };

        // 第二步：检查用户是否已绑定微信
        if user.wechat_openid.is_none() {
            warn!("用户 {} 未绑定微信", user.username);
            return Err(BusinessError::new(
                INVALID_STATE,
                "您还未绑定微信账号".to_string(),
            ));
        }

        // 第三步：验证密码是否正确
        let is_valid = utils::verify_password(&req.password, &user.password)
            .map_err(|_e| BusinessError::new(INTERNAL_ERROR, "密码验证失败".to_string()))?;

        if !is_valid {
            warn!("用户 {} 解绑微信时密码错误", user.username);
            return Err(BusinessError::new(
                OLD_PASSWORD_INCORRECT,
                "密码错误，解绑失败".to_string(),
            ));
        }

        // 第四步：清空用户的微信信息（OpenID、昵称、头像）
        let mut user_active_model: ActiveModel = user.clone().into();
        user_active_model.wechat_openid = Set(None);
        user_active_model.wechat_nickname = Set(None);
        user_active_model.wechat_avatar = Set(None);
        user_active_model.updated_date = Set(Local::now().into());

        self.user_repository
            .update(user_active_model, &self.db)
            .await
            .map_err(|_e| BusinessError::new(DATABASE_ERROR, "解绑微信失败".to_string()))?;

        info!("系统用户 {} 微信解绑成功", user.username);
        Ok(())
    }
}

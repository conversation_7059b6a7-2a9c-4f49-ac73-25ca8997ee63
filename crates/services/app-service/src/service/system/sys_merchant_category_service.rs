use crate::constants::*;
use crate::domain::business::merchants::entities::merchant_categories::{
    ActiveModel, MerchantCategoryStatus,
};
use crate::domain::system::dto::sys_merchant_category_request::{
    MerchantCategoryStatusRequest, SysMerchantCategoryBatchDeleteRequest,
    SysMerchantCategoryCreateRequest, SysMerchantCategoryPageRequest,
    SysMerchantCategoryUpdateRequest,
};
use crate::domain::system::vo::merchant_category_vo::{
    SysMerchantCategoryDetailResponse, SysMerchantCategoryListResponse,
    SysMerchantCategorySelectItem, SysMerchantCategoryStatsResponse,
};
use crate::repository::MerchantCategoryRepository;
use chrono::Local;
use lib_core::app::plugin::service::Service as ServiceTrait;
use lib_core::response::PageData;
use lib_core::BusinessError;
use lib_macros::Service;
use sea_orm::{ActiveEnum, DatabaseConnection, Set};
use tracing::info;
use uuid::Uuid;

/// 商户分类管理服务实现
#[derive(Clone, Service)]
pub struct SysMerchantCategoryService {
    #[inject(component)]
    db: DatabaseConnection,
    #[inject(component)]
    merchant_category_repository: MerchantCategoryRepository,
}

impl SysMerchantCategoryService {
    /// 分页查询商户分类列表
    pub async fn page_categories(
        &self,
        req: SysMerchantCategoryPageRequest,
    ) -> Result<PageData<SysMerchantCategoryListResponse>, BusinessError> {
        info!("分页查询商户分类，参数: {:?}", req);

        let categories = self
            .merchant_category_repository
            .page_by_condition(&req, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("分页查询商户分类失败: {}", e)))?;

        // 转换为响应类型
        let response_categories = PageData::new(
            categories.items.into_iter().map(|category| category.into()).collect(),
            categories.total,
            categories.page,
            categories.page_size,
        );

        Ok(response_categories)
    }

    /// 根据ID查询商户分类详情
    pub async fn get_category_by_id(
        &self,
        category_id: Uuid,
    ) -> Result<SysMerchantCategoryDetailResponse, BusinessError> {
        info!("查询商户分类详情，ID: {}", category_id);

        let category = self
            .merchant_category_repository
            .find_by_id(category_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户分类失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "商户分类不存在".to_string()))?;

        let detail: SysMerchantCategoryDetailResponse = category.into();
        Ok(detail)
    }

    /// 创建商户分类
    pub async fn create_category(
        &self,
        req: SysMerchantCategoryCreateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("创建商户分类，请求: {:?}，操作人: {}", req, operator_id);

        // 检查分类名称是否已存在
        let name_exists = self
            .merchant_category_repository
            .exists_by_name(&req.category_name, None, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查分类名称失败: {}", e)))?;

        if name_exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "分类名称已存在".to_string(),
            ));
        }

        // 检查分类编码是否已存在（如果提供了编码）
        if let Some(ref code) = req.category_code {
            if !code.trim().is_empty() {
                let code_exists = self
                    .merchant_category_repository
                    .exists_by_code(code, None, &self.db)
                    .await
                    .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查分类编码失败: {}", e)))?;

                if code_exists {
                    return Err(BusinessError::new(
                        RESOURCE_ALREADY_EXISTS,
                        "分类编码已存在".to_string(),
                    ));
                }
            }
        }

        // 创建分类
        let new_category = req.to_active_model(Some(operator_id));
        let category_id = new_category.id.clone().unwrap();

        self.merchant_category_repository
            .create(new_category, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("创建商户分类失败: {}", e)))?;

        info!("商户分类创建成功，ID: {}", category_id);
        Ok("商户分类创建成功".to_string())
    }

    /// 更新商户分类
    pub async fn update_category(
        &self,
        category_id: Uuid,
        req: SysMerchantCategoryUpdateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "更新商户分类，ID: {}，请求: {:?}，操作人: {}",
            category_id, req, operator_id
        );

        // 检查分类是否存在
        let category = self
            .merchant_category_repository
            .find_by_id(category_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户分类失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "商户分类不存在".to_string()))?;

        // 检查分类名称是否已存在（排除当前分类）
        let name_exists = self
            .merchant_category_repository
            .exists_by_name(&req.category_name, Some(category_id), &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查分类名称失败: {}", e)))?;

        if name_exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "分类名称已存在".to_string(),
            ));
        }

        // 检查分类编码是否已存在（如果提供了编码，排除当前分类）
        if let Some(ref code) = req.category_code {
            if !code.trim().is_empty() {
                let code_exists = self
                    .merchant_category_repository
                    .exists_by_code(code, Some(category_id), &self.db)
                    .await
                    .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查分类编码失败: {}", e)))?;

                if code_exists {
                    return Err(BusinessError::new(
                        RESOURCE_ALREADY_EXISTS,
                        "分类编码已存在".to_string(),
                    ));
                }
            }
        }

        // 更新分类
        let existing_active: ActiveModel = category.into();
        let updated_category = req.update_active_model(existing_active, Some(operator_id));

        self.merchant_category_repository
            .update(updated_category, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("更新商户分类失败: {}", e)))?;

        info!("商户分类更新成功，ID: {}", category_id);
        Ok("商户分类更新成功".to_string())
    }

    /// 删除商户分类
    pub async fn delete_category(
        &self,
        category_id: Uuid,
        _operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("删除商户分类，ID: {}", category_id);

        // 检查分类是否存在
        let _category = self
            .merchant_category_repository
            .find_by_id(category_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户分类失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "商户分类不存在".to_string()))?;

        // 检查分类下是否有商户
        let has_merchants = self
            .merchant_category_repository
            .has_merchants(category_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查分类下商户失败: {}", e)))?;

        if has_merchants {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "该分类下存在商户，无法删除".to_string(),
            ));
        }

        // 删除分类
        self.merchant_category_repository
            .delete_by_id(category_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除商户分类失败: {}", e)))?;

        info!("商户分类删除成功，ID: {}", category_id);
        Ok("商户分类删除成功".to_string())
    }

    /// 批量删除商户分类
    pub async fn batch_delete_categories(
        &self,
        req: SysMerchantCategoryBatchDeleteRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "批量删除商户分类，IDs: {:?}，操作人: {}",
            req.category_ids, operator_id
        );

        let mut success_count = 0;
        let mut error_messages = Vec::new();

        for category_id in req.category_ids {
            match self.delete_category(category_id, operator_id).await {
                Ok(_) => success_count += 1,
                Err(err) => {
                    error_messages.push(format!("分类 {} 删除失败: {}", category_id, err));
                }
            }
        }

        if error_messages.is_empty() {
            Ok(format!("批量删除成功，共删除 {} 个分类", success_count))
        } else {
            Err(BusinessError::new(
                OPERATION_PARTIAL_FAILURE,
                format!(
                    "批量删除完成，成功 {} 个，失败原因: {}",
                    success_count,
                    error_messages.join("; ")
                ),
            ))
        }
    }

    /// 切换商户分类状态
    pub async fn change_category_status(
        &self,
        category_id: Uuid,
        req: MerchantCategoryStatusRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "切换商户分类状态，ID: {}，状态: {}，操作人: {}",
            category_id, req.status, operator_id
        );

        // 检查分类是否存在
        let category = self
            .merchant_category_repository
            .find_by_id(category_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户分类失败: {}", e)))?
            .ok_or_else(|| BusinessError::new(RESOURCE_NOT_FOUND, "商户分类不存在".to_string()))?;

        // 更新状态
        let mut category_active: ActiveModel = category.into();
        category_active.status = Set(MerchantCategoryStatus::try_from_value(&req.status).map_err(
            |err| {
                BusinessError::new(
                    INVALID_PARAMETER,
                    format!("无效的分类状态: {}，错误: {}", req.status, err),
                )
            },
        )?);
        category_active.updated_date = Set(Local::now().fixed_offset());
        category_active.updated_by = Set(Some(operator_id));

        self.merchant_category_repository
            .update(category_active, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("更新商户分类状态失败: {}", e)))?;

        let status_text = if req.status == 1 { "启用" } else { "禁用" };

        info!(
            "商户分类状态切换成功，ID: {}，状态: {}",
            category_id, status_text
        );
        Ok(format!("分类状态已切换为{}", status_text))
    }

    /// 获取商户分类选择项
    pub async fn get_category_select_items(
        &self,
    ) -> Result<Vec<SysMerchantCategorySelectItem>, BusinessError> {
        info!("获取商户分类选择项");

        let categories = self
            .merchant_category_repository
            .find_all_enabled(&self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户分类列表失败: {}", e)))?;

        let select_items = categories
            .into_iter()
            .map(|category| category.into())
            .collect();

        Ok(select_items)
    }

    /// 获取商户分类统计信息
    pub async fn get_category_stats(
        &self,
    ) -> Result<Vec<SysMerchantCategoryStatsResponse>, BusinessError> {
        info!("获取商户分类统计信息");

        let categories = self
            .merchant_category_repository
            .find_all(&self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户分类列表失败: {}", e)))?;

        let mut stats_list = Vec::new();

        for category in categories {
            // 查询该分类下的总商户数
            let total_merchant_count = self
                .merchant_category_repository
                .count_merchants(category.id, &self.db)
                .await
                .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户数量失败: {}", e)))?;

            // 查询该分类下的启用商户数
            let active_merchant_count = self
                .merchant_category_repository
                .count_active_merchants(category.id, &self.db)
                .await
                .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询启用商户数量失败: {}", e)))?;

            stats_list.push(SysMerchantCategoryStatsResponse {
                category_id: category.id,
                category_name: category.category_name,
                merchant_count: total_merchant_count as i32,
                active_merchant_count: active_merchant_count as i32,
                inactive_merchant_count: (total_merchant_count - active_merchant_count) as i32,
            });
        }

        Ok(stats_list)
    }

    /// 检查分类名称是否存在
    pub async fn check_category_name_exists(
        &self,
        category_name: &str,
        exclude_category_id: Option<Uuid>,
    ) -> Result<bool, BusinessError> {
        self.merchant_category_repository
            .exists_by_name(category_name, exclude_category_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查分类名称是否存在失败: {}", e)))
    }

    /// 检查分类编码是否存在
    pub async fn check_category_code_exists(
        &self,
        category_code: &str,
        exclude_category_id: Option<Uuid>,
    ) -> Result<bool, BusinessError> {
        self.merchant_category_repository
            .exists_by_code(category_code, exclude_category_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查分类编码是否存在失败: {}", e)))
    }

    /// 检查分类下是否有商户
    pub async fn check_category_has_merchants(
        &self,
        category_id: Uuid,
    ) -> Result<bool, BusinessError> {
        self.merchant_category_repository
            .has_merchants(category_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查分类下商户失败: {}", e)))
    }

    /// 检查分类下是否有商户（保持向后兼容）
    pub async fn has_merchants(&self, category_id: Uuid) -> Result<bool, BusinessError> {
        self.check_category_has_merchants(category_id).await
    }
}

use crate::domain::system::dto::sys_user_request::{
    SysUserBatchDeleteRequest, SysUserCreateRequest, SysUserPageRequest,
    SysUserResetPasswordRequest, SysUserStatusRequest, SysUserUpdateRequest,
};
use crate::domain::system::entities::users::{ActiveModel, UserStatus};
use crate::domain::system::vo::{
    SysUnassignedUserCountResponse, SysUserDetailResponse, SysUserListResponse, SysUserRoleDetailVo,
};
use crate::repository::{SysUserRepository, SysUserRoleRepository};
use crate::utils;
use chrono::Local;
use lib_core::BusinessError;
use lib_core::app::plugin::service::Service as ServiceTrait;
use lib_core::response::PageData;
use lib_macros::Service;
use sea_orm::{DatabaseConnection, DatabaseTransaction, Set, TransactionTrait};
use tracing::{error, info, warn};
use uuid::Uuid;

/// 系统用户管理服务实现
#[derive(Clone, Service)]
pub struct SysUserService {
    #[inject(component)]
    db: DatabaseConnection,
    #[inject(component)]
    user_repository: SysUserRepository,
    #[inject(component)]
    user_role_repository: SysUserRoleRepository,
}

const ADMIN_NAME: &str = "admin";

/// 实现SysUserService特征
impl SysUserService {
    /// 分页查询用户列表实现
    pub async fn page_users(
        &self,
        req: SysUserPageRequest,
    ) -> Result<PageData<SysUserListResponse>, BusinessError> {
        info!("分页查询用户列表: {:?}", req);

        // 使用Repository进行分页查询
        let page_data = self
            .user_repository
            .page_by_condition(&req, &self.db)
            .await
            .map_err(|e| {
                error!("分页查询用户失败: {}", e);
                BusinessError::new(500, "分页查询用户失败".to_string())
            })?;

        // 转换为VO
        let items: Vec<SysUserListResponse> = page_data
            .items
            .into_iter()
            .map(|user| user.into())
            .collect();

        Ok(PageData::new(
            items,
            page_data.total,
            page_data.page,
            page_data.page_size,
        ))
    }

    /// 分页查询未分配角色的用户列表实现
    pub async fn page_unassigned_users(
        &self,
        req: SysUserPageRequest,
    ) -> Result<PageData<SysUserListResponse>, BusinessError> {
        info!("分页查询未分配角色用户列表: {:?}", req);

        // 使用Repository进行分页查询
        let page_data = self
            .user_repository
            .page_unassigned_users(&req, &self.db)
            .await
            .map_err(|e| {
                error!("分页查询未分配角色用户失败: {}", e);
                BusinessError::new(500, "分页查询未分配角色用户失败".to_string())
            })?;

        // 转换为VO
        let items: Vec<SysUserListResponse> = page_data
            .items
            .into_iter()
            .map(|user| user.into())
            .collect();

        Ok(PageData::new(
            items,
            page_data.total,
            page_data.page,
            page_data.page_size,
        ))
    }

    /// 获取未分配角色用户数量
    pub async fn get_unassigned_user_count(
        &self,
    ) -> Result<SysUnassignedUserCountResponse, BusinessError> {
        info!("获取未分配角色用户数量");

        // 使用Repository查询未分配角色用户数量
        let unassigned_user_count = self
            .user_repository
            .count_unassigned_users(&self.db)
            .await
            .map_err(|e| {
                error!("查询未分配角色用户总数失败: {}", e);
                BusinessError::new(500, "查询未分配角色用户总数失败".to_string())
            })?;

        let response = SysUnassignedUserCountResponse {
            unassigned_user_count,
        };

        Ok(response)
    }

    /// 根据ID查询用户详情实现
    pub async fn get_user_by_id(
        &self,
        user_id: Uuid,
    ) -> Result<SysUserDetailResponse, BusinessError> {
        info!("查询用户详情: {}", user_id);

        // 使用Repository查询用户详情和角色
        let result = self
            .user_repository
            .find_user_detail_with_roles(user_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询用户详情失败: {}", e);
                BusinessError::new(500, "查询用户详情失败".to_string())
            })?;

        let (user, roles) = match result {
            Some(data) => data,
            None => {
                warn!("用户不存在: {}", user_id);
                return Err(BusinessError::new(404, "用户不存在".to_string()));
            }
        };

        // 构建角色详情列表
        let role_details: Vec<SysUserRoleDetailVo> =
            roles.into_iter().map(|role| role.into()).collect();

        let user_detail: SysUserDetailResponse = user.into();
        let user_detail = user_detail.with_roles(role_details);

        Ok(user_detail)
    }

    /// 创建新用户实现
    pub async fn create_user(
        &self,
        req: SysUserCreateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("创建用户: {}", req.username);

        // 检查用户名是否已存在
        let exists = self
            .user_repository
            .username_exists(&req.username, None, &self.db)
            .await
            .map_err(|e| {
                error!("检查用户名是否存在失败: {}", e);
                BusinessError::new(500, "检查用户名失败".to_string())
            })?;
        if exists {
            return Err(BusinessError::new(400, "用户名已存在".to_string()));
        }

        // 检查邮箱是否已存在
        if let Some(email) = &req.email {
            if !email.trim().is_empty() {
                let exists = self
                    .user_repository
                    .email_exists(email, None, &self.db)
                    .await
                    .map_err(|e| {
                        error!("检查邮箱是否存在失败: {}", e);
                        BusinessError::new(500, "检查邮箱失败".to_string())
                    })?;
                if exists {
                    return Err(BusinessError::new(400, "邮箱已存在".to_string()));
                }
            }
        }

        // 检查手机号是否已存在
        if let Some(phone) = &req.phone {
            if !phone.trim().is_empty() {
                let exists = self
                    .user_repository
                    .phone_exists(phone, None, &self.db)
                    .await
                    .map_err(|e| {
                        error!("检查手机号是否存在失败: {}", e);
                        BusinessError::new(500, "检查手机号失败".to_string())
                    })?;
                if exists {
                    return Err(BusinessError::new(400, "手机号已存在".to_string()));
                }
            }
        }

        // 密码加密
        let hashed_password = utils::hash_password(&req.password).map_err(|e| {
            error!("密码加密失败: {}", e);
            BusinessError::new(500, "密码加密失败".to_string())
        })?;

        // 开启事务
        let txn = self.db.begin().await.map_err(|e| {
            error!("开启事务失败: {}", e);
            BusinessError::new(500, "创建用户失败".to_string())
        })?;

        // 创建用户
        let user_id = Uuid::now_v7();
        let now = Local::now();

        let user_active_model = ActiveModel {
            id: Set(user_id),
            username: Set(req.username.clone()),
            password: Set(hashed_password),
            real_name: Set(req.real_name),
            phone: Set(req.phone),
            email: Set(req.email),
            avatar: Set(req.avatar),
            gender: Set(req.gender),
            status: Set(req.status.unwrap_or(UserStatus::Active as i32)),
            created_date: Set(now.into()),
            updated_date: Set(now.into()),
            created_by: Set(Some(operator_id)),
            updated_by: Set(Some(operator_id)),
            remark: Set(req.remark),
            ..Default::default()
        };

        self.user_repository
            .create(user_active_model, &txn)
            .await
            .map_err(|e| {
                error!("插入用户记录失败: {}", e);
                BusinessError::new(500, "创建用户失败".to_string())
            })?;

        // 处理角色关联
        if let Some(role_ids) = req.role_ids {
            self.assign_user_roles(&txn, user_id, &role_ids, operator_id)
                .await
                .map_err(|e| {
                    error!("分配用户角色失败: {}", e);
                    BusinessError::new(500, "创建用户失败".to_string())
                })?;
        }

        // 提交事务
        txn.commit().await.map_err(|e| {
            error!("提交事务失败: {}", e);
            BusinessError::new(500, "创建用户失败".to_string())
        })?;

        info!("用户 {} 创建成功，ID: {}", req.username, user_id);
        Ok("用户创建成功".to_string())
    }

    /// 更新用户信息实现
    pub async fn update_user(
        &self,
        user_id: Uuid,
        req: SysUserUpdateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("更新用户: {}", user_id);

        // 检查用户是否存在
        let user = self
            .user_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询用户失败: {}", e);
                BusinessError::new(500, "查询用户失败".to_string())
            })?
            .ok_or_else(|| BusinessError::new(404, "用户不存在".to_string()))?;

        // 检查邮箱是否已存在（排除当前用户）
        if let Some(email) = &req.email {
            if !email.trim().is_empty() {
                let exists = self
                    .user_repository
                    .email_exists(email, Some(user_id), &self.db)
                    .await
                    .map_err(|e| {
                        error!("检查邮箱是否存在失败: {}", e);
                        BusinessError::new(500, "检查邮箱失败".to_string())
                    })?;
                if exists {
                    return Err(BusinessError::new(400, "邮箱已存在".to_string()));
                }
            }
        }

        // 检查手机号是否已存在（排除当前用户）
        if let Some(phone) = &req.phone {
            if !phone.trim().is_empty() {
                let exists = self
                    .user_repository
                    .phone_exists(phone, Some(user_id), &self.db)
                    .await
                    .map_err(|e| {
                        error!("检查手机号是否存在失败: {}", e);
                        BusinessError::new(500, "检查手机号失败".to_string())
                    })?;
                if exists {
                    return Err(BusinessError::new(400, "手机号已存在".to_string()));
                }
            }
        }

        if user.username == ADMIN_NAME {
            return Err(BusinessError::new(400, "管理员不能修改".to_string()));
        }

        // 开启事务
        let txn = self.db.begin().await.map_err(|e| {
            error!("开启事务失败: {}", e);
            BusinessError::new(500, "更新用户失败".to_string())
        })?;

        // 更新用户信息
        let mut user_active_model: ActiveModel = user.into();
        user_active_model.real_name = Set(req.real_name);
        user_active_model.phone = Set(req.phone);
        user_active_model.email = Set(req.email);
        user_active_model.avatar = Set(req.avatar);
        user_active_model.gender = Set(req.gender);
        if let Some(status) = req.status {
            user_active_model.status = Set(status);
        }
        user_active_model.remark = Set(req.remark);
        user_active_model.updated_date = Set(Local::now().into());
        user_active_model.updated_by = Set(Some(operator_id));

        self.user_repository
            .update(user_active_model, &txn)
            .await
            .map_err(|e| {
                error!("更新用户信息失败: {}", e);
                BusinessError::new(500, "更新用户失败".to_string())
            })?;

        // 处理角色关联更新
        if let Some(role_ids) = req.role_ids {
            // 先删除原有角色关联
            self.clear_user_roles(&txn, user_id).await.map_err(|e| {
                error!("清除用户角色失败: {}", e);
                BusinessError::new(500, "更新用户失败".to_string())
            })?;

            // 重新分配角色
            self.assign_user_roles(&txn, user_id, &role_ids, operator_id)
                .await
                .map_err(|e| {
                    error!("重新分配用户角色失败: {}", e);
                    BusinessError::new(500, "更新用户失败".to_string())
                })?;
        }

        // 提交事务
        txn.commit().await.map_err(|e| {
            error!("提交事务失败: {}", e);
            BusinessError::new(500, "更新用户失败".to_string())
        })?;

        info!("用户 {} 更新成功", user_id);
        Ok("用户更新成功".to_string())
    }

    /// 删除用户实现
    pub async fn delete_user(
        &self,
        user_id: Uuid,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("删除用户: {}", user_id);

        // 检查用户是否存在
        let user = self
            .user_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询用户失败: {}", e);
                BusinessError::new(500, "查询用户失败".to_string())
            })?
            .ok_or_else(|| BusinessError::new(404, "用户不存在".to_string()))?;

        // 不能删除自己
        if user_id == operator_id {
            return Err(BusinessError::new(400, "不能删除自己".to_string()));
        }

        // 开启事务
        let txn = self.db.begin().await.map_err(|e| {
            error!("开启事务失败: {}", e);
            BusinessError::new(500, "删除用户失败".to_string())
        })?;

        // 先删除用户角色关联
        self.clear_user_roles(&txn, user_id).await.map_err(|e| {
            error!("删除用户角色关联失败: {}", e);
            BusinessError::new(500, "删除用户失败".to_string())
        })?;

        // 删除用户
        self.user_repository
            .delete_by_id(user_id, &txn)
            .await
            .map_err(|e| {
                error!("删除用户记录失败: {}", e);
                BusinessError::new(500, "删除用户失败".to_string())
            })?;

        // 提交事务
        txn.commit().await.map_err(|e| {
            error!("提交事务失败: {}", e);
            BusinessError::new(500, "删除用户失败".to_string())
        })?;

        info!("用户 {} ({}) 删除成功", user.username, user_id);
        Ok("用户删除成功".to_string())
    }

    /// 批量删除用户实现
    pub async fn batch_delete_users(
        &self,
        req: SysUserBatchDeleteRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("批量删除用户: {:?}", req.user_ids);

        // 检查是否要删除自己
        if req.user_ids.contains(&operator_id) {
            return Err(BusinessError::new(400, "不能删除自己".to_string()));
        }

        let user_ids = req.user_ids;

        // 验证用户是否存在
        let existing_users = self
            .user_repository
            .find_by_ids(user_ids.clone(), &self.db)
            .await
            .map_err(|e| {
                error!("查询用户失败: {}", e);
                BusinessError::new(500, "查询用户失败".to_string())
            })?;

        if existing_users.is_empty() {
            return Err(BusinessError::new(400, "没有找到要删除的用户".to_string()));
        }

        // 检查是否有管理员用户
        for user in &existing_users {
            if user.username == ADMIN_NAME {
                return Err(BusinessError::new(
                    400,
                    format!("不能删除管理员用户: {}", user.username),
                ));
            }
        }

        // 开启事务
        let txn = self.db.begin().await.map_err(|e| {
            error!("开启事务失败: {}", e);
            BusinessError::new(500, "批量删除用户失败".to_string())
        })?;

        // 先删除用户角色关联
        self.user_role_repository
            .delete_by_user_ids(user_ids.clone(), &txn)
            .await
            .map_err(|e| {
                error!("删除用户角色关联失败: {}", e);
                BusinessError::new(500, "删除用户角色关联失败".to_string())
            })?;

        // 再删除用户
        let deleted_count = self
            .user_repository
            .delete_by_ids(user_ids.clone(), &txn)
            .await
            .map_err(|e| {
                error!("批量删除用户失败: {}", e);
                BusinessError::new(500, "批量删除用户失败".to_string())
            })?;

        // 提交事务
        txn.commit().await.map_err(|e| {
            error!("提交事务失败: {}", e);
            BusinessError::new(500, "批量删除用户失败".to_string())
        })?;

        info!("批量删除用户成功，删除数量: {}", deleted_count);
        Ok(format!("批量删除成功，删除 {} 个用户", deleted_count))
    }

    /// 重置用户密码实现
    pub async fn reset_password(
        &self,
        user_id: Uuid,
        req: SysUserResetPasswordRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("重置用户密码: {}", user_id);

        // 检查用户是否存在
        let user = self
            .user_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询用户失败: {}", e);
                BusinessError::new(500, "查询用户失败".to_string())
            })?
            .ok_or_else(|| BusinessError::new(404, "用户不存在".to_string()))?;

        if user.username == ADMIN_NAME {
            return Err(BusinessError::new(
                400,
                format!("不能重置管理员用户密码: {}", user.username),
            ));
        }

        // 密码加密
        let hashed_password = utils::hash_password(&req.new_password).map_err(|e| {
            error!("密码加密失败: {}", e);
            BusinessError::new(500, "密码加密失败".to_string())
        })?;

        // 更新密码
        let mut user_active_model: ActiveModel = user.into();
        user_active_model.password = Set(hashed_password);
        user_active_model.updated_date = Set(Local::now().into());
        user_active_model.updated_by = Set(Some(operator_id));

        self.user_repository
            .update(user_active_model, &self.db)
            .await
            .map_err(|e| {
                error!("重置密码失败: {}", e);
                BusinessError::new(500, "重置密码失败".to_string())
            })?;

        info!("用户 {} 密码重置成功", user_id);
        Ok("密码重置成功".to_string())
    }

    /// 切换用户状态实现
    pub async fn change_status(
        &self,
        user_id: Uuid,
        req: SysUserStatusRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("切换用户状态: {} -> {}", user_id, req.status);

        // 检查用户是否存在
        let user = self
            .user_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询用户失败: {}", e);
                BusinessError::new(500, "查询用户失败".to_string())
            })?
            .ok_or_else(|| BusinessError::new(404, "用户不存在".to_string()))?;

        // 不能禁用自己
        if user_id == operator_id && req.status != UserStatus::Active as i32 {
            return Err(BusinessError::new(400, "不能禁用自己".to_string()));
        }

        if user.username == ADMIN_NAME {
            return Err(BusinessError::new(
                400,
                format!("不能修改管理员用户状态: {}", user.username),
            ));
        }

        // 更新状态
        let mut user_active_model: ActiveModel = user.into();
        user_active_model.status = Set(req.status);
        user_active_model.updated_date = Set(Local::now().into());
        user_active_model.updated_by = Set(Some(operator_id));

        self.user_repository
            .update(user_active_model, &self.db)
            .await
            .map_err(|e| {
                error!("切换用户状态失败: {}", e);
                BusinessError::new(500, "切换用户状态失败".to_string())
            })?;

        let status_text = match req.status {
            1 => "启用",
            2 => "禁用",
            _ => "未知状态",
        };

        info!("用户 {} 状态切换为: {}", user_id, status_text);
        Ok(format!("用户状态已切换为: {}", status_text))
    }

    /// 检查用户名是否存在实现
    pub async fn check_username_exists(
        &self,
        username: &str,
        exclude_user_id: Option<Uuid>,
    ) -> Result<bool, BusinessError> {
        self.user_repository
            .username_exists(username, exclude_user_id, &self.db)
            .await
            .map_err(|e| {
                error!("检查用户名是否存在失败: {}", e);
                BusinessError::new(500, "检查用户名失败".to_string())
            })
    }

    /// 检查邮箱是否存在实现
    pub async fn check_email_exists(
        &self,
        email: &str,
        exclude_user_id: Option<Uuid>,
    ) -> Result<bool, BusinessError> {
        self.user_repository
            .email_exists(email, exclude_user_id, &self.db)
            .await
            .map_err(|e| {
                error!("检查邮箱是否存在失败: {}", e);
                BusinessError::new(500, "检查邮箱失败".to_string())
            })
    }

    /// 检查手机号是否存在实现
    pub async fn check_phone_exists(
        &self,
        phone: &str,
        exclude_user_id: Option<Uuid>,
    ) -> Result<bool, BusinessError> {
        self.user_repository
            .phone_exists(phone, exclude_user_id, &self.db)
            .await
            .map_err(|e| {
                error!("检查手机号是否存在失败: {}", e);
                BusinessError::new(500, "检查手机号失败".to_string())
            })
    }
}

/// 私有辅助方法实现
impl SysUserService {
    /// 清除用户角色关联
    pub async fn clear_user_roles(
        &self,
        txn: &DatabaseTransaction,
        user_id: Uuid,
    ) -> Result<(), sea_orm::DbErr> {
        self.user_role_repository
            .delete_by_user_id(user_id, txn)
            .await
            .map_err(|e| {
                error!("清除用户角色关联失败: {}", e);
                sea_orm::DbErr::Custom(format!("清除用户角色关联失败: {}", e))
            })?;
        Ok(())
    }

    /// 分配用户角色
    pub async fn assign_user_roles(
        &self,
        txn: &DatabaseTransaction,
        user_id: Uuid,
        role_ids: &[Uuid],
        operator_id: Uuid,
    ) -> Result<(), sea_orm::DbErr> {
        for role_id in role_ids {
            let user_role = crate::domain::system::entities::user_roles::ActiveModel {
                id: Set(Uuid::now_v7()),
                user_id: Set(user_id),
                role_id: Set(*role_id),
                created_date: Set(Local::now().into()),
                created_by: Set(Some(operator_id)),
                ..Default::default()
            };
            self.user_role_repository
                .create(user_role, txn)
                .await
                .map_err(|e| {
                    error!("插入用户角色关联失败: {}", e);
                    sea_orm::DbErr::Custom(format!("插入用户角色关联失败: {}", e))
                })?;
        }
        Ok(())
    }
}

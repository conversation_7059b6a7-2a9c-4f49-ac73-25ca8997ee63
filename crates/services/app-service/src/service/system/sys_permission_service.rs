use crate::constants::*;
use crate::domain::system::dto::sys_permission_request::{
    SysPermissionBatchDeleteRequest, SysPermissionCreateRequest, SysPermissionPageRequest,
    SysPermissionStatusRequest, SysPermissionUpdateRequest,
};
use crate::domain::system::entities::permissions::ActiveModel;
use crate::domain::system::vo::{
    RouterMeta, RouterVo, SysPermissionDetailResponse, SysPermissionListResponse,
    SysPermissionSelectItem, SysPermissionTreeNode,
};
use crate::repository::{SysPermissionRepository, SysRolePermissionRepository};
use lib_core::app::plugin::service::Service as ServiceTrait;

use chrono::Local;
use lib_core::{BusinessError, response::PageData};
use lib_macros::Service;
use sea_orm::{DatabaseConnection, Set};
use tracing::{error, info, warn};
use uuid::Uuid;

/// 系统权限菜单管理服务实现
#[derive(Clone, Service)]
pub struct SysPermissionService {
    #[inject(component)]
    permission_repository: SysPermissionRepository,
    #[inject(component)]
    role_permission_repository: SysRolePermissionRepository,
    #[inject(component)]
    db: DatabaseConnection,
}

pub const ALL_PERMISSIONS_CODE: &str = "*:*:*"; // 全部权限标识

impl SysPermissionService {
    /// 格式化日期时间为字符串
    fn format_datetime(dt: &chrono::DateTime<chrono::Local>) -> String {
        crate::utils::datetime::DateTimeUtils::format_datetime(dt)
    }

    /// 检查菜单是否有子菜单
    pub async fn has_children(&self, permission_id: Uuid) -> Result<bool, BusinessError> {
        self.permission_repository
            .has_children(permission_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查子菜单失败: {}", e)))
    }

    /// 根据ID获取父菜单名称
    pub async fn get_parent_menu_name(
        &self,
        parent_id: Uuid,
    ) -> Result<Option<String>, BusinessError> {
        let parent = self
            .permission_repository
            .find_by_id(parent_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询父级菜单失败: {}", e)))?;

        Ok(parent.map(|p| p.menu_name))
    }

    /// 检查是否为某菜单的子菜单
    pub async fn is_child_permission(&self, potential_parent_id: Uuid, child_id: Uuid) -> bool {
        let child_permission = match self
            .permission_repository
            .find_by_id(child_id, &self.db)
            .await
        {
            Ok(Some(perm)) => perm,
            _ => return false,
        };

        if let Some(parent_id) = child_permission.parent_id {
            if parent_id == potential_parent_id {
                return true;
            }

            // 递归检查，使用Box::pin避免无限大的Future
            return Box::pin(self.is_child_permission(potential_parent_id, parent_id)).await;
        }

        false
    }

    /// 递归添加子路由（带权限过滤）
    fn add_child_routers_filtered(
        &self,
        parent: &mut RouterVo,
        parent_to_children: &std::collections::HashMap<Uuid, Vec<Uuid>>,
        id_to_menu: &std::collections::HashMap<
            Uuid,
            &crate::domain::system::entities::permissions::Model,
        >,
        visible_menu_ids: &std::collections::HashSet<Uuid>,
    ) {
        // 卫语句：查找父菜单
        let parent_menu = match self.find_parent_menu(parent, id_to_menu) {
            Some(menu) => menu,
            None => return,
        };

        // 卫语句：获取子菜单ID列表
        let children_ids = match parent_to_children.get(&parent_menu.id) {
            Some(ids) => ids,
            None => return,
        };

        // 构建子路由列表
        let mut children_routers = Vec::new();
        for &child_id in children_ids {
            if let Some(child_router) =
                self.create_child_router(child_id, id_to_menu, visible_menu_ids, parent_to_children)
            {
                children_routers.push(child_router);
            }
        }

        // 排序子路由
        self.sort_children_routers(&mut children_routers, id_to_menu);

        // 更新父路由信息
        self.update_parent_router_info(parent, children_routers);
    }

    /// 查找父菜单
    fn find_parent_menu<'a>(
        &self,
        parent: &RouterVo,
        id_to_menu: &'a std::collections::HashMap<
            Uuid,
            &'a crate::domain::system::entities::permissions::Model,
        >,
    ) -> Option<&'a crate::domain::system::entities::permissions::Model> {
        id_to_menu
            .values()
            .find(|menu| menu.menu_name == parent.meta.title)
            .copied()
    }

    /// 创建子路由
    fn create_child_router(
        &self,
        child_id: Uuid,
        id_to_menu: &std::collections::HashMap<
            Uuid,
            &crate::domain::system::entities::permissions::Model,
        >,
        visible_menu_ids: &std::collections::HashSet<Uuid>,
        parent_to_children: &std::collections::HashMap<Uuid, Vec<Uuid>>,
    ) -> Option<RouterVo> {
        // 卫语句：检查是否在可见菜单中
        if !visible_menu_ids.contains(&child_id) {
            return None;
        }

        // 卫语句：获取子菜单信息
        let child_menu = id_to_menu.get(&child_id)?;

        // 构建路由名称
        let name = child_menu
            .path
            .as_ref()
            .filter(|path| !path.is_empty())
            .map(|path| path.replace("/", "_"));

        // 创建子路由
        let mut child_router = RouterVo {
            name,
            path: child_menu.path.clone().unwrap_or_default(),
            hidden: child_menu.visible.unwrap_or(0) == 1,
            redirect: None,
            component: child_menu.component.clone(),
            always_show: Some(false),
            perms: child_menu.perms.clone(),
            menu_type: child_menu.menu_type,
            meta: RouterMeta {
                title: child_menu.menu_name.clone(),
                icon: child_menu.icon.clone(),
                no_cache: child_menu.is_cache.unwrap_or(0) != 0,
                link: None,
            },
            children: Some(Box::new(Vec::new())),
        };

        // 递归处理子路由
        self.add_child_routers_filtered(
            &mut child_router,
            parent_to_children,
            id_to_menu,
            visible_menu_ids,
        );

        Some(child_router)
    }

    /// 排序子路由
    fn sort_children_routers(
        &self,
        children_routers: &mut Vec<RouterVo>,
        id_to_menu: &std::collections::HashMap<
            Uuid,
            &crate::domain::system::entities::permissions::Model,
        >,
    ) {
        children_routers.sort_by(|a, b| {
            let a_order = self.get_menu_order_num(a, id_to_menu);
            let b_order = self.get_menu_order_num(b, id_to_menu);
            a_order.cmp(&b_order)
        });
    }

    /// 获取菜单排序号
    fn get_menu_order_num(
        &self,
        router: &RouterVo,
        id_to_menu: &std::collections::HashMap<
            Uuid,
            &crate::domain::system::entities::permissions::Model,
        >,
    ) -> i32 {
        id_to_menu
            .values()
            .find(|menu| menu.menu_name == router.meta.title)
            .and_then(|menu| menu.order_num)
            .unwrap_or(999)
    }

    /// 更新父路由信息
    fn update_parent_router_info(&self, parent: &mut RouterVo, children_routers: Vec<RouterVo>) {
        // 更新always_show，如果有子菜单则总是显示
        let has_children = !children_routers.is_empty();
        parent.always_show = Some(has_children);

        // 设置父路由的子路由列表
        if let Some(children) = &mut parent.children {
            *children = Box::from(children_routers);
        }
    }

    pub async fn check_before_deleting_menu_permissions(
        &self,
        permission_id: Uuid,
    ) -> Result<(), BusinessError> {
        // 检查权限菜单是否存在
        let _permission = self
            .permission_repository
            .find_by_id(permission_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询权限菜单失败: {}", e)))?;

        if _permission.is_none() {
            warn!("权限菜单不存在: {}", permission_id);
            return Err(BusinessError::new(
                RESOURCE_NOT_FOUND,
                "权限菜单不存在".to_string(),
            ));
        }

        // 检查是否有子菜单
        let has_children = self.has_children(permission_id).await?;

        if has_children {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "存在子菜单，无法删除".to_string(),
            ));
        }

        // 检查是否有角色使用了该权限
        let role_count = self
            .role_permission_repository
            .count_by_permission_id(permission_id, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("检查权限使用情况失败: {}", e))
            })?;

        if role_count > 0 {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "该菜单已分配给角色，无法删除".to_string(),
            ));
        }
        Ok(())
    }
}

impl SysPermissionService {
    /// 分页查询权限菜单列表
    pub async fn page_permissions(
        &self,
        req: SysPermissionPageRequest,
    ) -> Result<PageData<SysPermissionListResponse>, BusinessError> {
        info!("分页查询权限菜单列表: {:?}", req);

        // 使用Repository进行分页查询
        self.permission_repository
            .page_by_condition(&req, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("分页查询权限菜单失败: {}", e)))
    }

    /// 根据ID查询权限菜单详情
    pub async fn get_permission_by_id(
        &self,
        permission_id: Uuid,
    ) -> Result<SysPermissionDetailResponse, BusinessError> {
        info!("查询权限菜单详情: {}", permission_id);

        // 查询权限菜单
        let permission = self
            .permission_repository
            .find_by_id(permission_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询权限菜单失败: {}", e)))?;

        let permission = match permission {
            Some(p) => p,
            None => {
                warn!("权限菜单不存在: {}", permission_id);
                return Err(BusinessError::new(
                    RESOURCE_NOT_FOUND,
                    "权限菜单不存在".to_string(),
                ));
            }
        };

        // 获取父级菜单名称（如果有）
        let parent_name = if let Some(parent_id) = permission.parent_id {
            self.get_parent_menu_name(parent_id).await?
        } else {
            None
        };

        // 查询子菜单
        let children = if self.has_children(permission_id).await? {
            let child_permissions = self
                .permission_repository
                .find_by_parent_id(permission_id, &self.db)
                .await
                .map_err(|e| {
                    BusinessError::new(DATABASE_ERROR, format!("查询子菜单列表失败: {}", e))
                })?;

            SysPermissionTreeNode::build_tree(child_permissions)
        } else {
            Vec::new()
        };

        // 构建详情响应
        let detail = SysPermissionDetailResponse {
            id: permission.id,
            menu_name: permission.menu_name,
            parent_id: permission.parent_id,
            parent_name,
            order_num: permission.order_num,
            path: permission.path,
            component: permission.component,
            query: permission.query,
            is_frame: permission.is_frame,
            is_cache: permission.is_cache,
            menu_type: permission.menu_type,
            visible: permission.visible,
            status: permission.status,
            perms: permission.perms,
            icon: permission.icon,
            created_date: Self::format_datetime(&permission.created_date),
            updated_date: Self::format_datetime(&permission.updated_date),
            created_by: permission.created_by,
            updated_by: permission.updated_by,
            remark: permission.remark,
            children: Some(Box::new(children)),
        };

        Ok(detail)
    }

    /// 获取权限菜单树
    pub async fn get_permission_tree(&self) -> Result<Vec<SysPermissionTreeNode>, BusinessError> {
        info!("获取权限菜单树");

        // 查询所有菜单权限，按照顺序排序
        let all_permissions = self
            .permission_repository
            .find_all(&self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询权限菜单列表失败: {}", e))
            })?;

        // 构建树形结构
        Ok(SysPermissionTreeNode::build_tree(all_permissions))
    }

    /// 创建新权限菜单
    pub async fn create_permission(
        &self,
        req: SysPermissionCreateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("创建权限菜单: {}", req.menu_name);

        // 检查权限标识是否已存在
        if let Some(ref perms) = req.perms {
            if !perms.trim().is_empty() {
                let exists = self.check_perms_exists(perms, None).await?;
                if exists {
                    return Err(BusinessError::new(
                        RESOURCE_ALREADY_EXISTS,
                        "权限标识已存在".to_string(),
                    ));
                }
            }
        }

        // 检查父级菜单是否存在
        if let Some(parent_id) = req.parent_id {
            let parent_exists = self
                .permission_repository
                .find_by_id(parent_id, &self.db)
                .await
                .map_err(|e| {
                    error!("查询父级菜单失败: {}", e);
                    BusinessError::new(DATABASE_ERROR, "查询父级菜单失败".to_string())
                })?
                .is_some();

            if !parent_exists {
                return Err(BusinessError::new(
                    RESOURCE_NOT_FOUND,
                    "父级菜单不存在".to_string(),
                ));
            }
        }

        // 创建权限菜单
        let permission_id = Uuid::now_v7();
        let now = Local::now();

        let new_permission = ActiveModel {
            id: Set(permission_id),
            menu_name: Set(req.menu_name),
            parent_id: Set(req.parent_id),
            order_num: Set(req.order_num),
            path: Set(req.path),
            component: Set(req.component),
            query: Set(req.query),
            is_frame: Set(req.is_frame),
            is_cache: Set(req.is_cache),
            menu_type: Set(req.menu_type),
            visible: Set(req.visible),
            status: Set(req.status.or(Some(0))), // 默认正常状态
            perms: Set(req.perms),
            icon: Set(req.icon),
            created_date: Set(now),
            updated_date: Set(now),
            created_by: Set(Some(operator_id)),
            updated_by: Set(Some(operator_id)),
            remark: Set(req.remark),
        };

        self.permission_repository
            .create(new_permission, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("创建权限菜单失败: {}", e)))?;

        info!("权限菜单创建成功，ID: {}", permission_id);
        Ok("权限菜单创建成功".to_string())
    }

    /// 更新权限菜单信息
    pub async fn update_permission(
        &self,
        permission_id: Uuid,
        req: SysPermissionUpdateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("更新权限菜单: {}", permission_id);

        // 检查权限菜单是否存在
        let permission = self
            .permission_repository
            .find_by_id(permission_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询权限菜单失败: {}", e)))?;

        let permission = match permission {
            Some(p) => p,
            None => {
                warn!("权限菜单不存在: {}", permission_id);
                return Err(BusinessError::new(
                    RESOURCE_NOT_FOUND,
                    "权限菜单不存在".to_string(),
                ));
            }
        };

        // 检查是否修改为自己的子菜单
        if let Some(parent_id) = req.parent_id {
            if parent_id == permission_id {
                return Err(BusinessError::new(
                    INVALID_PARAMETER,
                    "上级菜单不能选择自己".to_string(),
                ));
            }

            // 检查是否为自己的子菜单
            let is_child = self.is_child_permission(permission_id, parent_id).await;
            if is_child {
                return Err(BusinessError::new(
                    INVALID_PARAMETER,
                    "上级菜单不能选择当前菜单的子菜单".to_string(),
                ));
            }

            // 如果父菜单ID变化，检查新父菜单是否存在
            if parent_id != permission.parent_id.unwrap_or_default() {
                let parent_exists = self
                    .permission_repository
                    .find_by_id(parent_id, &self.db)
                    .await
                    .map_err(|e| {
                        error!("查询父级菜单失败: {}", e);
                        BusinessError::new(DATABASE_ERROR, "查询父级菜单失败".to_string())
                    })?
                    .is_some();

                if !parent_exists {
                    return Err(BusinessError::new(
                        RESOURCE_NOT_FOUND,
                        "父级菜单不存在".to_string(),
                    ));
                }
            }
        }

        // 检查权限标识是否已存在
        if let Some(ref perms) = req.perms {
            if !perms.trim().is_empty() {
                let exists = self.check_perms_exists(perms, Some(permission_id)).await?;
                if exists {
                    return Err(BusinessError::new(
                        RESOURCE_ALREADY_EXISTS,
                        "权限标识已存在".to_string(),
                    ));
                }
            }
        }

        // 更新权限菜单
        let mut permission_active: ActiveModel = permission.into();
        permission_active.menu_name = Set(req.menu_name);
        permission_active.parent_id = Set(req.parent_id);
        permission_active.order_num = Set(req.order_num);
        permission_active.path = Set(req.path);
        permission_active.component = Set(req.component);
        permission_active.query = Set(req.query);
        permission_active.is_frame = Set(req.is_frame);
        permission_active.is_cache = Set(req.is_cache);
        permission_active.menu_type = Set(req.menu_type);
        permission_active.visible = Set(req.visible);
        permission_active.status = Set(req.status);
        permission_active.perms = Set(req.perms);
        permission_active.icon = Set(req.icon);
        permission_active.updated_date = Set(Local::now());
        permission_active.updated_by = Set(Some(operator_id));
        permission_active.remark = Set(req.remark);

        self.permission_repository
            .update(permission_active, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("更新权限菜单失败: {}", e)))?;

        info!("权限菜单更新成功，ID: {}", permission_id);
        Ok("权限菜单更新成功".to_string())
    }

    /// 删除权限菜单
    pub async fn delete_permission(
        &self,
        permission_id: Uuid,
        _operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("删除权限菜单: {}", permission_id);

        self.check_before_deleting_menu_permissions(permission_id)
            .await?;

        // 删除权限菜单
        self.permission_repository
            .delete_by_id(permission_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除权限菜单失败: {}", e)))?;

        info!("权限菜单删除成功，ID: {}", permission_id);
        Ok("权限菜单删除成功".to_string())
    }

    /// 批量删除权限菜单
    pub async fn batch_delete_permissions(
        &self,
        req: SysPermissionBatchDeleteRequest,
        _operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("批量删除权限菜单: {:?}", req.permission_ids);

        if req.permission_ids.is_empty() {
            return Err(BusinessError::new(
                INVALID_PARAMETER,
                "删除列表不能为空".to_string(),
            ));
        }

        // 第一步：批量查询所有要删除的权限是否存在
        let existing_permissions = self
            .permission_repository
            .find_by_ids(req.permission_ids.clone(), &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询权限菜单失败: {}", e)))?;

        let existing_ids: std::collections::HashSet<Uuid> =
            existing_permissions.iter().map(|p| p.id).collect();

        // 检查是否有不存在的权限ID
        let not_found_ids: Vec<Uuid> = req
            .permission_ids
            .iter()
            .filter(|id| !existing_ids.contains(id))
            .cloned()
            .collect();

        if !not_found_ids.is_empty() {
            warn!("权限菜单不存在: {:?}", not_found_ids);
            return Err(BusinessError::new(
                RESOURCE_NOT_FOUND,
                format!("权限菜单不存在: {:?}", not_found_ids),
            ));
        }

        // 第二步：批量检查是否有子菜单
        let children_count = self
            .permission_repository
            .count_by_parent_ids(req.permission_ids.clone(), &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查子菜单失败: {}", e)))?;

        if children_count > 0 {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "存在子菜单，无法删除".to_string(),
            ));
        }

        // 第三步：批量检查是否有角色使用了这些权限
        let role_permissions_count = self
            .role_permission_repository
            .count_by_permission_ids(req.permission_ids.clone(), &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("检查权限使用情况失败: {}", e))
            })?;

        if role_permissions_count > 0 {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "这些菜单已分配给角色，无法删除".to_string(),
            ));
        }

        // 第四步：批量删除权限菜单
        let deleted_count = self
            .permission_repository
            .delete_by_ids(req.permission_ids.clone(), &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("批量删除权限菜单失败: {}", e))
            })?;

        info!("批量删除权限菜单成功，删除了 {} 条记录", deleted_count);

        Ok(format!("批量删除成功，共删除 {} 个菜单", deleted_count))
    }

    /// 切换权限菜单状态
    pub async fn change_permission_status(
        &self,
        permission_id: Uuid,
        req: SysPermissionStatusRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("切换权限菜单状态: {} -> {}", permission_id, req.status);

        // 检查权限菜单是否存在
        let permission = self
            .permission_repository
            .find_by_id(permission_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询权限菜单失败: {}", e)))?;

        let permission = match permission {
            Some(p) => p,
            None => {
                warn!("权限菜单不存在: {}", permission_id);
                return Err(BusinessError::new(
                    RESOURCE_NOT_FOUND,
                    "权限菜单不存在".to_string(),
                ));
            }
        };

        // 更新状态
        let mut permission_active: ActiveModel = permission.into();
        permission_active.status = Set(Some(req.status));
        permission_active.updated_date = Set(Local::now());
        permission_active.updated_by = Set(Some(operator_id));

        self.permission_repository
            .update(permission_active, &self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("更新权限菜单状态失败: {}", e))
            })?;

        let status_text = if req.status == 0 { "正常" } else { "停用" };

        info!(
            "权限菜单状态切换成功，ID: {}，状态: {}",
            permission_id, status_text
        );
        Ok(format!("菜单状态已切换为{}", status_text))
    }

    /// 获取下拉选择的权限菜单列表
    pub async fn get_permission_select_items(
        &self,
    ) -> Result<Vec<SysPermissionSelectItem>, BusinessError> {
        info!("获取权限菜单选择项");

        let permissions = self
            .permission_repository
            .find_active(&self.db)
            .await
            .map_err(|e| {
                BusinessError::new(DATABASE_ERROR, format!("查询权限菜单列表失败: {}", e))
            })?;

        let select_items = permissions
            .into_iter()
            .map(|perm| SysPermissionSelectItem {
                id: perm.id,
                menu_name: perm.menu_name,
                parent_id: perm.parent_id,
                order_num: perm.order_num,
                menu_type: perm.menu_type,
            })
            .collect();

        Ok(select_items)
    }

    /// 获取用户可访问的路由菜单
    pub async fn get_user_routers(&self, user_id: Uuid) -> Result<Vec<RouterVo>, BusinessError> {
        info!("获取用户路由菜单: {}", user_id);

        // 1. 查询用户拥有的所有权限
        let user_permissions = self
            .permission_repository
            .find_user_permissions(user_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询用户权限失败: {}", e)))?;

        // 如果用户没有任何权限，直接返回空列表
        if user_permissions.is_empty() {
            return Ok(Vec::new());
        }

        // 2. 查询所有菜单（包括目录和菜单，不包括按钮）
        let all_menus = self
            .permission_repository
            .find_by_menu_type_lt(4, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询所有菜单失败: {}", e)))?;

        // 收集所有菜单ID到一个HashMap，用于后续查找
        let mut id_to_menu = std::collections::HashMap::new();
        for menu in &all_menus {
            id_to_menu.insert(menu.id, menu);
        }

        // 3. 构建父子关系映射
        let mut parent_to_children = std::collections::HashMap::new();
        for menu in &all_menus {
            if let Some(parent_id) = menu.parent_id {
                parent_to_children
                    .entry(parent_id)
                    .or_insert_with(Vec::new)
                    .push(menu.id);
            }
        }

        // 4. 找出所有用户有权限的菜单以及它们的所有父菜单
        let mut visible_menu_ids = std::collections::HashSet::new();

        // 先添加用户直接有权限的菜单ID
        for perm in &user_permissions {
            // 只添加目录和菜单类型，不添加按钮
            if perm.menu_type.unwrap_or(0) < 3 && perm.status.unwrap_or(1) == 0 {
                visible_menu_ids.insert(perm.id);

                // 递归添加所有父菜单
                let mut current_menu = perm;
                while let Some(parent_id) = current_menu.parent_id {
                    visible_menu_ids.insert(parent_id);
                    if let Some(parent_menu) = id_to_menu.get(&parent_id) {
                        current_menu = parent_menu;
                    } else {
                        break;
                    }
                }
            }
        }

        // 5. 找出根菜单（无父菜单的顶级菜单）
        let root_menus: Vec<_> = all_menus
            .iter()
            .filter(|menu| menu.parent_id.is_none() && visible_menu_ids.contains(&menu.id))
            .collect();

        // 6. 构建路由树
        let mut root_routers = Vec::new();

        for root_menu in root_menus {
            let hidden = root_menu.visible.unwrap_or(0) == 1;
            let name = root_menu
                .path
                .as_ref()
                .filter(|path| !path.is_empty())
                .map(|path| path.replace("/", "_"));

            // 创建根路由
            let mut router = RouterVo {
                name,
                path: root_menu.path.clone().unwrap_or_default(),
                hidden,
                redirect: None,
                component: root_menu.component.clone(),
                always_show: Some(true), // 根节点通常总是显示
                perms: root_menu.perms.clone(),
                menu_type: root_menu.menu_type,
                meta: RouterMeta {
                    title: root_menu.menu_name.clone(),
                    icon: root_menu.icon.clone(),
                    no_cache: root_menu.is_cache.unwrap_or(0) != 0,
                    link: None,
                },
                children: Some(Box::new(Vec::new())),
            };

            // 递归添加子路由
            self.add_child_routers_filtered(
                &mut router,
                &parent_to_children,
                &id_to_menu,
                &visible_menu_ids,
            );

            root_routers.push(router);
        }

        // 按照菜单的排序字段排序
        root_routers.sort_by(|a, b| {
            let a_menu = all_menus.iter().find(|menu| menu.menu_name == a.meta.title);
            let b_menu = all_menus.iter().find(|menu| menu.menu_name == b.meta.title);

            let a_order = a_menu.and_then(|menu| menu.order_num).unwrap_or(999);
            let b_order = b_menu.and_then(|menu| menu.order_num).unwrap_or(999);

            a_order.cmp(&b_order)
        });

        Ok(root_routers)
    }

    /// 检查权限标识是否存在
    pub async fn check_perms_exists(
        &self,
        perms: &str,
        exclude_permission_id: Option<Uuid>,
    ) -> Result<bool, BusinessError> {
        self.permission_repository
            .exists_by_perms(perms, exclude_permission_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("检查权限标识失败: {}", e)))
    }
}

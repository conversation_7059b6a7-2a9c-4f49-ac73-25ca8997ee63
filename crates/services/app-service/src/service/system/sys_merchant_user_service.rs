use crate::constants::*;
use crate::domain::business::merchants::entities::merchant_users::{
    ActiveModel, MerchantUserStatus,
};
use crate::domain::system::dto::sys_merchant_user_request::{
    SysMerchantUserBatchDeleteRequest, SysMerchantUserCreateRequest, SysMerchantUserPageRequest,
    SysMerchantUserResetPasswordRequest, SysMerchantUserStatusRequest,
    SysMerchantUserUpdateRequest,
};
use crate::domain::system::dto::sys_merchant_user_role_request::SysMerchantUserRoleAssignRequest;
use crate::domain::system::vo::merchant_user_vo::{
    SysMerchantUserBasicResponse, SysMerchantUserDetailResponse, SysMerchantUserGenderStats,
    SysMerchantUserListResponse, SysMerchantUserRoleInfo, SysMerchantUserSelectItem,
    SysMerchantUserStatsResponse,
};
use crate::repository::{
    MerchantRoleRepository, MerchantUserMerchantsRepository, MerchantUserRolesRepository,
    MerchantUsersRepository,
};
use crate::utils;
use chrono::Local;
use lib_core::BusinessError;
use lib_core::app::plugin::service::Service as ServiceTrait;
use lib_core::response::PageData;
use lib_macros::Service;
use sea_orm::{ActiveEnum, ActiveModelBehavior, DatabaseConnection, Set, TransactionTrait};
use tracing::{info, warn};
use uuid::Uuid;

/// 商户用户管理服务实现
///
/// 基于Repository的商户用户管理功能实现
#[derive(Clone, Service)]
pub struct SysMerchantUserService {
    #[inject(component)]
    db: DatabaseConnection,
    #[inject(component)]
    merchant_users_repository: MerchantUsersRepository,
    #[inject(component)]
    merchant_user_roles_repository: MerchantUserRolesRepository,
    #[inject(component)]
    merchant_role_repository: MerchantRoleRepository,
    #[inject(component)]
    merchant_user_merchants_repository: MerchantUserMerchantsRepository,
}

/// 实现SysMerchantUserService特征
impl SysMerchantUserService {
    /// 分页查询商户用户列表实现
    pub async fn page_merchant_users(
        &self,
        req: SysMerchantUserPageRequest,
    ) -> Result<PageData<SysMerchantUserListResponse>, BusinessError> {
        info!("分页查询商户用户列表: {:?}", req);

        let users = self
            .merchant_users_repository
            .page_by_condition(&req, &self.db)
            .await
            .map_err(|e| {
                warn!("分页查询商户用户失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "分页查询商户用户失败".to_string())
            })?;

        // 转换为响应VO
        let response_data = PageData::new(
            users
                .items
                .into_iter()
                .map(|user| SysMerchantUserListResponse::from(user))
                .collect(),
            users.total,
            users.page,
            users.page_size,
        );

        Ok(response_data)
    }

    /// 根据ID查询商户用户详情实现
    pub async fn get_merchant_user_by_id(
        &self,
        user_id: Uuid,
    ) -> Result<SysMerchantUserDetailResponse, BusinessError> {
        info!("查询商户用户详情: {}", user_id);

        // 查询用户基本信息
        let user = self
            .merchant_users_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|e| {
                warn!("查询商户用户失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "查询商户用户详情失败".to_string())
            })?
            .ok_or_else(|| {
                warn!("商户用户不存在: {}", user_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户用户不存在".to_string())
            })?;

        // 查询用户角色关联和角色详情
        let user_role_with_roles = self
            .merchant_user_roles_repository
            .find_with_roles_by_user_id(user_id, &self.db)
            .await
            .map_err(|e| {
                warn!("查询用户角色信息失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "查询用户角色信息失败".to_string())
            })?;

        let mut role_infos = Vec::new();

        // 构建角色信息
        for (_user_role, roles) in user_role_with_roles {
            if let Some(role) = roles.first() {
                let role_info = SysMerchantUserRoleInfo::new(
                    role.id,
                    role.role_name.clone(),
                    role.role_code.clone(),
                    role.role_type.to_value(),
                    role.status.to_value(),
                );
                role_infos.push(role_info);
            }
        }

        let user_detail = SysMerchantUserDetailResponse::from(user).with_roles(role_infos);
        Ok(user_detail)
    }

    /// 创建新商户用户实现
    pub async fn create_merchant_user(
        &self,
        req: SysMerchantUserCreateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("创建商户用户: {}", req.username);

        // 检查用户名是否已存在
        let exists = self
            .merchant_users_repository
            .exists_by_username(&req.username, None, &self.db)
            .await
            .map_err(|e| {
                warn!("检查用户名是否存在失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "检查用户名失败".to_string())
            })?;
        if exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "用户名已存在".to_string(),
            ));
        }

        // 检查手机号是否已存在
        let exists = self
            .merchant_users_repository
            .exists_by_phone(&req.phone, None, &self.db)
            .await
            .map_err(|e| {
                warn!("检查手机号是否存在失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "检查手机号失败".to_string())
            })?;
        if exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "手机号已存在".to_string(),
            ));
        }

        // 检查邮箱是否已存在
        if let Some(email) = &req.email {
            if !email.trim().is_empty() {
                let exists = self
                    .merchant_users_repository
                    .exists_by_email(email, None, &self.db)
                    .await
                    .map_err(|e| {
                        warn!("检查邮箱是否存在失败: {}", e);
                        BusinessError::new(DATABASE_ERROR, "检查邮箱失败".to_string())
                    })?;
                if exists {
                    return Err(BusinessError::new(
                        RESOURCE_ALREADY_EXISTS,
                        "邮箱已存在".to_string(),
                    ));
                }
            }
        }

        // 密码加密
        let hashed_password = utils::hash_password(&req.password)
            .map_err(|_| BusinessError::new(INTERNAL_ERROR, "密码加密失败".to_string()))?;

        // 开启事务
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "创建商户用户失败".to_string()))?;

        // 创建商户用户
        let user_active_model =
            req.to_active_model_with_hashed_password(hashed_password, Some(operator_id));

        let result = self
            .merchant_users_repository
            .create(user_active_model, &txn)
            .await
            .map_err(|e| {
                warn!("创建商户用户失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "创建商户用户失败".to_string())
            })?;

        // 提交事务
        txn.commit().await.map_err(|_| {
            BusinessError::new(DATABASE_ERROR, "创建商户用户事务提交失败".to_string())
        })?;

        info!("商户用户创建成功: {}", result.id);
        Ok(result.id.to_string())
    }

    /// 更新商户用户信息实现
    pub async fn update_merchant_user(
        &self,
        user_id: Uuid,
        req: SysMerchantUserUpdateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("更新商户用户: {}", user_id);

        // 查询用户是否存在
        let existing_user = self
            .merchant_users_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|e| {
                warn!("查询商户用户失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "查询商户用户失败".to_string())
            })?
            .ok_or_else(|| {
                warn!("商户用户不存在: {}", user_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户用户不存在".to_string())
            })?;

        // 检查用户名是否已存在（排除当前用户）
        let exists = self
            .merchant_users_repository
            .exists_by_username(&req.username, Some(user_id), &self.db)
            .await
            .map_err(|e| {
                warn!("检查用户名是否存在失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "检查用户名失败".to_string())
            })?;
        if exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "用户名已存在".to_string(),
            ));
        }

        // 检查手机号是否已存在（排除当前用户）
        let exists = self
            .merchant_users_repository
            .exists_by_phone(&req.phone, Some(user_id), &self.db)
            .await
            .map_err(|e| {
                warn!("检查手机号是否存在失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "检查手机号失败".to_string())
            })?;
        if exists {
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "手机号已存在".to_string(),
            ));
        }

        // 检查邮箱是否已存在（排除当前用户）
        if let Some(email) = &req.email {
            if !email.trim().is_empty() {
                let exists = self
                    .merchant_users_repository
                    .exists_by_email(email, Some(user_id), &self.db)
                    .await
                    .map_err(|e| {
                        warn!("检查邮箱是否存在失败: {}", e);
                        BusinessError::new(DATABASE_ERROR, "检查邮箱失败".to_string())
                    })?;
                if exists {
                    return Err(BusinessError::new(
                        RESOURCE_ALREADY_EXISTS,
                        "邮箱已存在".to_string(),
                    ));
                }
            }
        }

        // 开启事务
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "更新商户用户失败".to_string()))?;

        // 更新用户信息
        let existing_active_model = existing_user.into();
        let updated_active_model =
            req.update_active_model(existing_active_model, Some(operator_id));

        self.merchant_users_repository
            .update(updated_active_model, &txn)
            .await
            .map_err(|e| {
                warn!("更新商户用户失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "更新商户用户失败".to_string())
            })?;

        // 提交事务
        txn.commit().await.map_err(|_| {
            BusinessError::new(DATABASE_ERROR, "更新商户用户事务提交失败".to_string())
        })?;

        info!("商户用户更新成功: {}", user_id);
        Ok("商户用户更新成功".to_string())
    }

    /// 删除商户用户实现
    pub async fn delete_merchant_user(
        &self,
        user_id: Uuid,
        _operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("删除商户用户: {}", user_id);

        // 查询用户是否存在
        let _existing_user = self
            .merchant_users_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|e| {
                warn!("查询商户用户失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "查询商户用户失败".to_string())
            })?
            .ok_or_else(|| {
                warn!("商户用户不存在: {}", user_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户用户不存在".to_string())
            })?;

        // 开启事务
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "删除商户用户失败".to_string()))?;

        // 删除用户
        self.merchant_users_repository
            .delete_by_id(user_id, &txn)
            .await
            .map_err(|e| {
                warn!("删除商户用户失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "删除商户用户失败".to_string())
            })?;

        // 提交事务
        txn.commit().await.map_err(|_| {
            BusinessError::new(DATABASE_ERROR, "删除商户用户事务提交失败".to_string())
        })?;

        info!("商户用户删除成功: {}", user_id);
        Ok("商户用户删除成功".to_string())
    }

    /// 批量删除商户用户实现
    pub async fn batch_delete_merchant_users(
        &self,
        req: SysMerchantUserBatchDeleteRequest,
        _operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("批量删除商户用户: {:?}", req.user_ids);

        if req.user_ids.is_empty() {
            return Err(BusinessError::new(
                INVALID_PARAMETER,
                "用户ID列表不能为空".to_string(),
            ));
        }

        // 开启事务
        let txn =
            self.db.begin().await.map_err(|_| {
                BusinessError::new(DATABASE_ERROR, "批量删除商户用户失败".to_string())
            })?;

        // 批量删除用户
        let deleted_count = self
            .merchant_users_repository
            .delete_by_ids(req.user_ids.clone(), &txn)
            .await
            .map_err(|e| {
                warn!("批量删除商户用户失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "批量删除商户用户失败".to_string())
            })?;

        // 提交事务
        txn.commit().await.map_err(|_| {
            BusinessError::new(DATABASE_ERROR, "批量删除商户用户事务提交失败".to_string())
        })?;

        info!("商户用户批量删除成功，删除数量: {}", deleted_count);
        Ok(format!("成功删除{}个商户用户", deleted_count))
    }

    /// 重置商户用户密码实现
    pub async fn reset_merchant_user_password(
        &self,
        user_id: Uuid,
        req: SysMerchantUserResetPasswordRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("重置商户用户密码: {}", user_id);

        // 查询用户是否存在
        let _existing_user = self
            .merchant_users_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|e| {
                warn!("查询商户用户失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "查询商户用户失败".to_string())
            })?
            .ok_or_else(|| {
                warn!("商户用户不存在: {}", user_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户用户不存在".to_string())
            })?;

        // 密码加密
        let hashed_password = utils::hash_password(&req.new_password)
            .map_err(|_| BusinessError::new(INTERNAL_ERROR, "密码加密失败".to_string()))?;

        // 开启事务
        let txn =
            self.db.begin().await.map_err(|_| {
                BusinessError::new(DATABASE_ERROR, "重置商户用户密码失败".to_string())
            })?;

        // 更新密码
        let mut active_model = ActiveModel::new();
        active_model.id = Set(user_id);
        active_model.password = Set(hashed_password);
        active_model.updated_by = Set(Some(operator_id));
        active_model.updated_date = Set(Local::now().into());

        self.merchant_users_repository
            .update(active_model, &txn)
            .await
            .map_err(|e| {
                warn!("重置商户用户密码失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "重置商户用户密码失败".to_string())
            })?;

        // 提交事务
        txn.commit().await.map_err(|_| {
            BusinessError::new(DATABASE_ERROR, "重置商户用户密码事务提交失败".to_string())
        })?;

        info!("商户用户密码重置成功: {}", user_id);
        Ok("密码重置成功".to_string())
    }

    /// 切换商户用户状态实现
    pub async fn change_merchant_user_status(
        &self,
        user_id: Uuid,
        req: SysMerchantUserStatusRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!("切换商户用户状态: {} -> {}", user_id, req.status);

        // 查询用户是否存在
        let _existing_user = self
            .merchant_users_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|e| {
                warn!("查询商户用户失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "查询商户用户失败".to_string())
            })?
            .ok_or_else(|| {
                warn!("商户用户不存在: {}", user_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户用户不存在".to_string())
            })?;

        // 开启事务
        let txn =
            self.db.begin().await.map_err(|_| {
                BusinessError::new(DATABASE_ERROR, "切换商户用户状态失败".to_string())
            })?;

        // 更新状态
        let mut active_model = ActiveModel::new();
        active_model.id = Set(user_id);
        active_model.status = Set(MerchantUserStatus::try_from_value(&req.status)
            .map_err(|_| BusinessError::new(INVALID_PARAMETER, "无效的用户状态".to_string()))?);

        active_model.updated_by = Set(Some(operator_id));
        active_model.updated_date = Set(Local::now().into());

        self.merchant_users_repository
            .update(active_model, &txn)
            .await
            .map_err(|e| {
                warn!("切换商户用户状态失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "切换商户用户状态失败".to_string())
            })?;

        // 提交事务
        txn.commit().await.map_err(|_| {
            BusinessError::new(DATABASE_ERROR, "切换商户用户状态事务提交失败".to_string())
        })?;

        info!("商户用户状态切换成功: {}", user_id);
        Ok("状态切换成功".to_string())
    }

    /// 获取商户用户选择项实现
    pub async fn get_merchant_user_select_items(
        &self,
    ) -> Result<Vec<SysMerchantUserSelectItem>, BusinessError> {
        info!("获取商户用户选择项");

        let users = self
            .merchant_users_repository
            .find_select_items(&self.db)
            .await
            .map_err(|e| {
                warn!("获取商户用户选择项失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "获取商户用户选择项失败".to_string())
            })?;

        let select_items: Vec<SysMerchantUserSelectItem> = users
            .into_iter()
            .map(|user| SysMerchantUserSelectItem::from(user))
            .collect();

        Ok(select_items)
    }

    /// 获取商户用户基础信息列表实现
    pub async fn get_merchant_user_basic_list(
        &self,
    ) -> Result<Vec<SysMerchantUserBasicResponse>, BusinessError> {
        info!("获取商户用户基础信息列表");

        let users = self
            .merchant_users_repository
            .find_basic_list(&self.db)
            .await
            .map_err(|e| {
                warn!("获取商户用户基础信息失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "获取商户用户基础信息失败".to_string())
            })?;

        let basic_list: Vec<SysMerchantUserBasicResponse> = users
            .into_iter()
            .map(|user| SysMerchantUserBasicResponse::from(user))
            .collect();

        Ok(basic_list)
    }

    /// 获取商户用户统计信息实现
    pub async fn get_merchant_user_stats(
        &self,
    ) -> Result<SysMerchantUserStatsResponse, BusinessError> {
        info!("获取商户用户统计信息");

        // 总用户数
        let total_count = self
            .merchant_users_repository
            .count_total(&self.db)
            .await
            .map_err(|e| {
                warn!("获取商户用户总数失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "获取商户用户总数失败".to_string())
            })?;

        // 启用用户数
        let active_count = self
            .merchant_users_repository
            .count_active(&self.db)
            .await
            .map_err(|e| {
                warn!("获取启用商户用户数失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "获取启用商户用户数失败".to_string())
            })?;

        // 禁用用户数
        let disabled_count = self
            .merchant_users_repository
            .count_disabled(&self.db)
            .await
            .map_err(|e| {
                warn!("获取禁用商户用户数失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "获取禁用商户用户数失败".to_string())
            })?;

        // 锁定用户数
        let locked_count = self
            .merchant_users_repository
            .count_locked(&self.db)
            .await
            .map_err(|e| {
                warn!("获取锁定商户用户数失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "获取锁定商户用户数失败".to_string())
            })?;

        // 按性别统计
        let male_count = self
            .merchant_users_repository
            .count_by_gender(1, &self.db)
            .await
            .map_err(|e| {
                warn!("获取男性用户数失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "获取男性用户数失败".to_string())
            })?;

        let female_count = self
            .merchant_users_repository
            .count_by_gender(2, &self.db)
            .await
            .map_err(|e| {
                warn!("获取女性用户数失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "获取女性用户数失败".to_string())
            })?;

        let unknown_gender_count = self
            .merchant_users_repository
            .count_by_gender(3, &self.db)
            .await
            .map_err(|e| {
                warn!("获取未知性别用户数失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "获取未知性别用户数失败".to_string())
            })?;

        let by_gender = vec![
            SysMerchantUserGenderStats {
                gender: Some(1),
                gender_desc: "男".to_string(),
                user_count: male_count as i32,
            },
            SysMerchantUserGenderStats {
                gender: Some(2),
                gender_desc: "女".to_string(),
                user_count: female_count as i32,
            },
            SysMerchantUserGenderStats {
                gender: Some(3),
                gender_desc: "未知".to_string(),
                user_count: unknown_gender_count as i32,
            },
        ];

        let stats = SysMerchantUserStatsResponse {
            total_count: total_count as i32,
            active_count: active_count as i32,
            disabled_count: disabled_count as i32,
            locked_count: locked_count as i32,
            by_gender,
        };

        Ok(stats)
    }

    /// 检查用户名是否存在实现
    pub async fn check_username_exists(
        &self,
        username: &str,
        exclude_user_id: Option<Uuid>,
    ) -> Result<bool, BusinessError> {
        self.merchant_users_repository
            .exists_by_username(username, exclude_user_id, &self.db)
            .await
            .map_err(|e| {
                warn!("检查用户名是否存在失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "检查用户名是否存在失败".to_string())
            })
    }

    /// 检查真实姓名是否存在实现
    pub async fn check_real_name_exists(
        &self,
        real_name: &str,
        exclude_user_id: Option<Uuid>,
    ) -> Result<bool, BusinessError> {
        self.merchant_users_repository
            .exists_by_real_name(real_name, exclude_user_id, &self.db)
            .await
            .map_err(|e| {
                warn!("检查真实姓名是否存在失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "检查真实姓名是否存在失败".to_string())
            })
    }

    /// 检查手机号是否存在实现
    pub async fn check_phone_exists(
        &self,
        phone: &str,
        exclude_user_id: Option<Uuid>,
    ) -> Result<bool, BusinessError> {
        self.merchant_users_repository
            .exists_by_phone(phone, exclude_user_id, &self.db)
            .await
            .map_err(|e| {
                warn!("检查手机号是否存在失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "检查手机号是否存在失败".to_string())
            })
    }

    /// 检查邮箱是否存在实现
    pub async fn check_email_exists(
        &self,
        email: &str,
        exclude_user_id: Option<Uuid>,
    ) -> Result<bool, BusinessError> {
        self.merchant_users_repository
            .exists_by_email(email, exclude_user_id, &self.db)
            .await
            .map_err(|e| {
                warn!("检查邮箱是否存在失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "检查邮箱是否存在失败".to_string())
            })
    }

    /// 为用户分配角色实现
    pub async fn assign_roles_to_user(
        &self,
        req: SysMerchantUserRoleAssignRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        // 提前克隆需要的值
        let user_id = req.user_id;
        let merchant_id = req.merchant_id;
        let role_ids = req.role_ids.clone();
        let role_count = req.role_ids.len();

        info!(
            "为用户分配角色，用户ID: {}，商户ID: {}，角色: {:?}，操作人: {}",
            user_id, merchant_id, role_ids, operator_id
        );

        // 检查用户是否存在
        let user_exists = self
            .merchant_users_repository
            .find_by_id(user_id, &self.db)
            .await
            .map_err(|e| {
                warn!("查询用户失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "查询用户失败".to_string())
            })?;

        if user_exists.is_none() {
            return Err(BusinessError::new(
                RESOURCE_NOT_FOUND,
                "用户不存在".to_string(),
            ));
        }

        // 批量检查角色是否都存在并属于指定商户
        let existing_roles = self
            .merchant_role_repository
            .find_by_ids_and_merchant(role_ids.clone(), merchant_id, &self.db)
            .await
            .map_err(|e| {
                warn!("批量查询角色失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "批量查询角色失败".to_string())
            })?;

        // 检查所有角色是否都存在且启用
        if existing_roles.len() != role_ids.len() {
            let existing_role_ids: std::collections::HashSet<_> =
                existing_roles.iter().map(|r| r.id).collect();
            let missing_roles: Vec<_> = role_ids
                .iter()
                .filter(|id| !existing_role_ids.contains(id))
                .collect();
            return Err(BusinessError::new(
                RESOURCE_NOT_FOUND,
                &format!("以下角色不存在或不属于指定商户: {:?}", missing_roles),
            ));
        }

        // 检查所有角色是否都启用
        let disabled_roles: Vec<_> = existing_roles.iter()
            .filter(|role| role.status != crate::domain::business::merchants::entities::merchant_roles::MerchantRoleStatus::Enabled)
            .map(|role| role.id)
            .collect();

        if !disabled_roles.is_empty() {
            return Err(BusinessError::new(
                RESOURCE_NOT_FOUND,
                &format!("以下角色已禁用: {:?}", disabled_roles),
            ));
        }

        // 开启事务
        let txn = self
            .db
            .begin()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "开启事务失败".to_string()))?;

        // 获取用户商户关系ID
        let user_merchant_id = self
            .merchant_user_merchants_repository
            .get_user_merchant_relation_id(user_id, merchant_id, &txn)
            .await
            .map_err(|e| {
                warn!("获取用户商户关系失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "获取用户商户关系失败".to_string())
            })?
            .ok_or_else(|| {
                warn!(
                    "用户不属于该商户: user_id={}, merchant_id={}",
                    user_id, merchant_id
                );
                BusinessError::new(RESOURCE_NOT_FOUND, "用户不属于该商户".to_string())
            })?;

        // 删除用户在该商户的现有角色分配
        self.merchant_user_roles_repository
            .delete_by_user_and_merchant(user_id, merchant_id, &txn)
            .await
            .map_err(|e| {
                warn!("删除现有角色分配失败: {}", e);
                BusinessError::new(DATABASE_ERROR, "删除现有角色分配失败".to_string())
            })?;

        // 批量插入新的角色分配
        if !role_ids.is_empty() {
            let role_assignments = req
                .to_active_models_with_user_merchant_id(user_merchant_id, Some(operator_id))
                .map_err(|e| {
                    warn!("构建角色分配数据失败: {}", e);
                    BusinessError::new(INTERNAL_ERROR, "构建角色分配数据失败".to_string())
                })?;

            // 使用批量插入提高性能
            if !role_assignments.is_empty() {
                self.merchant_user_roles_repository
                    .insert_many(role_assignments, &txn)
                    .await
                    .map_err(|e| {
                        warn!("批量插入角色分配失败: {}", e);
                        BusinessError::new(DATABASE_ERROR, "批量插入角色分配失败".to_string())
                    })?;
            }
        }

        // 提交事务
        txn.commit()
            .await
            .map_err(|_| BusinessError::new(DATABASE_ERROR, "提交事务失败".to_string()))?;

        info!(
            "用户角色分配成功，用户ID: {}，分配角色数: {}",
            user_id, role_count
        );

        Ok(format!("成功为用户分配{}个角色", role_count))
    }
}

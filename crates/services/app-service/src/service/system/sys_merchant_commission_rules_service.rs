//! 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
//! Generated by lib-codegen

use crate::constants::error_codes::{
    DATABASE_ERROR, INVALID_PARAMETER, RESOURCE_ALREADY_EXISTS, RESOURCE_NOT_FOUND,
};
use crate::domain::business::merchants::entities::merchant_commission_rules::ActiveModel;
use crate::domain::system::dto::merchant_commission_rules_request::{
    MerchantCommissionRulesBatchDeleteRequest, MerchantCommissionRulesCreateRequest,
    MerchantCommissionRulesListRequest, MerchantCommissionRulesUpdateRequest,
};
use crate::domain::system::vo::merchant_commission_rules_vo::{
    MerchantCommissionRulesDetailResponse, MerchantCommissionRulesListResponse,
    MerchantCommissionRulesSelectItem, MerchantCommissionRulesStatsResponse,
};
use crate::repository::MerchantCommissionRulesRepository;
use lib_core::BusinessError;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_macros::Service;
use rust_decimal::Decimal;
use sea_orm::{DatabaseConnection, Set};
use tracing::{error, info, warn};
use uuid::Uuid;
use validator::Validate;

/// 商户门店佣金规则服务实现
///
/// 提供商户门店佣金规则的业务逻辑处理，包括：
/// - 基础CRUD操作
/// - 佣金规则管理
/// - 佣金计算逻辑
/// - 业务验证
#[derive(Clone, Service)]
pub struct MerchantCommissionRulesService {
    #[inject(component)]
    db: DatabaseConnection,
    #[inject(component)]
    merchant_commission_rules_repository: MerchantCommissionRulesRepository,
}

impl MerchantCommissionRulesService {
    // ==================== 基础CRUD操作 ====================

    /// 根据ID查询佣金规则
    pub async fn find_by_id(
        &self,
        rule_id: Uuid,
    ) -> Result<Option<MerchantCommissionRulesDetailResponse>, BusinessError> {
        info!("查询佣金规则详情，ID: {}", rule_id);
        let model = self
            .merchant_commission_rules_repository
            .find_by_id(rule_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询佣金规则失败，ID: {}，错误: {}", rule_id, e);
                BusinessError::new(DATABASE_ERROR, format!("查询佣金规则失败: {}", e))
            })?;

        let result = model.map(|m| m.into());
        if result.is_some() {
            info!("查询佣金规则成功，ID: {}", rule_id);
        } else {
            warn!("佣金规则不存在，ID: {}", rule_id);
        }
        Ok(result)
    }

    /// 创建佣金规则
    pub async fn create(
        &self,
        request: MerchantCommissionRulesCreateRequest,
        created_by: Uuid,
    ) -> Result<MerchantCommissionRulesDetailResponse, BusinessError> {
        info!(
            "创建佣金规则，商户ID: {}，规则名称: {}，操作人: {}",
            request.merchant_id, &request.rule_name, created_by
        );
        // 验证请求数据
        request.validate().map_err(|e| {
            error!("佣金规则数据验证失败，错误: {}", e);
            BusinessError::new(INVALID_PARAMETER, format!("佣金规则数据验证失败: {}", e))
        })?;

        // 检查规则名称是否已存在
        let name_exists = self
            .merchant_commission_rules_repository
            .exists_rule_name(request.merchant_id, &request.rule_name, None, &self.db)
            .await
            .map_err(|e| {
                error!("检查规则名称是否存在失败，错误: {}", e);
                BusinessError::new(DATABASE_ERROR, format!("检查规则名称是否存在失败: {}", e))
            })?;

        if name_exists {
            error!(
                "规则名称已存在，商户ID: {}，规则名称: {}",
                request.merchant_id, &request.rule_name
            );
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "规则名称已存在".to_string(),
            ));
        }

        // 转换阶梯规则
        let tier_rules = request.tier_rules.map(|tr| tr.into());

        // 构建ActiveModel
        let active_model = ActiveModel {
            id: Set(Uuid::now_v7()),
            merchant_id: Set(request.merchant_id),
            rule_name: Set(request.rule_name.clone()),
            base_commission_rate: Set(request.base_commission_rate),
            tier_rules: Set(tier_rules),
            created_by: Set(Some(created_by)),
            updated_by: Set(Some(created_by)),
            remark: Set(request.remark),
            ..Default::default()
        };

        let model = self
            .merchant_commission_rules_repository
            .create(active_model, &self.db)
            .await
            .map_err(|e| {
                error!("创建佣金规则失败，错误: {}", e);
                BusinessError::new(DATABASE_ERROR, format!("创建佣金规则失败: {}", e))
            })?;

        let result = model.into();
        info!(
            "创建佣金规则成功，商户ID: {}，规则名称: {}，操作人: {}",
            request.merchant_id, &request.rule_name, created_by
        );
        Ok(result)
    }

    /// 更新佣金规则
    pub async fn update(
        &self,
        rule_id: Uuid,
        request: MerchantCommissionRulesUpdateRequest,
        updated_by: Uuid,
    ) -> Result<MerchantCommissionRulesDetailResponse, BusinessError> {
        info!("更新佣金规则，ID: {}，操作人: {}", rule_id, updated_by);
        // 验证请求数据
        request.validate().map_err(|e| {
            error!("佣金规则数据验证失败，错误: {}", e);
            BusinessError::new(INVALID_PARAMETER, format!("佣金规则数据验证失败: {}", e))
        })?;

        // 检查规则是否存在
        let existing = self
            .merchant_commission_rules_repository
            .find_by_id(rule_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询佣金规则失败，ID: {}，错误: {}", rule_id, e);
                BusinessError::new(DATABASE_ERROR, format!("查询佣金规则失败: {}", e))
            })?
            .ok_or_else(|| {
                warn!("佣金规则不存在，ID: {}", rule_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "佣金规则不存在".to_string())
            })?;

        // 检查规则名称是否冲突
        if let Some(ref rule_name) = request.rule_name {
            let name_exists = self
                .merchant_commission_rules_repository
                .exists_rule_name(existing.merchant_id, rule_name, Some(rule_id), &self.db)
                .await
                .map_err(|e| {
                    error!("检查规则名称是否存在失败，错误: {}", e);
                    BusinessError::new(DATABASE_ERROR, format!("检查规则名称是否存在失败: {}", e))
                })?;

            if name_exists {
                error!(
                    "规则名称已存在，商户ID: {}，规则名称: {}",
                    existing.merchant_id, rule_name
                );
                return Err(BusinessError::new(
                    RESOURCE_ALREADY_EXISTS,
                    "规则名称已存在".to_string(),
                ));
            }
        }

        // 构建ActiveModel
        let mut active_model = ActiveModel {
            id: Set(rule_id),
            updated_by: Set(Some(updated_by)),
            ..Default::default()
        };

        // 设置更新字段
        if let Some(rule_name) = request.rule_name {
            active_model.rule_name = Set(rule_name);
        }
        if let Some(base_commission_rate) = request.base_commission_rate {
            active_model.base_commission_rate = Set(base_commission_rate);
        }
        if let Some(tier_rules) = request.tier_rules {
            active_model.tier_rules = Set(Some(tier_rules.into()));
        }
        if let Some(remark) = request.remark {
            active_model.remark = Set(Some(remark));
        }

        let model = self
            .merchant_commission_rules_repository
            .update(active_model, &self.db)
            .await
            .map_err(|e| {
                error!("更新佣金规则失败，ID: {}，错误: {}", rule_id, e);
                BusinessError::new(DATABASE_ERROR, format!("更新佣金规则失败: {}", e))
            })?;

        let result = model.into();
        info!("更新佣金规则成功，ID: {}，操作人: {}", rule_id, updated_by);
        Ok(result)
    }

    /// 删除佣金规则
    pub async fn delete(&self, rule_id: Uuid) -> Result<(), BusinessError> {
        info!("删除佣金规则，ID: {}", rule_id);
        // 检查规则是否存在
        let existing = self
            .merchant_commission_rules_repository
            .find_by_id(rule_id, &self.db)
            .await
            .map_err(|e| {
                error!("查询佣金规则失败，ID: {}，错误: {}", rule_id, e);
                BusinessError::new(DATABASE_ERROR, format!("查询佣金规则失败: {}", e))
            })?;

        if existing.is_none() {
            warn!("佣金规则不存在，ID: {}", rule_id);
            return Err(BusinessError::new(
                RESOURCE_NOT_FOUND,
                "佣金规则不存在".to_string(),
            ));
        }

        self.merchant_commission_rules_repository
            .delete_by_id(rule_id, &self.db)
            .await
            .map_err(|e| {
                error!("删除佣金规则失败，ID: {}，错误: {}", rule_id, e);
                BusinessError::new(DATABASE_ERROR, format!("删除佣金规则失败: {}", e))
            })?;

        info!("删除佣金规则成功，ID: {}", rule_id);
        Ok(())
    }

    /// 批量删除佣金规则
    pub async fn batch_delete(
        &self,
        request: MerchantCommissionRulesBatchDeleteRequest,
    ) -> Result<u64, BusinessError> {
        info!("批量删除佣金规则，数量: {}", request.ids.len());
        request.validate().map_err(|e| {
            error!("批量删除请求验证失败，错误: {}", e);
            BusinessError::new(INVALID_PARAMETER, format!("批量删除请求验证失败: {}", e))
        })?;

        let deleted_count = self
            .merchant_commission_rules_repository
            .delete_by_ids(request.ids.clone(), &self.db)
            .await
            .map_err(|e| {
                error!("批量删除佣金规则失败，错误: {}", e);
                BusinessError::new(DATABASE_ERROR, format!("批量删除佣金规则失败: {}", e))
            })?;

        info!("批量删除佣金规则成功，删除数量: {}", deleted_count);
        Ok(deleted_count)
    }

    // ==================== 查询操作 ====================

    /// 查询商户佣金规则列表
    pub async fn list(
        &self,
        request: MerchantCommissionRulesListRequest,
    ) -> Result<Vec<MerchantCommissionRulesListResponse>, BusinessError> {
        info!("查询商户佣金规则列表，请求: {:?}", request);
        request.validate().map_err(|e| {
            error!("列表查询请求验证失败，错误: {}", e);
            BusinessError::new(INVALID_PARAMETER, format!("列表查询请求验证失败: {}", e))
        })?;

        let models = if let Some(ref rule_name) = request.rule_name {
            self.merchant_commission_rules_repository
                .find_by_rule_name_like(request.merchant_id, rule_name, &self.db)
                .await
                .map_err(|e| {
                    error!("按规则名称查询佣金规则失败，错误: {}", e);
                    BusinessError::new(DATABASE_ERROR, format!("按规则名称查询佣金规则失败: {}", e))
                })?
        } else if let Some(merchant_id) = request.merchant_id {
            self.merchant_commission_rules_repository
                .find_by_merchant_id(merchant_id, &self.db)
                .await
                .map_err(|e| {
                    error!("按商户ID查询佣金规则失败，错误: {}", e);
                    BusinessError::new(DATABASE_ERROR, format!("按商户ID查询佣金规则失败: {}", e))
                })?
        } else {
            self.merchant_commission_rules_repository
                .find_all(&self.db)
                .await
                .map_err(|e| {
                    error!("查询所有佣金规则失败，错误: {}", e);
                    BusinessError::new(DATABASE_ERROR, format!("查询所有佣金规则失败: {}", e))
                })?
        };

        let result: Vec<MerchantCommissionRulesListResponse> =
            models.into_iter().map(|model| model.into()).collect();
        info!("查询商户佣金规则列表成功，数量: {}", result.len());
        Ok(result)
    }

    /// 获取商户佣金规则选择项
    pub async fn get_select_items(
        &self,
        merchant_id: i64,
    ) -> Result<Vec<MerchantCommissionRulesSelectItem>, BusinessError> {
        info!("获取商户佣金规则选择项，商户ID: {}", merchant_id);
        let models = self
            .merchant_commission_rules_repository
            .find_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| {
                error!(
                    "查询商户佣金规则选择项失败，商户ID: {}，错误: {}",
                    merchant_id, e
                );
                BusinessError::new(DATABASE_ERROR, format!("查询商户佣金规则选择项失败: {}", e))
            })?;

        let result: Vec<MerchantCommissionRulesSelectItem> =
            models.into_iter().map(|model| model.into()).collect();
        info!(
            "获取商户佣金规则选择项成功，商户ID: {}，数量: {}",
            merchant_id,
            result.len()
        );
        Ok(result)
    }

    /// 获取商户佣金规则统计
    pub async fn get_stats(
        &self,
        merchant_id: Option<i64>,
    ) -> Result<MerchantCommissionRulesStatsResponse, BusinessError> {
        info!("获取商户佣金规则统计，商户ID: {:?}", merchant_id);
        let models = if let Some(merchant_id) = merchant_id {
            self.merchant_commission_rules_repository
                .find_by_merchant_id(merchant_id, &self.db)
                .await
                .map_err(|e| {
                    error!(
                        "查询商户佣金规则统计失败，商户ID: {}，错误: {}",
                        merchant_id, e
                    );
                    BusinessError::new(DATABASE_ERROR, format!("查询商户佣金规则统计失败: {}", e))
                })?
        } else {
            self.merchant_commission_rules_repository
                .find_all(&self.db)
                .await
                .map_err(|e| {
                    error!("查询所有佣金规则统计失败，错误: {}", e);
                    BusinessError::new(DATABASE_ERROR, format!("查询所有佣金规则统计失败: {}", e))
                })?
        };

        let total_rules = models.len() as u64;
        let tier_rules_count = models.iter().filter(|m| m.tier_rules.is_some()).count() as u64;
        let basic_only_count = total_rules - tier_rules_count;

        let (avg_rate, max_rate, min_rate) = if !models.is_empty() {
            let rates: Vec<Decimal> = models.iter().map(|m| m.base_commission_rate).collect();
            let sum: Decimal = rates.iter().sum();
            let avg = sum / Decimal::from(rates.len());
            let max = rates.iter().max().cloned().unwrap_or_default();
            let min = rates.iter().min().cloned().unwrap_or_default();
            (avg, max, min)
        } else {
            (Decimal::ZERO, Decimal::ZERO, Decimal::ZERO)
        };

        let result = MerchantCommissionRulesStatsResponse {
            total_rules,
            tier_rules_count,
            basic_only_count,
            avg_base_commission_rate: avg_rate.to_string(),
            max_base_commission_rate: max_rate.to_string(),
            min_base_commission_rate: min_rate.to_string(),
        };
        info!(
            "获取商户佣金规则统计成功，商户ID: {:?}，总数: {}",
            merchant_id, total_rules
        );
        Ok(result)
    }

    // ==================== 业务逻辑方法 ====================

    /// 为商户创建默认佣金规则
    pub async fn create_default_rule(
        &self,
        merchant_id: i64,
        base_rate: Decimal,
        created_by: Uuid,
    ) -> Result<MerchantCommissionRulesDetailResponse, BusinessError> {
        info!(
            "为商户创建默认佣金规则，商户ID: {}，基础费率: {}，操作人: {}",
            merchant_id, base_rate, created_by
        );
        // 检查是否已有佣金规则
        let exists = self
            .merchant_commission_rules_repository
            .exists_by_merchant_id(merchant_id, &self.db)
            .await
            .map_err(|e| {
                error!(
                    "检查商户是否已有佣金规则失败，商户ID: {}，错误: {}",
                    merchant_id, e
                );
                BusinessError::new(
                    DATABASE_ERROR,
                    format!("检查商户是否已有佣金规则失败: {}", e),
                )
            })?;

        if exists {
            error!("商户已存在佣金规则，商户ID: {}", merchant_id);
            return Err(BusinessError::new(
                RESOURCE_ALREADY_EXISTS,
                "商户已存在佣金规则".to_string(),
            ));
        }

        let model = self
            .merchant_commission_rules_repository
            .create_default_rule(merchant_id, base_rate, created_by, &self.db)
            .await
            .map_err(|e| {
                error!("创建默认佣金规则失败，商户ID: {}，错误: {}", merchant_id, e);
                BusinessError::new(DATABASE_ERROR, format!("创建默认佣金规则失败: {}", e))
            })?;

        let result = model.into();
        info!(
            "为商户创建默认佣金规则成功，商户ID: {}，操作人: {}",
            merchant_id, created_by
        );
        Ok(result)
    }
}

use crate::domain::comm::storage::{
    FailedUploadInfo, FileUploadInfo, MultiFileUploadResult, UploadResponse,
};
use crate::utils::FileUtil;
use anyhow::Result;
use axum::body::Bytes;
use futures::future::join_all;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_macros::Service;
use lib_store::config::StorageConfig;
use lib_store::object::object::ObjectOperations;
use lib_store::object::object_common::PutObjectOptionsBuilder;
use lib_store::Client;

#[derive(Clone, Service)]
pub struct StorageService {
    #[inject(component)]
    client: Client,
    #[inject(config)]
    config: StorageConfig,
}

impl StorageService {
    /// 上传文件
    ///
    /// # 参数
    /// * `file_data` - 文件二进制数据
    /// * `file_name` - 文件名
    /// * `content_type` - 内容类型（可选，如果不提供会根据文件扩展名推断）
    /// * `path_prefix` - 文件路径前缀（可选，如 "system/users"）
    /// * `bucket_name` - 存储桶名称（可选，默认使用配置中的桶）
    pub async fn upload_file(
        &self,
        file_data: Bytes,
        file_name: String,
        content_type: Option<String>,
        path_prefix: Option<String>,
        bucket_name: Option<String>,
    ) -> Result<UploadResponse> {
        // 生成存储路径，使用路径前缀
        let storage_path = self.generate_storage_path(&file_name, path_prefix.as_deref());

        // 确定内容类型
        let content_type = content_type.unwrap_or_else(|| FileUtil::get_content_type(&file_name));

        // 确定存储桶名称 - 优先使用参数，否则使用配置中的默认桶
        let bucket_name =
            bucket_name.unwrap_or_else(|| self.config.default_bucket_name().to_string());

        // 构建上传选项
        let options = PutObjectOptionsBuilder::new()
            .mime_type(content_type.clone())
            .build();

        // 上传文件到OSS
        let _result = self
            .client
            .put_object_from_buffer(
                &bucket_name,
                &storage_path,
                file_data.to_vec(),
                Some(options),
            )
            .await?;

        // 获取文件大小
        let file_size = file_data.len() as u64;

        // 构建文件访问URL - 使用配置的便捷方法
        let file_url = self
            .config
            .build_best_url(Some(&bucket_name), &storage_path, Some(true));

        Ok(UploadResponse {
            file_url,
            file_path: storage_path,
            file_size,
            content_type,
        })
    }

    /// 批量上传多个文件（并发上传）
    ///
    /// # 参数
    /// * `files` - 文件信息列表
    /// * `path_prefix` - 文件路径前缀（可选）
    /// * `bucket_name` - 存储桶名称（可选）
    /// * `max_concurrent` - 最大并发数（可选，默认为 10）
    pub async fn upload_multiple_files(
        &self,
        files: Vec<FileUploadInfo>,
        path_prefix: Option<String>,
        bucket_name: Option<String>,
        _max_concurrent: Option<usize>,
    ) -> Result<MultiFileUploadResult> {
        let total_files = files.len();
        let total_size = files.iter().map(|f| f.file_size as u64).sum();

        // 确定存储桶名称
        let bucket_name =
            bucket_name.unwrap_or_else(|| self.config.default_bucket_name().to_string());

        // 创建并发上传任务
        let upload_tasks: Vec<_> = files
            .into_iter()
            .map(|file_info| {
                let client = self.client.clone();
                let config = self.config.clone();
                let bucket_name = bucket_name.clone();
                let path_prefix = path_prefix.clone();

                async move {
                    // 生成存储路径
                    let storage_path = Self::generate_storage_path_static(
                        &file_info.file_name,
                        path_prefix.as_deref(),
                    );

                    // 确定内容类型
                    let content_type = file_info
                        .content_type
                        .unwrap_or_else(|| FileUtil::get_content_type(&file_info.file_name));

                    // 构建上传选项
                    let options = PutObjectOptionsBuilder::new()
                        .mime_type(content_type.clone())
                        .build();

                    // 执行上传
                    match client
                        .put_object_from_buffer(
                            &bucket_name,
                            &storage_path,
                            file_info.file_data.to_vec(),
                            Some(options),
                        )
                        .await
                    {
                        Ok(_) => {
                            // 构建文件访问URL
                            let file_url = config.build_best_url(
                                Some(&bucket_name),
                                &storage_path,
                                Some(true),
                            );

                            Ok(UploadResponse {
                                file_url,
                                file_path: storage_path,
                                file_size: file_info.file_size as u64,
                                content_type,
                            })
                        }
                        Err(e) => Err(FailedUploadInfo {
                            file_name: file_info.file_name,
                            error: e.to_string(),
                            file_size: file_info.file_size,
                        }),
                    }
                }
            })
            .collect();

        // 执行并发上传（使用 join_all 进行并发处理）
        let results = join_all(upload_tasks).await;

        // 分离成功和失败的结果
        let mut successful_uploads = Vec::new();
        let mut failed_uploads = Vec::new();

        for result in results {
            match result {
                Ok(upload_response) => successful_uploads.push(upload_response),
                Err(failed_info) => failed_uploads.push(failed_info),
            }
        }

        let successful_count = successful_uploads.len();
        let failed_count = failed_uploads.len();

        Ok(MultiFileUploadResult {
            successful_uploads,
            failed_uploads,
            total_files,
            successful_count,
            failed_count,
            total_size,
        })
    }

    /// 使用指定路径上传文件
    ///
    /// # 参数
    /// * `file_data` - 文件二进制数据
    /// * `file_name` - 文件名
    /// * `storage_path` - 完整的存储路径（对象键）
    /// * `content_type` - 内容类型（可选）
    /// * `bucket_name` - 存储桶名称（可选）
    pub async fn upload_file_with_path(
        &self,
        _file_data: Bytes,
        _file_name: String,
        _storage_path: String,
        _content_type: Option<String>,
        _bucket_name: Option<String>,
    ) -> Result<UploadResponse> {
        // 上传文件到指定路径的逻辑
        todo!()
    }

    /// 从Base64字符串上传文件
    ///
    /// # 参数
    /// * `base64_data` - Base64编码的文件数据
    /// * `file_name` - 文件名
    /// * `content_type` - 内容类型（可选）
    /// * `path_prefix` - 文件路径前缀（可选）
    /// * `bucket_name` - 存储桶名称（可选）
    pub async fn upload_file_from_base64(
        &self,
        _base64_data: String,
        _file_name: String,
        _content_type: Option<String>,
        _path_prefix: Option<String>,
        _bucket_name: Option<String>,
    ) -> Result<UploadResponse> {
        // 从Base64字符串上传文件的逻辑
        todo!()
    }

    /// 删除文件
    ///
    /// # 参数
    /// * `file_path` - 文件路径（对象键）
    /// * `bucket_name` - 存储桶名称（可选）
    pub async fn delete_file(
        &self,
        _file_path: String,
        _bucket_name: Option<String>,
    ) -> Result<bool> {
        // 删除文件的逻辑
        todo!()
    }

    /// 生成存储路径
    ///
    /// # 参数
    /// * `file_name` - 文件名
    /// * `prefix` - 路径前缀（可选）
    pub fn generate_storage_path(&self, file_name: &str, prefix: Option<&str>) -> String {
        let extension = FileUtil::get_extension(file_name);
        FileUtil::generate_file_path(prefix, extension.as_deref())
    }

    /// 静态方法：生成存储路径（用于并发上传任务）
    ///
    /// # 参数
    /// * `file_name` - 文件名
    /// * `prefix` - 路径前缀（可选）
    fn generate_storage_path_static(file_name: &str, prefix: Option<&str>) -> String {
        let extension = FileUtil::get_extension(file_name);
        FileUtil::generate_file_path(prefix, extension.as_deref())
    }

    /// 批量删除文件
    ///
    /// # 参数
    /// * `file_paths` - 文件路径列表
    /// * `bucket_name` - 存储桶名称（可选）
    pub async fn batch_delete_files(
        &self,
        _file_paths: Vec<String>,
        _bucket_name: Option<String>,
    ) -> Result<(u32, u32)> {
        // 批量删除文件的逻辑，返回(成功数量, 失败数量)
        todo!()
    }

    /// 检查文件是否存在
    ///
    /// # 参数
    /// * `file_path` - 文件路径（对象键）
    /// * `bucket_name` - 存储桶名称（可选）
    pub async fn file_exists(&self, file_path: &str, bucket_name: Option<String>) -> Result<bool> {
        let bucket_name =
            bucket_name.unwrap_or_else(|| self.config.default_bucket_name().to_string());

        // 使用 exists 方法检查文件是否存在
        match self.client.exists(&bucket_name, file_path, None).await {
            Ok(exists) => Ok(exists),
            Err(e) => Err(anyhow::anyhow!("检查文件存在性失败: {}", e)),
        }
    }

    /// 生成文件临时上传凭证
    ///
    /// # 参数
    /// * `path_prefix` - 路径前缀（可选）
    /// * `expires_in` - 凭证有效期（秒）
    /// * `bucket_name` - 存储桶名称（可选）
    pub async fn generate_upload_credentials(
        &self,
        _path_prefix: Option<String>,
        _expires_in: Option<u64>,
        _bucket_name: Option<String>,
    ) -> Result<serde_json::Value> {
        // 生成临时上传凭证的逻辑
        todo!()
    }

    /// 创建文件夹
    ///
    /// # 参数
    /// * `folder_path` - 文件夹路径
    /// * `bucket_name` - 存储桶名称（可选）
    pub async fn create_folder(
        &self,
        _folder_path: String,
        _bucket_name: Option<String>,
    ) -> Result<bool> {
        // 创建文件夹的逻辑
        todo!()
    }
}

/// 业务错误状态码常量
///
/// 定义了业务层面的自定义错误状态码，用于 BusinessError
///
/// 错误码分类：
/// - 10000-10999: 用户相关错误
/// - 20000-20999: 认证授权错误  
/// - 30000-30999: 数据验证错误
/// - 40000-40999: 业务规则错误
/// - 50000-50999: 资源错误
/// - 90000-90999: 系统级错误

// ========== 用户相关错误 (10000-10999) ==========
/// 用户不存在
pub const USER_NOT_FOUND: i32 = 10001;
/// 用户已存在
pub const USER_ALREADY_EXISTS: i32 = 10002;
/// 账号或密码错误
pub const INVALID_CREDENTIALS: i32 = 10003;
/// 账号已被禁用
pub const ACCOUNT_DISABLED: i32 = 10004;
/// 旧密码错误
pub const OLD_PASSWORD_INCORRECT: i32 = 10005;
/// 新旧密码相同
pub const SAME_PASSWORD: i32 = 10006;
/// 用户名已被占用
pub const USERNAME_TAKEN: i32 = 10007;
/// 邮箱已被注册
pub const EMAIL_TAKEN: i32 = 10008;
/// 手机号已被注册
pub const PHONE_TAKEN: i32 = 10009;

// ========== 认证授权错误 (20000-20999) ==========
/// 未登录
pub const UNAUTHENTICATED: i32 = 20001;
/// Token已过期
pub const TOKEN_EXPIRED: i32 = 20002;
/// Token无效
pub const TOKEN_INVALID: i32 = 20003;
/// 权限不足
pub const PERMISSION_DENIED: i32 = 20004;
/// 访问被拒绝
pub const ACCESS_DENIED: i32 = 20005;
/// 刷新Token失效
pub const REFRESH_TOKEN_INVALID: i32 = 20006;
/// 登录会话已过期
pub const SESSION_EXPIRED: i32 = 20007;
/// 账号被锁定
pub const ACCOUNT_LOCKED: i32 = 20008;

// ========== 数据验证错误 (30000-30999) ==========
/// 参数无效
pub const INVALID_PARAMETER: i32 = 30001;
/// 数据格式错误
pub const INVALID_FORMAT: i32 = 30002;
/// 必填字段缺失
pub const REQUIRED_FIELD_MISSING: i32 = 30003;
/// 数据长度超限
pub const DATA_TOO_LONG: i32 = 30004;
/// 数据重复
pub const DUPLICATE_DATA: i32 = 30005;
/// 参数值超出范围
pub const PARAMETER_OUT_OF_RANGE: i32 = 30006;
/// 数据格式不匹配
pub const FORMAT_MISMATCH: i32 = 30007;
/// 文件大小超限
pub const FILE_TOO_LARGE: i32 = 30008;
/// 文件类型不支持
pub const UNSUPPORTED_FILE_TYPE: i32 = 30009;

// ========== 业务规则错误 (40000-40999) ==========
/// 操作冲突
pub const OPERATION_CONFLICT: i32 = 40001;
/// 状态不允许
pub const INVALID_STATE: i32 = 40002;
/// 业务规则违反
pub const BUSINESS_RULE_VIOLATION: i32 = 40003;
/// 余额不足
pub const INSUFFICIENT_BALANCE: i32 = 40004;
/// 库存不足
pub const INSUFFICIENT_STOCK: i32 = 40005;
/// 订单状态错误
pub const INVALID_ORDER_STATUS: i32 = 40006;
/// 已达到最大限制
pub const LIMIT_EXCEEDED: i32 = 40007;
/// 操作时间窗口已过
pub const OPERATION_TIMEOUT: i32 = 40008;
/// 重复操作
pub const DUPLICATE_OPERATION: i32 = 40009;
/// 操作部分失败
pub const OPERATION_PARTIAL_FAILURE: i32 = 40010;

// ========== 资源错误 (50000-50999) ==========
/// 资源不存在
pub const RESOURCE_NOT_FOUND: i32 = 50001;
/// 资源已存在
pub const RESOURCE_ALREADY_EXISTS: i32 = 50002;
/// 资源已删除
pub const RESOURCE_DELETED: i32 = 50003;
/// 资源不可用
pub const RESOURCE_UNAVAILABLE: i32 = 50004;
/// 文件不存在
pub const FILE_NOT_FOUND: i32 = 50005;
/// 目录不存在
pub const DIRECTORY_NOT_FOUND: i32 = 50006;
/// 资源被占用
pub const RESOURCE_OCCUPIED: i32 = 50007;
/// 存储空间不足
pub const STORAGE_INSUFFICIENT: i32 = 50008;

// ========== 发票业务错误 (60000-60999) ==========
/// 发票已开具
pub const INVOICE_ALREADY_ISSUED: i32 = 60001;
/// 发票状态错误
pub const INVALID_INVOICE_STATUS: i32 = 60002;
/// 发票金额错误
pub const INVALID_INVOICE_AMOUNT: i32 = 60003;
/// 发票税率错误
pub const INVALID_TAX_RATE: i32 = 60004;
/// 商户未认证
pub const MERCHANT_NOT_VERIFIED: i32 = 60005;
/// 发票额度不足
pub const INVOICE_QUOTA_INSUFFICIENT: i32 = 60006;
/// 发票模板不存在
pub const INVOICE_TEMPLATE_NOT_FOUND: i32 = 60007;
/// 发票已作废
pub const INVOICE_VOIDED: i32 = 60008;
/// 开票权限不足
pub const INVOICE_PERMISSION_DENIED: i32 = 60009;

// ========== 第三方服务错误 (80000-80999) ==========
/// 支付服务错误
pub const PAYMENT_SERVICE_ERROR: i32 = 80001;
/// 短信服务错误
pub const SMS_SERVICE_ERROR: i32 = 80002;
/// 邮件服务错误
pub const EMAIL_SERVICE_ERROR: i32 = 80003;
/// 税务接口错误
pub const TAX_API_ERROR: i32 = 80004;
/// 银行接口错误
pub const BANK_API_ERROR: i32 = 80005;
/// 实名认证服务错误
pub const ID_VERIFICATION_ERROR: i32 = 80006;
/// 文件存储服务错误
pub const STORAGE_SERVICE_ERROR: i32 = 80007;
/// 微信API服务错误
pub const WECHAT_API_ERROR: i32 = 80008;
/// 微信已绑定
pub const WECHAT_ALREADY_BOUND: i32 = 80009;

// ========== 系统级错误 (90000-90999) ==========
/// 数据库操作失败
pub const DATABASE_ERROR: i32 = 90001;
/// 缓存服务错误
pub const CACHE_ERROR: i32 = 90002;
/// 配置错误
pub const CONFIGURATION_ERROR: i32 = 90003;
/// 系统内部错误
pub const INTERNAL_ERROR: i32 = 90004;
/// 网络连接错误
pub const NETWORK_ERROR: i32 = 90005;
/// 服务不可用
pub const SERVICE_UNAVAILABLE: i32 = 90006;
/// 系统维护中
pub const SYSTEM_MAINTENANCE: i32 = 90007;
/// 并发冲突
pub const CONCURRENCY_CONFLICT: i32 = 90008;
/// 资源分配失败
pub const RESOURCE_ALLOCATION_FAILED: i32 = 90009;

use crate::controller::system::sys_auth_controller::SysAuthApiDoc;
use crate::controller::system::sys_permission_controller::SysPermissionApiDoc;
use crate::controller::system::sys_role_controller::SysRoleApiDoc;
use crate::controller::system::sys_user_controller::SysUserApiDoc;
use utoipa::OpenApi;

/// 开单记账系统API文档
///
/// 新增模块时的操作步骤：
/// 1. 导入新模块的ApiDoc
/// 2. 在 nest 中添加新模块的API文档
/// 3. 在 tags 中添加新模块的标签描述
#[derive(OpenApi)]
#[openapi(
    nest(
        (path = "/", api = SysUserApiDoc),
        (path = "/", api = SysRoleApiDoc),
        (path = "/", api = SysPermissionApiDoc),
        (path = "/", api = SysAuthApiDoc),
    ),
    tags(
        // (name = "系统管理", description = "系统管理模块，包含用户管理和角色管理功能")
    ),
    info(
        title = "开单记账",
        version = "1.0.0",
        description = "开单记账系统的RESTful API文档"
    ),
    servers(
        (url = "http://localhost:9002", description = "本地开发环境")
    )
)]
pub struct ApiDoc;

/// 获取OpenAPI JSON文档
pub fn get_openapi_json() -> String {
    ApiDoc::openapi().to_pretty_json().unwrap()
}

/// 获取OpenAPI YAML文档
pub fn get_openapi_yaml() -> String {
    serde_yaml::to_string(&ApiDoc::openapi()).unwrap()
}

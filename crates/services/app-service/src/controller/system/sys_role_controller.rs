use crate::domain::system::dto::sys_role_request::{
    SysRoleBatchDeleteRequest, SysRoleCreateRequest, SysRolePageRequest, SysRolePermissionRequest,
    SysRoleUpdateRequest,
};
use crate::domain::system::vo::{
    SysRoleDetailResponse, SysRoleListResponse, SysRoleSimpleAndUserCountResponse,
    SysRoleSimpleResponse,
};
use crate::service::system::SysRoleService;
use crate::utils::custom_validator::{MyPath, MyQuery, ValidJson};
use axum::{
    Router, middleware,
    routing::{delete, get, post, put},
};
use lib_auth::require_permission;
use lib_core::response::{ApiResult, PageData};
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use tracing::{error, info};
use utoipa::OpenApi;
use uuid::Uuid;

/// 系统角色管理控制器
pub struct SysRoleController;

impl SysRoleController {
    /// 创建角色管理公开路由
    pub fn public_routes() -> Router {
        Router::new()
        // 角色管理暂无公开路由
    }

    /// 创建角色管理受保护路由
    pub fn protected_routes() -> Router {
        Router::new()
            // 获取所有角色简单信息（下拉选择用）
            .route(
                "/system/roles/simple",
                get(get_all_roles_simple)
                    .layer(middleware::from_fn(require_permission("sys:role:page"))),
            )
            // 批量删除角色
            .route(
                "/system/roles/batch-delete",
                post(batch_delete_roles)
                    .layer(middleware::from_fn(require_permission("sys:role:delete"))),
            )
            // 角色权限分配
            .route(
                "/system/roles/{role_id}/permissions",
                put(assign_permissions).layer(middleware::from_fn(require_permission(
                    "sys:role:permission",
                ))),
            )
            // 角色详情查询
            .route(
                "/system/roles/{role_id}",
                get(get_role_by_id)
                    .layer(middleware::from_fn(require_permission("sys:role:detail"))),
            )
            // 角色更新
            .route(
                "/system/roles/{role_id}",
                put(update_role).layer(middleware::from_fn(require_permission("sys:role:update"))),
            )
            // 角色删除
            .route(
                "/system/roles/{role_id}",
                delete(delete_role)
                    .layer(middleware::from_fn(require_permission("sys:role:delete"))),
            )
            // 角色列表查询
            .route(
                "/system/roles",
                get(page_roles).layer(middleware::from_fn(require_permission("sys:role:page"))),
            )
            // 角色创建
            .route(
                "/system/roles",
                post(create_role).layer(middleware::from_fn(require_permission("sys:role:create"))),
            )
    }
}

/// 分页查询角色列表
#[utoipa::path(
    get,
    path = "system/roles",
    summary = "分页查询角色列表",
    description = "根据查询条件分页获取系统角色信息",
    tags = ["系统角色管理"],
    params(SysRolePageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<SysRoleListResponse>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "items": [
                     {
                         "id": "550e8400-e29b-41d4-a716-************",
                         "name": "系统管理员",
                         "code": "ADMIN",
                         "description": "系统管理员角色，拥有所有权限",
                         "status": 1,
                         "created_date": "2023-01-01 10:00:00",
                         "updated_date": "2023-01-01 10:00:00",
                         "remark": "系统默认角色"
                     }
                 ],
                 "total": 1,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 1
             },
         })
        )
    )
)]
pub async fn page_roles(
    Component(role_service): Component<SysRoleService>,
    MyQuery(req): MyQuery<SysRolePageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<SysRoleListResponse>> {
    info!(
        "分页查询角色列表请求: {:?}，操作人: {}",
        req, current_user.account
    );

    match role_service.page_roles(req).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 根据ID查询角色详情
#[utoipa::path(
    get,
    path = "system/roles/{role_id}",
    summary = "查询角色详情",
    description = "根据角色ID获取角色详细信息，包含权限和用户列表",
    tags = ["系统角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SysRoleDetailResponse>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "id": "550e8400-e29b-41d4-a716-************",
                 "name": "系统管理员",
                 "code": "ADMIN",
                 "description": "系统管理员角色，拥有所有权限",
                 "status": 1,
                 "created_date": "2023-01-01 10:00:00",
                 "updated_date": "2023-01-01 10:00:00",
                 "created_by": "550e8400-e29b-41d4-a716-************",
                 "updated_by": "550e8400-e29b-41d4-a716-************",
                 "remark": "系统默认角色",
                 "permissions": [
                     {
                         "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                         "name": "用户管理",
                         "code": "sys:user:page",
                         "type": 1,
                         "status": 1
                     }
                 ],
                 "users": [
                     {
                         "id": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
                         "username": "admin",
                         "real_name": "系统管理员"
                     }
                 ]
             },
         })
        )
    )
)]
pub async fn get_role_by_id(
    Component(role_service): Component<SysRoleService>,
    MyPath(role_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SysRoleDetailResponse> {
    info!(
        "查询角色详情: {}，操作人: {}",
        role_id, current_user.account
    );

    match role_service.get_role_by_id(role_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 创建新角色
#[utoipa::path(
    post,
    path = "system/roles",
    summary = "创建角色",
    description = "创建新的系统角色",
    tags = ["系统角色管理"],
    request_body = SysRoleCreateRequest,
    responses(
        (status = 200, description = "创建成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": "角色创建成功",
         })
        )
    )
)]
pub async fn create_role(
    Component(role_service): Component<SysRoleService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysRoleCreateRequest>,
) -> ApiResult<String> {
    info!(
        "创建角色请求: {}，操作人: {}",
        req.name, current_user.account
    );
    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match role_service.create_role(req, operator_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新角色信息
#[utoipa::path(
    put,
    path = "system/roles/{role_id}",
    summary = "更新角色",
    description = "更新角色基本信息",
    tags = ["系统角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    request_body = SysRoleUpdateRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": "角色信息更新成功",
         })
        )
    )
)]
pub async fn update_role(
    Component(role_service): Component<SysRoleService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(role_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysRoleUpdateRequest>,
) -> ApiResult<String> {
    info!("更新角色: {}，操作人: {}", role_id, current_user.account);

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match role_service.update_role(role_id, req, operator_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 删除角色
#[utoipa::path(
    delete,
    path = "system/roles/{role_id}",
    summary = "删除角色",
    description = "删除指定角色，如果角色正在被用户使用则无法删除",
    tags = ["系统角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": "角色删除成功",
         })
        )
    )
)]
pub async fn delete_role(
    Component(role_service): Component<SysRoleService>,
    MyPath(role_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!("删除角色: {}，操作人: {}", role_id, current_user.account);

    match role_service.delete_role(role_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 批量删除角色
#[utoipa::path(
    post,
    path = "system/roles/batch-delete",
    summary = "批量删除角色",
    description = "批量删除多个角色",
    tags = ["系统角色管理"],
    request_body = SysRoleBatchDeleteRequest,
    responses(
        (status = 200, description = "批量删除完成", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": "成功删除 3 个角色",
         })
        )
    )
)]
pub async fn batch_delete_roles(
    Component(role_service): Component<SysRoleService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysRoleBatchDeleteRequest>,
) -> ApiResult<String> {
    info!(
        "批量删除角色: {:?}，操作人: {}",
        req.role_ids, current_user.account
    );

    match role_service.batch_delete_roles(req).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 为角色分配权限
#[utoipa::path(
    put,
    path = "system/roles/{role_id}/permissions",
    summary = "角色权限分配",
    description = "为指定角色分配权限，会覆盖原有权限",
    tags = ["系统角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    request_body = SysRolePermissionRequest,
    responses(
        (status = 200, description = "权限分配成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": "角色权限分配成功",
         })
        )
    )
)]
pub async fn assign_permissions(
    Component(role_service): Component<SysRoleService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(role_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysRolePermissionRequest>,
) -> ApiResult<String> {
    info!(
        "角色权限分配: {} 分配 {} 个权限，操作人: {}",
        role_id,
        req.permission_ids.len(),
        current_user.account
    );
    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };
    match role_service
        .assign_permissions(role_id, req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取所有角色简单信息
#[utoipa::path(
    get,
    path = "system/roles/simple",
    summary = "获取角色简单信息",
    description = "获取所有角色的简单信息，包含用户数量，用于下拉选择等场景",
    tags = ["系统角色管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SysRoleSimpleAndUserCountResponse>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": [
                 {
                     "id": "550e8400-e29b-41d4-a716-************",
                     "name": "系统管理员",
                     "user_count": 5,
                     "description": "拥有系统全部权限"
                 },
                 {
                     "id": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
                     "name": "普通用户",
                     "user_count": 12,
                     "description": "普通用户权限"
                 }
             ],
         })
        )
    )
)]
pub async fn get_all_roles_simple(
    Component(role_service): Component<SysRoleService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SysRoleSimpleAndUserCountResponse>> {
    info!("获取角色简单信息，操作人: {}", current_user.account);

    match role_service.get_all_roles_simple().await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}
/// OpenAPI 文档生成
#[derive(OpenApi)]
#[openapi(
    paths(
        page_roles,
        get_role_by_id,
        create_role,
        update_role,
        delete_role,
        batch_delete_roles,
        assign_permissions,
        get_all_roles_simple
    ),
    components(
        schemas(
            // DTO 请求对象
            SysRolePageRequest,
            SysRoleCreateRequest,
            SysRoleUpdateRequest,
            SysRoleBatchDeleteRequest,
            SysRolePermissionRequest,
            // VO 响应对象
            SysRoleListResponse,
            SysRoleDetailResponse,
            SysRoleSimpleResponse,
            SysRoleSimpleAndUserCountResponse,
            PageData<SysRoleListResponse>
        )
    ),
    tags(
        (name = "系统角色管理", description = "系统角色管理相关接口")
    )
)]
pub struct SysRoleApiDoc;

impl SysRoleApiDoc {
    pub fn get_openapi_json() -> String {
        SysRoleApiDoc::openapi().to_pretty_json().unwrap()
    }

    /// 获取OpenAPI YAML文档
    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&SysRoleApiDoc::openapi()).unwrap()
    }
}

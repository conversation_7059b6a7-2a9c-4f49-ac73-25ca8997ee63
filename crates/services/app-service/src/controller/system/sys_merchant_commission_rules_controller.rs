use crate::domain::system::dto::merchant_commission_rules_request::{
    MerchantCommissionRulesBatchDeleteRequest, MerchantCommissionRulesCreateRequest,
    MerchantCommissionRulesListRequest, MerchantCommissionRulesUpdateRequest,
};
use crate::domain::system::vo::merchant_commission_rules_vo::{
    MerchantCommissionRulesDetailResponse, MerchantCommissionRulesListResponse,
    MerchantCommissionRulesSelectItem,
};
use crate::service::system::MerchantCommissionRulesService;
use crate::utils::custom_validator::{MyPath, MyQuery, ValidJson};
use axum::{
    Router, middleware,
    routing::{delete, get, post, put},
};
use lib_auth::require_permission;
use lib_core::response::ApiResult;
use lib_web::extractor::Component;
use tracing::{error, info, warn};
use utoipa::OpenApi;
use uuid::Uuid;

/// 商户佣金规则管理控制器
pub struct SysMerchantCommissionRulesController;

impl SysMerchantCommissionRulesController {
    /// 创建商户佣金规则公开路由
    pub fn public_routes() -> Router {
        Router::new()
        // 商户佣金规则暂无公开路由
    }

    /// 创建商户佣金规则受保护路由
    pub fn protected_routes() -> Router {
        Router::new()
            .route(
                "/business/merchant-commission-rules/{rule_id}",
                get(get_commission_rule_by_id).layer(middleware::from_fn(require_permission(
                    "commission_rule:read",
                ))),
            )
            .route(
                "/business/merchant-commission-rules",
                post(create_commission_rule).layer(middleware::from_fn(require_permission(
                    "commission_rule:create",
                ))),
            )
            .route(
                "/business/merchant-commission-rules/{rule_id}",
                put(update_commission_rule).layer(middleware::from_fn(require_permission(
                    "commission_rule:update",
                ))),
            )
            .route(
                "/business/merchant-commission-rules/{rule_id}",
                delete(delete_commission_rule).layer(middleware::from_fn(require_permission(
                    "commission_rule:delete",
                ))),
            )
            .route(
                "/business/merchant-commission-rules/batch-delete",
                delete(batch_delete_commission_rules).layer(middleware::from_fn(
                    require_permission("commission_rule:delete"),
                )),
            )
            .route(
                "/business/merchants/{merchant_id}/commission-rules/select",
                get(get_commission_rules_select_items).layer(middleware::from_fn(
                    require_permission("commission_rule:read"),
                )),
            )
            .route(
                "/business/merchants/{merchant_id}/commission-rules",
                get(get_commission_rules_by_merchant_id).layer(middleware::from_fn(
                    require_permission("commission_rule:read"),
                )),
            )
    }
}

/// 根据ID查询商户佣金规则详情
#[utoipa::path(
    get,
    path = "/business/commission-rules/{rule_id}",
    summary = "查询商户佣金规则详情",
    description = "根据规则ID获取佣金规则的详细信息",
    tags = ["商户佣金规则"],
    params(
        ("rule_id" = Uuid, Path, description = "佣金规则ID")
    ),
    responses(
        (status = 200, description = "查询成功"),
        (status = 404, description = "佣金规则不存在"),
    )
)]
pub async fn get_commission_rule_by_id(
    Component(merchant_commission_rules_service): Component<MerchantCommissionRulesService>,
    MyPath(rule_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<MerchantCommissionRulesDetailResponse> {
    info!(
        "查询商户佣金规则详情请求，ID: {}，操作人: {}",
        rule_id, current_user.account
    );

    match merchant_commission_rules_service.find_by_id(rule_id).await {
        Ok(Some(data)) => {
            info!(
                "查询商户佣金规则详情成功，ID: {}，操作人: {}",
                rule_id, current_user.account
            );
            ApiResult::success(data)
        }
        Ok(None) => {
            warn!(
                "佣金规则不存在，ID: {}，操作人: {}",
                rule_id, current_user.account
            );
            ApiResult::error(404, "佣金规则不存在".to_string())
        }
        Err(business) => {
            error!(
                "查询商户佣金规则详情失败，ID: {}，操作人: {}，错误: {}",
                rule_id, current_user.account, business.message
            );
            ApiResult::error(business.code, business.message)
        }
    }
}

/// 创建商户佣金规则
#[utoipa::path(
    post,
    path = "/business/commission-rules",
    summary = "创建商户佣金规则",
    description = "创建新的商户佣金规则",
    tags = ["商户佣金规则"],
    responses(
        (status = 200, description = "创建成功"),
    )
)]
pub async fn create_commission_rule(
    Component(merchant_commission_rules_service): Component<MerchantCommissionRulesService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<MerchantCommissionRulesCreateRequest>,
) -> ApiResult<String> {
    info!(
        "创建商户佣金规则请求: {:?}，操作人: {}",
        req, current_user.account
    );

    let user_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(e) => {
            error!("解析用户ID失败: {}, 用户ID: {}", e, current_user.id);
            return ApiResult::error(400, "无效的用户ID".to_string());
        }
    };

    match merchant_commission_rules_service.create(req, user_id).await {
        Ok(_) => {
            info!("创建商户佣金规则成功，操作人: {}", current_user.account);
            ApiResult::success("佣金规则创建成功".to_string())
        }
        Err(business) => {
            error!(
                "创建商户佣金规则失败，操作人: {}，错误: {}",
                current_user.account, business.message
            );
            ApiResult::error(business.code, business.message)
        }
    }
}

/// 更新商户佣金规则
#[utoipa::path(
    put,
    path = "/business/commission-rules/{rule_id}",
    summary = "更新商户佣金规则",
    description = "更新指定ID的商户佣金规则",
    tags = ["商户佣金规则"],
    params(
        ("rule_id" = Uuid, Path, description = "佣金规则ID")
    ),
    responses(
        (status = 200, description = "更新成功"),
    )
)]
pub async fn update_commission_rule(
    Component(merchant_commission_rules_service): Component<MerchantCommissionRulesService>,
    MyPath(rule_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<MerchantCommissionRulesUpdateRequest>,
) -> ApiResult<String> {
    info!(
        "更新商户佣金规则请求，ID: {}，请求: {:?}，操作人: {}",
        rule_id, req, current_user.account
    );

    let user_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(e) => {
            error!("解析用户ID失败: {}, 用户ID: {}", e, current_user.id);
            return ApiResult::error(400, "无效的用户ID".to_string());
        }
    };

    match merchant_commission_rules_service
        .update(rule_id, req, user_id)
        .await
    {
        Ok(_) => {
            info!(
                "更新商户佣金规则成功，ID: {}，操作人: {}",
                rule_id, current_user.account
            );
            ApiResult::success("佣金规则更新成功".to_string())
        }
        Err(business) => {
            error!(
                "更新商户佣金规则失败，ID: {}，操作人: {}，错误: {}",
                rule_id, current_user.account, business.message
            );
            ApiResult::error(business.code, business.message)
        }
    }
}

/// 删除商户佣金规则
#[utoipa::path(
    delete,
    path = "/business/commission-rules/{rule_id}",
    summary = "删除商户佣金规则",
    description = "删除指定ID的商户佣金规则",
    tags = ["商户佣金规则"],
    params(
        ("rule_id" = Uuid, Path, description = "佣金规则ID")
    ),
    responses(
        (status = 200, description = "删除成功"),
    )
)]
pub async fn delete_commission_rule(
    Component(merchant_commission_rules_service): Component<MerchantCommissionRulesService>,
    MyPath(rule_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!(
        "删除商户佣金规则请求，ID: {}，操作人: {}",
        rule_id, current_user.account
    );

    match merchant_commission_rules_service.delete(rule_id).await {
        Ok(_) => {
            info!(
                "删除商户佣金规则成功，ID: {}，操作人: {}",
                rule_id, current_user.account
            );
            ApiResult::success("佣金规则删除成功".to_string())
        }
        Err(business) => {
            error!(
                "删除商户佣金规则失败，ID: {}，操作人: {}，错误: {}",
                rule_id, current_user.account, business.message
            );
            ApiResult::error(business.code, business.message)
        }
    }
}

/// 批量删除商户佣金规则
#[utoipa::path(
    delete,
    path = "/business/commission-rules/batch-delete",
    summary = "批量删除商户佣金规则",
    description = "批量删除多个商户佣金规则",
    tags = ["商户佣金规则"],
    responses(
        (status = 200, description = "批量删除成功"),
    )
)]
pub async fn batch_delete_commission_rules(
    Component(merchant_commission_rules_service): Component<MerchantCommissionRulesService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<MerchantCommissionRulesBatchDeleteRequest>,
) -> ApiResult<String> {
    info!(
        "批量删除商户佣金规则请求，数量: {}，操作人: {}",
        req.ids.len(),
        current_user.account
    );

    match merchant_commission_rules_service.batch_delete(req).await {
        Ok(deleted_count) => {
            info!(
                "批量删除商户佣金规则成功，删除数量: {}，操作人: {}",
                deleted_count, current_user.account
            );
            ApiResult::success(format!("成功删除 {} 条佣金规则", deleted_count))
        }
        Err(business) => {
            error!(
                "批量删除商户佣金规则失败，操作人: {}，错误: {}",
                current_user.account, business.message
            );
            ApiResult::error(business.code, business.message)
        }
    }
}

/// 获取商户佣金规则选择项
#[utoipa::path(
    get,
    path = "/business/merchants/{merchant_id}/commission-rules/select",
    summary = "获取商户佣金规则选择项",
    description = "获取指定商户的佣金规则选择项列表",
    tags = ["商户佣金规则"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    responses(
        (status = 200, description = "查询成功"),
    )
)]
pub async fn get_commission_rules_select_items(
    Component(merchant_commission_rules_service): Component<MerchantCommissionRulesService>,
    MyPath(merchant_id): MyPath<i64>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<MerchantCommissionRulesSelectItem>> {
    info!(
        "获取商户佣金规则选择项请求，商户ID: {}，操作人: {}",
        merchant_id, current_user.account
    );

    match merchant_commission_rules_service
        .get_select_items(merchant_id)
        .await
    {
        Ok(data) => {
            info!(
                "获取商户佣金规则选择项成功，商户ID: {}，数量: {}，操作人: {}",
                merchant_id,
                data.len(),
                current_user.account
            );
            ApiResult::success(data)
        }
        Err(business) => {
            error!(
                "获取商户佣金规则选择项失败，商户ID: {}，操作人: {}，错误: {}",
                merchant_id, current_user.account, business.message
            );
            ApiResult::error(business.code, business.message)
        }
    }
}

/// 根据商户ID查询商户的佣金规则列表
#[utoipa::path(
    get,
    path = "/business/merchants/{merchant_id}/commission-rules",
    summary = "查询商户的佣金规则列表",
    description = "根据商户ID获取该商户的所有佣金规则",
    tags = ["商户佣金规则"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID"),
        MerchantCommissionRulesListRequest
    ),
    responses(
        (status = 200, description = "查询成功"),
    )
)]
pub async fn get_commission_rules_by_merchant_id(
    Component(merchant_commission_rules_service): Component<MerchantCommissionRulesService>,
    MyPath(merchant_id): MyPath<i64>,
    MyQuery(req): MyQuery<MerchantCommissionRulesListRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<MerchantCommissionRulesListResponse>> {
    info!(
        "查询商户佣金规则列表请求，商户ID: {}，查询条件: {:?}，操作人: {}",
        merchant_id, req, current_user.account
    );

    // 构建查询请求，强制指定merchant_id
    let mut list_request = req;
    list_request.merchant_id = Some(merchant_id);

    match merchant_commission_rules_service.list(list_request).await {
        Ok(data) => {
            info!(
                "查询商户佣金规则列表成功，商户ID: {}，数量: {}，操作人: {}",
                merchant_id,
                data.len(),
                current_user.account
            );
            ApiResult::success(data)
        }
        Err(business) => {
            error!(
                "查询商户佣金规则列表失败，商户ID: {}，操作人: {}，错误: {}",
                merchant_id, current_user.account, business.message
            );
            ApiResult::error(business.code, business.message)
        }
    }
}

/// 商户佣金规则管理API文档
#[derive(OpenApi)]
#[openapi(
    paths(
        get_commission_rule_by_id,
        create_commission_rule,
        update_commission_rule,
        delete_commission_rule,
        batch_delete_commission_rules,
        get_commission_rules_select_items,
        get_commission_rules_by_merchant_id,
    ),
    tags(
        (name = "商户佣金规则", description = "商户佣金规则管理API")
    )
)]
pub struct SysMerchantCommissionRulesApiDoc;

impl SysMerchantCommissionRulesApiDoc {
    pub fn get_openapi_json() -> String {
        serde_json::to_string_pretty(&Self::openapi()).unwrap()
    }

    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&Self::openapi()).unwrap()
    }
}

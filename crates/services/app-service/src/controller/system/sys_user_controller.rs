use crate::controller::system::sys_auth_controller::SysAuthApiDoc;
use crate::domain::system::dto::sys_user_request::{
    SysUserBatchDeleteRequest, SysUserCreateRequest, SysUserPageRequest,
    SysUserResetPasswordRequest, SysUserStatusRequest, SysUserUpdateRequest,
};
use crate::domain::system::vo::{
    SysUnassignedUserCountResponse, SysUserDetailResponse, SysUserListResponse,
};
use crate::service::system::SysUserService;
use crate::utils::custom_validator::{MyPath, MyQuery, ValidJson};
use axum::extract::Query;
use axum::{
    Router, middleware,
    routing::{delete, get, post, put},
};
use lib_auth::require_permission;
use lib_core::response::{ApiResult, PageData};
use lib_web::extractor::Component;
use tracing::{error, info};
use utoipa;
use utoipa::OpenApi;
use uuid::Uuid;

// ==================== 路由配置 ====================

impl SysUserController {
    /// 创建系统用户管理的公开路由（无需授权）
    pub fn public_routes() -> Router {
        Router::new()
        // 系统用户管理暂无公开路由
    }

    /// 创建系统用户管理的受保护路由（需要授权）
    pub fn protected_routes() -> Router {
        Router::new()
            // 检查接口（具体路径在前）
            .route(
                "/system/users/check-username",
                get(check_username_exists)
                    .layer(middleware::from_fn(require_permission("sys:user:check"))),
            )
            .route(
                "/system/users/check-email",
                get(check_email_exists)
                    .layer(middleware::from_fn(require_permission("sys:user:check"))),
            )
            .route(
                "/system/users/check-phone",
                get(check_phone_exists)
                    .layer(middleware::from_fn(require_permission("sys:user:check"))),
            )
            // 批量操作
            .route(
                "/system/users/batch",
                delete(batch_delete_users)
                    .layer(middleware::from_fn(require_permission("sys:user:delete"))),
            )
            // 重置密码
            .route(
                "/system/users/{id}/reset-password",
                put(reset_password).layer(middleware::from_fn(require_permission(
                    "sys:user:reset-password",
                ))),
            )
            // 切换状态
            .route(
                "/system/users/{id}/status",
                put(change_status)
                    .layer(middleware::from_fn(require_permission("sys:user:status"))),
            )
            // 用户详情查询
            .route(
                "/system/users/{id}",
                get(get_user_by_id)
                    .layer(middleware::from_fn(require_permission("sys:user:detail"))),
            )
            // 用户更新
            .route(
                "/system/users/{id}",
                put(update_user).layer(middleware::from_fn(require_permission("sys:user:update"))),
            )
            // 用户删除
            .route(
                "/system/users/{id}",
                delete(delete_user)
                    .layer(middleware::from_fn(require_permission("sys:user:delete"))),
            )
            // 用户列表查询
            .route(
                "/system/users",
                get(page_users).layer(middleware::from_fn(require_permission("sys:user:page"))),
            )
            // 未分配角色用户查询
            .route(
                "/system/users/unassigned",
                get(page_unassigned_users)
                    .layer(middleware::from_fn(require_permission("sys:user:page"))),
            )
            // 获取未分配角色用户数量
            .route(
                "/system/users/unassigned/count",
                get(get_unassigned_user_count)
                    .layer(middleware::from_fn(require_permission("sys:user:page"))),
            )
            // 用户创建
            .route(
                "/system/users",
                post(create_user).layer(middleware::from_fn(require_permission("sys:user:create"))),
            )
    }
}

/// 系统用户管理控制器结构体（用于组织路由方法）
pub struct SysUserController;

// ==================== 系统用户管理接口 ====================

/// 分页查询用户列表
#[utoipa::path(
    get,
    path = "/system/users",
    tags = ["系统用户管理"],
    summary = "分页查询用户列表",
    description = "根据查询条件分页获取用户列表，支持多条件搜索和过滤",
    params(SysUserPageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<SysUserListResponse>>,
         example = json!({
             "code": 200,
             "message": "查询成功",
             "data": {
                 "items": [
                     {
                         "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                         "username": "admin",
                         "real_name": "系统管理员",
                         "phone": "138****1234",
                         "email": "a***@example.com",
                         "gender": 1,
                         "status": 1,
                         "last_login_date": "2023-05-24 10:30:00",
                         "last_login_ip": "***********",
                         "created_date": "2023-01-01 00:00:00",
                         "updated_date": "2023-05-24 10:30:00"
                     }
                 ],
                 "total": 100,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 10
             },
         })
        )
    )
)]
pub async fn page_users(
    Component(user_service): Component<SysUserService>,
    MyQuery(req): MyQuery<SysUserPageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<SysUserListResponse>> {
    info!(
        "分页查询用户列表请求: {:?}，操作人: {}",
        req, current_user.account
    );

    match user_service.page_users(req).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 分页查询未分配角色的用户列表
#[utoipa::path(
    get,
    path = "/system/users/unassigned",
    tags = ["系统用户管理"],
    summary = "分页查询未分配角色的用户列表",
    description = "根据查询条件分页获取未分配任何角色的用户列表，支持多条件搜索和过滤",
    params(SysUserPageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<SysUserListResponse>>,
         example = json!({
             "code": 200,
             "message": "查询成功",
             "data": {
                 "items": [
                     {
                         "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                         "username": "user1",
                         "real_name": "普通用户1",
                         "phone": "138****1234",
                         "email": "u***@example.com",
                         "gender": 1,
                         "status": 1,
                         "last_login_date": "2023-05-24 10:30:00",
                         "last_login_ip": "***********",
                         "created_date": "2023-01-01 00:00:00",
                         "updated_date": "2023-05-24 10:30:00"
                     }
                 ],
                 "total": 25,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 3
             },
         })
        )
    )
)]
pub async fn page_unassigned_users(
    Component(user_service): Component<SysUserService>,
    MyQuery(req): MyQuery<SysUserPageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<SysUserListResponse>> {
    info!(
        "分页查询未分配角色用户列表请求: {:?}，操作人: {}",
        req, current_user.account
    );

    match user_service.page_unassigned_users(req).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取未分配角色用户数量
#[utoipa::path(
    get,
    path = "/system/users/unassigned/count",
    tags = ["系统用户管理"],
    summary = "获取未分配角色用户数量",
    description = "获取系统中未分配任何角色的用户总数量",
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SysUnassignedUserCountResponse>,
         example = json!({
             "code": 200,
             "message": "查询成功",
             "data": {
                 "unassigned_user_count": 25
             },
         })
        )
    )
)]
pub async fn get_unassigned_user_count(
    Component(user_service): Component<SysUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SysUnassignedUserCountResponse> {
    info!("获取未分配角色用户数量，操作人: {}", current_user.account);

    match user_service.get_unassigned_user_count().await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 根据ID查询用户详情
#[utoipa::path(
    get,
    path = "/system/users/{id}",
    tags = ["系统用户管理"],
    summary = "查询用户详情",
    description = "根据用户ID查询用户的详细信息，包括角色信息",
    params(
        ("id" = String, Path, description = "用户ID", example = "550e8400-e29b-41d4-a716-************")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SysUserDetailResponse>,
         example = json!({
             "code": 200,
             "message": "查询成功",
             "data": {
                 "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                 "username": "admin",
                 "real_name": "系统管理员",
                 "phone": "***********",
                 "email": "<EMAIL>",
                 "gender": 1,
                 "status": 1,
                 "last_login_date": "2023-05-24 10:30:00",
                 "last_login_ip": "***********",
                 "created_date": "2023-01-01 00:00:00",
                 "updated_date": "2023-05-24 10:30:00",
                 "created_by": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                 "updated_by": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                 "remark": "系统默认管理员",
                 "roles": [
                     {
                         "id": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
                         "name": "超级管理员",
                         "description": "拥有系统全部权限",
                         "created_date": "2023-01-01 00:00:00",
                         "updated_date": "2023-01-01 00:00:00",
                         "remark": "系统默认角色"
                     }
                 ]
             },
         })
        )
    )
)]
pub async fn get_user_by_id(
    Component(user_service): Component<SysUserService>,
    MyPath(user_id): MyPath<String>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SysUserDetailResponse> {
    info!(
        "查询用户详情: {}，操作人: {}",
        user_id, current_user.account
    );

    let user_id = match Uuid::parse_str(&user_id) {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id);
            return ApiResult::error(400, "用户ID格式错误".to_string());
        }
    };

    match user_service.get_user_by_id(user_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 创建新用户
#[utoipa::path(
    post,
    path = "/system/users",
    tags = ["系统用户管理"],
    summary = "创建新用户",
    description = "创建新的系统用户，包括基本信息和角色分配",
    request_body = SysUserCreateRequest,
    responses(
        (status = 200, description = "创建成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": "用户创建成功",
         })
        )
    )
)]
pub async fn create_user(
    Component(user_service): Component<SysUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysUserCreateRequest>,
) -> ApiResult<String> {
    info!(
        "创建用户请求: {}，操作人: {}",
        req.username, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match user_service.create_user(req, operator_id).await {
        Ok(data) => ApiResult::success_with_message(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新用户信息
#[utoipa::path(
    put,
    path = "/system/users/{id}",
    tags = ["系统用户管理"],
    summary = "更新用户信息",
    description = "更新指定用户的基本信息和角色分配",
    params(
        ("id" = String, Path, description = "用户ID", example = "550e8400-e29b-41d4-a716-************")
    ),
    request_body = SysUserUpdateRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": "用户信息更新成功",
         })
        )
    )
)]
pub async fn update_user(
    Component(user_service): Component<SysUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(user_id): MyPath<String>,
    ValidJson(req): ValidJson<SysUserUpdateRequest>,
) -> ApiResult<String> {
    info!("更新用户: {}，操作人: {}", user_id, current_user.account);

    let user_id = match Uuid::parse_str(&user_id) {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id);
            return ApiResult::error(400, "用户ID格式错误".to_string());
        }
    };

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match user_service.update_user(user_id, req, operator_id).await {
        Ok(data) => ApiResult::success_with_message(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 删除用户
#[utoipa::path(
    delete,
    path = "/system/users/{id}",
    tags = ["系统用户管理"],
    summary = "删除用户",
    description = "删除指定的用户，同时清除相关的角色关联",
    params(
        ("id" = String, Path, description = "用户ID", example = "550e8400-e29b-41d4-a716-************")
    ),
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": "用户删除成功",
         })
        )
    )
)]
pub async fn delete_user(
    Component(user_service): Component<SysUserService>,
    MyPath(user_id): MyPath<String>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!("删除用户: {}，操作人: {}", user_id, current_user.account);

    let user_id = match Uuid::parse_str(&user_id) {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id);
            return ApiResult::error(400, "用户ID格式错误".to_string());
        }
    };

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match user_service.delete_user(user_id, operator_id).await {
        Ok(data) => ApiResult::success_with_message(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 批量删除用户
#[utoipa::path(
    delete,
    path = "/system/users/batch",
    tags = ["系统用户管理"],
    summary = "批量删除用户",
    description = "批量删除多个用户，同时清除相关的角色关联",
    request_body = SysUserBatchDeleteRequest,
    responses(
        (status = 200, description = "批量删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": "成功删除 3 个用户",
         })
        )
    )
)]
pub async fn batch_delete_users(
    Component(user_service): Component<SysUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysUserBatchDeleteRequest>,
) -> ApiResult<String> {
    info!(
        "批量删除用户: {:?}，操作人: {}",
        req.user_ids, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match user_service.batch_delete_users(req, operator_id).await {
        Ok(data) => ApiResult::success_with_message(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 重置用户密码
#[utoipa::path(
    put,
    path = "/system/users/{id}/reset-password",
    tags = ["系统用户管理"],
    summary = "重置用户密码",
    description = "重置指定用户的登录密码",
    params(
        ("id" = String, Path, description = "用户ID", example = "550e8400-e29b-41d4-a716-************")
    ),
    request_body = SysUserResetPasswordRequest,
    responses(
        (status = 200, description = "密码重置成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": "密码重置成功",
         })
        )
    )
)]
pub async fn reset_password(
    Component(user_service): Component<SysUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(user_id): MyPath<String>,
    ValidJson(req): ValidJson<SysUserResetPasswordRequest>,
) -> ApiResult<String> {
    info!(
        "重置用户密码: {}，操作人: {}",
        user_id, current_user.account
    );

    let user_id = match Uuid::parse_str(&user_id) {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id);
            return ApiResult::error(400, "用户ID格式错误".to_string());
        }
    };

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match user_service.reset_password(user_id, req, operator_id).await {
        Ok(data) => ApiResult::success_with_message(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 切换用户状态
#[utoipa::path(
    put,
    path = "/system/users/{id}/status",
    tags = ["系统用户管理"],
    summary = "切换用户状态",
    description = "启用或禁用指定用户账户",
    params(
        ("id" = String, Path, description = "用户ID", example = "550e8400-e29b-41d4-a716-************")
    ),
    request_body = SysUserStatusRequest,
    responses(
        (status = 200, description = "状态切换成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": "用户状态已切换为: 启用",
         })
        )
    )
)]
pub async fn change_status(
    Component(user_service): Component<SysUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(user_id): MyPath<String>,
    ValidJson(req): ValidJson<SysUserStatusRequest>,
) -> ApiResult<String> {
    info!(
        "切换用户状态: {} -> {}，操作人: {}",
        user_id, req.status, current_user.account
    );

    let user_id = match Uuid::parse_str(&user_id) {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", user_id);
            return ApiResult::error(400, "用户ID格式错误".to_string());
        }
    };

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match user_service.change_status(user_id, req, operator_id).await {
        Ok(data) => ApiResult::success_with_message(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 检查用户名是否存在
#[utoipa::path(
    get,
    path = "/system/users/check-username",
    tags = ["系统用户管理"],
    summary = "检查用户名是否存在",
    description = "检查指定用户名是否已被使用，支持排除指定用户ID",
    params(
        ("username" = String, Query, description = "要检查的用户名"),
        ("exclude_id" = Option<String>, Query, description = "排除的用户ID")
    ),
    responses(
        (status = 200, description = "检查成功", body = ApiResult<bool>,
         example = json!({
             "code": 200,
             "message": "检查成功",
             "data": true
         })
        )
    )
)]
pub async fn check_username_exists(
    Component(user_service): Component<SysUserService>,
    Query(params): Query<serde_json::Value>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<bool> {
    let username = match params.get("username").and_then(|v| v.as_str()) {
        Some(username) => username,
        None => {
            return ApiResult::error(400, "缺少username参数".to_string());
        }
    };

    let exclude_id = params
        .get("exclude_id")
        .and_then(|v| v.as_str())
        .map(|s| Uuid::parse_str(s))
        .transpose();

    let exclude_id = match exclude_id {
        Ok(id) => id,
        Err(_) => {
            return ApiResult::error(400, "exclude_id格式错误".to_string());
        }
    };

    info!(
        "检查用户名是否存在: {}，操作人: {}",
        username, current_user.account
    );

    match user_service
        .check_username_exists(username, exclude_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 检查邮箱是否存在
#[utoipa::path(
    get,
    path = "/system/users/check-email",
    tags = ["系统用户管理"],
    summary = "检查邮箱是否存在",
    description = "检查指定邮箱是否已被使用，支持排除指定用户ID",
    params(
        ("email" = String, Query, description = "要检查的邮箱"),
        ("exclude_id" = Option<String>, Query, description = "排除的用户ID")
    ),
    responses(
        (status = 200, description = "检查成功", body = ApiResult<bool>,
         example = json!({
             "code": 200,
             "message": "检查成功",
             "data": false
         })
        )
    )
)]
pub async fn check_email_exists(
    Component(user_service): Component<SysUserService>,
    Query(params): Query<serde_json::Value>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<bool> {
    let email = match params.get("email").and_then(|v| v.as_str()) {
        Some(email) => email,
        None => {
            return ApiResult::error(400, "缺少email参数".to_string());
        }
    };

    let exclude_id = params
        .get("exclude_id")
        .and_then(|v| v.as_str())
        .map(|s| Uuid::parse_str(s))
        .transpose();

    let exclude_id = match exclude_id {
        Ok(id) => id,
        Err(_) => {
            return ApiResult::error(400, "exclude_id格式错误".to_string());
        }
    };

    info!(
        "检查邮箱是否存在: {}，操作人: {}",
        email, current_user.account
    );

    match user_service.check_email_exists(email, exclude_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 检查手机号是否存在
#[utoipa::path(
    get,
    path = "/system/users/check-phone",
    tags = ["系统用户管理"],
    summary = "检查手机号是否存在",
    description = "检查指定手机号是否已被使用，支持排除指定用户ID",
    params(
        ("phone" = String, Query, description = "要检查的手机号"),
        ("exclude_id" = Option<String>, Query, description = "排除的用户ID")
    ),
    responses(
        (status = 200, description = "检查成功", body = ApiResult<bool>,
         example = json!({
             "code": 200,
             "message": "检查成功",
             "data": false
         })
        )
    )
)]
pub async fn check_phone_exists(
    Component(user_service): Component<SysUserService>,
    Query(params): Query<serde_json::Value>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<bool> {
    let phone = match params.get("phone").and_then(|v| v.as_str()) {
        Some(phone) => phone,
        None => {
            return ApiResult::error(400, "缺少phone参数".to_string());
        }
    };

    let exclude_id = params
        .get("exclude_id")
        .and_then(|v| v.as_str())
        .map(|s| Uuid::parse_str(s))
        .transpose();

    let exclude_id = match exclude_id {
        Ok(id) => id,
        Err(_) => {
            return ApiResult::error(400, "exclude_id格式错误".to_string());
        }
    };

    info!(
        "检查手机号是否存在: {}，操作人: {}",
        phone, current_user.account
    );

    match user_service.check_phone_exists(phone, exclude_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 系统用户管理API文档
#[derive(utoipa::OpenApi)]
#[openapi(
    paths(
        page_users,
        page_unassigned_users,
        get_unassigned_user_count,
        get_user_by_id,
        create_user,
        update_user,
        delete_user,
        batch_delete_users,
        reset_password,
        change_status,
        check_username_exists,
        check_email_exists,
        check_phone_exists
    ),
    components(
        schemas(
            // DTO 请求对象
            SysUserPageRequest,
            SysUserCreateRequest,
            SysUserUpdateRequest,
            SysUserBatchDeleteRequest,
            SysUserResetPasswordRequest,
            SysUserStatusRequest,
            // VO 响应对象
            SysUserListResponse,
            SysUserDetailResponse,
            PageData<SysUserListResponse>,
            SysUnassignedUserCountResponse
        )
    ),
    tags(
        (name = "系统用户管理", description = "系统用户管理相关接口")
    )
)]
pub struct SysUserApiDoc;

impl SysUserApiDoc {
    /// 获取OpenAPI JSON文档
    pub fn get_openapi_json() -> String {
        SysUserApiDoc::openapi().to_pretty_json().unwrap()
    }

    /// 获取OpenAPI YAML文档
    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&SysAuthApiDoc::openapi()).unwrap()
    }
}

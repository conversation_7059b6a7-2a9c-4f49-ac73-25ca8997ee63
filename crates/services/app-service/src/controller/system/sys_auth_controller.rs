use crate::domain::system::dto::sys_user_request::{
    ChangePasswordRequest, RefreshTokenRequest, SysUserLoginRequest, UpdateProfileRequest,
    SysUserWechatLoginRequest, BindWechatRequest, UnbindWechatRequest,
};
use crate::domain::system::vo::{RefreshTokenResponse, SysUserLoginResponse, UserProfileResponse};
use crate::service::system::SysUserAuthService;
use crate::utils::ValidJson;
use axum::{
    Router,
    routing::{get, post, put},
};
use lib_auth::middleware::ip_resolver_middleware::ClientIp;
use lib_core::response::ApiResult;
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use tracing::{error, info};
use uuid::Uuid;
use utoipa::OpenApi;
// ==================== 路由配置 ====================

impl SysAuthController {
    /// 创建认证的公开路由（无需授权）
    pub fn public_routes() -> Router {
        Router::new()
            // 用户登录
            .route("/sys_user/login", post(login))
            // 系统用户微信登录
            .route("/sys_user/wechat_login", post(wechat_login))
            // 刷新token
            .route("/sys_user/refresh", post(refresh_token))
    }

    /// 创建认证的受保护路由（需要授权）
    pub fn protected_routes() -> Router {
        Router::new()
            // 用户注销
            .route("/sys_user/logout", post(logout))
            // 修改当前登录用户密码
            .route("/sys_user/change_password", put(change_password))
            // 用户个人信息路由
            .route("/sys_user/profile", get(get_profile))
            // 修改个人信息
            .route("/sys_user/profile", put(update_profile))
            // 绑定微信
            .route("/sys_user/bind_wechat", post(bind_wechat))
            // 解绑微信
            .route("/sys_user/unbind_wechat", post(unbind_wechat))
    }
}

/// 认证控制器结构体（用于组织路由方法）
pub struct SysAuthController {}

/// 登录接口 - 使用自定义验证提取器返回统一格式错误
#[utoipa::path(
    post,
    path = "sys_user/login",
    summary = "用户登录",
    description = "系统用户登录接口，验证用户名和密码，返回访问令牌",
    tags = ["系统认证管理"],
    request_body = SysUserLoginRequest,
    responses(
        (status = 200, description = "登录成功", body = ApiResult<SysUserLoginResponse>,
         example = json!({
             "code": 200,
             "message": "登录成功",
             "data": {
                 "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                 "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
             }
         })
        )
    )
)]
pub async fn login(
    Component(auth_service): Component<SysUserAuthService>,
    axum::extract::Extension(client_ip): axum::extract::Extension<ClientIp>,
    ValidJson(req): ValidJson<SysUserLoginRequest>,
) -> ApiResult<SysUserLoginResponse> {
    info!("登录请求: {:?}", req);
    info!("客户端IP: {}", client_ip.0);
    match auth_service.login(req, client_ip.0).await {
        Ok(data) => ApiResult::success_with_message_and_data(data, "登录成功".to_string()),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 注销接口
#[utoipa::path(
    post,
    path = "sys_user/logout",
    summary = "用户注销",
    description = "用户注销登录，清除服务端会话信息",
    tags = ["系统认证管理"],
    responses(
        (status = 200, description = "注销成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "注销成功",
             "data": "用户已成功注销"
         })
        )
    )
)]
pub async fn logout(
    Component(auth_service): Component<SysUserAuthService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!("用户 {} 请求注销", current_user.account);

    match auth_service.logout(current_user).await {
        Ok(()) => ApiResult::success_with_message("注销成功".to_string()),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 手动刷新token接口 - 使用自定义验证提取器返回统一格式错误
///
/// 客户端可以使用refresh token来获取新的token对
/// 适用于access token过期但refresh token仍有效的场景
#[utoipa::path(
    post,
    path = "sys_user/refresh",
    summary = "刷新访问令牌",
    description = "使用refresh token获取新的access token和refresh token对",
    tags = ["系统认证管理"],
    request_body = RefreshTokenRequest,
    responses(
        (status = 200, description = "刷新成功", body = ApiResult<RefreshTokenResponse>,
         example = json!({
             "code": 200,
             "message": "令牌刷新成功",
             "data": {
                 "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                 "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                 "expires_in": 3600
             }
         })
        )
    )
)]
pub async fn refresh_token(
    Component(auth_service): Component<SysUserAuthService>,
    ValidJson(req): ValidJson<RefreshTokenRequest>,
) -> ApiResult<RefreshTokenResponse> {
    info!("收到token刷新请求");

    match auth_service.refresh_token(req).await {
        Ok(data) => ApiResult::success_with_message_and_data(data, "Token刷新成功".to_string()),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 修改密码接口
#[utoipa::path(
    put,
    path = "sys_user/change_password",
    summary = "修改密码",
    description = "当前登录用户修改自己的密码",
    tags = ["系统认证管理"],
    request_body = ChangePasswordRequest,
    responses(
        (status = 200, description = "密码修改成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "密码修改成功",
             "data": "密码已成功更新"
         })
        )
    )
)]
pub async fn change_password(
    Component(auth_service): Component<SysUserAuthService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<ChangePasswordRequest>,
) -> ApiResult<String> {
    info!("用户 {} 请求修改密码", current_user.account);

    match auth_service
        .change_password(current_user.id, req.old_password, req.new_password)
        .await
    {
        Ok(()) => ApiResult::success_with_message("密码修改成功".to_string()),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取个人信息接口
#[utoipa::path(
    get,
    path = "sys_user/profile",
    summary = "获取个人信息",
    description = "获取当前登录用户的个人信息，包括基本信息、权限和角色",
    tags = ["系统认证管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<UserProfileResponse>,
         example = json!({
             "code": 200,
             "message": "查询成功",
             "data": {
                 "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                 "username": "admin",
                 "real_name": "系统管理员",
                 "phone": "***********",
                 "email": "<EMAIL>",
                 "avatar": "https://example.com/avatar.jpg",
                 "gender": 1,
                 "status": 1,
                 "last_login_date": "2023-05-24 10:30:00",
                 "last_login_ip": "***********",
                 "permissions": ["sys:user:page", "sys:role:page"],
                 "role_ids": ["b2c3d4e5-f6g7-8901-bcde-f23456789012"]
             }
         })
        )
    )
)]
pub async fn get_profile(
    Component(auth_service): Component<SysUserAuthService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<UserProfileResponse> {
    info!("用户 {} 请求获取个人信息", current_user.account);

    match auth_service.get_user_profile(current_user.id).await {
        Ok(data) => ApiResult::success_with_message_and_data(data, "个人信息获取成功".to_string()),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新个人信息接口
#[utoipa::path(
    put,
    path = "sys_user/profile",
    summary = "更新个人信息",
    description = "当前登录用户更新自己的个人信息，包括姓名、手机、邮箱等",
    tags = ["系统认证管理"],
    request_body = UpdateProfileRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "个人信息更新成功",
             "data": "个人信息已成功更新"
         })
        )
    )
)]
pub async fn update_profile(
    Component(auth_service): Component<SysUserAuthService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<UpdateProfileRequest>,
) -> ApiResult<String> {
    info!("用户 {} 请求更新个人信息", current_user.account);

    match auth_service.update_profile(current_user.id, req).await {
        Ok(()) => ApiResult::success_with_message("个人信息更新成功".to_string()),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 系统用户微信登录接口
#[utoipa::path(
    post,
    path = "sys_user/wechat_login",
    summary = "系统用户微信登录",
    description = "系统用户微信登录接口，通过微信授权码进行登录，返回访问令牌",
    tags = ["系统认证管理"],
    request_body = SysUserWechatLoginRequest,
    responses(
        (status = 200, description = "登录成功", body = ApiResult<SysUserLoginResponse>,
         example = json!({
             "code": 200,
             "message": "登录成功",
             "data": {
                 "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                 "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
             }
         })
        )
    )
)]
pub async fn wechat_login(
    Component(auth_service): Component<SysUserAuthService>,
    axum::extract::Extension(client_ip): axum::extract::Extension<ClientIp>,
    ValidJson(req): ValidJson<SysUserWechatLoginRequest>,
) -> ApiResult<SysUserLoginResponse> {
    info!("系统用户微信登录请求，IP: {}", client_ip.0);
    match auth_service.wechat_login(req, client_ip.0).await {
        Ok(response) => ApiResult::success_with_message_and_data(response, "登录成功".to_string()),
        Err(business_error) => ApiResult::error(business_error.code, business_error.message),
    }
}

/// 绑定微信接口
#[utoipa::path(
    post,
    path = "sys_user/bind_wechat",
    summary = "绑定微信",
    description = "系统用户绑定微信账号，通过微信授权码进行绑定，支持同时保存微信昵称和头像",
    tags = ["系统认证管理"],
    request_body = BindWechatRequest,
    responses(
        (status = 200, description = "绑定微信成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "绑定微信成功",
             "data": null
         })
        )
    )
)]
pub async fn bind_wechat(
    Component(auth_service): Component<SysUserAuthService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<BindWechatRequest>,
) -> ApiResult<String> {
    info!("系统用户 {} 绑定微信请求", current_user.account);

    // 将用户ID从String转换为Uuid
    let user_uuid = match Uuid::parse_str(&current_user.id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("无效的系统用户ID格式: {}, 错误: {}", current_user.id, e);
            return ApiResult::error(400, "无效的系统用户ID格式".to_string());
        }
    };

    match auth_service.bind_wechat(req, user_uuid).await {
        Ok(_) => ApiResult::success_with_message("绑定微信成功".to_string()),
        Err(business_error) => ApiResult::error(business_error.code, business_error.message),
    }
}

/// 解绑微信接口
#[utoipa::path(
    post,
    path = "sys_user/unbind_wechat",
    summary = "解绑微信",
    description = "系统用户解绑微信账号，需要验证当前密码",
    tags = ["系统认证管理"],
    request_body = UnbindWechatRequest,
    responses(
        (status = 200, description = "解绑微信成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "解绑微信成功",
             "data": null
         })
        )
    )
)]
pub async fn unbind_wechat(
    Component(auth_service): Component<SysUserAuthService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<UnbindWechatRequest>,
) -> ApiResult<String> {
    info!("系统用户 {} 解绑微信请求", current_user.account);

    // 将用户ID从String转换为Uuid
    let user_uuid = match Uuid::parse_str(&current_user.id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("无效的系统用户ID格式: {}, 错误: {}", current_user.id, e);
            return ApiResult::error(400, "无效的系统用户ID格式".to_string());
        }
    };

    match auth_service.unbind_wechat(req, user_uuid).await {
        Ok(_) => ApiResult::success_with_message("解绑微信成功".to_string()),
        Err(business_error) => ApiResult::error(business_error.code, business_error.message),
    }
}

/// 系统认证管理API文档
#[derive(OpenApi)]
#[openapi(
    paths(
        login,
        wechat_login,
        logout,
        refresh_token,
        change_password,
        get_profile,
        update_profile,
        bind_wechat,
        unbind_wechat
    ),
    components(
        schemas(
            // DTO 请求对象
            SysUserLoginRequest,
            SysUserWechatLoginRequest,
            RefreshTokenRequest,
            ChangePasswordRequest,
            UpdateProfileRequest,
            BindWechatRequest,
            UnbindWechatRequest,
            // VO 响应对象
            SysUserLoginResponse,
            RefreshTokenResponse,
            UserProfileResponse,
            // 通用响应类型
            lib_core::response::ApiResult<SysUserLoginResponse>,
            lib_core::response::ApiResult<RefreshTokenResponse>,
            lib_core::response::ApiResult<UserProfileResponse>,
            lib_core::response::ApiResult<String>
        )
    ),
    tags(
        (name = "系统认证管理", description = "系统用户认证相关接口，包括登录、微信登录、注销、令牌刷新、密码修改、个人信息管理和微信绑定功能")
    )
)]
pub struct SysAuthApiDoc;

impl SysAuthApiDoc {
    /// 获取OpenAPI JSON文档
    pub fn get_openapi_json() -> String {
        SysAuthApiDoc::openapi().to_pretty_json().unwrap()
    }

    /// 获取OpenAPI YAML文档
    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&SysAuthApiDoc::openapi()).unwrap()
    }
}

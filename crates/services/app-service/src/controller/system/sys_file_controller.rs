use crate::domain::comm::storage::{
    FileDownloadRequest, FileDownloadResponse, MultiFileUploadResult, UploadResponse,
};
use crate::service::comm::StorageService;
use crate::utils::FileUtil;
use axum::Router;
use axum::extract::{DefaultBodyLimit, Multipart};
use axum::routing::post;
use lib_core::response::ApiResult;
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::OpenApi;
// ==================== 路由配置 ====================

impl SysFileController {
    /// 创建认证的公开路由（无需授权）
    pub fn public_routes() -> Router {
        Router::new()
    }

    /// （需要授权）
    pub fn protected_routes() -> Router {
        Router::new()
            .route("/sys-upload", post(upload_file_2_oss))
            .route("/sys-upload-multiple", post(upload_multiple_files_2_oss))
            .layer(DefaultBodyLimit::max(200 * 1024 * 1024)) // 200MB 支持多文件上传
    }
}

pub struct SysFileController;

#[utoipa::path(
    post,
    path = "/sys-upload",
    summary = "系统文件上传",
    description = "上传单个文件到对象存储服务，支持多种文件类型，最大文件大小 50MB",
    tags = ["系统文件管理"],
    request_body(
        content_type = "multipart/form-data",
        description = "文件上传表单"
    ),
    responses(
        (status = 200, description = "文件上传成功", body = ApiResult<UploadResponse>,
         example = json!({
             "code": 200,
             "message": "上传成功",
             "data": {
                 "file_url": "https://static.oywm.top/invoice-book/system/a7/b2/f47ac10b-58cc-4372-a567-0e02b2c3d479.json",
                 "file_path": "system/uploads/a7/b2/f47ac10b-58cc-4372-a567-0e02b2c3d479.json",
                 "file_size": 1024,
                 "content_type": "application/json"
             }
         })
        )
    )
)]
pub async fn upload_file_2_oss(
    Component(storage_service): Component<StorageService>,
    multipart: Multipart,
) -> ApiResult<UploadResponse> {
    // 提取文件信息
    let file_info =
        match FileUtil::extract_single_file_from_multipart(multipart, Some("file"), None, None)
            .await
        {
            Ok(info) => info,
            Err(e) => {
                return ApiResult::error(400, format!("文件校验失败: {}", e));
            }
        };

    // 上传文件
    let upload_result = match storage_service
        .upload_file(
            file_info.file_data,
            file_info.file_name,
            file_info.content_type,
            Some("system/uploads".to_string()),
            None,
        )
        .await
    {
        Ok(result) => result,
        Err(e) => {
            return ApiResult::error(500, format!("文件上传失败: {}", e));
        }
    };

    ApiResult::success_with_message_and_data(upload_result, "上传成功".to_string())
}

#[utoipa::path(
    post,
    path = "/sys-upload-multiple",
    summary = "系统多文件上传",
    description = "批量上传多个文件到对象存储服务，支持并发上传，总大小限制 200MB",
    tags = ["系统文件管理"],
    request_body(
        content_type = "multipart/form-data",
        description = "多文件上传表单，支持多个 files 字段"
    ),
    responses(
        (status = 200, description = "多文件上传完成", body = ApiResult<MultiFileUploadResult>,
         example = json!({
             "code": 200,
             "message": "批量上传完成",
             "data": {
                 "successful_uploads": [
                     {
                         "file_url": "https://static.oywm.top/invoice-book/system/a7/b2/f47ac10b-58cc-4372-a567-0e02b2c3d479.jpg",
                         "file_path": "system/uploads/a7/b2/f47ac10b-58cc-4372-a567-0e02b2c3d479.jpg",
                         "file_size": 2048,
                         "content_type": "image/jpeg"
                     }
                 ],
                 "failed_uploads": [],
                 "total_files": 1,
                 "successful_count": 1,
                 "failed_count": 0,
                 "total_size": 2048
             }
         })
        )
    )
)]
pub async fn upload_multiple_files_2_oss(
    Component(storage_service): Component<StorageService>,
    multipart: Multipart,
) -> ApiResult<MultiFileUploadResult> {
    // 提取多个文件信息
    let multi_file_info = match FileUtil::extract_files_from_multipart(
        multipart,
        Some(&["files"]),       // 只接受 files 字段
        Some(50 * 1024 * 1024), // 单个文件最大 50MB
        None,                   // 不限制文件类型
    )
    .await
    {
        Ok(info) => info,
        Err(e) => {
            return ApiResult::error(400, format!("文件校验失败: {}", e));
        }
    };

    // 检查是否有文件
    if multi_file_info.files.is_empty() {
        return ApiResult::error(400, "未找到有效的文件".to_string());
    }

    // 转换为 FileUploadInfo 格式
    let file_upload_infos: Vec<crate::domain::comm::storage::FileUploadInfo> = multi_file_info
        .files
        .into_iter()
        .map(|file_info| crate::domain::comm::storage::FileUploadInfo {
            file_name: file_info.file_name,
            file_data: file_info.file_data,
            content_type: file_info.content_type,
            file_size: file_info.file_size,
        })
        .collect();

    // 批量上传文件
    let upload_result = match storage_service
        .upload_multiple_files(
            file_upload_infos,
            Some("system/uploads".to_string()),
            None,
            Some(10), // 最大并发数
        )
        .await
    {
        Ok(result) => result,
        Err(e) => {
            return ApiResult::error(500, format!("批量上传失败: {}", e));
        }
    };

    // 构建响应消息
    let message = if upload_result.failed_count > 0 {
        format!(
            "批量上传完成，成功 {} 个，失败 {} 个",
            upload_result.successful_count, upload_result.failed_count
        )
    } else {
        format!(
            "批量上传完成，全部 {} 个文件上传成功",
            upload_result.successful_count
        )
    };

    ApiResult::success_with_message_and_data(upload_result, message)
}

#[derive(OpenApi)]
#[openapi(
    paths(
        upload_file_2_oss,
        upload_multiple_files_2_oss,
    ),
    components(
        schemas(
            UploadResponse,
            MultiFileUploadResult,
            FileDownloadRequest,
            FileDownloadResponse,
            crate::domain::comm::storage::FailedUploadInfo,
            lib_core::response::ApiResult<UploadResponse>,
            lib_core::response::ApiResult<MultiFileUploadResult>,
            lib_core::response::ApiResult<String>
        )
    ),
    tags(
        (name = "系统文件管理", description = "系统文件上传、下载、管理相关接口")
    )
)]
pub struct SysFileDoc;

impl SysFileDoc {
    /// 获取OpenAPI JSON文档
    pub fn get_openapi_json() -> String {
        SysFileDoc::openapi().to_pretty_json().unwrap()
    }

    /// 获取OpenAPI YAML文档
    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&SysFileDoc::openapi()).unwrap()
    }
}

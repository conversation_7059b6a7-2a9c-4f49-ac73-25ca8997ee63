use crate::domain::system::dto::sys_merchant_category_request::{
    MerchantCategoryStatusRequest, SysMerchantCategoryBatchDeleteRequest,
    SysMerchantCategoryCreateRequest, SysMerchantCategoryPageRequest,
    SysMerchantCategoryUpdateRequest,
};
use crate::domain::system::vo::merchant_category_vo::{
    SysMerchantCategoryDetailResponse, SysMerchantCategoryListResponse,
    SysMerchantCategorySelectItem, SysMerchantCategoryStatsResponse,
};
use crate::service::system::SysMerchantCategoryService;
use crate::utils::custom_validator::{MyPath, MyQuery, ValidJson};
use axum::{
    middleware, routing::{delete, get, post, put},
    Router,
};
use lib_auth::require_permission;
use lib_core::response::{ApiResult, PageData};
use lib_web::extractor::Component;
use tracing::info;
use utoipa::OpenApi;
use uuid::Uuid;

/// 商户分类管理控制器
pub struct SysMerchantCategoryController;

impl SysMerchantCategoryController {
    /// 创建商户分类管理公开路由
    pub fn public_routes() -> Router {
        Router::new()
        // 商户分类管理暂无公开路由
    }

    /// 创建商户分类管理受保护路由
    pub fn protected_routes() -> Router {
        Router::new()
            // 获取商户分类选择项（下拉选择用）
            .route(
                "/business/merchant-categories/select",
                get(get_category_select_items).layer(middleware::from_fn(require_permission(
                    "merchant:category:list",
                ))),
            )
            // 获取商户分类统计信息
            .route(
                "/business/merchant-categories/stats",
                get(get_category_stats).layer(middleware::from_fn(require_permission(
                    "merchant:category:list",
                ))),
            )
            // 批量删除商户分类
            .route(
                "/business/merchant-categories/batch-delete",
                post(batch_delete_categories).layer(middleware::from_fn(require_permission(
                    "merchant:category:delete",
                ))),
            )
            // 商户分类状态切换
            .route(
                "/business/merchant-categories/{category_id}/status",
                put(change_category_status).layer(middleware::from_fn(require_permission(
                    "merchant:category:update",
                ))),
            )
            // 商户分类详情查询
            .route(
                "/business/merchant-categories/{category_id}",
                get(get_category_by_id).layer(middleware::from_fn(require_permission(
                    "merchant:category:detail",
                ))),
            )
            // 商户分类更新
            .route(
                "/business/merchant-categories/{category_id}",
                put(update_category).layer(middleware::from_fn(require_permission(
                    "merchant:category:update",
                ))),
            )
            // 商户分类删除
            .route(
                "/business/merchant-categories/{category_id}",
                delete(delete_category).layer(middleware::from_fn(require_permission(
                    "merchant:category:delete",
                ))),
            )
            // 商户分类列表查询
            .route(
                "/business/merchant-categories",
                get(page_categories).layer(middleware::from_fn(require_permission(
                    "merchant:category:list",
                ))),
            )
            // 商户分类创建
            .route(
                "/business/merchant-categories",
                post(create_category).layer(middleware::from_fn(require_permission(
                    "merchant:category:create",
                ))),
            )
    }
}

/// 分页查询商户分类列表
#[utoipa::path(
    get,
    path = "/business/merchant-categories",
    summary = "分页查询商户分类列表",
    description = "根据查询条件分页获取商户分类信息",
    tags = ["商户分类管理"],
    params(SysMerchantCategoryPageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<SysMerchantCategoryListResponse>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "items": [
                     {
                         "id": "550e8400-e29b-41d4-a716-************",
                         "category_name": "餐饮",
                         "category_code": "CATERING",
                         "sort_order": 1,
                         "status": 1,
                         "created_date": "2023-01-01 10:00:00",
                         "updated_date": "2023-01-01 10:00:00"
                     }
                 ],
                 "total": 1,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 1
             }
         })
        )
    )
)]
pub async fn page_categories(
    Component(sys_merchant_category_service): Component<SysMerchantCategoryService>,
    MyQuery(req): MyQuery<SysMerchantCategoryPageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<SysMerchantCategoryListResponse>> {
    info!(
        "分页查询商户分类列表请求: {:?}，操作人: {}",
        req, current_user.account
    );

    match sys_merchant_category_service.page_categories(req).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 根据ID查询商户分类详情
#[utoipa::path(
    get,
    path = "/business/merchant-categories/{category_id}",
    summary = "查询商户分类详情",
    description = "根据商户分类ID获取详细信息",
    tags = ["商户分类管理"],
    params(
        ("category_id" = Uuid, Path, description = "商户分类ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SysMerchantCategoryDetailResponse>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "id": "550e8400-e29b-41d4-a716-************",
                 "category_name": "餐饮",
                 "category_code": "CATERING",
                 "description": "餐饮类商户分类",
                 "sort_order": 1,
                 "status": 1,
                 "created_date": "2023-01-01 10:00:00",
                 "updated_date": "2023-01-01 10:00:00",
                 "created_by": "550e8400-e29b-41d4-a716-************",
                 "updated_by": "550e8400-e29b-41d4-a716-************",
                 "remark": "餐饮类商户分类备注"
             }
         })
        )
    )
)]
pub async fn get_category_by_id(
    Component(sys_merchant_category_service): Component<SysMerchantCategoryService>,
    MyPath(category_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SysMerchantCategoryDetailResponse> {
    info!(
        "查询商户分类详情: {}，操作人: {}",
        category_id, current_user.account
    );

    match sys_merchant_category_service
        .get_category_by_id(category_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 创建商户分类
#[utoipa::path(
    post,
    path = "/business/merchant-categories",
    summary = "创建商户分类",
    description = "创建新的商户分类",
    tags = ["商户分类管理"],
    request_body = SysMerchantCategoryPageRequest,
    responses(
        (status = 200, description = "创建成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户分类创建成功",
             "data": null
         })
        )
    )
)]
pub async fn create_category(
    Component(sys_merchant_category_service): Component<SysMerchantCategoryService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantCategoryCreateRequest>,
) -> ApiResult<String> {
    info!(
        "创建商户分类请求: {:?}，操作人: {}",
        req, current_user.account
    );

    let operator_id = Uuid::parse_str(&current_user.id).unwrap_or_default();
    match sys_merchant_category_service
        .create_category(req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新商户分类
#[utoipa::path(
    put,
    path = "/business/merchant-categories/{category_id}",
    summary = "更新商户分类",
    description = "更新指定ID的商户分类信息",
    tags = ["商户分类管理"],
    params(
        ("category_id" = Uuid, Path, description = "商户分类ID")
    ),
    request_body = SysMerchantCategoryUpdateRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户分类更新成功",
             "data": null
         })
        )
    )
)]
pub async fn update_category(
    Component(sys_merchant_category_service): Component<SysMerchantCategoryService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(category_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysMerchantCategoryUpdateRequest>,
) -> ApiResult<String> {
    info!(
        "更新商户分类请求: {}，{:?}，操作人: {}",
        category_id, req, current_user.account
    );

    let operator_id = Uuid::parse_str(&current_user.id).unwrap_or_default();
    match sys_merchant_category_service
        .update_category(category_id, req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 删除商户分类
#[utoipa::path(
    delete,
    path = "/business/merchant-categories/{category_id}",
    summary = "删除商户分类",
    description = "删除指定ID的商户分类",
    tags = ["商户分类管理"],
    params(
        ("category_id" = Uuid, Path, description = "商户分类ID")
    ),
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户分类删除成功",
             "data": null
         })
        )
    )
)]
pub async fn delete_category(
    Component(sys_merchant_category_service): Component<SysMerchantCategoryService>,
    MyPath(category_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!(
        "删除商户分类请求: {}，操作人: {}",
        category_id, current_user.account
    );

    let operator_id = Uuid::parse_str(&current_user.id).unwrap_or_default();
    match sys_merchant_category_service
        .delete_category(category_id, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 批量删除商户分类
#[utoipa::path(
    post,
    path = "/business/merchant-categories/batch-delete",
    summary = "批量删除商户分类",
    description = "批量删除多个商户分类",
    tags = ["商户分类管理"],
    request_body = SysMerchantCategoryBatchDeleteRequest,
    responses(
        (status = 200, description = "批量删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "批量删除成功，共删除 2 个分类",
             "data": null
         })
        )
    )
)]
pub async fn batch_delete_categories(
    Component(sys_merchant_category_service): Component<SysMerchantCategoryService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantCategoryBatchDeleteRequest>,
) -> ApiResult<String> {
    info!(
        "批量删除商户分类请求: {:?}，操作人: {}",
        req.category_ids, current_user.account
    );

    let operator_id = Uuid::parse_str(&current_user.id).unwrap_or_default();
    match sys_merchant_category_service
        .batch_delete_categories(req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 切换商户分类状态
#[utoipa::path(
    put,
    path = "/business/merchant-categories/{category_id}/status",
    summary = "切换商户分类状态",
    description = "启用或禁用指定ID的商户分类",
    tags = ["商户分类管理"],
    params(
        ("category_id" = Uuid, Path, description = "商户分类ID")
    ),
    request_body = MerchantCategoryStatusRequest,
    responses(
        (status = 200, description = "状态切换成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "分类状态已切换为启用",
             "data": null
         })
        )
    )
)]
pub async fn change_category_status(
    Component(sys_merchant_category_service): Component<SysMerchantCategoryService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(category_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<MerchantCategoryStatusRequest>,
) -> ApiResult<String> {
    info!(
        "切换商户分类状态请求: {} -> {}，操作人: {}",
        category_id, req.status, current_user.account
    );

    let operator_id = Uuid::parse_str(&current_user.id).unwrap_or_default();
    match sys_merchant_category_service
        .change_category_status(category_id, req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户分类选择项
#[utoipa::path(
    get,
    path = "/business/merchant-categories/select",
    summary = "获取商户分类选择项",
    description = "获取所有可选的商户分类项，用于下拉选择",
    tags = ["商户分类管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SysMerchantCategorySelectItem>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": [
                 {
                     "id": "550e8400-e29b-41d4-a716-************",
                     "category_name": "餐饮",
                     "category_code": "CATERING",
                     "sort_order": 1
                 }
             ]
         })
        )
    )
)]
pub async fn get_category_select_items(
    Component(sys_merchant_category_service): Component<SysMerchantCategoryService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SysMerchantCategorySelectItem>> {
    info!("获取商户分类选择项请求，操作人: {}", current_user.account);

    match sys_merchant_category_service
        .get_category_select_items()
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户分类统计信息
#[utoipa::path(
    get,
    path = "/business/merchant-categories/stats",
    summary = "获取商户分类统计信息",
    description = "获取所有商户分类的统计信息，包含每个分类下的商户数量",
    tags = ["商户分类管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SysMerchantCategoryStatsResponse>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": [
                 {
                     "category_id": "550e8400-e29b-41d4-a716-************",
                     "category_name": "餐饮",
                     "merchant_count": 25,
                     "active_merchant_count": 20,
                     "inactive_merchant_count": 5
                 }
             ]
         })
        )
    )
)]
pub async fn get_category_stats(
    Component(sys_merchant_category_service): Component<SysMerchantCategoryService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SysMerchantCategoryStatsResponse>> {
    info!("获取商户分类统计信息请求，操作人: {}", current_user.account);

    match sys_merchant_category_service.get_category_stats().await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 商户分类管理API文档
#[derive(OpenApi)]
#[openapi(
    paths(
        page_categories,
        get_category_by_id,
        create_category,
        update_category,
        delete_category,
        batch_delete_categories,
        change_category_status,
        get_category_select_items,
        get_category_stats
    ),
    components(
        schemas(
            SysMerchantCategoryPageRequest,
            SysMerchantCategoryCreateRequest,
            SysMerchantCategoryUpdateRequest,
            SysMerchantCategoryBatchDeleteRequest,
            MerchantCategoryStatusRequest,
            SysMerchantCategoryListResponse,
            SysMerchantCategoryDetailResponse,
            SysMerchantCategorySelectItem,
            SysMerchantCategoryStatsResponse,
            lib_core::response::PageData<SysMerchantCategoryListResponse>,
            lib_core::response::ApiResult<SysMerchantCategoryListResponse>,
            lib_core::response::ApiResult<String>,
            lib_core::response::ApiResult<Vec<SysMerchantCategorySelectItem>>,
            lib_core::response::ApiResult<Vec<SysMerchantCategoryStatsResponse>>,
            lib_core::response::ApiResult<SysMerchantCategoryDetailResponse>
        )
    ),
    tags(
        (name = "商户分类管理", description = "商户分类管理相关接口")
    )
)]
pub struct SysMerchantCategoryApiDoc;

impl SysMerchantCategoryApiDoc {
    pub fn get_openapi_json() -> String {
        serde_json::to_string_pretty(&Self::openapi()).unwrap()
    }

    pub fn get_openapi_yaml() -> String {
        let json = Self::get_openapi_json();
        let value: serde_json::Value = serde_json::from_str(&json).unwrap();
        serde_yaml::to_string(&value).unwrap()
    }
}

use crate::domain::system::dto::sys_permission_request::{
    SysPermissionBatchDeleteRequest, SysPermissionCreateRequest, SysPermissionPageRequest,
    SysPermissionStatusRequest, SysPermissionUpdateRequest,
};
use crate::domain::system::vo::{
    RouterVo, SysPermissionDetailResponse, SysPermissionListResponse, SysPermissionSelectItem,
    SysPermissionTreeNode,
};
use crate::service::system::SysPermissionService;
use crate::utils::custom_validator::{MyPath, MyQuery, ValidJson};
use axum::{
    middleware, routing::{delete, get, post, put},
    Router,
};
use lib_auth::require_permission;
use lib_core::response::{ApiResult, PageData};
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use tracing::{error, info};
use utoipa::OpenApi;
use uuid::Uuid;

/// 系统权限菜单管理控制器
pub struct SysPermissionController;

impl SysPermissionController {
    /// 创建权限菜单管理公开路由
    pub fn public_routes() -> Router {
        Router::new()
        // 权限菜单管理暂无公开路由
    }

    /// 创建权限菜单管理受保护路由
    pub fn protected_routes() -> Router {
        Router::new()
            // 获取用户可访问的路由菜单
            .route("/system/user/routers", get(get_user_routers))
            // 获取权限菜单选择项（下拉选择用）
            .route(
                "/system/permissions/select",
                get(get_permission_select_items).layer(middleware::from_fn(require_permission(
                    "sys:permission:list",
                ))),
            )
            // 获取权限菜单树
            .route(
                "/system/permissions/tree",
                get(get_permission_tree).layer(middleware::from_fn(require_permission(
                    "sys:permission:list",
                ))),
            )
            // 批量删除权限菜单
            .route(
                "/system/permissions/batch-delete",
                post(batch_delete_permissions).layer(middleware::from_fn(require_permission(
                    "sys:permission:delete",
                ))),
            )
            // 权限菜单状态切换
            .route(
                "/system/permissions/{permission_id}/status",
                put(change_permission_status).layer(middleware::from_fn(require_permission(
                    "sys:permission:update",
                ))),
            )
            // 权限菜单详情查询
            .route(
                "/system/permissions/{permission_id}",
                get(get_permission_by_id).layer(middleware::from_fn(require_permission(
                    "sys:permission:detail",
                ))),
            )
            // 权限菜单更新
            .route(
                "/system/permissions/{permission_id}",
                put(update_permission).layer(middleware::from_fn(require_permission(
                    "sys:permission:update",
                ))),
            )
            // 权限菜单删除
            .route(
                "/system/permissions/{permission_id}",
                delete(delete_permission).layer(middleware::from_fn(require_permission(
                    "sys:permission:delete",
                ))),
            )
            // 权限菜单列表查询
            .route(
                "/system/permissions",
                get(page_permissions).layer(middleware::from_fn(require_permission(
                    "sys:permission:list",
                ))),
            )
            // 权限菜单创建
            .route(
                "/system/permissions",
                post(create_permission).layer(middleware::from_fn(require_permission(
                    "sys:permission:create",
                ))),
            )
    }
}

/// 分页查询权限菜单列表
#[utoipa::path(
    get,
    path = "system/permissions",
    summary = "分页查询权限菜单列表",
    description = "根据查询条件分页获取系统权限菜单信息",
    tags = ["系统权限菜单管理"],
    params(SysPermissionPageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<SysPermissionListResponse>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "items": [
                     {
                         "id": "550e8400-e29b-41d4-a716-************",
                         "menu_name": "系统管理",
                         "parent_id": null,
                         "order_num": 1,
                         "path": "/system",
                         "component": "Layout",
                         "menu_type": 0,
                         "status": 0,
                         "perms": null,
                         "icon": "system",
                         "created_date": "2023-01-01 10:00:00",
                         "updated_date": "2023-01-01 10:00:00"
                     }
                 ],
                 "total": 1,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 1
             }
         })
        )
    )
)]
pub async fn page_permissions(
    Component(permission_service): Component<SysPermissionService>,
    MyQuery(req): MyQuery<SysPermissionPageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<SysPermissionListResponse>> {
    info!(
        "分页查询权限菜单列表请求: {:?}，操作人: {}",
        req, current_user.account
    );

    match permission_service.page_permissions(req).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 根据ID查询权限菜单详情
#[utoipa::path(
    get,
    path = "system/permissions/{permission_id}",
    summary = "查询权限菜单详情",
    description = "根据权限菜单ID获取详细信息，包含子菜单信息",
    tags = ["系统权限菜单管理"],
    params(
        ("permission_id" = Uuid, Path, description = "权限菜单ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SysPermissionDetailResponse>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "id": "550e8400-e29b-41d4-a716-************",
                 "menu_name": "系统管理",
                 "parent_id": null,
                 "parent_name": null,
                 "order_num": 1,
                 "path": "/system",
                 "component": "Layout",
                 "query": null,
                 "is_frame": 1,
                 "is_cache": 0,
                 "menu_type": 0,
                 "visible": 0,
                 "status": 0,
                 "perms": null,
                 "icon": "system",
                 "created_date": "2023-01-01 10:00:00",
                 "updated_date": "2023-01-01 10:00:00",
                 "created_by": "550e8400-e29b-41d4-a716-************",
                 "updated_by": "550e8400-e29b-41d4-a716-************",
                 "remark": "系统管理目录",
                 "children": []
             }
         })
        )
    )
)]
pub async fn get_permission_by_id(
    Component(permission_service): Component<SysPermissionService>,
    MyPath(permission_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SysPermissionDetailResponse> {
    info!(
        "查询权限菜单详情: {}，操作人: {}",
        permission_id, current_user.account
    );

    match permission_service.get_permission_by_id(permission_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取权限菜单树
#[utoipa::path(
    get,
    path = "system/permissions/tree",
    summary = "获取权限菜单树",
    description = "获取所有权限菜单的树形结构",
    tags = ["系统权限菜单管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SysPermissionTreeNode>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": [
                 {
                     "id": "550e8400-e29b-41d4-a716-************",
                     "menu_name": "系统管理",
                     "parent_id": null,
                     "order_num": 1,
                     "path": "/system",
                     "component": "Layout",
                     "menu_type": 0,
                     "status": 0,
                     "perms": null,
                     "icon": "system",
                     "children": [
                         {
                             "id": "550e8400-e29b-41d4-a716-446655440001",
                             "menu_name": "用户管理",
                             "parent_id": "550e8400-e29b-41d4-a716-************",
                             "order_num": 1,
                             "path": "user",
                             "component": "system/user/index",
                             "menu_type": 1,
                             "status": 0,
                             "perms": "sys:user:list",
                             "icon": "user",
                             "children": []
                         }
                     ]
                 }
             ]
         })
        )
    )
)]
pub async fn get_permission_tree(
    Component(permission_service): Component<SysPermissionService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SysPermissionTreeNode>> {
    info!("获取权限菜单树，操作人: {}", current_user.account);

    match permission_service.get_permission_tree().await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 创建权限菜单
#[utoipa::path(
    post,
    path = "system/permissions",
    summary = "创建权限菜单",
    description = "创建新的权限菜单",
    tags = ["系统权限菜单管理"],
    request_body = SysPermissionCreateRequest,
    responses(
        (status = 200, description = "创建成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "权限菜单创建成功",
             "data": null
         })
        )
    )
)]
pub async fn create_permission(
    Component(permission_service): Component<SysPermissionService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysPermissionCreateRequest>,
) -> ApiResult<String> {
    info!(
        "创建权限菜单请求: {:?}，操作人: {}",
        req, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match permission_service.create_permission(req, operator_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新权限菜单
#[utoipa::path(
    put,
    path = "system/permissions/{permission_id}",
    summary = "更新权限菜单",
    description = "更新指定ID的权限菜单信息",
    tags = ["系统权限菜单管理"],
    params(
        ("permission_id" = Uuid, Path, description = "权限菜单ID")
    ),
    request_body = SysPermissionUpdateRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "权限菜单更新成功",
             "data": null
         })
        )
    )
)]
pub async fn update_permission(
    Component(permission_service): Component<SysPermissionService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(permission_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysPermissionUpdateRequest>,
) -> ApiResult<String> {
    info!(
        "更新权限菜单请求: {}，{:?}，操作人: {}",
        permission_id, req, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match permission_service
        .update_permission(permission_id, req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 删除权限菜单
#[utoipa::path(
    delete,
    path = "system/permissions/{permission_id}",
    summary = "删除权限菜单",
    description = "删除指定ID的权限菜单",
    tags = ["系统权限菜单管理"],
    params(
        ("permission_id" = Uuid, Path, description = "权限菜单ID")
    ),
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "权限菜单删除成功",
             "data": null
         })
        )
    )
)]
pub async fn delete_permission(
    Component(permission_service): Component<SysPermissionService>,
    MyPath(permission_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!(
        "删除权限菜单请求: {}，操作人: {}",
        permission_id, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match permission_service
        .delete_permission(permission_id, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 批量删除权限菜单
#[utoipa::path(
    post,
    path = "system/permissions/batch-delete",
    summary = "批量删除权限菜单",
    description = "批量删除多个权限菜单",
    tags = ["系统权限菜单管理"],
    request_body = SysPermissionBatchDeleteRequest,
    responses(
        (status = 200, description = "批量删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "批量删除成功，共删除 2 个菜单",
             "data": null
         })
        )
    )
)]
pub async fn batch_delete_permissions(
    Component(permission_service): Component<SysPermissionService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysPermissionBatchDeleteRequest>,
) -> ApiResult<String> {
    info!(
        "批量删除权限菜单请求: {:?}，操作人: {}",
        req.permission_ids, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match permission_service
        .batch_delete_permissions(req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 切换权限菜单状态
#[utoipa::path(
    put,
    path = "system/permissions/{permission_id}/status",
    summary = "切换权限菜单状态",
    description = "启用或停用指定ID的权限菜单",
    tags = ["系统权限菜单管理"],
    params(
        ("permission_id" = Uuid, Path, description = "权限菜单ID")
    ),
    request_body = SysPermissionStatusRequest,
    responses(
        (status = 200, description = "状态切换成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "菜单状态已切换为正常",
             "data": null
         })
        )
    )
)]
pub async fn change_permission_status(
    Component(permission_service): Component<SysPermissionService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(permission_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysPermissionStatusRequest>,
) -> ApiResult<String> {
    info!(
        "切换权限菜单状态请求: {} -> {}，操作人: {}",
        permission_id, req.status, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match permission_service
        .change_permission_status(permission_id, req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取权限菜单选择项
#[utoipa::path(
    get,
    path = "system/permissions/select",
    summary = "获取权限菜单选择项",
    description = "获取所有可选的权限菜单项，用于下拉选择",
    tags = ["系统权限菜单管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SysPermissionSelectItem>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": [
                 {
                     "id": "550e8400-e29b-41d4-a716-************",
                     "menu_name": "系统管理",
                     "parent_id": null,
                     "order_num": 1,
                     "menu_type": 0
                 }
             ]
         })
        )
    )
)]
pub async fn get_permission_select_items(
    Component(permission_service): Component<SysPermissionService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SysPermissionSelectItem>> {
    info!("获取权限菜单选择项请求，操作人: {}", current_user.account);

    match permission_service.get_permission_select_items().await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取用户可访问的路由菜单
#[utoipa::path(
    get,
    path = "system/user/routers",
    summary = "获取用户可访问的路由菜单",
    description = "根据当前用户权限获取可访问的前端路由菜单",
    tags = ["系统权限菜单管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<RouterVo>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": [
                 {
                     "name": "System",
                     "path": "/system",
                     "hidden": false,
                     "redirect": null,
                     "component": "Layout",
                     "always_show": true,
                     "perms": null,
                     "menu_type": 1,
                     "meta": {
                         "title": "系统管理",
                         "icon": "system",
                         "no_cache": false,
                         "link": null
                     },
                     "children": [
                         {
                             "name": "User",
                             "path": "user",
                             "hidden": false,
                             "redirect": null,
                             "component": "system/user/index",
                             "always_show": false,
                             "perms": "sys:user:page",
                             "menu_type": 2,
                             "meta": {
                                 "title": "用户管理",
                                 "icon": "user",
                                 "no_cache": false,
                                 "link": null
                             },
                             "children": []
                         }
                     ]
                 }
             ]
         })
        )
    )
)]
pub async fn get_user_routers(
    Component(permission_service): Component<SysPermissionService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<RouterVo>> {
    info!("获取用户路由菜单请求，用户: {}", current_user.account);

    let user_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("用户ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "用户ID格式错误".to_string());
        }
    };

    match permission_service.get_user_routers(user_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 权限菜单管理API文档
#[derive(OpenApi)]
#[openapi(
    paths(
        page_permissions,
        get_permission_by_id,
        get_permission_tree,
        create_permission,
        update_permission,
        delete_permission,
        batch_delete_permissions,
        change_permission_status,
        get_permission_select_items,
        get_user_routers
    ),
    components(
        schemas(
            crate::domain::system::dto::sys_permission_request::SysPermissionPageRequest,
            crate::domain::system::dto::sys_permission_request::SysPermissionCreateRequest,
            crate::domain::system::dto::sys_permission_request::SysPermissionUpdateRequest,
            crate::domain::system::dto::sys_permission_request::SysPermissionBatchDeleteRequest,
            crate::domain::system::dto::sys_permission_request::SysPermissionStatusRequest,
            crate::domain::system::vo::SysPermissionListResponse,
            crate::domain::system::vo::SysPermissionSelectItem,
            crate::domain::system::vo::RouterVo,
            crate::domain::system::vo::RouterMeta,
            crate::domain::system::vo::SysPermissionTreeNode,
            crate::domain::system::vo::SysPermissionDetailResponse,
            lib_core::response::PageData<crate::domain::system::vo::SysPermissionListResponse>,
            lib_core::response::ApiResult<lib_core::response::PageData<crate::domain::system::vo::SysPermissionListResponse>>,
            lib_core::response::ApiResult<String>,
            lib_core::response::ApiResult<Vec<crate::domain::system::vo::SysPermissionSelectItem>>,
            lib_core::response::ApiResult<Vec<crate::domain::system::vo::RouterVo>>,
            lib_core::response::ApiResult<Vec<crate::domain::system::vo::SysPermissionTreeNode>>,
            lib_core::response::ApiResult<crate::domain::system::vo::SysPermissionDetailResponse>
        )
    ),
    tags(
        (name = "系统权限菜单管理", description = "系统权限管理相关接口")
    )
)]
pub struct SysPermissionApiDoc;

impl SysPermissionApiDoc {
    pub fn get_openapi_json() -> String {
        serde_json::to_string_pretty(&Self::openapi()).unwrap()
    }

    pub fn get_openapi_yaml() -> String {
        let json = Self::get_openapi_json();
        let value: serde_json::Value = serde_json::from_str(&json).unwrap();
        serde_yaml::to_string(&value).unwrap()
    }
}

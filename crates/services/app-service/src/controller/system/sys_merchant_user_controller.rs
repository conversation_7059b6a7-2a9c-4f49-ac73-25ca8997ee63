use crate::domain::system::dto::sys_merchant_user_request::{
    SysMerchantUserBatchDeleteRequest, SysMerchantUserCreateRequest, SysMerchantUserPageRequest,
    SysMerchantUserResetPasswordRequest, SysMerchantUserStatusRequest,
    SysMerchantUserUpdateRequest,
};
use crate::domain::system::dto::sys_merchant_user_role_request::SysMerchantUserRoleAssignRequest;
use crate::domain::system::vo::merchant_user_vo::{
    SysMerchantUserBasicResponse, SysMerchantUserDetailResponse, SysMerchantUserListResponse,
    SysMerchantUserRoleInfo, SysMerchantUserSelectItem, SysMerchantUserStatsResponse,
};
use crate::service::system::SysMerchantUserService;
use crate::utils::custom_validator::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>};
use axum::extract::Query;
use axum::{
    middleware, routing::{delete, get, post, put},
    Router,
};
use lib_auth::require_permission;
use lib_core::response::{ApiResult, PageData};
use lib_web::extractor::Component;
use tracing::{error, info};
use utoipa::OpenApi;
use uuid::Uuid;

// ==================== 路由配置 ====================

impl SysMerchantUserController {
    /// 创建系统商户用户管理的公开路由（无需授权）
    pub fn public_routes() -> Router {
        Router::new()
        // 系统商户用户管理暂无公开路由
    }

    /// 创建系统商户用户管理的受保护路由（需要授权）
    pub fn protected_routes() -> Router {
        Router::new()
            // 检查接口（具体路径在前）
            // 检查用户名是否存在
            .route(
                "/system/merchant-users/check-username",
                get(check_username_exists).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:check",
                ))),
            )
            // 检查真实姓名是否存在
            .route(
                "/system/merchant-users/check-real-name",
                get(check_real_name_exists).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:check",
                ))),
            )
            // 检查手机号是否存在
            .route(
                "/system/merchant-users/check-phone",
                get(check_phone_exists).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:check",
                ))),
            )
            // 检查邮箱是否存在
            .route(
                "/system/merchant-users/check-email",
                get(check_email_exists).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:check",
                ))),
            )
            // 选择项和基础信息接口
            .route(
                "/system/merchant-users/select-items",
                get(get_merchant_user_select_items).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:page",
                ))),
            )
            // 基础列表接口
            .route(
                "/system/merchant-users/basic-list",
                get(get_merchant_user_basic_list).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:page",
                ))),
            )
            // 统计接口
            .route(
                "/system/merchant-users/stats",
                get(get_merchant_user_stats).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:stats",
                ))),
            )
            // 批量操作
            .route(
                "/system/merchant-users/batch",
                delete(batch_delete_merchant_users).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:delete",
                ))),
            )
            // 重置密码
            .route(
                "/system/merchant-users/{id}/reset-password",
                put(reset_merchant_user_password).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:reset-password",
                ))),
            )
            // 切换状态
            .route(
                "/system/merchant-users/{id}/status",
                put(change_merchant_user_status).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:status",
                ))),
            )
            // 商户用户详情查询
            .route(
                "/system/merchant-users/{id}",
                get(get_merchant_user_by_id).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:detail",
                ))),
            )
            // 商户用户更新
            .route(
                "/system/merchant-users/{id}",
                put(update_merchant_user).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:update",
                ))),
            )
            // 商户用户删除
            .route(
                "/system/merchant-users/{id}",
                delete(delete_merchant_user).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:delete",
                ))),
            )
            // 商户用户列表查询
            .route(
                "/system/merchant-users",
                get(page_merchant_users).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:page",
                ))),
            )
            // 商户用户创建
            .route(
                "/system/merchant-users",
                post(create_merchant_user).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:create",
                ))),
            )
            // 角色分配接口
            .route(
                "/system/merchant-users/{id}/assign-roles",
                post(assign_roles_to_user).layer(middleware::from_fn(require_permission(
                    "sys:merchant-user:assign-roles",
                ))),
            )
    }
}

/// 系统商户用户管理控制器结构体（用于组织路由方法）
pub struct SysMerchantUserController;

// ==================== 系统商户用户管理接口 ====================

/// 分页查询商户用户列表
#[utoipa::path(
    get,
    path = "system/merchant-users",
    tags = ["系统商户用户管理"],
    summary = "分页查询商户用户列表",
    description = "根据查询条件分页获取商户用户列表，支持多条件搜索和过滤",
    params(SysMerchantUserPageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<SysMerchantUserListResponse>>,
         example = json!({
             "code": 200,
             "message": "查询成功",
             "data": {
                 "items": [
                     {
                         "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                         "username": "zhangwei",
                         "real_name": "张伟",
                         "phone": "138****1234",
                         "email": "z***@example.com",
                         "gender": 1,
                         "status": 1,
                         "created_date": "2023-05-24 10:30:00",
                         "updated_date": "2023-05-24 10:30:00"
                     }
                 ],
                 "total": 100,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 10
             }
         })
        )
    )
)]
pub async fn page_merchant_users(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    MyQuery(req): MyQuery<SysMerchantUserPageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<SysMerchantUserListResponse>> {
    info!(
        "分页查询商户用户列表请求: {:?}，操作人: {}",
        req, current_user.account
    );

    match merchant_user_service.page_merchant_users(req).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 根据ID查询商户用户详情
#[utoipa::path(
    get,
    path = "system/merchant-users/{id}",
    tags = ["系统商户用户管理"],
    summary = "查询商户用户详情",
    description = "根据商户用户ID查询商户用户的详细信息",
    params(
        ("id" = String, Path, description = "商户用户ID", example = "550e8400-e29b-41d4-a716-************")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SysMerchantUserDetailResponse>,
         example = json!({
             "code": 200,
             "message": "查询成功",
             "data": {
                 "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                 "username": "zhangwei",
                 "real_name": "张伟",
                 "phone": "***********",
                 "email": "<EMAIL>",
                 "gender": 1,
                 "status": 1,
                 "created_date": "2023-01-01 00:00:00",
                 "updated_date": "2023-05-24 10:30:00",
                 "remark": "商户管理员"
             }
         })
        )
    )
)]
pub async fn get_merchant_user_by_id(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    MyPath(user_id): MyPath<String>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SysMerchantUserDetailResponse> {
    info!(
        "查询商户用户详情请求: {}，操作人: {}",
        user_id, current_user.account
    );

    let user_uuid = match Uuid::parse_str(&user_id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("无效的商户用户ID格式: {}, 错误: {}", user_id, e);
            return ApiResult::error(400, "无效的商户用户ID格式".to_string());
        }
    };

    match merchant_user_service.get_merchant_user_by_id(user_uuid).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 创建商户用户
#[utoipa::path(
    post,
    path = "system/merchant-users",
    tags = ["系统商户用户管理"],
    summary = "创建商户用户",
    description = "创建新的商户用户，包括用户基本信息验证",
    request_body = SysMerchantUserCreateRequest,
    responses(
        (status = 200, description = "创建成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户用户创建成功",
             "data": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
         })
        )
    )

)]
pub async fn create_merchant_user(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantUserCreateRequest>,
) -> ApiResult<String> {
    info!(
        "创建商户用户请求: {}，操作人: {}",
        req.username, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("操作人ID格式错误: {}, 错误: {}", current_user.id, e);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match merchant_user_service.create_merchant_user(req, operator_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新商户用户
#[utoipa::path(
    put,
    path = "system/merchant-users/{id}",
    tags = ["系统商户用户管理"],
    summary = "更新商户用户",
    description = "更新指定商户用户的信息",
    params(
        ("id" = String, Path, description = "商户用户ID", example = "550e8400-e29b-41d4-a716-************")
    ),
    request_body = SysMerchantUserUpdateRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户用户更新成功",
             "data": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
         })
        )
    )
)]
pub async fn update_merchant_user(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(user_id): MyPath<String>,
    ValidJson(req): ValidJson<SysMerchantUserUpdateRequest>,
) -> ApiResult<String> {
    info!(
        "更新商户用户请求: {}，操作人: {}",
        user_id, current_user.account
    );

    let user_uuid = match Uuid::parse_str(&user_id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("无效的商户用户ID格式: {}, 错误: {}", user_id, e);
            return ApiResult::error(400, "无效的商户用户ID格式".to_string());
        }
    };

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("操作人ID格式错误: {}, 错误: {}", current_user.id, e);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match merchant_user_service.update_merchant_user(user_uuid, req, operator_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 删除商户用户
#[utoipa::path(
    delete,
    path = "system/merchant-users/{id}",
    tags = ["系统商户用户管理"],
    summary = "删除商户用户",
    description = "删除指定的商户用户",
    params(
        ("id" = String, Path, description = "商户用户ID", example = "550e8400-e29b-41d4-a716-************")
    ),
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户用户删除成功",
             "data": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
         })
        )
    )
)]
pub async fn delete_merchant_user(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    MyPath(user_id): MyPath<String>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!(
        "删除商户用户请求: {}，操作人: {}",
        user_id, current_user.account
    );

    let user_uuid = match Uuid::parse_str(&user_id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("无效的商户用户ID格式: {}, 错误: {}", user_id, e);
            return ApiResult::error(400, "无效的商户用户ID格式".to_string());
        }
    };

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("操作人ID格式错误: {}, 错误: {}", current_user.id, e);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match merchant_user_service.delete_merchant_user(user_uuid, operator_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 批量删除商户用户
#[utoipa::path(
    delete,
    path = "system/merchant-users/batch",
    tags = ["系统商户用户管理"],
    summary = "批量删除商户用户",
    description = "根据商户用户ID列表批量删除商户用户",
    request_body = SysMerchantUserBatchDeleteRequest,
    responses(
        (status = 200, description = "批量删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "批量删除商户用户成功",
             "data": "成功删除2个商户用户"
         })
        )
    )
)]
pub async fn batch_delete_merchant_users(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantUserBatchDeleteRequest>,
) -> ApiResult<String> {
    info!(
        "批量删除商户用户请求: {:?}，操作人: {}",
        req.user_ids, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("操作人ID格式错误: {}, 错误: {}", current_user.id, e);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match merchant_user_service.batch_delete_merchant_users(req, operator_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 重置商户用户密码
#[utoipa::path(
    put,
    path = "system/merchant-users/{id}/reset-password",
    tags = ["系统商户用户管理"],
    summary = "重置商户用户密码",
    description = "重置指定商户用户的密码",
    params(
        ("id" = String, Path, description = "商户用户ID", example = "550e8400-e29b-41d4-a716-************")
    ),
    request_body = SysMerchantUserResetPasswordRequest,
    responses(
        (status = 200, description = "密码重置成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "密码重置成功",
             "data": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
         })
        )
    )
)]
pub async fn reset_merchant_user_password(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(user_id): MyPath<String>,
    ValidJson(req): ValidJson<SysMerchantUserResetPasswordRequest>,
) -> ApiResult<String> {
    info!(
        "重置商户用户密码请求: {}，操作人: {}",
        user_id, current_user.account
    );

    let user_uuid = match Uuid::parse_str(&user_id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("无效的商户用户ID格式: {}, 错误: {}", user_id, e);
            return ApiResult::error(400, "无效的商户用户ID格式".to_string());
        }
    };

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("操作人ID格式错误: {}, 错误: {}", current_user.id, e);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match merchant_user_service.reset_merchant_user_password(user_uuid, req, operator_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 切换商户用户状态
#[utoipa::path(
    put,
    path = "system/merchant-users/{id}/status",
    tags = ["系统商户用户管理"],
    summary = "切换商户用户状态",
    description = "切换指定商户用户的状态（启用/禁用/锁定）",
    params(
        ("id" = String, Path, description = "商户用户ID", example = "550e8400-e29b-41d4-a716-************")
    ),
    request_body = SysMerchantUserStatusRequest,
    responses(
        (status = 200, description = "状态切换成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "状态切换成功",
             "data": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
         })
        )
    )
)]
pub async fn change_merchant_user_status(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(user_id): MyPath<String>,
    ValidJson(req): ValidJson<SysMerchantUserStatusRequest>,
) -> ApiResult<String> {
    info!(
        "切换商户用户状态请求: {} -> {}，操作人: {}",
        user_id, req.status, current_user.account
    );

    let user_uuid = match Uuid::parse_str(&user_id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("无效的商户用户ID格式: {}, 错误: {}", user_id, e);
            return ApiResult::error(400, "无效的商户用户ID格式".to_string());
        }
    };

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("操作人ID格式错误: {}, 错误: {}", current_user.id, e);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match merchant_user_service.change_merchant_user_status(user_uuid, req, operator_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户用户选择项
#[utoipa::path(
    get,
    path = "system/merchant-users/select-items",
    tags = ["系统商户用户管理"],
    summary = "获取商户用户选择项",
    description = "获取用于下拉选择的商户用户列表（仅启用状态）",
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SysMerchantUserSelectItem>>,
         example = json!({
             "code": 200,
             "message": "获取商户用户选择项成功",
             "data": [
                 {
                     "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                     "username": "zhangwei",
                     "real_name": "张伟"
                 }
             ]
         })
        )
    )
)]
pub async fn get_merchant_user_select_items(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SysMerchantUserSelectItem>> {
    info!("获取商户用户选择项请求，操作人: {}", current_user.account);

    match merchant_user_service.get_merchant_user_select_items().await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户用户基础信息列表
#[utoipa::path(
    get,
    path = "system/merchant-users/basic-list",
    tags = ["系统商户用户管理"],
    summary = "获取商户用户基础信息列表",
    description = "获取所有商户用户的基础信息（不分页）",
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SysMerchantUserBasicResponse>>,
         example = json!({
             "code": 200,
             "message": "获取商户用户基础信息成功",
             "data": [
                 {
                     "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
                     "username": "zhangwei",
                     "real_name": "张伟",
                     "status": 1
                 }
             ]
         })
        )
    )
)]
pub async fn get_merchant_user_basic_list(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SysMerchantUserBasicResponse>> {
    info!(
        "获取商户用户基础信息列表请求，操作人: {}",
        current_user.account
    );

    match merchant_user_service.get_merchant_user_basic_list().await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户用户统计信息
#[utoipa::path(
    get,
    path = "system/merchant-users/stats",
    tags = ["系统商户用户管理"],
    summary = "获取商户用户统计信息",
    description = "获取商户用户的统计数据，包括总数、各状态数量等",
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SysMerchantUserStatsResponse>,
         example = json!({
             "code": 200,
             "message": "获取商户用户统计信息成功",
             "data": {
                 "total_count": 100,
                 "active_count": 80,
                 "disabled_count": 15,
                 "locked_count": 5
             }
         })
        )
    )
)]
pub async fn get_merchant_user_stats(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SysMerchantUserStatsResponse> {
    info!("获取商户用户统计信息请求，操作人: {}", current_user.account);

    match merchant_user_service.get_merchant_user_stats().await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

// ==================== 数据检查接口 ====================

/// 检查用户名是否存在
#[utoipa::path(
    get,
    path = "system/merchant-users/check-username",
    tags = ["系统商户用户管理"],
    summary = "检查用户名是否存在",
    description = "检查指定用户名是否已被使用",
    params(
        ("username" = String, Query, description = "用户名", example = "zhangwei"),
        ("exclude_id" = Option<String>, Query, description = "排除的用户ID（更新时使用）", example = "550e8400-e29b-41d4-a716-************")
    ),
    responses(
        (status = 200, description = "检查完成", body = ApiResult<bool>)
    )
)]
pub async fn check_username_exists(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    Query(params): Query<serde_json::Value>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<bool> {
    let username = match params.get("username").and_then(|v| v.as_str()) {
        Some(name) => name,
        None => {
            return ApiResult::error(400, "缺少username参数".to_string());
        }
    };

    let exclude_id = params
        .get("exclude_id")
        .and_then(|v| v.as_str())
        .and_then(|s| Uuid::parse_str(s).ok());

    info!(
        "检查用户名是否存在: {}，排除ID: {:?}，操作人: {}",
        username, exclude_id, current_user.account
    );

    match merchant_user_service.check_username_exists(username, exclude_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 检查真实姓名是否存在
#[utoipa::path(
    get,
    path = "system/merchant-users/check-real-name",
    tags = ["系统商户用户管理"],
    summary = "检查真实姓名是否存在",
    description = "检查指定真实姓名是否已被使用",
    params(
        ("real_name" = String, Query, description = "真实姓名", example = "张伟"),
        ("exclude_id" = Option<String>, Query, description = "排除的用户ID（更新时使用）", example = "550e8400-e29b-41d4-a716-************")
    ),
    responses(
        (status = 200, description = "检查完成", body = ApiResult<bool>)
    )
)]
pub async fn check_real_name_exists(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    Query(params): Query<serde_json::Value>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<bool> {
    let real_name = match params.get("real_name").and_then(|v| v.as_str()) {
        Some(name) => name,
        None => {
            return ApiResult::error(400, "缺少real_name参数".to_string());
        }
    };

    let exclude_id = params
        .get("exclude_id")
        .and_then(|v| v.as_str())
        .and_then(|s| Uuid::parse_str(s).ok());

    info!(
        "检查真实姓名是否存在: {}，排除ID: {:?}，操作人: {}",
        real_name, exclude_id, current_user.account
    );

    match merchant_user_service.check_real_name_exists(real_name, exclude_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 检查手机号是否存在
#[utoipa::path(
    get,
    path = "system/merchant-users/check-phone",
    tags = ["系统商户用户管理"],
    summary = "检查手机号是否存在",
    description = "检查指定手机号是否已被使用",
    params(
        ("phone" = String, Query, description = "手机号", example = "***********"),
        ("exclude_id" = Option<String>, Query, description = "排除的用户ID（更新时使用）", example = "550e8400-e29b-41d4-a716-************")
    ),
    responses(
        (status = 200, description = "检查完成", body = ApiResult<bool>)
    )
)]
pub async fn check_phone_exists(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    Query(params): Query<serde_json::Value>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<bool> {
    let phone = match params.get("phone").and_then(|v| v.as_str()) {
        Some(phone) => phone,
        None => {
            return ApiResult::error(400, "缺少phone参数".to_string());
        }
    };

    let exclude_id = params
        .get("exclude_id")
        .and_then(|v| v.as_str())
        .and_then(|s| Uuid::parse_str(s).ok());

    info!(
        "检查手机号是否存在: {}，排除ID: {:?}，操作人: {}",
        phone, exclude_id, current_user.account
    );

    match merchant_user_service.check_phone_exists(phone, exclude_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 检查邮箱是否存在
#[utoipa::path(
    get,
    path = "system/merchant-users/check-email",
    tags = ["系统商户用户管理"],
    summary = "检查邮箱是否存在",
    description = "检查指定邮箱是否已被使用",
    params(
        ("email" = String, Query, description = "邮箱", example = "<EMAIL>"),
        ("exclude_id" = Option<String>, Query, description = "排除的用户ID（更新时使用）", example = "550e8400-e29b-41d4-a716-************")
    ),
    responses(
        (status = 200, description = "检查完成", body = ApiResult<bool>)
    )
)]
pub async fn check_email_exists(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    Query(params): Query<serde_json::Value>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<bool> {
    let email = match params.get("email").and_then(|v| v.as_str()) {
        Some(email) => email,
        None => {
            return ApiResult::error(400, "缺少email参数".to_string());
        }
    };

    let exclude_id = params
        .get("exclude_id")
        .and_then(|v| v.as_str())
        .and_then(|s| Uuid::parse_str(s).ok());

    info!(
        "检查邮箱是否存在: {}，排除ID: {:?}，操作人: {}",
        email, exclude_id, current_user.account
    );

    match merchant_user_service.check_email_exists(email, exclude_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 为用户分配角色
#[utoipa::path(
    post,
    path = "system/merchant-users/{id}/assign-roles",
    tags = ["系统商户用户管理"],
    summary = "为用户分配角色",
    description = "为指定用户分配多个角色，会先清除该用户在指定商户的现有角色再分配新角色",
    params(
        ("id" = String, Path, description = "用户ID", example = "550e8400-e29b-41d4-a716-************")
    ),
    request_body = SysMerchantUserRoleAssignRequest,
    responses(
        (status = 200, description = "角色分配成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "成功为用户分配2个角色",
             "data": "成功为用户分配2个角色"
         })
        )
    )
)]
pub async fn assign_roles_to_user(
    Component(merchant_user_service): Component<SysMerchantUserService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(user_id): MyPath<String>,
    ValidJson(req): ValidJson<SysMerchantUserRoleAssignRequest>,
) -> ApiResult<String> {
    info!(
        "为用户分配角色请求: {}，角色: {:?}，操作人: {}",
        user_id, req.role_ids, current_user.account
    );

    let _ = match Uuid::parse_str(&user_id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("无效的用户ID格式: {}, 错误: {}", user_id, e);
            return ApiResult::error(400, "无效的用户ID格式".to_string());
        }
    };

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(uuid) => uuid,
        Err(e) => {
            error!("操作人ID格式错误: {}, 错误: {}", current_user.id, e);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match merchant_user_service.assign_roles_to_user(req, operator_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

// ==================== OpenAPI 文档配置 ====================

#[derive(utoipa::OpenApi)]
#[openapi(
    paths(
        page_merchant_users,
        get_merchant_user_by_id,
        create_merchant_user,
        update_merchant_user,
        delete_merchant_user,
        batch_delete_merchant_users,
        reset_merchant_user_password,
        change_merchant_user_status,
        get_merchant_user_select_items,
        get_merchant_user_basic_list,
        get_merchant_user_stats,
        check_username_exists,
        check_real_name_exists,
        check_phone_exists,
        check_email_exists,
        assign_roles_to_user,
    ),
    components(
        schemas(
            SysMerchantUserPageRequest,
            SysMerchantUserCreateRequest,
            SysMerchantUserUpdateRequest,
            SysMerchantUserResetPasswordRequest,
            SysMerchantUserStatusRequest,
            SysMerchantUserBatchDeleteRequest,
            SysMerchantUserListResponse,
            SysMerchantUserDetailResponse,
            SysMerchantUserSelectItem,
            SysMerchantUserBasicResponse,
            SysMerchantUserStatsResponse,
            SysMerchantUserRoleAssignRequest,
            SysMerchantUserRoleInfo,
        )
    ),
    tags(
        (name = "系统商户用户管理", description = "系统商户用户管理相关接口")
    )
)]
pub struct SysMerchantUserApiDoc;

impl SysMerchantUserApiDoc {
    pub fn get_openapi_json() -> String {
        serde_json::to_string_pretty(&Self::openapi()).unwrap()
    }

    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&Self::openapi()).unwrap()
    }
}

use crate::domain::system::dto::sys_permission_template_request::{
    SysPermissionTemplateBatchDeleteRequest, SysPermissionTemplateCreateRequest,
    SysPermissionTemplatePageRequest, SysPermissionTemplateStatusRequest,
    SysPermissionTemplateUpdateRequest,
};
use crate::domain::system::vo::permission_template_vo::{
    SystemPermissionTemplateDetailResponse, SystemPermissionTemplateListResponse,
    SystemPermissionTemplateSelectItem, SystemPermissionTemplateStatsResponse,
    SystemPermissionTemplateTreeNode,
};
use crate::service::system::SysPermissionTemplateService;
use crate::utils::custom_validator::{MyPath, MyQuery, ValidJson};
use axum::{
    middleware, routing::{delete, get, post, put},
    Router,
};
use lib_auth::require_permission;
use lib_core::response::{ApiResult, PageData};
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use tracing::{error, info};
use utoipa::OpenApi;
use uuid::Uuid;

/// 系统系统权限模板管理控制器
pub struct SysPermissionTemplateController;

impl SysPermissionTemplateController {
    /// 创建系统系统权限模板管理公开路由
    pub fn public_routes() -> Router {
        Router::new()
        // 系统系统权限模板管理暂无公开路由
    }

    /// 创建系统系统权限模板管理受保护路由
    pub fn protected_routes() -> Router {
        Router::new()
            // 获取权限模板统计信息
            .route(
                "/system/permission-templates/stats",
                get(get_permission_template_stats).layer(middleware::from_fn(require_permission(
                    "permission_template:list",
                ))),
            )
            // 获取权限模板选择项
            .route(
                "/system/permission-templates/select",
                get(get_permission_template_select_items).layer(middleware::from_fn(
                    require_permission("permission_template:list"),
                )),
            )
            // 获取权限模板树形结构
            .route(
                "/system/permission-templates/tree",
                get(get_permission_template_tree).layer(middleware::from_fn(require_permission(
                    "permission_template:list",
                ))),
            )
            // 批量删除权限模板
            .route(
                "/system/permission-templates/batch-delete",
                post(batch_delete_permission_templates).layer(middleware::from_fn(
                    require_permission("permission_template:delete"),
                )),
            )
            // 权限模板状态切换
            .route(
                "/system/permission-templates/{template_id}/status",
                put(change_permission_template_status).layer(middleware::from_fn(
                    require_permission("permission_template:update"),
                )),
            )
            // 权限模板详情查询
            .route(
                "/system/permission-templates/{template_id}",
                get(get_permission_template_by_id).layer(middleware::from_fn(require_permission(
                    "permission_template:detail",
                ))),
            )
            // 权限模板更新
            .route(
                "/system/permission-templates/{template_id}",
                put(update_permission_template).layer(middleware::from_fn(require_permission(
                    "permission_template:update",
                ))),
            )
            // 权限模板删除
            .route(
                "/system/permission-templates/{template_id}",
                delete(delete_permission_template).layer(middleware::from_fn(require_permission(
                    "permission_template:delete",
                ))),
            )
            // 权限模板列表查询
            .route(
                "/system/permission-templates",
                get(page_permission_templates).layer(middleware::from_fn(require_permission(
                    "permission_template:list",
                ))),
            )
            // 权限模板创建
            .route(
                "/system/permission-templates",
                post(create_permission_template).layer(middleware::from_fn(require_permission(
                    "permission_template:create",
                ))),
            )
    }
}

/// 分页查询权限模板列表
#[utoipa::path(
    get,
    path = "/system/permission-templates",
    summary = "分页查询权限模板列表",
    description = "根据条件分页查询系统权限模板信息",
    tags = ["系统权限模板管理"],
    params(SysPermissionTemplatePageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<SystemPermissionTemplateListResponse>>,
         example = json!({
             "code": 200,
             "message": "查询权限模板列表成功",
             "data": {
                 "items": [
                     {
                         "id": "550e8400-e29b-41d4-a716-************",
                         "permission_name": "系统管理",
                         "permission_code": "system",
                         "parent_id": null,
                         "parent_name": null,
                         "order_num": 1,
                         "path": "/system",
                         "component": "Layout",
                         "permission_type": 1,
                         "permission_type_desc": "目录",
                         "visible": 0,
                         "visible_desc": "显示",
                         "icon": "system",
                         "description": "系统管理模块",
                         "created_date": "2023-01-01 10:00:00",
                         "updated_date": "2023-01-01 10:00:00"
                     },
                     {
                         "id": "550e8400-e29b-41d4-a716-************",
                         "permission_name": "用户管理",
                         "permission_code": "system:user:list",
                         "parent_id": "550e8400-e29b-41d4-a716-************",
                         "parent_name": "系统管理",
                         "order_num": 1,
                         "path": "/system/user",
                         "component": "system/user/index",
                         "permission_type": 2,
                         "permission_type_desc": "菜单",
                         "visible": 0,
                         "visible_desc": "显示",
                         "icon": "user",
                         "description": "系统用户管理功能",
                         "created_date": "2023-01-01 10:00:00",
                         "updated_date": "2023-01-01 10:00:00"
                     }
                 ],
                 "total": 2,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 1
             }
         })
        )
    )
)]
pub async fn page_permission_templates(
    Component(permission_templates_service): Component<SysPermissionTemplateService>,
    MyQuery(req): MyQuery<SysPermissionTemplatePageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<SystemPermissionTemplateListResponse>> {
    info!(
        "分页查询权限模板列表请求: {:?}，操作人: {}",
        req, current_user.account
    );

    match permission_templates_service
        .page_permission_templates(req)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 根据ID查询权限模板详情
#[utoipa::path(
    get,
    path = "/system/permission-templates/{template_id}",
    summary = "查询权限模板详情",
    description = "根据权限模板ID获取详细信息",
    tags = ["系统权限模板管理"],
    params(
        ("template_id" = Uuid, Path, description = "权限模板ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SystemPermissionTemplateDetailResponse>,
         example = json!({
             "code": 200,
             "message": "查询权限模板详情成功",
             "data": {
                 "id": "550e8400-e29b-41d4-a716-************",
                 "permission_name": "用户管理",
                 "permission_code": "system:user:list",
                 "parent_id": "550e8400-e29b-41d4-a716-************",
                 "parent_name": "系统管理",
                 "order_num": 1,
                 "path": "/system/user",
                 "component": "system/user/index",
                 "query": "{\"tab\":\"basic\"}",
                 "is_frame": 1,
                 "is_frame_desc": "否",
                 "is_cache": 0,
                 "is_cache_desc": "缓存",
                 "permission_type": 2,
                 "permission_type_desc": "菜单",
                 "visible": 0,
                 "visible_desc": "显示",
                 "icon": "user",
                 "description": "系统用户管理功能",
                 "created_date": "2023-01-01 10:00:00",
                 "updated_date": "2023-01-01 10:00:00",
                 "created_by": "550e8400-e29b-41d4-a716-************",
                 "updated_by": "550e8400-e29b-41d4-a716-************",
                 "remark": "基础权限模板"
             }
         })
        )
    )
)]
pub async fn get_permission_template_by_id(
    Component(permission_templates_service): Component<SysPermissionTemplateService>,
    MyPath(template_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SystemPermissionTemplateDetailResponse> {
    info!(
        "查询权限模板详情请求，ID: {}，操作人: {}",
        template_id, current_user.account
    );

    match permission_templates_service
        .get_permission_template_by_id(template_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 创建权限模板
#[utoipa::path(
    post,
    path = "/system/permission-templates",
    summary = "创建权限模板",
    description = "创建新的系统权限模板",
    tags = ["系统权限模板管理"],
    request_body = SysPermissionTemplateCreateRequest,
    responses(
        (status = 200, description = "创建成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "权限模板创建成功",
             "data": "550e8400-e29b-41d4-a716-************"
         })
        )
    )
)]
pub async fn create_permission_template(
    Component(permission_templates_service): Component<SysPermissionTemplateService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysPermissionTemplateCreateRequest>,
) -> ApiResult<String> {
    info!(
        "创建权限模板请求: {:?}，操作人: {}",
        req, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };
    match permission_templates_service
        .create_permission_template(req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新权限模板
#[utoipa::path(
    put,
    path = "/system/permission-templates/{template_id}",
    summary = "更新权限模板",
    description = "更新权限模板信息",
    tags = ["系统权限模板管理"],
    params(
        ("template_id" = Uuid, Path, description = "权限模板ID")
    ),
    request_body = SysPermissionTemplateUpdateRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "权限模板更新成功",
             "data": null
         })
        )
    )
)]
pub async fn update_permission_template(
    Component(permission_templates_service): Component<SysPermissionTemplateService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(template_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysPermissionTemplateUpdateRequest>,
) -> ApiResult<String> {
    info!(
        "更新权限模板请求，ID: {}，数据: {:?}，操作人: {}",
        template_id, req, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match permission_templates_service
        .update_permission_template(template_id, req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 删除权限模板
#[utoipa::path(
    delete,
    path = "/system/permission-templates/{template_id}",
    summary = "删除权限模板",
    description = "删除指定的权限模板",
    tags = ["系统权限模板管理"],
    params(
        ("template_id" = Uuid, Path, description = "权限模板ID")
    ),
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "成功删除3个权限模板",
             "data": null
         })
        )
    )
)]
pub async fn delete_permission_template(
    Component(permission_templates_service): Component<SysPermissionTemplateService>,
    MyPath(template_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!(
        "删除权限模板请求，ID: {}，操作人: {}",
        template_id, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };
    match permission_templates_service
        .delete_permission_template(template_id, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 批量删除权限模板
#[utoipa::path(
    post,
    path = "/system/permission-templates/batch-delete",
    summary = "批量删除权限模板",
    description = "批量删除多个权限模板",
    tags = ["系统权限模板管理"],
    request_body = SysPermissionTemplateBatchDeleteRequest,
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>),
    )
)]
pub async fn batch_delete_permission_templates(
    Component(permission_templates_service): Component<SysPermissionTemplateService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysPermissionTemplateBatchDeleteRequest>,
) -> ApiResult<String> {
    info!(
        "批量删除权限模板请求: {:?}，操作人: {}",
        req, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };
    match permission_templates_service
        .batch_delete_permission_templates(req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 切换权限模板状态
#[utoipa::path(
    put,
    path = "/system/permission-templates/{template_id}/status",
    summary = "切换权限模板状态",
    description = "切换权限模板的显示/隐藏状态",
    tags = ["系统权限模板管理"],
    params(
        ("template_id" = Uuid, Path, description = "权限模板ID")
    ),
    request_body = SysPermissionTemplateStatusRequest,
    responses(
        (status = 200, description = "状态切换成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "权限模板状态已切换为显示",
             "data": null
         })
        )
    )
)]
pub async fn change_permission_template_status(
    Component(permission_templates_service): Component<SysPermissionTemplateService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(template_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysPermissionTemplateStatusRequest>,
) -> ApiResult<String> {
    info!(
        "切换权限模板状态请求，ID: {}，状态: {}，操作人: {}",
        template_id, req.visible, current_user.account
    );

    let operator_id = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(_) => {
            error!("操作人ID格式错误: {}", current_user.id);
            return ApiResult::error(400, "操作人ID格式错误".to_string());
        }
    };

    match permission_templates_service
        .change_permission_template_status(template_id, req, operator_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取权限模板树形结构
#[utoipa::path(
    get,
    path = "/system/permission-templates/tree",
    summary = "获取权限模板树形结构",
    description = "获取系统权限模板的树形结构数据",
    tags = ["系统权限模板管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SystemPermissionTemplateTreeNode>>,
         example = json!({
             "code": 200,
             "message": "获取权限模板树形结构成功",
             "data": [
                 {
                     "id": "550e8400-e29b-41d4-a716-************",
                     "label": "系统管理",
                     "permission_code": "system",
                     "parent_id": null,
                     "permission_type": 1,
                     "visible": 0,
                     "icon": "system",
                     "order_num": 1,
                     "children": [
                         {
                             "id": "550e8400-e29b-41d4-a716-************",
                             "label": "用户管理",
                             "permission_code": "system:user:list",
                             "parent_id": "550e8400-e29b-41d4-a716-************",
                             "permission_type": 2,
                             "visible": 0,
                             "icon": "user",
                             "order_num": 1,
                             "children": [
                                 {
                                     "id": "550e8400-e29b-41d4-a716-446655440002",
                                     "label": "用户新增",
                                     "permission_code": "system:user:create",
                                     "parent_id": "550e8400-e29b-41d4-a716-************",
                                     "permission_type": 3,
                                     "visible": 0,
                                     "icon": null,
                                     "order_num": 1,
                                     "children": []
                                 }
                             ]
                         }
                     ]
                 }
             ]
         })
        )
    )
)]
pub async fn get_permission_template_tree(
    Component(permission_templates_service): Component<SysPermissionTemplateService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SystemPermissionTemplateTreeNode>> {
    info!("获取权限模板树形结构请求，操作人: {}", current_user.account);

    match permission_templates_service
        .get_permission_template_tree()
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取权限模板选择项
#[utoipa::path(
    get,
    path = "/system/permission-templates/select",
    summary = "获取权限模板选择项",
    description = "获取权限模板下拉选择列表",
    tags = ["系统权限模板管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SystemPermissionTemplateSelectItem>>,
         example = json!({
             "code": 200,
             "message": "获取权限模板选择项成功",
             "data": [
                 {
                     "id": "550e8400-e29b-41d4-a716-************",
                     "permission_name": "系统管理",
                     "permission_code": "system",
                     "permission_type": 1,
                     "parent_id": null,
                     "visible": 0
                 },
                 {
                     "id": "550e8400-e29b-41d4-a716-************",
                     "permission_name": "用户管理",
                     "permission_code": "system:user:list",
                     "permission_type": 2,
                     "parent_id": "550e8400-e29b-41d4-a716-************",
                     "visible": 0
                 },
                 {
                     "id": "550e8400-e29b-41d4-a716-446655440002",
                     "permission_name": "用户新增",
                     "permission_code": "system:user:create",
                     "permission_type": 3,
                     "parent_id": "550e8400-e29b-41d4-a716-************",
                     "visible": 0
                 }
             ]
         })
        )
    )
)]
pub async fn get_permission_template_select_items(
    Component(permission_templates_service): Component<SysPermissionTemplateService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SystemPermissionTemplateSelectItem>> {
    info!("获取权限模板选择项请求，操作人: {}", current_user.account);

    match permission_templates_service
        .get_permission_template_select_items()
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取权限模板统计信息
#[utoipa::path(
    get,
    path = "/system/permission-templates/stats",
    summary = "获取权限模板统计信息",
    description = "获取系统权限模板的统计数据",
    tags = ["系统权限模板管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SystemPermissionTemplateStatsResponse>,
         example = json!({
             "code": 200,
             "message": "获取权限模板统计信息成功",
             "data": {
                 "total_count": 50,
                 "directory_count": 8,
                 "menu_count": 32,
                 "button_count": 10,
                 "visible_count": 45,
                 "hidden_count": 5
             }
         })
        )
    )
)]
#[axum::debug_handler]
pub async fn get_permission_template_stats(
    Component(permission_templates_service): Component<SysPermissionTemplateService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SystemPermissionTemplateStatsResponse> {
    info!("获取权限模板统计信息请求，操作人: {}", current_user.account);

    match permission_templates_service
        .get_permission_template_stats()
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 系统系统权限模板管理 API 文档
#[derive(OpenApi)]
#[openapi(
    paths(
        page_permission_templates,
        get_permission_template_by_id,
        create_permission_template,
        update_permission_template,
        delete_permission_template,
        batch_delete_permission_templates,
        change_permission_template_status,
        get_permission_template_tree,
        get_permission_template_select_items,
        get_permission_template_stats,
    ),
    components(schemas(
        SysPermissionTemplatePageRequest,
        SysPermissionTemplateCreateRequest,
        SysPermissionTemplateUpdateRequest,
        SysPermissionTemplateBatchDeleteRequest,
        SysPermissionTemplateStatusRequest,
        SystemPermissionTemplateListResponse,
        SystemPermissionTemplateDetailResponse,
        SystemPermissionTemplateTreeNode,
        SystemPermissionTemplateSelectItem,
        SystemPermissionTemplateStatsResponse,
    ))
)]
pub struct SysPermissionTemplateApiDoc;

impl SysPermissionTemplateApiDoc {
    pub fn get_openapi_json() -> String {
        serde_json::to_string_pretty(&Self::openapi()).unwrap()
    }

    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&Self::openapi()).unwrap()
    }
}

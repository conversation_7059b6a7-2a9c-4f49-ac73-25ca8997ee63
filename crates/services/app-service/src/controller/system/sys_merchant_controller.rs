use crate::domain::business::merchant_user_vo::FollowerUserResponse;
use crate::domain::system::dto::sys_merchant_request::{
    SysMerchantBatchDeleteRequest, SysMerchantCreateRequest, SysMerchantFollowerCommissionRequest,
    SysMerchantFollowerRequest, SysMerchantListRequest, SysMerchantPageRequest,
    SysMerchantPlatformCommissionRequest, SysMerchantStatusRequest, SysMerchantUpdateRequest,
};
use crate::domain::system::vo::merchant_vo::{
    SysMerchantBasicResponse, SysMerchantCommissionResponse, SysMerchantDetailResponse,
    SysMerchantFollowerResponse, SysMerchantListResponse, SysMerchantLocationResponse,
    SysMerchantSelectItem, SysMerchantStatsResponse,
};
use crate::service::system::SysMerchantService;
use crate::utils::custom_validator::{<PERSON><PERSON><PERSON>, <PERSON><PERSON>uery, <PERSON>id<PERSON>son};
use axum::{
    middleware, routing::{delete, get, post, put},
    Router,
};
use lib_auth::require_permission;
use lib_core::response::{ApiResult, PageData};
use lib_web::extractor::Component;
use tracing::{error, info};
use utoipa::OpenApi;
use uuid::Uuid;

/// 商户管理控制器
pub struct SysMerchantController;

impl SysMerchantController {
    /// 创建商户管理公开路由
    pub fn public_routes() -> Router {
        Router::new()
        // 商户管理暂无公开路由
    }

    /// 创建商户管理受保护路由
    pub fn protected_routes() -> Router {
        Router::new()
            // 获取商户选择项（下拉选择用）
            .route(
                "/business/merchants/select",
                get(get_merchant_select_items)
                    .layer(middleware::from_fn(require_permission("sys:merchant:list"))),
            )
            // 获取商户基础信息列表
            .route(
                "/business/merchants/basic",
                get(get_merchant_basic_list)
                    .layer(middleware::from_fn(require_permission("sys:merchant:list"))),
            )
            // 批量删除商户
            .route(
                "/business/merchants/batch-delete",
                post(batch_delete_merchants).layer(middleware::from_fn(require_permission(
                    "sys:merchant:delete",
                ))),
            )
            // 商户状态切换
            .route(
                "/business/merchants/{merchant_id}/status",
                put(change_merchant_status).layer(middleware::from_fn(require_permission(
                    "sys:merchant:update",
                ))),
            )
            // 设置商户平台佣金
            .route(
                "/business/merchants/{merchant_id}/platform-commission",
                put(set_merchant_platform_commission).layer(middleware::from_fn(
                    require_permission("sys:merchant:update"),
                )),
            )
            // 设置商户跟进人佣金
            .route(
                "/business/merchants/{merchant_id}/follower-commission",
                put(set_merchant_follower_commission).layer(middleware::from_fn(
                    require_permission("sys:merchant:update"),
                )),
            )
            // 获取商户地理位置信息
            .route(
                "/business/merchants/{merchant_id}/location",
                get(get_merchant_location).layer(middleware::from_fn(require_permission(
                    "sys:merchant:detail",
                ))),
            )
            // 商户详情查询
            .route(
                "/business/merchants/{merchant_id}",
                get(get_merchant_by_id).layer(middleware::from_fn(require_permission(
                    "sys:merchant:detail",
                ))),
            )
            // 商户更新
            .route(
                "/business/merchants/{merchant_id}",
                put(update_merchant).layer(middleware::from_fn(require_permission(
                    "sys:merchant:update",
                ))),
            )
            // 商户删除
            .route(
                "/business/merchants/{merchant_id}",
                delete(delete_merchant).layer(middleware::from_fn(require_permission(
                    "sys:merchant:delete",
                ))),
            )
            // 商户列表查询
            .route(
                "/business/merchants",
                get(page_merchants)
                    .layer(middleware::from_fn(require_permission("sys:merchant:list"))),
            )
            // 商户创建
            .route(
                "/business/merchants",
                post(create_merchant).layer(middleware::from_fn(require_permission(
                    "sys:merchant:create",
                ))),
            )
            // 获取商户跟进人信息
            .route(
                "/business/merchants/{merchant_id}/followers",
                get(get_merchant_followers_by_merchant_id).layer(middleware::from_fn(
                    require_permission("sys:merchant:follower"),
                )),
            )
    }
}

/// 分页查询商户列表
#[utoipa::path(
    get,
    path = "/business/merchants",
    summary = "分页查询商户列表",
    description = "根据查询条件分页获取商户信息",
    tags = ["商户管理"],
    params(SysMerchantPageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<SysMerchantListResponse>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "items": [
                     {
                         "id": 1,
                         "merchant_name": "张三餐厅",
                         "merchant_code": "M001",
                         "category_id": "550e8400-e29b-41d4-a716-************",
                         "category_name": "餐饮",
                         "phone": "***********",
                         "email": "<EMAIL>",
                         "address": "北京市朝阳区xxx街道xxx号",
                         "platform_commission_rate": "0.0500",
                         "follower_name": "李四",
                         "follower_phone": "***********",
                         "status": 1,
                         "status_desc": "启用",
                         "created_date": "2023-01-01 10:00:00",
                         "updated_date": "2023-01-01 10:00:00"
                     }
                 ],
                 "total": 1,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 1
             }
         })
        )
    )
)]
pub async fn page_merchants(
    Component(merchant_service): Component<SysMerchantService>,
    MyQuery(req): MyQuery<SysMerchantPageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<SysMerchantListResponse>> {
    info!(
        "分页查询商户列表请求: {:?}，操作人: {}",
        req, current_user.account
    );

    match merchant_service.page_merchants(req).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 根据ID查询商户详情
#[utoipa::path(
    get,
    path = "/business/merchants/{merchant_id}",
    summary = "查询商户详情",
    description = "根据商户ID获取详细信息",
    tags = ["商户管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SysMerchantDetailResponse>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "id": 1,
                 "merchant_name": "张三餐厅",
                 "merchant_code": "M001",
                 "category_id": "550e8400-e29b-41d4-a716-************",
                 "category_name": "餐饮",
                 "phone": "***********",
                 "email": "<EMAIL>",
                 "address": "北京市朝阳区xxx街道xxx号",
                 "location": {
                     "longitude": 116.4470,
                     "latitude": 39.9200
                 },
                 "business_license": "91110000MA001234567",
                 "license_photo": "https://example.com/license.jpg",
                 "avatar": "https://example.com/avatar.jpg",
                 "description": "专业的中式餐厅",
                 "platform_commission_rate": "0.0500",
                 "follower_id": "550e8400-e29b-41d4-a716-************",
                 "follower_name": "李四",
                 "follower_phone": "***********",
                 "follower_commission_rate": "0.0200",
                 "status": 1,
                 "status_desc": "启用",
                 "auto_clear_date": 15,
                 "sort_order": 1,
                 "created_date": "2023-01-01 10:00:00",
                 "updated_date": "2023-01-01 10:00:00",
                 "created_by": "550e8400-e29b-41d4-a716-************",
                 "updated_by": "550e8400-e29b-41d4-a716-************",
                 "remark": "优质商户"
             }
         })
        )
    )
)]
pub async fn get_merchant_by_id(
    Component(merchant_service): Component<SysMerchantService>,
    MyPath(merchant_id): MyPath<i64>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SysMerchantDetailResponse> {
    info!(
        "查询商户详情请求，ID: {}，操作人: {}",
        merchant_id, current_user.account
    );

    match merchant_service.get_merchant_by_id(merchant_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 创建商户
#[utoipa::path(
    post,
    path = "/business/merchants",
    summary = "创建商户",
    description = "创建新的商户信息",
    tags = ["商户管理"],
    request_body = SysMerchantCreateRequest,
    responses(
        (status = 200, description = "创建成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户创建成功",
             "data": null
         })
        )
    )
)]
pub async fn create_merchant(
    Component(merchant_service): Component<SysMerchantService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantCreateRequest>,
) -> ApiResult<String> {
    info!("创建商户请求: {:?}，操作人: {}", req, current_user.account);

    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());
    match merchant_service.create_merchant(req, userid).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新商户
#[utoipa::path(
    put,
    path = "/business/merchants/{merchant_id}",
    summary = "更新商户",
    description = "更新商户信息",
    tags = ["商户管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    request_body = SysMerchantUpdateRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户更新成功",
             "data": null
         })
        )
    )
)]
pub async fn update_merchant(
    Component(merchant_service): Component<SysMerchantService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(merchant_id): MyPath<i64>,
    ValidJson(req): ValidJson<SysMerchantUpdateRequest>,
) -> ApiResult<String> {
    info!(
        "更新商户请求，ID: {}，数据: {:?}，操作人: {}",
        merchant_id, req, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_service
        .update_merchant(merchant_id, req, userid)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 删除商户
#[utoipa::path(
    delete,
    path = "/business/merchants/{merchant_id}",
    summary = "删除商户",
    description = "删除指定的商户",
    tags = ["商户管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户删除成功",
             "data": null
         })
        )
    )
)]
pub async fn delete_merchant(
    Component(merchant_service): Component<SysMerchantService>,
    MyPath(merchant_id): MyPath<i64>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!(
        "删除商户请求，ID: {}，操作人: {}",
        merchant_id, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_service.delete_merchant(merchant_id, userid).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 批量删除商户
#[utoipa::path(
    post,
    path = "/business/merchants/batch-delete",
    summary = "批量删除商户",
    description = "批量删除多个商户",
    tags = ["商户管理"],
    request_body = SysMerchantBatchDeleteRequest,
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "成功删除3个商户",
             "data": null
         })
        )
    )
)]
pub async fn batch_delete_merchants(
    Component(merchant_service): Component<SysMerchantService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantBatchDeleteRequest>,
) -> ApiResult<String> {
    info!(
        "批量删除商户请求: {:?}，操作人: {}",
        req, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_service.batch_delete_merchants(req, userid).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 切换商户状态
#[utoipa::path(
    put,
    path = "/business/merchants/{merchant_id}/status",
    summary = "切换商户状态",
    description = "切换商户的启用/禁用状态",
    tags = ["商户管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    request_body = SysMerchantStatusRequest,
    responses(
        (status = 200, description = "状态切换成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户状态已切换为正常",
             "data": null
         })
        )
    )
)]
pub async fn change_merchant_status(
    Component(merchant_service): Component<SysMerchantService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(merchant_id): MyPath<i64>,
    ValidJson(req): ValidJson<SysMerchantStatusRequest>,
) -> ApiResult<String> {
    info!(
        "切换商户状态请求，ID: {}，状态: {}，操作人: {}",
        merchant_id, req.status, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_service
        .change_merchant_status(merchant_id, req, userid)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 设置商户平台佣金
#[utoipa::path(
    put,
    path = "/business/merchants/{merchant_id}/platform-commission",
    summary = "设置商户平台佣金",
    description = "设置商户的平台佣金比例",
    tags = ["商户管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    request_body = SysMerchantPlatformCommissionRequest,
    responses(
        (status = 200, description = "设置成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户平台佣金设置成功",
             "data": null
         })
        )
    )
)]
pub async fn set_merchant_platform_commission(
    Component(merchant_service): Component<SysMerchantService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(merchant_id): MyPath<i64>,
    ValidJson(req): ValidJson<SysMerchantPlatformCommissionRequest>,
) -> ApiResult<String> {
    info!(
        "设置商户平台佣金请求，商户ID: {}，平台佣金: {}，操作人: {}",
        merchant_id, req.platform_commission_rate, current_user.account
    );

    let userid = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(e) => {
            error!("解析用户ID失败: {}, 用户ID: {}", e, current_user.id);
            return ApiResult::error(400, "无效的用户ID".to_string());
        }
    };

    match merchant_service
        .set_merchant_platform_commission(merchant_id, req, userid)
        .await
    {
        Ok(data) => {
            info!(
                "设置商户平台佣金成功，商户ID: {}，操作人: {}",
                merchant_id, current_user.account
            );
            ApiResult::success(data)
        }
        Err(business) => {
            error!(
                "设置商户平台佣金失败，商户ID: {}，操作人: {}，错误: {}",
                merchant_id, current_user.account, business.message
            );
            ApiResult::error(business.code, business.message)
        }
    }
}

/// 设置商户跟进人佣金
#[utoipa::path(
    put,
    path = "/business/merchants/{merchant_id}/follower-commission",
    summary = "设置商户跟进人佣金",
    description = "设置商户跟进人的佣金配置",
    tags = ["商户管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    request_body = SysMerchantFollowerCommissionRequest,
    responses(
        (status = 200, description = "设置成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户跟进人佣金设置成功",
             "data": null
         })
        )
    )
)]
pub async fn set_merchant_follower_commission(
    Component(merchant_service): Component<SysMerchantService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(merchant_id): MyPath<i64>,
    ValidJson(req): ValidJson<SysMerchantFollowerCommissionRequest>,
) -> ApiResult<String> {
    let follower_id = req.follower_id;
    info!(
        "设置商户跟进人佣金请求，商户ID: {}，跟进人ID: {}，佣金类型: {}，佣金值: {}，操作人: {}",
        merchant_id,
        follower_id,
        req.follower_commission_type,
        req.follower_commission_value,
        current_user.account
    );

    let userid = match Uuid::parse_str(&current_user.id) {
        Ok(id) => id,
        Err(e) => {
            error!("解析用户ID失败: {}, 用户ID: {}", e, current_user.id);
            return ApiResult::error(400, "无效的用户ID".to_string());
        }
    };

    match merchant_service
        .set_merchant_follower_commission(merchant_id, req, userid)
        .await
    {
        Ok(data) => {
            info!(
                "设置商户跟进人佣金成功，商户ID: {}，跟进人ID: {}，操作人: {}",
                merchant_id, follower_id, current_user.account
            );
            ApiResult::success(data)
        }
        Err(business) => {
            error!(
                "设置商户跟进人佣金失败，商户ID: {}，跟进人ID: {}，操作人: {}，错误: {}",
                merchant_id, follower_id, current_user.account, business.message
            );
            ApiResult::error(business.code, business.message)
        }
    }
}

/// 获取商户选择项
#[utoipa::path(
    get,
    path = "/business/merchants/select",
    summary = "获取商户选择项",
    description = "获取商户下拉选择列表",
    tags = ["商户管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SysMerchantSelectItem>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": [
                 {
                     "id": 1,
                     "merchant_name": "张三餐厅",
                     "merchant_code": "M001",
                     "contact_person": "张三",
                     "contact_phone": "***********",
                     "merchant_level": 2,
                     "status": 1
                 }
             ]
         })
       )
    )
)]
pub async fn get_merchant_select_items(
    Component(merchant_service): Component<SysMerchantService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SysMerchantSelectItem>> {
    info!("获取商户选择项请求，操作人: {}", current_user.account);

    match merchant_service.get_merchant_select_items().await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户基础信息列表
#[utoipa::path(
    get,
    path = "/business/merchants/basic",
    summary = "获取商户基础信息列表",
    description = "获取商户基础信息列表",
    tags = ["商户管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SysMerchantBasicResponse>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": [
                 {
                     "id": 1,
                     "merchant_name": "张三餐厅",
                     "merchant_code": "M001",
                     "contact_person": "张三",
                     "contact_phone": "***********",
                     "business_address": "北京市朝阳区XXX街道XXX号",
                     "merchant_level": 2,
                     "merchant_level_desc": "优质商户",
                     "status": 1,
                     "status_desc": "正常"
                 }
             ]
         })
        )
    )
)]
pub async fn get_merchant_basic_list(
    Component(merchant_service): Component<SysMerchantService>,
    MyQuery(req): MyQuery<SysMerchantListRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SysMerchantBasicResponse>> {
    info!("获取商户基础信息列表请求，操作人: {}", current_user.account);

    match merchant_service.get_merchant_basic_list(req).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户统计信息
#[utoipa::path(
    get,
    path = "/business/merchants/stats",
    summary = "获取商户统计信息",
    description = "获取商户的统计数据",
    tags = ["商户管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SysMerchantStatsResponse>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "total_count": 100,
                 "active_count": 85,
                 "disabled_count": 10,
                 "frozen_count": 5,
                 "by_level": [],
                 "by_category": []
             }
         })
        )
    )
)]
pub async fn get_merchant_stats(
    Component(merchant_service): Component<SysMerchantService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SysMerchantStatsResponse> {
    info!("获取商户统计信息请求，操作人: {}", current_user.account);

    match merchant_service.get_merchant_stats().await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户地理位置信息
#[utoipa::path(
    get,
    path = "/business/merchants/{merchant_id}/location",
    summary = "获取商户地理位置信息",
    description = "获取指定商户的地理位置信息",
    tags = ["商户管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<SysMerchantLocationResponse>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "merchant_id": 1,
                 "merchant_name": "张三餐厅",
                 "address": "北京市朝阳区xxx街道xxx号",
                 "location": {
                     "longitude": 116.4470,
                     "latitude": 39.9200
                 }
             }
         })
        )
    )
)]
pub async fn get_merchant_location(
    Component(merchant_service): Component<SysMerchantService>,
    MyPath(merchant_id): MyPath<i64>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<SysMerchantLocationResponse> {
    info!(
        "获取商户地理位置信息请求，ID: {}，操作人: {}",
        merchant_id, current_user.account
    );

    match merchant_service.get_merchant_location(merchant_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户跟进人信息
#[utoipa::path(
    get,
    path = "/business/merchants/{merchant_id}/followers",
    summary = "获取商户跟进人信息",
    description = "获取商户跟进人信息",
    tags = ["商户管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<FollowerUserResponse>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                "username": "zhangsan",
                "real_name": "张三",
                "phone": "***********",
                "avatar": "https://example.com/avatar.png",
                "gender": 1,
                "status": 1,
                "last_login_date": "2024-07-18 10:00:00",
                "last_login_ip": "*************",
                "remark": "核心跟进人",
                "commission_type": 1,
                "commission_value": "0.05"
             }
         })
        )
    )
)]
pub async fn get_merchant_followers_by_merchant_id(
    Component(sys_merchant_service): Component<SysMerchantService>,
    MyPath(merchant_id): MyPath<i64>,
) -> ApiResult<FollowerUserResponse> {
    info!("获取商户跟进人信息请求，商户ID: {}", merchant_id);

    match sys_merchant_service
        .get_merchant_followers_by_merchant_id(merchant_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 商户管理 API 文档
#[derive(OpenApi)]
#[openapi(
    paths(
        page_merchants,
        get_merchant_by_id,
        create_merchant,
        update_merchant,
        delete_merchant,
        batch_delete_merchants,
        change_merchant_status,
        set_merchant_platform_commission,
        set_merchant_follower_commission,
        get_merchant_select_items,
        get_merchant_basic_list,
        get_merchant_stats,
        get_merchant_location,
        get_merchant_followers_by_merchant_id
    ),
    components(schemas(
        SysMerchantPageRequest,
        SysMerchantCreateRequest,
        SysMerchantUpdateRequest,
        SysMerchantBatchDeleteRequest,
        SysMerchantStatusRequest,
        SysMerchantPlatformCommissionRequest,
        SysMerchantFollowerCommissionRequest,
        SysMerchantFollowerRequest,
        SysMerchantListResponse,
        SysMerchantDetailResponse,
        SysMerchantSelectItem,
        SysMerchantBasicResponse,
        SysMerchantStatsResponse,
        SysMerchantCommissionResponse,
        SysMerchantFollowerResponse,
        SysMerchantLocationResponse,
        FollowerUserResponse
    ))
)]
pub struct SysMerchantApiDoc;

impl SysMerchantApiDoc {
    pub fn get_openapi_json() -> String {
        serde_json::to_string_pretty(&Self::openapi()).unwrap()
    }

    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&Self::openapi()).unwrap()
    }
}

use crate::domain::system::dto::sys_merchant_role_request::{
    SysMerchantRoleBatchDeleteRequest, SysMerchantRoleCreateRequest, SysMerchantRolePageRequest,
    SysMerchantRolePermissionRequest, SysMerchantRoleStatusRequest, SysMerchantRoleUpdateRequest,
};
use crate::domain::system::vo::merchant_authorized_permissions_vo::MerchantAuthorizedPermissionSelectItem;
use crate::domain::system::vo::merchant_role_permissions_vo::{
    AvailablePermissionTreeNode, AvailablePermissionTreeResponse,
    MerchantRolePermissionListResponse, RolePermissionAssignmentResponse,
    RolePermissionTreeDetailResponse, RolePermissionTreeNode,
};
use crate::domain::system::vo::merchant_roles_vo::{
    MerchantRoleBasicResponse, MerchantRoleDetailResponse, MerchantRoleListResponse,
    MerchantRoleSelectItem, MerchantRoleStatsResponse,
};
use crate::service::system::SysMerchantRoleService;
use crate::utils::custom_validator::{MyPath, MyQuery, ValidJson};
use axum::{
    Router, middleware,
    routing::{delete, get, post, put},
};
use lib_auth::require_permission;
use lib_core::response::{ApiResult, PageData};
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use tracing::{error, info};
use utoipa::OpenApi;
use uuid::Uuid;

/// 系统商户角色管理控制器
pub struct SysMerchantRoleController;

impl SysMerchantRoleController {
    /// 创建系统商户角色管理公开路由
    pub fn public_routes() -> Router {
        Router::new()
        // 系统商户角色管理暂无公开路由
    }

    /// 创建系统商户角色管理受保护路由
    pub fn protected_routes() -> Router {
        Router::new()
            // 获取角色权限列表
            .route(
                "/business/merchant-roles/{role_id}/permissions",
                get(get_role_permissions).layer(middleware::from_fn(require_permission(
                    "merchant_role:permission",
                ))),
            )
            // 获取可分配权限列表
            .route(
                "/business/merchant-roles/{role_id}/available-permissions",
                get(get_available_permissions_for_role).layer(middleware::from_fn(
                    require_permission("merchant_role:permission"),
                )),
            )
            // 获取商户角色选择项（下拉选择用）
            .route(
                "/business/merchant-roles/select/{merchant_id}",
                get(get_merchant_role_select_items).layer(middleware::from_fn(require_permission(
                    "merchant_role:list",
                ))),
            )
            // 获取商户角色基础信息列表
            .route(
                "/business/merchant-roles/basic/{merchant_id}",
                get(get_merchant_role_basic_list).layer(middleware::from_fn(require_permission(
                    "merchant_role:list",
                ))),
            )
            // 获取商户角色统计信息
            .route(
                "/business/merchant-roles/stats/{merchant_id}",
                get(get_merchant_role_stats).layer(middleware::from_fn(require_permission(
                    "merchant_role:list",
                ))),
            )
            // 批量删除商户角色
            .route(
                "/business/merchant-roles/batch-delete",
                post(batch_delete_merchant_roles).layer(middleware::from_fn(require_permission(
                    "merchant_role:delete",
                ))),
            )
            // 分配权限给角色
            .route(
                "/business/merchant-roles/{role_id}/permissions/assign",
                post(assign_permissions_to_role).layer(middleware::from_fn(require_permission(
                    "merchant_role:permission",
                ))),
            )
            // 移除角色权限
            .route(
                "/business/merchant-roles/{role_id}/permissions/remove",
                post(remove_permissions_from_role).layer(middleware::from_fn(require_permission(
                    "merchant_role:permission",
                ))),
            )
            // 商户角色状态切换
            .route(
                "/business/merchant-roles/{role_id}/status",
                put(change_merchant_role_status).layer(middleware::from_fn(require_permission(
                    "merchant_role:update",
                ))),
            )
            // 商户角色详情查询
            .route(
                "/business/merchant-roles/{role_id}",
                get(get_merchant_role_by_id).layer(middleware::from_fn(require_permission(
                    "merchant_role:detail",
                ))),
            )
            // 商户角色更新
            .route(
                "/business/merchant-roles/{role_id}",
                put(update_merchant_role).layer(middleware::from_fn(require_permission(
                    "merchant_role:update",
                ))),
            )
            // 商户角色删除
            .route(
                "/business/merchant-roles/{role_id}",
                delete(delete_merchant_role).layer(middleware::from_fn(require_permission(
                    "merchant_role:delete",
                ))),
            )
            // 商户角色列表查询
            .route(
                "/business/merchant-roles",
                get(page_merchant_roles).layer(middleware::from_fn(require_permission(
                    "merchant_role:list",
                ))),
            )
            // 商户角色创建
            .route(
                "/business/merchant-roles",
                post(create_merchant_role).layer(middleware::from_fn(require_permission(
                    "merchant_role:create",
                ))),
            )
    }
}

/// 分页查询商户角色列表
#[utoipa::path(
    get,
    path = "/business/merchant-roles",
    summary = "分页查询商户角色列表",
    description = "根据查询条件分页获取商户角色信息",
    tags = ["系统商户角色管理"],
    params(SysMerchantRolePageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<MerchantRoleListResponse>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "items": [
                     {
                         "id": "550e8400-e29b-41d4-a716-446655440000",
                         "merchant_id": 1,
                         "merchant_name": "张三餐厅",
                         "role_code": "MERCHANT_ADMIN",
                         "role_name": "商户管理员",
                         "role_type": 1,
                         "role_type_desc": "管理员角色",
                         "is_default": true,
                         "data_scope": 1,
                         "data_scope_desc": "商户全部数据",
                         "role_description": "商户管理员，拥有商户内所有权限",
                         "status": 1,
                         "status_desc": "启用",
                         "user_count": 3,
                         "permission_count": 25,
                         "created_date": "2023-01-01 10:00:00",
                         "updated_date": "2023-01-01 10:00:00"
                     }
                 ],
                 "total": 1,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 1
             }
         })
        )
    )
)]
pub async fn page_merchant_roles(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    MyQuery(req): MyQuery<SysMerchantRolePageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<MerchantRoleListResponse>> {
    info!(
        "分页查询商户角色列表请求: {:?}，操作人: {}",
        req, current_user.account
    );

    match merchant_role_service.page_merchant_roles(req).await {
        Ok(data) => {
            info!(
                "分页查询商户角色列表成功，总数: {}，操作人: {}",
                data.total, current_user.account
            );
            ApiResult::success(data)
        }
        Err(business) => {
            error!(
                "分页查询商户角色列表失败，操作人: {}，错误: {}",
                current_user.account, business.message
            );
            ApiResult::error(business.code, business.message)
        }
    }
}

/// 根据ID查询商户角色详情
#[utoipa::path(
    get,
    path = "/business/merchant-roles/{role_id}",
    summary = "查询商户角色详情",
    description = "根据角色ID获取详细信息",
    tags = ["系统商户角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<MerchantRoleDetailResponse>),
    )
)]
pub async fn get_merchant_role_by_id(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    MyPath(role_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<MerchantRoleDetailResponse> {
    info!(
        "查询商户角色详情请求，ID: {}，操作人: {}",
        role_id, current_user.account
    );

    match merchant_role_service.get_merchant_role_by_id(role_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 创建商户角色
#[utoipa::path(
    post,
    path = "/business/merchant-roles",
    summary = "创建商户角色",
    description = "创建新的商户角色信息",
    tags = ["系统商户角色管理"],
    request_body = SysMerchantRoleCreateRequest,
    responses(
        (status = 200, description = "创建成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户角色创建成功",
             "data": null
         })
        )
    )
)]
pub async fn create_merchant_role(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantRoleCreateRequest>,
) -> ApiResult<String> {
    info!(
        "创建商户角色请求: {:?}，操作人: {}",
        req, current_user.account
    );

    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());
    match merchant_role_service
        .create_merchant_role(req, userid)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新商户角色
#[utoipa::path(
    put,
    path = "/business/merchant-roles/{role_id}",
    summary = "更新商户角色",
    description = "更新商户角色信息",
    tags = ["系统商户角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    request_body = SysMerchantRoleUpdateRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户角色更新成功",
             "data": null
         })
        )
    )
)]
pub async fn update_merchant_role(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(role_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysMerchantRoleUpdateRequest>,
) -> ApiResult<String> {
    info!(
        "更新商户角色请求，ID: {}，数据: {:?}，操作人: {}",
        role_id, req, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_role_service
        .update_merchant_role(role_id, req, userid)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 删除商户角色
#[utoipa::path(
    delete,
    path = "/business/merchant-roles/{role_id}",
    summary = "删除商户角色",
    description = "删除指定的商户角色",
    tags = ["系统商户角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户角色删除成功",
             "data": null
         })
        )
    )
)]
pub async fn delete_merchant_role(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    MyPath(role_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!(
        "删除商户角色请求，ID: {}，操作人: {}",
        role_id, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_role_service
        .delete_merchant_role(role_id, userid)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 批量删除商户角色
#[utoipa::path(
    post,
    path = "/business/merchant-roles/batch-delete",
    summary = "批量删除商户角色",
    description = "批量删除多个商户角色",
    tags = ["系统商户角色管理"],
    request_body = SysMerchantRoleBatchDeleteRequest,
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "成功删除3个商户角色",
             "data": null
         })
        )
    )
)]
pub async fn batch_delete_merchant_roles(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantRoleBatchDeleteRequest>,
) -> ApiResult<String> {
    info!(
        "批量删除商户角色请求: {:?}，操作人: {}",
        req, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_role_service
        .batch_delete_merchant_roles(req, userid)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 切换商户角色状态
#[utoipa::path(
    put,
    path = "/business/merchant-roles/{role_id}/status",
    summary = "切换商户角色状态",
    description = "切换商户角色的启用/禁用状态",
    tags = ["系统商户角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    request_body = SysMerchantRoleStatusRequest,
    responses(
        (status = 200, description = "状态切换成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户角色状态已切换为启用",
             "data": null
         })
        )
    )
)]
pub async fn change_merchant_role_status(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(role_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysMerchantRoleStatusRequest>,
) -> ApiResult<String> {
    info!(
        "切换商户角色状态请求，ID: {}，状态: {}，操作人: {}",
        role_id, req.status, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_role_service
        .change_merchant_role_status(role_id, req, userid)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户角色选择项
#[utoipa::path(
    get,
    path = "/business/merchant-roles/select/{merchant_id}",
    summary = "获取商户角色选择项",
    description = "获取指定商户的角色下拉选择列表",
    tags = ["系统商户角色管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<MerchantRoleSelectItem>>),
    )
)]
pub async fn get_merchant_role_select_items(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    MyPath(merchant_id): MyPath<i64>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<MerchantRoleSelectItem>> {
    info!(
        "获取商户角色选择项请求，商户ID: {}，操作人: {}",
        merchant_id, current_user.account
    );

    match merchant_role_service
        .get_merchant_role_select_items(merchant_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户角色基础信息列表
#[utoipa::path(
    get,
    path = "/business/merchant-roles/basic/{merchant_id}",
    summary = "获取商户角色基础信息列表",
    description = "获取指定商户的角色基础信息列表",
    tags = ["系统商户角色管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<MerchantRoleBasicResponse>>)
    )
)]
pub async fn get_merchant_role_basic_list(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    MyPath(merchant_id): MyPath<i64>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<MerchantRoleBasicResponse>> {
    info!(
        "获取商户角色基础信息列表请求，商户ID: {}，操作人: {}",
        merchant_id, current_user.account
    );

    match merchant_role_service
        .get_merchant_role_basic_list(merchant_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户角色统计信息
#[utoipa::path(
    get,
    path = "/business/merchant-roles/stats/{merchant_id}",
    summary = "获取商户角色统计信息",
    description = "获取指定商户的角色统计数据",
    tags = ["系统商户角色管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<MerchantRoleStatsResponse>),
    )
)]
pub async fn get_merchant_role_stats(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    MyPath(merchant_id): MyPath<i64>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<MerchantRoleStatsResponse> {
    info!(
        "获取商户角色统计信息请求，商户ID: {}，操作人: {}",
        merchant_id, current_user.account
    );

    match merchant_role_service
        .get_merchant_role_stats(merchant_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取角色权限列表
#[utoipa::path(
    get,
    path = "/business/merchant-roles/{role_id}/permissions",
    summary = "获取角色权限列表",
    description = "获取指定角色拥有的权限树形结构列表",
    tags = ["系统商户角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<RolePermissionTreeDetailResponse>)
    )
)]
pub async fn get_role_permissions(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    MyPath(role_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<RolePermissionTreeDetailResponse> {
    info!(
        "获取角色权限列表请求，角色ID: {}，操作人: {}",
        role_id, current_user.account
    );

    match merchant_role_service.get_role_permissions(role_id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取可分配权限列表
#[utoipa::path(
    get,
    path = "/business/merchant-roles/{role_id}/available-permissions",
    summary = "获取可分配权限列表",
    description = "获取可以分配给指定角色的权限树形结构列表",
    tags = ["系统商户角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<AvailablePermissionTreeResponse>)
    )
)]
pub async fn get_available_permissions_for_role(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    MyPath(role_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<AvailablePermissionTreeResponse> {
    info!(
        "获取可分配权限列表请求，角色ID: {}，操作人: {}",
        role_id, current_user.account
    );

    match merchant_role_service
        .get_available_permissions_for_role(role_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 为角色分配权限
#[utoipa::path(
    post,
    path = "/business/merchant-roles/{role_id}/permissions/assign",
    summary = "为角色分配权限",
    description = "为指定角色分配权限",
    tags = ["系统商户角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    request_body = SysMerchantRolePermissionRequest,
    responses(
        (status = 200, description = "权限分配成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "角色权限分配成功",
             "data": null
         })
        )
    )
)]
pub async fn assign_permissions_to_role(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(role_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysMerchantRolePermissionRequest>,
) -> ApiResult<String> {
    info!(
        "为角色分配权限请求，角色ID: {}，权限: {:?}，操作人: {}",
        role_id, req.authorized_permission_ids, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_role_service
        .assign_permissions_to_role(role_id, req, userid)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 移除角色权限
#[utoipa::path(
    post,
    path = "/business/merchant-roles/{role_id}/permissions/remove",
    summary = "移除角色权限",
    description = "移除指定角色的权限",
    tags = ["系统商户角色管理"],
    params(
        ("role_id" = Uuid, Path, description = "角色ID")
    ),
    request_body = SysMerchantRolePermissionRequest,
    responses(
        (status = 200, description = "权限移除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "角色权限移除成功",
             "data": null
         })
        )
    )
)]
pub async fn remove_permissions_from_role(
    Component(merchant_role_service): Component<SysMerchantRoleService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(role_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysMerchantRolePermissionRequest>,
) -> ApiResult<String> {
    info!(
        "移除角色权限请求，角色ID: {}，权限: {:?}，操作人: {}",
        role_id, req.authorized_permission_ids, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_role_service
        .remove_permissions_from_role(role_id, req, userid)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 系统商户角色管理 API 文档
#[derive(OpenApi)]
#[openapi(
    paths(
        page_merchant_roles,
        get_merchant_role_by_id,
        create_merchant_role,
        update_merchant_role,
        delete_merchant_role,
        batch_delete_merchant_roles,
        change_merchant_role_status,
        assign_permissions_to_role,
        remove_permissions_from_role,
        get_merchant_role_select_items,
        get_merchant_role_basic_list,
        get_merchant_role_stats,
        get_role_permissions,
        get_available_permissions_for_role,
    ),
    components(schemas(
        SysMerchantRolePageRequest,
        SysMerchantRoleCreateRequest,
        SysMerchantRoleUpdateRequest,
        SysMerchantRoleBatchDeleteRequest,
        SysMerchantRoleStatusRequest,
        SysMerchantRolePermissionRequest,
        MerchantRoleListResponse,
        MerchantRoleDetailResponse,
        MerchantRoleSelectItem,
        MerchantRoleBasicResponse,
        MerchantRoleStatsResponse,
        MerchantRolePermissionListResponse,
        RolePermissionAssignmentResponse,
        RolePermissionTreeDetailResponse,
        RolePermissionTreeNode,
        AvailablePermissionTreeResponse,
        AvailablePermissionTreeNode,
        MerchantAuthorizedPermissionSelectItem,
    ))
)]
pub struct SysMerchantRoleApiDoc;

impl SysMerchantRoleApiDoc {
    pub fn get_openapi_json() -> String {
        serde_json::to_string_pretty(&Self::openapi()).unwrap()
    }

    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&Self::openapi()).unwrap()
    }
}

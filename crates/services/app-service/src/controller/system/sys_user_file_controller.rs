use crate::domain::comm::storage::UploadResponse;
use crate::service::comm::StorageService;
use crate::utils::FileUtil;
use axum::Router;
use axum::extract::{DefaultBodyLimit, Multipart};
use axum::routing::post;
use lib_core::response::ApiResult;
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::OpenApi;

impl SysUserFileController {
    /// 创建用户文件相关的公开路由（无需授权）
    pub fn public_routes() -> Router {
        Router::new()
    }

    /// 创建用户文件相关的保护路由（需要授权）
    pub fn protected_routes() -> Router {
        Router::new()
            .route("/sys-user/avatar", post(upload_user_avatar))
            .layer(DefaultBodyLimit::max(5 * 1024 * 1024)) // 5MB 用户头像
    }
}

pub struct SysUserFileController;

#[utoipa::path(
    post,
    path = "/sys-user/avatar",
    summary = "上传用户头像",
    description = "上传用户头像，支持 JPG、PNG、GIF、WEBP 格式，最大文件大小 5MB",
    tags = ["系统用户文件管理"],
    request_body(
        content_type = "multipart/form-data",
        description = "头像文件上传表单，使用 avatar 字段名"
    ),
    responses(
        (status = 200, description = "头像上传成功", body = ApiResult<UploadResponse>,
         example = json!({
             "code": 200,
             "message": "用户头像上传成功",
             "data": {
                 "file_url": "https://static.oywm.top/invoice-book/avatars/users/a7/b2/f47ac10b-58cc-4372-a567-0e02b2c3d479.jpg",
                 "file_path": "avatars/users/a7/b2/f47ac10b-58cc-4372-a567-0e02b2c3d479.jpg",
                 "file_size": 102400,
                 "content_type": "image/jpeg"
             }
         })
        )
    )
)]
pub async fn upload_user_avatar(
    Component(storage_service): Component<StorageService>,
    multipart: Multipart,
) -> ApiResult<UploadResponse> {
    // 定义头像允许的文件类型
    const ALLOWED_AVATAR_EXTENSIONS: [&str; 4] = ["jpg", "jpeg", "png", "webp"];
    const MAX_AVATAR_SIZE: usize = 5 * 1024 * 1024; // 5MB

    // 提取头像文件信息
    let file_info = match FileUtil::extract_single_file_from_multipart(
        multipart,
        Some("avatar"), // 使用 avatar 字段名
        Some(MAX_AVATAR_SIZE),
        Some(&ALLOWED_AVATAR_EXTENSIONS),
    )
    .await
    {
        Ok(info) => info,
        Err(e) => {
            return ApiResult::error(400, format!("文件校验失败: {}", e));
        }
    };

    // 验证文件确实是图片类型
    if let Some(extension) = &file_info.extension {
        if !ALLOWED_AVATAR_EXTENSIONS.contains(&extension.as_str()) {
            return ApiResult::error(
                400,
                format!("不支持的头像格式: {}，仅支持 JPG、PNG 格式", extension),
            );
        }
    } else {
        return ApiResult::error(400, "无法确定文件类型，请上传有效的图片文件".to_string());
    }

    // 构建用户头像存储路径前缀
    let path_prefix = "avatars/users".to_string();

    // 上传头像文件
    let upload_result = match storage_service
        .upload_file(
            file_info.file_data,
            file_info.file_name,
            file_info.content_type,
            Some(path_prefix),
            None,
        )
        .await
    {
        Ok(result) => result,
        Err(e) => {
            return ApiResult::error(500, format!("头像上传失败: {}", e));
        }
    };

    ApiResult::success_with_message_and_data(upload_result, "用户头像上传成功".to_string())
}

#[derive(OpenApi)]
#[openapi(
    paths(
        upload_user_avatar
    ),
    components(
        schemas(
            UploadResponse,
            lib_core::response::ApiResult<UploadResponse>,
            lib_core::response::ApiResult<String>
        )
    ),
    tags(
        (name = "系统用户文件管理", description = "系统用户相关文件上传、管理接口")
    )
)]
pub struct SysUserFileDoc;

impl SysUserFileDoc {
    /// 获取OpenAPI JSON文档
    pub fn get_openapi_json() -> String {
        SysUserFileDoc::openapi().to_pretty_json().unwrap()
    }

    /// 获取OpenAPI YAML文档
    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&SysUserFileDoc::openapi()).unwrap()
    }
}

use crate::domain::system::dto::sys_merchant_authorized_permission_request::{
    SysMerchantAuthorizedPermissionBatchDeleteRequest,
    SysMerchantAuthorizedPermissionCreateRequest, SysMerchantAuthorizedPermissionPageRequest,
    SysMerchantAuthorizedPermissionStatusRequest, SysMerchantAuthorizedPermissionUpdateRequest,
    SysMerchantPermissionBatchAuthorizeRequest, SysMerchantPermissionCopyRequest,
    SysMerchantPermissionRevokeRequest,
};
use crate::domain::system::vo::merchant_authorized_permissions_vo::{
    MerchantAuthorizedPermissionDetailResponse, MerchantAuthorizedPermissionListResponse,
    MerchantAuthorizedPermissionSelectItem, MerchantAuthorizedPermissionTreeResponse,
    MerchantPermissionStatsResponse,
};
use crate::domain::system::vo::permission_template_vo::{
    SystemPermissionTemplateSelectItem, SystemPermissionTemplateTreeNode,
};
use crate::service::system::SysMerchantAuthorizedPermissionService;
use crate::utils::custom_validator::{MyPath, MyQuery, ValidJson};
use axum::{
    middleware, routing::{delete, get, post, put},
    Router,
};
use lib_auth::require_permission;
use lib_core::response::{ApiResult, PageData};
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use tracing::info;
use utoipa::OpenApi;
use uuid::Uuid;

/// 系统商户授权权限管理控制器
pub struct SysMerchantAuthorizedPermissionController;
impl SysMerchantAuthorizedPermissionController {
    /// 创建系统商户授权权限管理公开路由
    pub fn public_routes() -> Router {
        Router::new()
        // 系统商户授权权限管理暂无公开路由
    }

    /// 创建系统商户授权权限管理受保护路由
    pub fn protected_routes() -> Router {
        Router::new()
            // 获取系统权限模板树形选择列表（用于授权选择）
            .route(
                "/business/merchant-authorized-permissions/permission-templates/tree",
                get(get_permission_template_tree_for_authorize).layer(middleware::from_fn(
                    require_permission("merchant_permission:authorize"),
                )),
            )
            // 获取系统权限模板选择列表（用于授权选择）
            .route(
                "/business/merchant-authorized-permissions/permission-templates/select",
                get(get_permission_template_select_for_authorize).layer(middleware::from_fn(
                    require_permission("merchant_permission:authorize"),
                )),
            )
            // 获取商户权限统计信息
            .route(
                "/business/merchant-authorized-permissions/stats/{merchant_id}",
                get(get_merchant_permission_stats).layer(middleware::from_fn(require_permission(
                    "merchant_permission:list",
                ))),
            )
            // 获取商户授权权限树形结构
            .route(
                "/business/merchant-authorized-permissions/tree/{merchant_id}",
                get(get_merchant_authorized_permission_tree).layer(middleware::from_fn(
                    require_permission("merchant_permission:list"),
                )),
            )
            // 获取商户授权权限选择项（下拉选择用）
            .route(
                "/business/merchant-authorized-permissions/select/{merchant_id}",
                get(get_merchant_authorized_permission_select_items).layer(middleware::from_fn(
                    require_permission("merchant_permission:list"),
                )),
            )
            // 批量删除商户授权权限
            .route(
                "/business/merchant-authorized-permissions/batch-delete",
                post(batch_delete_merchant_authorized_permissions).layer(middleware::from_fn(
                    require_permission("merchant_permission:delete"),
                )),
            )
            // 商户权限批量授权
            .route(
                "/business/merchant-authorized-permissions/batch-authorize",
                post(batch_authorize_merchant_permissions).layer(middleware::from_fn(
                    require_permission("merchant_permission:authorize"),
                )),
            )
            // 商户权限撤销
            .route(
                "/business/merchant-authorized-permissions/revoke",
                post(revoke_merchant_permissions).layer(middleware::from_fn(require_permission(
                    "merchant_permission:revoke",
                ))),
            )
            // 商户权限复制
            .route(
                "/business/merchant-authorized-permissions/copy",
                post(copy_merchant_permissions).layer(middleware::from_fn(require_permission(
                    "merchant_permission:authorize",
                ))),
            )
            // 商户授权权限状态切换
            .route(
                "/business/merchant-authorized-permissions/{authorized_permission_id}/status",
                put(change_merchant_authorized_permission_status).layer(middleware::from_fn(
                    require_permission("merchant_permission:update"),
                )),
            )
            // 商户授权权限详情查询
            .route(
                "/business/merchant-authorized-permissions/{authorized_permission_id}",
                get(get_merchant_authorized_permission_by_id).layer(middleware::from_fn(
                    require_permission("merchant_permission:detail"),
                )),
            )
            // 商户授权权限更新
            .route(
                "/business/merchant-authorized-permissions/{authorized_permission_id}",
                put(update_merchant_authorized_permission).layer(middleware::from_fn(
                    require_permission("merchant_permission:update"),
                )),
            )
            // 商户授权权限删除
            .route(
                "/business/merchant-authorized-permissions/{authorized_permission_id}",
                delete(delete_merchant_authorized_permission).layer(middleware::from_fn(
                    require_permission("merchant_permission:delete"),
                )),
            )
            // 商户授权权限列表查询
            .route(
                "/business/merchant-authorized-permissions",
                get(page_merchant_authorized_permissions).layer(middleware::from_fn(
                    require_permission("merchant_permission:list"),
                )),
            )
            // 商户授权权限创建
            .route(
                "/business/merchant-authorized-permissions",
                post(create_merchant_authorized_permission).layer(middleware::from_fn(
                    require_permission("merchant_permission:authorize"),
                )),
            )
    }
}

/// 分页查询商户授权权限列表
#[utoipa::path(
    get,
    path = "/business/merchant-authorized-permissions",
    summary = "分页查询商户授权权限列表",
    description = "根据查询条件分页获取商户授权权限信息",
    tags = ["系统商户授权权限管理"],
    params(SysMerchantAuthorizedPermissionPageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<MerchantAuthorizedPermissionListResponse>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "items": [
                     {
                         "id": "550e8400-e29b-41d4-a716-************",
                         "merchant_id": 1001,
                         "merchant_name": "张三餐厅",
                         "permission_template_id": "550e8400-e29b-41d4-a716-446655440001",
                         "permission_name": "订单管理",
                         "permission_code": "order:view",
                         "permission_type": 1,
                         "permission_type_desc": "目录",
                         "status": 0,
                         "status_desc": "正常",
                         "authorized_date": "2023-01-01 10:00:00",
                         "authorized_by_name": "系统管理员",
                         "remark": "商户订单管理权限"
                     }
                 ],
                 "total": 1,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 1
             }
         })
        )
    )
)]
pub async fn page_merchant_authorized_permissions(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    MyQuery(req): MyQuery<SysMerchantAuthorizedPermissionPageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<MerchantAuthorizedPermissionListResponse>> {
    info!(
        "分页查询商户授权权限列表请求: {:?}，操作人: {}",
        req, current_user.account
    );
    match merchant_authorized_permission_service
        .page_merchant_authorized_permissions(req)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 根据ID查询商户授权权限详情
#[utoipa::path(
    get,
    path = "/business/merchant-authorized-permissions/{authorized_permission_id}",
    summary = "查询商户授权权限详情",
    description = "根据授权权限ID获取详细信息",
    tags = ["系统商户授权权限管理"],
    params(
        ("authorized_permission_id" = Uuid, Path, description = "授权权限ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<MerchantAuthorizedPermissionDetailResponse>),
    )
)]
pub async fn get_merchant_authorized_permission_by_id(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    MyPath(authorized_permission_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<MerchantAuthorizedPermissionDetailResponse> {
    info!(
        "查询商户授权权限详情请求，ID: {}，操作人: {}",
        authorized_permission_id, current_user.account
    );

    match merchant_authorized_permission_service
        .get_merchant_authorized_permission_by_id(authorized_permission_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 创建商户授权权限
#[utoipa::path(
    post,
    path = "/business/merchant-authorized-permissions",
    summary = "创建商户授权权限",
    description = "为商户授权新的权限",
    tags = ["系统商户授权权限管理"],
    request_body = SysMerchantAuthorizedPermissionCreateRequest,
    responses(
        (status = 200, description = "创建成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户权限授权成功",
             "data": null
         })
        )
    )
)]
pub async fn create_merchant_authorized_permission(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantAuthorizedPermissionCreateRequest>,
) -> ApiResult<String> {
    info!(
        "创建商户授权权限请求: {:?}，操作人: {}",
        req, current_user.account
    );

    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_authorized_permission_service
        .create_merchant_authorized_permission(req, userid)
        .await
    {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新商户授权权限
#[utoipa::path(
    put,
    path = "/business/merchant-authorized-permissions/{authorized_permission_id}",
    summary = "更新商户授权权限",
    description = "更新商户授权权限信息",
    tags = ["系统商户授权权限管理"],
    params(
        ("authorized_permission_id" = Uuid, Path, description = "授权权限ID")
    ),
    request_body = SysMerchantAuthorizedPermissionUpdateRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户授权权限更新成功",
             "data": null
         })
        )
    )
)]
pub async fn update_merchant_authorized_permission(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(authorized_permission_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysMerchantAuthorizedPermissionUpdateRequest>,
) -> ApiResult<String> {
    info!(
        "更新商户授权权限请求，ID: {}，数据: {:?}，操作人: {}",
        authorized_permission_id, req, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_authorized_permission_service
        .update_merchant_authorized_permission(authorized_permission_id, req, userid)
        .await
    {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 删除商户授权权限
#[utoipa::path(
    delete,
    path = "/business/merchant-authorized-permissions/{authorized_permission_id}",
    summary = "删除商户授权权限",
    description = "删除指定的商户授权权限",
    tags = ["系统商户授权权限管理"],
    params(
        ("authorized_permission_id" = Uuid, Path, description = "授权权限ID")
    ),
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户授权权限删除成功",
             "data": null
         })
        )
    )
)]
pub async fn delete_merchant_authorized_permission(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    MyPath(authorized_permission_id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!(
        "删除商户授权权限请求，ID: {}，操作人: {}",
        authorized_permission_id, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_authorized_permission_service
        .delete_merchant_authorized_permission(authorized_permission_id, userid)
        .await
    {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 批量删除商户授权权限
#[utoipa::path(
    post,
    path = "/business/merchant-authorized-permissions/batch-delete",
    summary = "批量删除商户授权权限",
    description = "批量删除多个商户授权权限",
    tags = ["系统商户授权权限管理"],
    request_body = SysMerchantAuthorizedPermissionBatchDeleteRequest,
    responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "成功删除3个商户授权权限",
             "data": null
         })
        )
    )
)]
pub async fn batch_delete_merchant_authorized_permissions(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantAuthorizedPermissionBatchDeleteRequest>,
) -> ApiResult<String> {
    info!(
        "批量删除商户授权权限请求: {:?}，操作人: {}",
        req, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_authorized_permission_service
        .batch_delete_merchant_authorized_permissions(req, userid)
        .await
    {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 切换商户授权权限状态
#[utoipa::path(
    put,
    path = "/business/merchant-authorized-permissions/{authorized_permission_id}/status",
    summary = "切换商户授权权限状态",
    description = "切换商户授权权限的正常/停用状态",
    tags = ["系统商户授权权限管理"],
    params(
        ("authorized_permission_id" = Uuid, Path, description = "授权权限ID")
    ),
    request_body = SysMerchantAuthorizedPermissionStatusRequest,
    responses(
        (status = 200, description = "状态切换成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户授权权限状态已切换为正常",
             "data": null
         })
        )
    )
)]
pub async fn change_merchant_authorized_permission_status(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(authorized_permission_id): MyPath<Uuid>,
    ValidJson(req): ValidJson<SysMerchantAuthorizedPermissionStatusRequest>,
) -> ApiResult<String> {
    info!(
        "切换商户授权权限状态请求，ID: {}，状态: {}，操作人: {}",
        authorized_permission_id, req.status, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_authorized_permission_service
        .change_merchant_authorized_permission_status(authorized_permission_id, req, userid)
        .await
    {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 商户权限批量授权
#[utoipa::path(
    post,
    path = "/business/merchant-authorized-permissions/batch-authorize",
    summary = "商户权限批量授权",
    description = "为商户批量授权多个权限模板",
    tags = ["系统商户授权权限管理"],
    request_body = SysMerchantPermissionBatchAuthorizeRequest,
    responses(
        (status = 200, description = "授权成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户权限批量授权成功",
             "data": null
         })
        )
    )
)]
pub async fn batch_authorize_merchant_permissions(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantPermissionBatchAuthorizeRequest>,
) -> ApiResult<String> {
    info!(
        "商户权限批量授权请求: {:?}，操作人: {}",
        req, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_authorized_permission_service
        .batch_authorize_merchant_permissions(req, userid)
        .await
    {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 商户权限撤销
#[utoipa::path(
    post,
    path = "/business/merchant-authorized-permissions/revoke",
    summary = "商户权限撤销",
    description = "撤销商户的指定权限",
    tags = ["系统商户授权权限管理"],
    request_body = SysMerchantPermissionRevokeRequest,
    responses(
        (status = 200, description = "撤销成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户权限撤销成功",
             "data": null
         })
        )
    )
)]
pub async fn revoke_merchant_permissions(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantPermissionRevokeRequest>,
) -> ApiResult<String> {
    info!(
        "商户权限撤销请求: {:?}，操作人: {}",
        req, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_authorized_permission_service
        .revoke_merchant_permissions(req, userid)
        .await
    {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 商户权限复制
#[utoipa::path(
    post,
    path = "/business/merchant-authorized-permissions/copy",
    summary = "商户权限复制",
    description = "从源商户复制权限到目标商户",
    tags = ["系统商户授权权限管理"],
    request_body = SysMerchantPermissionCopyRequest,
    responses(
        (status = 200, description = "复制成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户权限复制成功",
             "data": null
         })
        )
    )
)]
pub async fn copy_merchant_permissions(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    ValidJson(req): ValidJson<SysMerchantPermissionCopyRequest>,
) -> ApiResult<String> {
    info!(
        "商户权限复制请求: {:?}，操作人: {}",
        req, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_authorized_permission_service
        .copy_merchant_permissions(req, userid)
        .await
    {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户授权权限选择项
#[utoipa::path(
    get,
    path = "/business/merchant-authorized-permissions/select/{merchant_id}",
    summary = "获取商户授权权限选择项",
    description = "获取指定商户的授权权限下拉选择列表",
    tags = ["系统商户授权权限管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<MerchantAuthorizedPermissionSelectItem>>),
    )
)]
pub async fn get_merchant_authorized_permission_select_items(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    MyPath(merchant_id): MyPath<i64>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<MerchantAuthorizedPermissionSelectItem>> {
    info!(
        "获取商户授权权限选择项请求，商户ID: {}，操作人: {}",
        merchant_id, current_user.account
    );

    match merchant_authorized_permission_service
        .get_merchant_authorized_permission_select_items(merchant_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户权限统计信息
#[utoipa::path(
    get,
    path = "/business/merchant-authorized-permissions/stats/{merchant_id}",
    summary = "获取商户权限统计信息",
    description = "获取指定商户的权限统计数据",
    tags = ["系统商户授权权限管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<MerchantPermissionStatsResponse>),
    )
)]
pub async fn get_merchant_permission_stats(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    MyPath(merchant_id): MyPath<i64>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<MerchantPermissionStatsResponse> {
    info!(
        "获取商户权限统计信息请求，商户ID: {}，操作人: {}",
        merchant_id, current_user.account
    );

    match merchant_authorized_permission_service
        .get_merchant_permission_stats(merchant_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取系统权限模板选择列表（用于授权选择）
#[utoipa::path(
    get,
    path = "/business/merchant-authorized-permissions/permission-templates/select",
    summary = "获取系统权限模板选择列表",
    description = "获取可用于商户授权的系统权限模板选择列表",
    tags = ["系统商户授权权限管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SystemPermissionTemplateSelectItem>>,
         example = json!({
             "code": 200,
             "message": "获取权限模板选择列表成功",
             "data": [
                 {
                     "id": "550e8400-e29b-41d4-a716-************",
                     "permission_name": "订单管理",
                     "permission_code": "order:view",
                     "permission_type": 1,
                     "permission_type_desc": "目录"
                 },
                 {
                     "id": "550e8400-e29b-41d4-a716-446655440001",
                     "permission_name": "用户管理",
                     "permission_code": "user:view",
                     "permission_type": 2,
                     "permission_type_desc": "菜单"
                 }
             ]
         })
        )
    )
)]
pub async fn get_permission_template_select_for_authorize(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SystemPermissionTemplateSelectItem>> {
    info!(
        "获取系统权限模板选择列表请求，操作人: {}",
        current_user.account
    );

    match merchant_authorized_permission_service
        .get_permission_template_select_for_authorize()
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取系统权限模板树形选择列表（用于授权选择）
#[utoipa::path(
    get,
    path = "/business/merchant-authorized-permissions/permission-templates/tree",
    summary = "获取系统权限模板树形选择列表",
    description = "获取可用于商户授权的系统权限模板树形选择列表",
    tags = ["系统商户授权权限管理"],
    responses(
        (status = 200, description = "查询成功", body = ApiResult<Vec<SystemPermissionTemplateTreeNode>>,
         example = json!({
             "code": 200,
             "message": "获取权限模板树形列表成功",
             "data": [
                 {
                     "id": "550e8400-e29b-41d4-a716-************",
                     "permission_name": "系统管理",
                     "permission_code": "system",
                     "parent_id": null,
                     "permission_type": 1,
                     "order_num": 1,
                     "children": [
                         {
                             "id": "550e8400-e29b-41d4-a716-446655440001",
                             "permission_name": "用户管理",
                             "permission_code": "system:user",
                             "parent_id": "550e8400-e29b-41d4-a716-************",
                             "permission_type": 2,
                             "order_num": 1,
                             "children": []
                         }
                     ]
                 }
             ]
         })
        )
    )
)]
pub async fn get_permission_template_tree_for_authorize(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<Vec<SystemPermissionTemplateTreeNode>> {
    info!(
        "获取系统权限模板树形选择列表请求，操作人: {}",
        current_user.account
    );

    match merchant_authorized_permission_service
        .get_permission_template_tree_for_authorize()
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 获取商户授权权限树形结构
#[utoipa::path(
    get,
    path = "/business/merchant-authorized-permissions/tree/{merchant_id}",
    summary = "获取商户授权权限树形结构",
    description = "根据商户ID查询其授权权限的树形结构，包含所有系统权限和授权状态",
    tags = ["系统商户授权权限管理"],
    params(
        ("merchant_id" = i64, Path, description = "商户ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<MerchantAuthorizedPermissionTreeResponse>,
         example = json!({
             "code": 200,
             "message": "获取商户授权权限树形结构成功",
             "data": {
                 "merchant_id": 1,
                 "merchant_name": "张三餐厅",
                 "tree": [
                     {
                         "id": "550e8400-e29b-41d4-a716-************",
                         "authorized_permission_id": "550e8400-e29b-41d4-a716-446655440001",
                         "permission_name": "系统管理",
                         "permission_code": "system",
                         "permission_type": 1,
                         "permission_type_desc": "目录",
                         "parent_id": null,
                         "is_authorized": true,
                         "status": 0,
                         "status_desc": "正常",
                         "authorized_date": "2023-01-01 10:00:00",
                         "order_num": 1,
                         "children": [
                             {
                                 "id": "550e8400-e29b-41d4-a716-446655440002",
                                 "authorized_permission_id": null,
                                 "permission_name": "用户管理",
                                 "permission_code": "system:user",
                                 "permission_type": 2,
                                 "permission_type_desc": "菜单",
                                 "parent_id": "550e8400-e29b-41d4-a716-************",
                                 "is_authorized": false,
                                 "status": null,
                                 "status_desc": null,
                                 "authorized_date": null,
                                 "order_num": 1,
                                 "children": []
                             }
                         ]
                     }
                 ]
             }
         })
        )
    )
)]
pub async fn get_merchant_authorized_permission_tree(
    Component(merchant_authorized_permission_service): Component<
        SysMerchantAuthorizedPermissionService,
    >,
    MyPath(merchant_id): MyPath<i64>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<MerchantAuthorizedPermissionTreeResponse> {
    info!(
        "获取商户授权权限树形结构请求，商户ID: {}，操作人: {}",
        merchant_id, current_user.account
    );

    match merchant_authorized_permission_service
        .get_merchant_authorized_permission_tree(merchant_id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 系统商户授权权限管理 API 文档
#[derive(OpenApi)]
#[openapi(
    paths(
        page_merchant_authorized_permissions,
        get_merchant_authorized_permission_by_id,
        create_merchant_authorized_permission,
        update_merchant_authorized_permission,
        delete_merchant_authorized_permission,
        batch_delete_merchant_authorized_permissions,
        change_merchant_authorized_permission_status,
        batch_authorize_merchant_permissions,
        revoke_merchant_permissions,
        copy_merchant_permissions,
        get_merchant_authorized_permission_select_items,
        get_merchant_permission_stats,
        get_permission_template_select_for_authorize,
        get_permission_template_tree_for_authorize,
        get_merchant_authorized_permission_tree,
    ),
    components(schemas(
        SysMerchantAuthorizedPermissionPageRequest,
        SysMerchantAuthorizedPermissionCreateRequest,
        SysMerchantAuthorizedPermissionUpdateRequest,
        SysMerchantAuthorizedPermissionBatchDeleteRequest,
        SysMerchantAuthorizedPermissionStatusRequest,
        SysMerchantPermissionBatchAuthorizeRequest,
        SysMerchantPermissionRevokeRequest,
        SysMerchantPermissionCopyRequest,
        MerchantAuthorizedPermissionListResponse,
        MerchantAuthorizedPermissionDetailResponse,
        MerchantAuthorizedPermissionSelectItem,
        MerchantPermissionStatsResponse,
        MerchantAuthorizedPermissionTreeResponse,
        SystemPermissionTemplateSelectItem,
        SystemPermissionTemplateTreeNode,
    ))
)]
pub struct SysMerchantAuthorizedPermissionApiDoc;

impl SysMerchantAuthorizedPermissionApiDoc {
    pub fn get_openapi_json() -> String {
        serde_json::to_string_pretty(&Self::openapi()).unwrap()
    }

    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&Self::openapi()).unwrap()
    }
}

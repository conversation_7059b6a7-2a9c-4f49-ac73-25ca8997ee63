use crate::domain::comm::storage::UploadResponse;
use crate::service::comm::StorageService;
use crate::utils::FileUtil;
use axum::Router;
use axum::extract::{DefaultBodyLimit, Multipart};
use axum::routing::post;
use lib_core::response::ApiResult;
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::OpenApi;

// ==================== 路由配置 ====================

impl SysMerchantFileController {
    /// 创建商户文件相关的公开路由（无需授权）
    pub fn public_routes() -> Router {
        Router::new()
    }

    /// 创建商户文件相关的保护路由（需要授权）
    pub fn protected_routes() -> Router {
        Router::new()
            .route("/sys-merchant/avatar", post(upload_merchant_avatar))
            .route("/sys-merchant/license", post(upload_merchant_license))
            .layer(DefaultBodyLimit::max(20 * 1024 * 1024)) // 20MB 营业执照可能较大
    }
}

pub struct SysMerchantFileController;

#[utoipa::path(
    post,
    path = "/sys-merchant/avatar",
    summary = "上传商户头像/Logo",
    description = "上传商户头像或Logo，支持 JPG、PNG、GIF、WEBP 格式，最大文件大小 5MB",
    tags = ["系统商户文件管理"],
    request_body(
        content_type = "multipart/form-data",
        description = "商户头像文件上传表单，使用 avatar 字段名"
    ),
    responses(
        (status = 200, description = "商户头像上传成功", body = ApiResult<UploadResponse>,
         example = json!({
             "code": 200,
             "message": "商户头像上传成功",
             "data": {
                 "file_url": "https://static.oywm.top/invoice-book/avatars/merchants/a7/b2/f47ac10b-58cc-4372-a567-0e02b2c3d479.jpg",
                 "file_path": "avatars/merchants/a7/b2/f47ac10b-58cc-4372-a567-0e02b2c3d479.jpg",
                 "file_size": 256000,
                 "content_type": "image/jpeg"
             }
         })
        )
    )
)]
pub async fn upload_merchant_avatar(
    Component(storage_service): Component<StorageService>,
    multipart: Multipart,
) -> ApiResult<UploadResponse> {
    // 定义商户头像允许的文件类型
    const ALLOWED_AVATAR_EXTENSIONS: [&str; 4] = ["jpg", "jpeg", "png", "webp"];
    const MAX_AVATAR_SIZE: usize = 5 * 1024 * 1024; // 5MB

    // 提取头像文件信息
    let file_info = match FileUtil::extract_single_file_from_multipart(
        multipart,
        Some("avatar"), // 使用 avatar 字段名
        Some(MAX_AVATAR_SIZE),
        Some(&ALLOWED_AVATAR_EXTENSIONS),
    )
    .await
    {
        Ok(info) => info,
        Err(e) => {
            return ApiResult::error(400, format!("文件校验失败: {}", e));
        }
    };

    // 验证文件确实是图片类型
    if let Some(extension) = &file_info.extension {
        if !ALLOWED_AVATAR_EXTENSIONS.contains(&extension.as_str()) {
            return ApiResult::error(
                400,
                format!(
                    "不支持的头像格式: {}，仅支持 JPG、PNG、WEBP 格式",
                    extension
                ),
            );
        }
    } else {
        return ApiResult::error(400, "无法确定文件类型，请上传有效的图片文件".to_string());
    }

    // 构建商户头像存储路径前缀
    let path_prefix = "avatars/merchants".to_string();

    // 上传头像文件
    let upload_result = match storage_service
        .upload_file(
            file_info.file_data,
            file_info.file_name,
            file_info.content_type,
            Some(path_prefix),
            None,
        )
        .await
    {
        Ok(result) => result,
        Err(e) => {
            return ApiResult::error(500, format!("商户头像上传失败: {}", e));
        }
    };

    ApiResult::success_with_message_and_data(upload_result, "商户头像上传成功".to_string())
}

#[utoipa::path(
    post,
    path = "/sys-merchant/license",
    summary = "上传商户营业执照",
    description = "上传商户营业执照，支持 JPG、PNG、PDF 格式，最大文件大小 10MB",
    tags = ["系统商户文件管理"],
    request_body(
        content_type = "multipart/form-data",
        description = "营业执照文件上传表单，使用 license 字段名"
    ),
    responses(
        (status = 200, description = "营业执照上传成功", body = ApiResult<UploadResponse>,
         example = json!({
             "code": 200,
             "message": "营业执照上传成功，请等待审核",
             "data": {
                 "file_url": "https://static.oywm.top/invoice-book/documents/merchants/licenses/a7/b2/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
                 "file_path": "documents/merchants/licenses/a7/b2/f47ac10b-58cc-4372-a567-0e02b2c3d479.pdf",
                 "file_size": 2048000,
                 "content_type": "application/pdf"
             }
         })
        )
    )
)]
pub async fn upload_merchant_license(
    Component(storage_service): Component<StorageService>,
    multipart: Multipart,
) -> ApiResult<UploadResponse> {
    // 定义营业执照允许的文件类型
    const ALLOWED_LICENSE_EXTENSIONS: [&str; 3] = ["jpg", "jpeg", "png"];
    const MAX_LICENSE_SIZE: usize = 10 * 1024 * 1024; // 10MB

    // 提取营业执照文件信息
    let file_info = match FileUtil::extract_single_file_from_multipart(
        multipart,
        Some("license"), // 使用 license 字段名
        Some(MAX_LICENSE_SIZE),
        Some(&ALLOWED_LICENSE_EXTENSIONS),
    )
    .await
    {
        Ok(info) => info,
        Err(e) => {
            return ApiResult::error(400, format!("文件校验失败: {}", e));
        }
    };

    // 验证文件类型
    if let Some(extension) = &file_info.extension {
        if !ALLOWED_LICENSE_EXTENSIONS.contains(&extension.as_str()) {
            return ApiResult::error(
                400,
                format!("不支持的营业执照格式: {}，仅支持 JPG、PNG 格式", extension),
            );
        }
    } else {
        return ApiResult::error(400, "无法确定文件类型，请上传有效的执照文件".to_string());
    }

    // 构建营业执照存储路径前缀
    let path_prefix = "documents/merchants/licenses".to_string();

    // 上传营业执照文件
    let upload_result = match storage_service
        .upload_file(
            file_info.file_data,
            file_info.file_name,
            file_info.content_type,
            Some(path_prefix),
            None,
        )
        .await
    {
        Ok(result) => result,
        Err(e) => {
            return ApiResult::error(500, format!("营业执照上传失败: {}", e));
        }
    };

    // TODO: 这里可以添加业务逻辑
    // 1. 更新商户表中的营业执照字段
    // 2. 删除旧营业执照文件
    // 3. 触发审核流程
    // 4. 发送审核通知
    // 5. 记录操作日志
    // 6. 可能的OCR识别营业执照信息

    ApiResult::success_with_message_and_data(upload_result, "营业执照上传成功".to_string())
}

#[derive(OpenApi)]
#[openapi(
    paths(
        upload_merchant_avatar,
        upload_merchant_license
    ),
    components(
        schemas(
            UploadResponse,
            lib_core::response::ApiResult<UploadResponse>,
            lib_core::response::ApiResult<String>
        )
    ),
    tags(
        (name = "系统商户文件管理", description = "系统商户相关文件上传、管理接口")
    )
)]
pub struct SysMerchantFileDoc;

impl SysMerchantFileDoc {
    /// 获取OpenAPI JSON文档
    pub fn get_openapi_json() -> String {
        SysMerchantFileDoc::openapi().to_pretty_json().unwrap()
    }

    /// 获取OpenAPI YAML文档
    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&SysMerchantFileDoc::openapi()).unwrap()
    }
}

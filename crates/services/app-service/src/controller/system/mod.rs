pub mod sys_auth_controller;
mod sys_file_controller;
pub mod sys_merchant_authorized_permission_controller;
pub mod sys_merchant_category_controller;
pub mod sys_merchant_commission_rules_controller;
pub mod sys_merchant_controller;
pub mod sys_merchant_file_controller;
pub mod sys_merchant_role_controller;
pub mod sys_merchant_user_controller;
pub mod sys_permission_controller;
pub mod sys_permission_template_controller;
pub mod sys_role_controller;
pub mod sys_user_controller;
pub mod sys_user_file_controller;

use crate::controller::system::sys_file_controller::SysFileController;
use crate::controller::system::sys_merchant_authorized_permission_controller::SysMerchantAuthorizedPermissionController;
use crate::controller::system::sys_merchant_category_controller::SysMerchantCategoryController;
use crate::controller::system::sys_merchant_commission_rules_controller::SysMerchantCommissionRulesController;
use crate::controller::system::sys_merchant_controller::SysMerchantController;
use crate::controller::system::sys_merchant_file_controller::SysMerchantFileController;
use crate::controller::system::sys_merchant_role_controller::SysMerchantRoleController;
use crate::controller::system::sys_merchant_user_controller::SysMerchantUserController;
use crate::controller::system::sys_permission_template_controller::SysPermissionTemplateController;
use crate::controller::system::sys_user_file_controller::SysUserFileController;
use axum::Router;
pub use sys_auth_controller::SysAuthController;
pub use sys_merchant_authorized_permission_controller::SysMerchantAuthorizedPermissionApiDoc;
pub use sys_merchant_commission_rules_controller::SysMerchantCommissionRulesApiDoc;
pub use sys_merchant_role_controller::SysMerchantRoleApiDoc;
pub use sys_permission_controller::{SysPermissionApiDoc, SysPermissionController};
pub use sys_permission_template_controller::SysPermissionTemplateApiDoc;
pub use sys_role_controller::{SysRoleApiDoc, SysRoleController};
pub use sys_user_controller::SysUserController;

/// 创建系统模块的公开路由（无需授权）
pub fn public_routes() -> Router {
    Router::new()
        // 系统授权相关接口
        .merge(SysAuthController::public_routes())
        // 系统用户管理相关接口
        .merge(SysUserController::public_routes())
        // 系统用户文件管理相关接口
        .merge(SysUserFileController::public_routes())
        // 系统角色管理相关接口
        .merge(SysRoleController::public_routes())
        // 系统商户用户管理相关接口
        .merge(SysMerchantUserController::public_routes())
        // 系统商户角色管理相关接口
        .merge(SysMerchantRoleController::public_routes())
        // 系统商户文件管理相关接口
        .merge(SysMerchantFileController::public_routes())
        // 系统商户授权权限管理相关接口
        .merge(SysMerchantAuthorizedPermissionController::public_routes())
        // 系统商户佣金规则管理相关接口
        .merge(SysMerchantCommissionRulesController::public_routes())
        // 系统权限模板管理相关接口
        .merge(SysPermissionTemplateController::public_routes())
}

/// 创建系统模块的受保护路由（需要授权）
pub fn protected_routes() -> Router {
    Router::new()
        // 系统通用文件上传
        .merge(SysFileController::protected_routes())
        // 系统用户文件上传
        .merge(SysUserFileController::protected_routes())
        // 系统商户文件上传
        .merge(SysMerchantFileController::protected_routes())
        // 系统授权相关接口
        .merge(SysAuthController::protected_routes())
        // 系统用户管理相关接口
        .merge(SysUserController::protected_routes())
        // 系统角色管理相关接口
        .merge(SysRoleController::protected_routes())
        // 系统权限菜单管理相关接口
        .merge(SysPermissionController::protected_routes())
        // 系统权限模板管理相关接口
        .merge(SysPermissionTemplateController::protected_routes())
        // 系统商户相关管理相关接口
        .merge(SysMerchantController::protected_routes())
        // 系统商户分类管理相关接口
        .merge(SysMerchantCategoryController::protected_routes())
        // 系统商户用户管理相关接口
        .merge(SysMerchantUserController::protected_routes())
        // 系统商户角色管理相关接口
        .merge(SysMerchantRoleController::protected_routes())
        // 系统商户授权权限管理相关接口
        .merge(SysMerchantAuthorizedPermissionController::protected_routes())
        // 系统商户佣金规则管理相关接口
        .merge(SysMerchantCommissionRulesController::protected_routes())
    }

use crate::domain::business::dto::merchant_user_request::UserMerchantListRequest;
use crate::domain::business::merchants::dto::merchant_user_request::{
    MerchantUserLoginRequest, MerchantUserWechatLoginRequest,
};
use crate::domain::business::merchants::vo::merchant_user_vo::{
    MerchantUserLoginResponse, MerchantUserProfileResponse,
};
use crate::service::business::merchants::MerchantUserAuthService;
use crate::utils::custom_validator::MyQuery;
use crate::utils::ValidJson;
use axum::Router;
use lib_auth::middleware::ip_resolver_middleware::ClientIp;
use lib_core::response::ApiResult;
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use tracing::info;
use utoipa::OpenApi;
// ==================== 路由配置 ====================

impl MerchantUserAuthController {
    /// 创建商户认证的公开路由（无需授权）
    pub fn public_routes() -> Router {
        Router::new()
            // 商户用户登录
            .route("/merchant_user/login", axum::routing::post(login))
            // 商户用户微信登录
            .route(
                "/merchant_user/wechat_login",
                axum::routing::post(wechat_login),
            )
    }

    /// 创建商户认证的受保护路由（需要授权）
    pub fn protected_routes() -> Router {
        Router::new()
            // 获取用户商户列表
            .route(
                "/merchant_user/merchants",
                axum::routing::get(get_user_merchant_list),
            )
            // 切换当前商户
            .route(
                "/merchant_user/switch_merchant",
                axum::routing::post(switch_merchant),
            )
            // 获取当前用户个人信息
            .route(
                "/merchant_user/profile",
                axum::routing::get(get_current_user_profile),
            )
            // 修改密码
            .route(
                "/merchant_user/change_password",
                axum::routing::post(change_password),
            )
            // 修改个人信息
            .route("/merchant_user/profile", axum::routing::put(update_profile))
            // 主动登出
            .route("/merchant_user/logout", axum::routing::post(logout))
    }
}

/// 商户认证控制器结构体（用于组织路由方法）
pub struct MerchantUserAuthController;

/// 商户用户登录接口
#[utoipa::path(
    post,
    path = "merchant_user/login",
    summary = "商户用户登录",
    description = "商户用户登录接口，验证手机号和密码，返回访问令牌",
    tags = ["商户认证管理"],
    request_body = MerchantUserLoginRequest,
    responses(
        (status = 200, description = "登录成功", body = ApiResult<MerchantUserLoginResponse>,
         example = json!({
             "code": 200,
             "message": "登录成功",
             "data": {
                 "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
             }
         })
        )
    )
)]
pub async fn login(
    Component(merchant_user_auth_service): Component<MerchantUserAuthService>,
    axum::extract::Extension(client_ip): axum::extract::Extension<ClientIp>,
    ValidJson(req): ValidJson<MerchantUserLoginRequest>,
) -> ApiResult<MerchantUserLoginResponse> {
    info!("商户用户登录请求: {:?}", req);
    info!("客户端IP: {}", client_ip.0);

    // 获取商户认证服务实例并处理登录
    match merchant_user_auth_service.login(req, client_ip.0).await {
        Ok(response) => ApiResult::success_with_message_and_data(response, "登录成功".to_string()),
        Err(business_error) => ApiResult::error(business_error.code, business_error.message),
    }
}

/// 商户用户微信登录接口
#[utoipa::path(
    post,
    path = "merchant_user/wechat_login",
    summary = "商户用户微信登录",
    description = "商户用户微信登录接口，通过微信授权码进行登录，返回访问令牌",
    tags = ["商户认证管理"],
    request_body = MerchantUserWechatLoginRequest,
    responses(
        (status = 200, description = "登录成功", body = ApiResult<MerchantUserLoginResponse>,
         example = json!({
             "code": 200,
             "message": "登录成功",
             "data": {
                 "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
             }
         })
        )
    )
)]
pub async fn wechat_login(
    Component(merchant_user_auth_service): Component<MerchantUserAuthService>,
    axum::extract::Extension(client_ip): axum::extract::Extension<ClientIp>,
    ValidJson(req): ValidJson<MerchantUserWechatLoginRequest>,
) -> ApiResult<MerchantUserLoginResponse> {
    info!("商户用户微信登录请求: {:?}", req);
    info!("客户端IP: {}", client_ip.0);
    match merchant_user_auth_service
        .wechat_login(req, client_ip.0)
        .await
    {
        Ok(response) => ApiResult::success_with_message_and_data(response, "登录成功".to_string()),
        Err(business_error) => ApiResult::error(business_error.code, business_error.message),
    }
}

/// 获取用户商户列表接口
#[utoipa::path(
    get,
    path = "merchant_user/merchants",
    summary = "获取用户商户列表",
    description = "获取当前登录用户关联的所有商户列表，包含商户基本信息、分类信息和用户角色",
    tags = ["商户认证管理"],
    params(
        ("include_disabled" = Option<bool>, Query, description = "是否包含已禁用的商户，默认为false")
    ),
    responses(
        (status = 200, description = "获取成功", body = ApiResult<crate::domain::business::merchants::vo::merchant_user_vo::UserMerchantListResponse>,
         example = json!({
             "code": 200,
             "message": "获取成功",
             "data": {
                 "merchants": [
                     {
                         "merchant_id": 123456,
                         "merchant_name": "张三餐厅",
                         "merchant_code": "REST001",
                         "merchant_category": {
                             "category_id": "550e8400-e29b-41d4-a716-446655440001",
                             "category_name": "餐饮服务"
                         },
                         "merchant_avatar": "https://example.com/avatar.jpg",
                         "user_roles": [
                             {
                                 "role_id": "550e8400-e29b-41d4-a716-446655440000",
                                 "role_name": "商户管理员",
                                 "role_code": "merchant_admin"
                             }
                         ]
                     }
                 ],
                 "total_count": 1
             }
         })
        )
    )
)]
pub async fn get_user_merchant_list(
    Component(merchant_user_auth_service): Component<MerchantUserAuthService>,
    MyQuery(include_disabled): MyQuery<UserMerchantListRequest>,
    axum::extract::Extension(session): axum::extract::Extension<
        lib_auth::middleware::merchant_session::MerchantAuthSession,
    >,
) -> ApiResult<crate::domain::business::merchants::vo::merchant_user_vo::UserMerchantListResponse> {
    info!("获取用户商户列表请求: 用户ID: {}", session.user_id);

    // 调用服务层获取商户列表
    match merchant_user_auth_service
        .get_user_merchant_list(include_disabled, session.user_id)
        .await
    {
        Ok(response) => ApiResult::success_with_message_and_data(response, "获取成功".to_string()),
        Err(business_error) => ApiResult::error(business_error.code, business_error.message),
    }
}

/// 获取当前用户个人信息接口
#[utoipa::path(
    get,
    path = "merchant_user/profile",
    summary = "获取当前用户个人信息", 
    description = "获取商户用户当前登录人的详细信息，包括用户基本信息、权限列表和角色信息",
    tags = ["商户认证管理"],
    responses(
        (status = 200, description = "个人信息获取成功", body = ApiResult<MerchantUserProfileResponse>)
    )
)]

pub async fn get_current_user_profile(
    Component(merchant_user_auth_service): Component<MerchantUserAuthService>,
    axum::extract::Extension(session): axum::extract::Extension<
        lib_auth::middleware::merchant_session::MerchantAuthSession,
    >,
) -> ApiResult<MerchantUserProfileResponse> {
    info!("获取当前用户个人信息请求: 用户ID: {}", session.user_id);

    // 调用服务层获取用户个人信息
    match merchant_user_auth_service
        .get_current_user_profile(session)
        .await
    {
        Ok(response) => ApiResult::success(response),
        Err(business_error) => ApiResult::error(business_error.code, business_error.message),
    }
}

/// 切换当前商户接口
#[utoipa::path(
    post,
    path = "merchant_user/switch_merchant",
    summary = "切换当前商户",
    description = "用户切换到其有权限的另一个商户，更新会话信息。由于JWT只包含session_id，无需返回新token",
    tags = ["商户认证管理"],
    request_body = crate::domain::business::merchants::dto::merchant_user_request::SwitchMerchantRequest,
    responses(
        (status = 200, description = "切换成功", body = ApiResult<crate::domain::business::merchants::vo::merchant_user_vo::SwitchMerchantResponse>,
         example = json!({
             "code": 200,
             "message": "切换成功",
             "data": {
                 "merchant_name": "张三餐厅"
             }
         })
        )
    )
)]
pub async fn switch_merchant(
    Component(merchant_user_auth_service): Component<MerchantUserAuthService>,
    axum::extract::Extension(session): axum::extract::Extension<
        lib_auth::middleware::merchant_session::MerchantAuthSession,
    >,
    axum::extract::Extension(token): axum::extract::Extension<
        lib_auth::middleware::merchant_session::MerchantToken,
    >,
    ValidJson(req): ValidJson<
        crate::domain::business::merchants::dto::merchant_user_request::SwitchMerchantRequest,
    >,
) -> ApiResult<crate::domain::business::merchants::vo::merchant_user_vo::SwitchMerchantResponse> {
    info!(
        "切换商户请求: 用户ID: {}, 目标商户ID: {}",
        session.user_id, req.merchant_id
    );

    // 调用服务层处理切换商户逻辑
    match merchant_user_auth_service
        .switch_merchant(req, session, token.as_str())
        .await
    {
        Ok(response) => ApiResult::success_with_message_and_data(response, "切换成功".to_string()),
        Err(business_error) => ApiResult::error(business_error.code, business_error.message),
    }
}

/// 修改密码接口
#[utoipa::path(
    post,
    path = "merchant_user/change_password",
    summary = "修改密码",
    description = "修改当前登录用户的密码，需要验证旧密码",
    tags = ["商户认证管理"],
    request_body = crate::domain::business::merchants::dto::merchant_user_request::ChangePasswordRequest,
    responses(
        (status = 200, description = "密码修改成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "密码修改成功",
             "data": null
         })
        )
    )
)]
pub async fn change_password(
    Component(merchant_user_auth_service): Component<MerchantUserAuthService>,
    axum::extract::Extension(session): axum::extract::Extension<
        lib_auth::middleware::merchant_session::MerchantAuthSession,
    >,
    ValidJson(req): ValidJson<
        crate::domain::business::merchants::dto::merchant_user_request::ChangePasswordRequest,
    >,
) -> ApiResult<String> {
    info!("修改密码请求: 用户ID: {}", session.user_id);

    // 调用服务层处理密码修改逻辑
    match merchant_user_auth_service
        .change_password(req, session)
        .await
    {
        Ok(_) => ApiResult::success_with_message("密码修改成功".to_string()),
        Err(business_error) => ApiResult::error(business_error.code, business_error.message),
    }
}

/// 修改个人信息接口
#[utoipa::path(
    put,
    path = "merchant_user/profile",
    summary = "修改个人信息",
    description = "修改当前登录用户的个人信息，不包括密码、身份证号、真实姓名、状态等敏感字段",
    tags = ["商户认证管理"],
    request_body = crate::domain::business::merchants::dto::merchant_user_request::UpdateProfileRequest,
    responses(
        (status = 200, description = "个人信息修改成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "个人信息修改成功",
             "data": null
         })
        )
    )
)]
pub async fn update_profile(
    Component(merchant_user_auth_service): Component<MerchantUserAuthService>,
    axum::extract::Extension(session): axum::extract::Extension<
        lib_auth::middleware::merchant_session::MerchantAuthSession,
    >,
    ValidJson(req): ValidJson<
        crate::domain::business::merchants::dto::merchant_user_request::UpdateProfileRequest,
    >,
) -> ApiResult<String> {
    info!("修改个人信息请求: 用户ID: {}", session.user_id);

    // 调用服务层处理个人信息修改逻辑
    match merchant_user_auth_service
        .update_profile(req, session)
        .await
    {
        Ok(_) => ApiResult::success_with_message("个人信息修改成功".to_string()),
        Err(business_error) => ApiResult::error(business_error.code, business_error.message),
    }
}

/// 主动登出接口
#[utoipa::path(
    post,
    path = "merchant_user/logout",
    summary = "主动登出",
    description = "用户主动退出登录，清理会话状态",
    tags = ["商户认证管理"],
    responses(
        (status = 200, description = "登出成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "登出成功",
             "data": null
         })
        )
    )
)]
pub async fn logout(
    Component(merchant_user_auth_service): Component<MerchantUserAuthService>,
    axum::extract::Extension(session): axum::extract::Extension<
        lib_auth::middleware::merchant_session::MerchantAuthSession,
    >,
    axum::extract::Extension(token): axum::extract::Extension<
        lib_auth::middleware::merchant_session::MerchantToken,
    >,
) -> ApiResult<String> {
    info!("主动登出请求: 用户ID: {}", session.user_id);

    // 调用服务层处理登出逻辑，传入token用于清理Redis会话
    match merchant_user_auth_service
        .logout(session, token.as_str())
        .await
    {
        Ok(_) => ApiResult::success_with_message("登出成功".to_string()),
        Err(business_error) => ApiResult::error(business_error.code, business_error.message),
    }
}

/// 商户认证管理API文档
#[derive(OpenApi)]
#[openapi(
    paths(
        login,
        wechat_login,
        get_user_merchant_list,
        get_current_user_profile,
        switch_merchant,
        change_password,
        update_profile,
        logout
    ),
    components(
        schemas(
            // DTO 请求对象
            MerchantUserLoginRequest,
            MerchantUserWechatLoginRequest,
            crate::domain::business::merchants::dto::merchant_user_request::UserMerchantListRequest,
            crate::domain::business::merchants::dto::merchant_user_request::SwitchMerchantRequest,
            crate::domain::business::merchants::dto::merchant_user_request::ChangePasswordRequest,
            crate::domain::business::merchants::dto::merchant_user_request::UpdateProfileRequest,
            // VO 响应对象
            MerchantUserLoginResponse,
            MerchantUserProfileResponse,
            crate::domain::business::merchants::vo::merchant_user_vo::UserMerchantListResponse,
            crate::domain::business::merchants::vo::merchant_user_vo::UserMerchantListItemResponse,
            crate::domain::business::merchants::vo::merchant_user_vo::MerchantCategoryInfo,
            crate::domain::business::merchants::vo::merchant_user_vo::MerchantRoleInfo,
            crate::domain::business::merchants::vo::merchant_user_vo::SwitchMerchantResponse,
            crate::domain::business::merchants::vo::merchant_role_vo::MerchantUserRoleInfo,
            // 通用响应类型
            lib_core::response::ApiResult<MerchantUserLoginResponse>,
            lib_core::response::ApiResult<MerchantUserProfileResponse>,
            lib_core::response::ApiResult<crate::domain::business::merchants::vo::merchant_user_vo::UserMerchantListResponse>,
            lib_core::response::ApiResult<crate::domain::business::merchants::vo::merchant_user_vo::SwitchMerchantResponse>,
            lib_core::response::ApiResult<String>
        )
    ),
    tags(
        (name = "商户认证管理", description = "商户用户认证相关接口，包括登录、获取商户列表、切换商户等功能")
    )
)]
pub struct MerchantUserAuthApiDoc;

impl MerchantUserAuthApiDoc {
    /// 获取OpenAPI JSON文档
    pub fn get_openapi_json() -> String {
        MerchantUserAuthApiDoc::openapi().to_pretty_json().unwrap()
    }

    /// 获取OpenAPI YAML文档
    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&MerchantUserAuthApiDoc::openapi()).unwrap()
    }
}

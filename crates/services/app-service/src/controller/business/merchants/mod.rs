use axum::Router;

pub mod merchant_user_auth_controller;

// 创建商户模块的公开路由（不需要授权）
pub fn public_routes() -> Router {
    Router::new()
        // 商户用户认证相关接口
        .merge(merchant_user_auth_controller::MerchantUserAuthController::public_routes())
}

/// 创建商户模块的公开路由（需要授权）
pub fn protected_routes() -> Router {
    Router::new()
        // 商户授权管理相关接口
        .merge(merchant_user_auth_controller::MerchantUserAuthController::protected_routes())
}

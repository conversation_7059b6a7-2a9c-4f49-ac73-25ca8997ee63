use crate::domain::comm::dto::wechat_auth_request::{WechatBindRequest, WechatLoginRequest};
use crate::domain::comm::vo::wechat_auth_vo::{WechatBindResponse, WechatLoginResponse};
use crate::utils::ValidJson;
use axum::Router;
use lib_auth::middleware::ip_resolver_middleware::ClientIp;
use lib_core::response::ApiResult;
// use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use tracing::info;
use utoipa::OpenApi;

// ==================== 路由配置 ====================

impl WechatAuthController {
    /// 创建微信认证的公开路由（无需授权）
    pub fn public_routes() -> Router {
        Router::new()
            // 微信登录
            .route("/wechat/login", axum::routing::post(wechat_login))
            // 微信账号绑定
            .route("/wechat/bind", axum::routing::post(wechat_bind))
    }

    /// 创建微信认证的受保护路由（需要授权）
    pub fn protected_routes() -> Router {
        Router::new()
    }
}

/// 微信认证控制器结构体（用于组织路由方法）
pub struct WechatAuthController;

/// 微信登录接口
#[utoipa::path(
    post,
    path = "/wechat/login",
    summary = "微信登录",
    description = "通用微信登录接口，支持系统用户和商户用户。通过微信授权码进行登录，返回JWT访问令牌",
    tags = ["微信认证管理"],
    request_body = WechatLoginRequest,
    responses(
        (status = 200, description = "登录成功", body = ApiResult<WechatLoginResponse>,
         example = json!({
             "code": 200,
             "message": "登录成功",
             "data": {
                 "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
                 "user_info": {
                     "id": "550e8400-e29b-41d4-a716-446655440000",
                     "username": "admin",
                     "nickname": "张三",
                     "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKxrUx0mxFiafEEHShLQLtJuIZOpTClp2DlMfbdBP0jkHH7TG6aVunZS5ar5GxPDJkMjqrGoFbpRaw/132",
                     "user_type": "system"
                 },
                 "expires_in": 28800
             }
         })
        ),
        (status = 400, description = "参数错误"),
        (status = 401, description = "微信授权失败或用户未绑定"),
        (status = 500, description = "服务器内部错误")
    )
)]
pub async fn wechat_login(
    // Component(wechat_auth_service): Component<WechatAuthService>,
    axum::extract::Extension(client_ip): axum::extract::Extension<ClientIp>,
    ValidJson(req): ValidJson<WechatLoginRequest>,
) -> ApiResult<WechatLoginResponse> {
    info!(
        "微信登录请求: user_type={:?}, merchant_id={:?}",
        req.user_type, req.merchant_id
    );
    info!("客户端IP: {}", client_ip.0);

    // TODO: 实现微信登录逻辑
    // 1. 验证参数（商户用户必须提供merchant_id）
    // 2. 通过code获取微信用户信息
    // 3. 根据user_type处理不同类型用户登录
    // 4. 生成JWT token并返回

    // 临时返回成功响应（实际实现时需要调用service）
    let response = WechatLoginResponse {
        token: "temp_token_placeholder".to_string(),
        user_info: crate::domain::comm::vo::wechat_auth_vo::UserInfo {
            id: "temp_user_id".to_string(),
            username: "temp_username".to_string(),
            nickname: "临时用户".to_string(),
            avatar_url: None,
            user_type: format!("{:?}", req.user_type).to_lowercase(),
        },
        expires_in: 28800,
    };

    ApiResult::success_with_message_and_data(response, "登录成功".to_string())
}

/// 微信账号绑定接口
#[utoipa::path(
    post,
    path = "/wechat/bind",
    summary = "微信账号绑定",
    description = "将微信账号绑定到现有用户账号。需要提供用户名密码进行身份验证",
    tags = ["微信认证管理"],
    request_body = WechatBindRequest,
    responses(
        (status = 200, description = "绑定成功", body = ApiResult<WechatBindResponse>,
         example = json!({
             "code": 200,
             "message": "绑定成功",
             "data": {
                 "message": "微信账号绑定成功"
             }
         })
        ),
        (status = 400, description = "参数错误"),
        (status = 401, description = "用户名密码错误"),
        (status = 409, description = "微信账号已被绑定"),
        (status = 500, description = "服务器内部错误")
    )
)]
pub async fn wechat_bind(
    // Component(wechat_auth_service): Component<WechatAuthService>,
    axum::extract::Extension(client_ip): axum::extract::Extension<ClientIp>,
    ValidJson(req): ValidJson<WechatBindRequest>,
) -> ApiResult<WechatBindResponse> {
    info!(
        "微信账号绑定请求: username={}, user_type={:?}",
        req.username, req.user_type
    );
    info!("客户端IP: {}", client_ip.0);

    // TODO: 实现微信账号绑定逻辑
    // 1. 验证用户名密码
    // 2. 获取微信用户信息
    // 3. 检查微信账号是否已被绑定
    // 4. 绑定微信openid到用户账号

    let response = WechatBindResponse {
        message: "微信账号绑定成功".to_string(),
    };

    ApiResult::success_with_message_and_data(response, "绑定成功".to_string())
}

/// 微信认证管理API文档
#[derive(OpenApi)]
#[openapi(
    paths(
        wechat_login,
        wechat_bind,
    ),
    components(
        schemas(
            // DTO 请求对象
            WechatLoginRequest,
            WechatBindRequest,
            crate::domain::comm::dto::wechat_auth_request::UserType,
            // VO 响应对象
            WechatLoginResponse,
            WechatBindResponse,
            crate::domain::comm::vo::wechat_auth_vo::UserInfo,
            // 通用响应类型
            lib_core::response::ApiResult<WechatLoginResponse>,
            lib_core::response::ApiResult<WechatBindResponse>,
        )
    ),
    tags(
        (name = "微信认证管理", description = "微信登录、绑定")
    )
)]
pub struct WechatAuthApiDoc;

impl WechatAuthApiDoc {
    /// 获取OpenAPI JSON文档
    pub fn get_openapi_json() -> String {
        WechatAuthApiDoc::openapi().to_pretty_json().unwrap()
    }

    /// 获取OpenAPI YAML文档
    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&WechatAuthApiDoc::openapi()).unwrap()
    }
}

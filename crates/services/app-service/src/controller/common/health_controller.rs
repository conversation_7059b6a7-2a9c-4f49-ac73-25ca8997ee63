use axum::{Router, response::Json, routing::get};
use chrono::Utc;
use serde_json::{Value, json};

/// 健康检查控制器
pub struct HealthController;

impl HealthController {
    /// 创建健康检查路由
    pub fn routes() -> Router {
        Router::new()
            .route("/health", get(health_check))
            .route("/", get(root_health))
    }
}

/// 健康检查接口
async fn health_check() -> Json<Value> {
    Json(json!({
        "status": "ok",
        "service": "invoice-book",
        "version": env!("CARGO_PKG_VERSION"),
        "timestamp": Utc::now().to_rfc3339(),
        "uptime": "running"
    }))
}

/// 根路径健康检查
async fn root_health() -> Json<Value> {
    Json(json!({
        "message": "Invoice Book API is running",
        "status": "healthy",
        "timestamp": Utc::now().to_rfc3339()
    }))
}

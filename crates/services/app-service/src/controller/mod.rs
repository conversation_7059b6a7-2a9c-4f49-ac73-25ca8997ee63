pub mod business;
mod common;
pub mod system;

use axum::{Router, middleware};
use lib_auth::{jwt_auth_middleware, merchant_jwt_auth_middleware};

/// 创建所有公开路由（无需授权）
pub fn create_public_routes() -> Router {
    Router::new()
        // 通用接口
        .merge(common::public_routes())
        // 系统模块的公开路由
        .merge(system::public_routes())
        .merge(business::public_routes())
}

/// 创建所有受保护路由（需要授权）
pub fn create_protected_routes() -> Router {
    Router::new()
        // 合并所有受保护路由，再统一应用JWT中间件
        .merge(system::protected_routes().route_layer(middleware::from_fn(jwt_auth_middleware)))
        // 业务相关的受保护路由
        .merge(
            business::merchants::protected_routes()
                .route_layer(middleware::from_fn(merchant_jwt_auth_middleware)),
        )
}

use crate::domain::business::merchants::entities::merchant_users::{
    ActiveModel, Column, Entity as MerchantUsers, MerchantUserStatus, Model as MerchantUsersModel,
};
use crate::domain::system::dto::sys_merchant_user_request::SysMerchantUserPageRequest;
use crate::utils::datetime::DateTimeUtils;
use anyhow::{Context, Result};
use lib_core::PageDataBuilder;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, DbErr, EntityTrait, PaginatorTrait,
    QueryFilter, QueryOrder,
};
use uuid::Uuid;

/// 商户用户Repository实现
#[derive(Clone, Service)]
pub struct MerchantUsersRepository;

impl MerchantUsersRepository {
    /// 构建查询条件
    fn build_query_conditions(req: &SysMerchantUserPageRequest) -> Condition {
        let mut condition = Condition::all();

        // 用户名搜索
        if let Some(ref username) = req.username {
            if !username.trim().is_empty() {
                condition = condition.add(Column::Username.contains(username.trim()));
            }
        }

        // 真实姓名搜索
        if let Some(ref real_name) = req.real_name {
            if !real_name.trim().is_empty() {
                condition = condition.add(Column::RealName.contains(real_name.trim()));
            }
        }

        // 手机号搜索
        if let Some(ref phone) = req.phone {
            if !phone.trim().is_empty() {
                condition = condition.add(Column::Phone.contains(phone.trim()));
            }
        }

        // 邮箱搜索
        if let Some(ref email) = req.email {
            if !email.trim().is_empty() {
                condition = condition.add(Column::Email.contains(email.trim()));
            }
        }

        // 性别筛选
        if let Some(gender) = req.gender {
            condition = condition.add(Column::Gender.eq(gender));
        }

        // 状态筛选
        if let Some(status) = req.status {
            condition = condition.add(Column::Status.eq(status));
        }

        // 关键词搜索（用户名、真实姓名、手机号）
        if let Some(ref keyword) = req.keyword {
            if !keyword.trim().is_empty() {
                let keyword_condition = Condition::any()
                    .add(Column::Username.contains(keyword.trim()))
                    .add(Column::RealName.contains(keyword.trim()))
                    .add(Column::Phone.contains(keyword.trim()));
                condition = condition.add(keyword_condition);
            }
        }

        // 创建时间范围筛选
        if let Some(ref created_start) = req.created_start {
            if !created_start.trim().is_empty() {
                if let Ok(start_datetime) = DateTimeUtils::parse_range_start(created_start) {
                    condition =
                        condition.add(Column::CreatedDate.gte(start_datetime.fixed_offset()));
                }
            }
        }

        if let Some(ref created_end) = req.created_end {
            if !created_end.trim().is_empty() {
                if let Ok(end_datetime) = DateTimeUtils::parse_range_end(created_end) {
                    condition = condition.add(Column::CreatedDate.lte(end_datetime.fixed_offset()));
                }
            }
        }

        condition
    }

    /// 分页查询商户用户
    pub async fn page_by_condition<C>(
        &self,
        req: &SysMerchantUserPageRequest,
        conn: &C,
    ) -> Result<PageDataBuilder<MerchantUsersModel>>
    where
        C: ConnectionTrait,
    {
        let condition = Self::build_query_conditions(req);
        let pagination = Pagination::from_one_based(req.page, req.page_size);

        MerchantUsers::find()
            .filter(condition)
            .order_by_desc(Column::CreatedDate)
            .page_builder(conn, &pagination)
            .await
            .context("分页查询商户用户失败")
    }

    /// 根据ID查询商户用户
    pub async fn find_by_id<C>(&self, user_id: Uuid, conn: &C) -> Result<Option<MerchantUsersModel>>
    where
        C: ConnectionTrait,
    {
        MerchantUsers::find_by_id(user_id)
            .one(conn)
            .await
            .context("查询商户用户失败")
    }

    /// 创建商户用户
    pub async fn create<C>(&self, model: ActiveModel, conn: &C) -> Result<MerchantUsersModel>
    where
        C: ConnectionTrait,
    {
        model.insert(conn).await.context("创建商户用户失败")
    }

    /// 更新商户用户
    pub async fn update<C>(&self, model: ActiveModel, conn: &C) -> Result<MerchantUsersModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context("更新商户用户失败")
    }

    /// 根据ID删除商户用户
    pub async fn delete_by_id<C>(&self, user_id: Uuid, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        MerchantUsers::delete_by_id(user_id)
            .exec(conn)
            .await
            .context("删除商户用户失败")?;

        Ok(())
    }

    /// 批量删除商户用户
    pub async fn delete_by_ids<C>(&self, user_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = MerchantUsers::delete_many()
            .filter(Column::Id.is_in(user_ids))
            .exec(conn)
            .await
            .context("批量删除商户用户失败")?;

        Ok(result.rows_affected)
    }

    /// 查询启用的商户用户选择项
    pub async fn find_select_items<C>(&self, conn: &C) -> Result<Vec<MerchantUsersModel>>
    where
        C: ConnectionTrait,
    {
        MerchantUsers::find()
            .filter(Column::Status.eq(MerchantUserStatus::Enabled))
            .order_by_asc(Column::Username)
            .all(conn)
            .await
            .context("查询商户用户选择项失败")
    }

    /// 查询商户用户基础信息列表
    pub async fn find_basic_list<C>(&self, conn: &C) -> Result<Vec<MerchantUsersModel>>
    where
        C: ConnectionTrait,
    {
        MerchantUsers::find()
            .order_by_asc(Column::Username)
            .all(conn)
            .await
            .context("查询商户用户基础信息失败")
    }

    /// 统计商户用户总数
    pub async fn count_total<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantUsers::find()
            .count(conn)
            .await
            .context("统计商户用户总数失败")
    }

    /// 统计启用商户用户数
    pub async fn count_active<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantUsers::find()
            .filter(Column::Status.eq(MerchantUserStatus::Enabled))
            .count(conn)
            .await
            .context("统计启用商户用户数失败")
    }

    /// 统计禁用商户用户数
    pub async fn count_disabled<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantUsers::find()
            .filter(Column::Status.eq(MerchantUserStatus::Disabled))
            .count(conn)
            .await
            .context("统计禁用商户用户数失败")
    }

    /// 统计锁定商户用户数
    pub async fn count_locked<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantUsers::find()
            .filter(Column::Status.eq(MerchantUserStatus::Locked))
            .count(conn)
            .await
            .context("统计锁定商户用户数失败")
    }

    /// 按性别统计商户用户数
    pub async fn count_by_gender<C>(&self, gender: i32, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantUsers::find()
            .filter(Column::Gender.eq(gender))
            .count(conn)
            .await
            .context("按性别统计商户用户数失败")
    }

    /// 检查用户名是否存在
    pub async fn exists_by_username<C>(
        &self,
        username: &str,
        exclude_user_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = MerchantUsers::find().filter(Column::Username.eq(username));

        if let Some(exclude_id) = exclude_user_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query.count(conn).await.context("检查用户名是否存在失败")?;

        Ok(count > 0)
    }

    /// 检查真实姓名是否存在
    pub async fn exists_by_real_name<C>(
        &self,
        real_name: &str,
        exclude_user_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = MerchantUsers::find().filter(Column::RealName.eq(real_name));

        if let Some(exclude_id) = exclude_user_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }
        let count = query
            .count(conn)
            .await
            .context("检查真实姓名是否存在失败")?;

        Ok(count > 0)
    }

    /// 检查手机号是否存在
    pub async fn exists_by_phone<C>(
        &self,
        phone: &str,
        exclude_user_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = MerchantUsers::find().filter(Column::Phone.eq(phone));

        if let Some(exclude_id) = exclude_user_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query.count(conn).await.context("检查手机号是否存在失败")?;

        Ok(count > 0)
    }

    /// 检查邮箱是否存在
    pub async fn exists_by_email<C>(
        &self,
        email: &str,
        exclude_user_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut condition = Condition::all().add(Column::Email.eq(email));

        if let Some(user_id) = exclude_user_id {
            condition = condition.add(Column::Id.ne(user_id));
        }

        let count = MerchantUsers::find()
            .filter(condition)
            .count(conn)
            .await
            .context("检查邮箱是否存在失败")?;

        Ok(count > 0)
    }

    /// 根据手机号查找商户用户信息
    pub async fn find_user_login_info<C>(
        &self,
        phone: &str,
        conn: &C,
    ) -> std::result::Result<Option<MerchantUsersModel>, DbErr>
    where
        C: ConnectionTrait,
    {
        // 1. 查找用户基本信息（不过滤状态，让业务层处理）
        let user = MerchantUsers::find()
            .filter(Column::Phone.eq(phone))
            .one(conn)
            .await?;

        let user = match user {
            Some(u) => u,
            None => return Ok(None),
        };

        Ok(Some(user))
    }

    /// 根据微信OpenID查找商户用户信息
    /// 
    /// 用于微信登录时根据OpenID查找对应的系统用户
    pub async fn find_by_wechat_openid<C>(
        &self,
        openid: &str,
        conn: &C,
    ) -> Result<Option<MerchantUsersModel>>
    where
        C: ConnectionTrait,
    {
        MerchantUsers::find()
            .filter(Column::WechatOpenid.eq(openid))
            .one(conn)
            .await
            .context("根据微信OpenID查询商户用户失败")
    }
}

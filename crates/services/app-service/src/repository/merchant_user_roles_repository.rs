use crate::domain::business::merchants::entities::{
    merchant_roles::Entity as MerchantRoles,
    merchant_user_merchants::{self, Entity as MerchantUserMerchants, MerchantUserMerchantStatus},
    merchant_user_roles::{
        ActiveModel, Column, Entity as MerchantUserRoles, Model as MerchantUserRolesModel,
    },
};
use anyhow::{Context, Result};
use lib_core::app::plugin::Service as ServiceTrait;
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, ConnectionTrait, EntityTrait, PaginatorTrait, QueryFilter, Set,
};
use uuid::Uuid;

/// 商户用户角色Repository实现
#[derive(Clone, Service)]
pub struct MerchantUserRolesRepository;

impl MerchantUserRolesRepository {
    /// 查询用户角色关联（带角色详情）- 通过用户商户关系表
    pub async fn find_with_roles_by_user_id<C>(
        &self,
        user_id: Uuid,
        conn: &C,
    ) -> Result<
        Vec<(
            MerchantUserRolesModel,
            Vec<crate::domain::business::merchants::entities::merchant_roles::Model>,
        )>,
    >
    where
        C: ConnectionTrait,
    {
        // 1. 先获取用户的所有有效商户关系ID
        let user_merchant_ids: Vec<Uuid> = MerchantUserMerchants::find()
            .filter(merchant_user_merchants::Column::UserId.eq(user_id))
            .filter(merchant_user_merchants::Column::Status.eq(MerchantUserMerchantStatus::Active))
            .all(conn)
            .await
            .context("查询用户商户关系失败")?
            .into_iter()
            .map(|m| m.id)
            .collect();

        if user_merchant_ids.is_empty() {
            return Ok(vec![]);
        }

        // 2. 通过用户商户关系ID查询角色
        MerchantUserRoles::find()
            .filter(Column::UserMerchantId.is_in(user_merchant_ids))
            .find_with_related(MerchantRoles)
            .all(conn)
            .await
            .context("查询用户角色关联失败")
    }

    /// 根据用户ID和商户ID删除角色分配
    pub async fn delete_by_user_and_merchant<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        // 1. 先获取用户在该商户的关系ID
        let user_merchant_ids: Vec<Uuid> = MerchantUserMerchants::find()
            .filter(merchant_user_merchants::Column::UserId.eq(user_id))
            .filter(merchant_user_merchants::Column::MerchantId.eq(merchant_id))
            .filter(merchant_user_merchants::Column::Status.eq(MerchantUserMerchantStatus::Active))
            .all(conn)
            .await
            .context("查询用户商户关系失败")?
            .into_iter()
            .map(|m| m.id)
            .collect();

        if user_merchant_ids.is_empty() {
            return Ok(0);
        }

        // 2. 删除角色分配
        let result = MerchantUserRoles::delete_many()
            .filter(Column::UserMerchantId.is_in(user_merchant_ids))
            .exec(conn)
            .await
            .context("删除用户角色分配失败")?;

        Ok(result.rows_affected)
    }

    /// 批量插入角色分配
    pub async fn insert_many<C>(&self, models: Vec<ActiveModel>, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        if models.is_empty() {
            return Ok(());
        }

        MerchantUserRoles::insert_many(models)
            .exec(conn)
            .await
            .context("批量插入角色分配失败")?;

        Ok(())
    }

    /// 根据用户ID查询角色分配
    pub async fn find_by_user_id<C>(
        &self,
        user_id: Uuid,
        conn: &C,
    ) -> Result<Vec<MerchantUserRolesModel>>
    where
        C: ConnectionTrait,
    {
        // 1. 先获取用户的所有有效商户关系ID
        let user_merchant_ids: Vec<Uuid> = MerchantUserMerchants::find()
            .filter(merchant_user_merchants::Column::UserId.eq(user_id))
            .filter(merchant_user_merchants::Column::Status.eq(MerchantUserMerchantStatus::Active))
            .all(conn)
            .await
            .context("查询用户商户关系失败")?
            .into_iter()
            .map(|m| m.id)
            .collect();

        if user_merchant_ids.is_empty() {
            return Ok(vec![]);
        }

        // 2. 查询角色分配
        MerchantUserRoles::find()
            .filter(Column::UserMerchantId.is_in(user_merchant_ids))
            .all(conn)
            .await
            .context("查询用户角色分配失败")
    }

    /// 根据角色ID查询用户分配
    pub async fn find_by_role_id<C>(
        &self,
        role_id: Uuid,
        conn: &C,
    ) -> Result<Vec<MerchantUserRolesModel>>
    where
        C: ConnectionTrait,
    {
        MerchantUserRoles::find()
            .filter(Column::RoleId.eq(role_id))
            .all(conn)
            .await
            .context("查询角色用户分配失败")
    }

    /// 统计用户角色分配数量
    pub async fn count_by_user_id<C>(&self, user_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        // 1. 先获取用户的所有有效商户关系ID
        let user_merchant_ids: Vec<Uuid> = MerchantUserMerchants::find()
            .filter(merchant_user_merchants::Column::UserId.eq(user_id))
            .filter(merchant_user_merchants::Column::Status.eq(MerchantUserMerchantStatus::Active))
            .all(conn)
            .await
            .context("查询用户商户关系失败")?
            .into_iter()
            .map(|m| m.id)
            .collect();

        if user_merchant_ids.is_empty() {
            return Ok(0);
        }

        // 2. 统计角色分配数量
        MerchantUserRoles::find()
            .filter(Column::UserMerchantId.is_in(user_merchant_ids))
            .count(conn)
            .await
            .context("统计用户角色分配数量失败")
    }

    /// 统计角色用户分配数量
    pub async fn count_by_role_id<C>(&self, role_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantUserRoles::find()
            .filter(Column::RoleId.eq(role_id))
            .count(conn)
            .await
            .context("统计角色用户分配数量失败")
    }

    /// 检查用户是否属于某个商户
    pub async fn user_belongs_to_merchant<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        // 直接从merchant_user_merchants表检查
        let count = MerchantUserMerchants::find()
            .filter(merchant_user_merchants::Column::UserId.eq(user_id))
            .filter(merchant_user_merchants::Column::MerchantId.eq(merchant_id))
            .filter(merchant_user_merchants::Column::Status.eq(MerchantUserMerchantStatus::Active))
            .count(conn)
            .await
            .context("检查用户是否属于商户失败")?;

        Ok(count > 0)
    }

    /// 根据商户ID和用户ID查询用户角色关联
    pub async fn find_by_merchant_and_user<C>(
        &self,
        merchant_id: i64,
        user_id: Uuid,
        conn: &C,
    ) -> Result<Vec<MerchantUserRolesModel>>
    where
        C: ConnectionTrait,
    {
        // 1. 先获取用户在该商户的关系ID
        let user_merchant_ids: Vec<Uuid> = MerchantUserMerchants::find()
            .filter(merchant_user_merchants::Column::UserId.eq(user_id))
            .filter(merchant_user_merchants::Column::MerchantId.eq(merchant_id))
            .filter(merchant_user_merchants::Column::Status.eq(MerchantUserMerchantStatus::Active))
            .all(conn)
            .await
            .context("查询用户商户关系失败")?
            .into_iter()
            .map(|m| m.id)
            .collect();

        if user_merchant_ids.is_empty() {
            return Ok(vec![]);
        }

        // 2. 查询角色关联
        MerchantUserRoles::find()
            .filter(Column::UserMerchantId.is_in(user_merchant_ids))
            .all(conn)
            .await
            .context("查询商户用户角色关联失败")
    }

    /// 为用户在指定商户中分配角色
    pub async fn assign_role_to_user_merchant<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        role_id: Uuid,
        created_by: Option<Uuid>,
        conn: &C,
    ) -> Result<MerchantUserRolesModel>
    where
        C: ConnectionTrait,
    {
        // 1. 获取用户在该商户的关系ID
        let user_merchant = MerchantUserMerchants::find()
            .filter(merchant_user_merchants::Column::UserId.eq(user_id))
            .filter(merchant_user_merchants::Column::MerchantId.eq(merchant_id))
            .filter(merchant_user_merchants::Column::Status.eq(MerchantUserMerchantStatus::Active))
            .one(conn)
            .await
            .context("查询用户商户关系失败")?;

        let user_merchant_id = match user_merchant {
            Some(um) => um.id,
            None => return Err(anyhow::anyhow!("用户不属于该商户或关系不是活跃状态")),
        };

        // 2. 创建角色分配
        let active_model = ActiveModel {
            user_merchant_id: Set(user_merchant_id),
            role_id: Set(role_id),
            created_by: Set(created_by),
            ..Default::default()
        };

        active_model.insert(conn).await.context("分配角色失败")
    }

    /// 撤销用户在指定商户中的特定角色
    pub async fn revoke_role_from_user_merchant<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        role_id: Uuid,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        // 1. 获取用户在该商户的关系ID
        let user_merchant_ids: Vec<Uuid> = MerchantUserMerchants::find()
            .filter(merchant_user_merchants::Column::UserId.eq(user_id))
            .filter(merchant_user_merchants::Column::MerchantId.eq(merchant_id))
            .filter(merchant_user_merchants::Column::Status.eq(MerchantUserMerchantStatus::Active))
            .all(conn)
            .await
            .context("查询用户商户关系失败")?
            .into_iter()
            .map(|m| m.id)
            .collect();

        if user_merchant_ids.is_empty() {
            return Ok(0);
        }

        // 2. 撤销角色
        let result = MerchantUserRoles::delete_many()
            .filter(Column::UserMerchantId.is_in(user_merchant_ids))
            .filter(Column::RoleId.eq(role_id))
            .exec(conn)
            .await
            .context("撤销角色失败")?;

        Ok(result.rows_affected)
    }
}

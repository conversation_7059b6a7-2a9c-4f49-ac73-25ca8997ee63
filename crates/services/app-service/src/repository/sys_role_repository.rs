use crate::domain::system::dto::sys_role_request::SysRolePageRequest;
use crate::domain::system::entities::roles::{
    ActiveModel, Column, Entity as Roles, Model as RoleModel,
};

use crate::domain::system::entities::permissions::Entity as Permissions;
use crate::domain::system::entities::role_permissions::Entity as RolePermissions;
use crate::domain::system::entities::users::Entity as Users;
use anyhow::{Context, Result};
use chrono::{Local, NaiveDate};
use lib_core::PageDataBuilder;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, EntityTrait, JoinType,
    PaginatorTrait, <PERSON>ry<PERSON><PERSON><PERSON>, Query<PERSON>rder, QuerySelect, RelationTrait,
};
use std::collections::HashMap;
use uuid::Uuid;

/// 系统角色Repository实现
#[derive(Clone, Service)]
pub struct SysRoleRepository;

impl SysRoleRepository {
    /// 构建查询条件
    fn build_query_conditions(req: &SysRolePageRequest) -> Condition {
        let mut conditions = Condition::all();

        // 角色名称模糊匹配
        Self::add_name_condition(&mut conditions, &req.name);

        // 角色描述模糊匹配
        Self::add_description_condition(&mut conditions, &req.description);

        // 创建时间范围
        Self::add_date_range_conditions(&mut conditions, &req.created_start, &req.created_end);

        conditions
    }

    /// 添加名称条件
    fn add_name_condition(conditions: &mut Condition, name: &Option<String>) {
        if let Some(name) = name {
            let trimmed_name = name.trim();
            if !trimmed_name.is_empty() {
                *conditions = conditions.clone().add(Column::Name.contains(trimmed_name));
            }
        }
    }

    /// 添加描述条件
    fn add_description_condition(conditions: &mut Condition, description: &Option<String>) {
        if let Some(description) = description {
            let trimmed_description = description.trim();
            if !trimmed_description.is_empty() {
                *conditions = conditions
                    .clone()
                    .add(Column::Description.contains(trimmed_description));
            }
        }
    }

    /// 添加日期范围条件
    fn add_date_range_conditions(
        conditions: &mut Condition,
        created_start: &Option<String>,
        created_end: &Option<String>,
    ) {
        // 处理开始日期
        if let Some(start_condition) = Self::parse_start_date_condition(created_start) {
            *conditions = conditions.clone().add(start_condition);
        }

        // 处理结束日期
        if let Some(end_condition) = Self::parse_end_date_condition(created_end) {
            *conditions = conditions.clone().add(end_condition);
        }
    }

    /// 解析开始日期条件
    fn parse_start_date_condition(date_str: &Option<String>) -> Option<Condition> {
        date_str
            .as_ref()?
            .trim()
            .parse::<NaiveDate>()
            .ok()
            .and_then(|date| date.and_hms_opt(0, 0, 0))
            .and_then(|datetime| datetime.and_local_timezone(Local).single())
            .map(|local_datetime| Condition::all().add(Column::CreatedDate.gte(local_datetime)))
    }

    /// 解析结束日期条件
    fn parse_end_date_condition(date_str: &Option<String>) -> Option<Condition> {
        date_str
            .as_ref()?
            .trim()
            .parse::<NaiveDate>()
            .ok()
            .and_then(|date| date.and_hms_opt(23, 59, 59))
            .and_then(|datetime| datetime.and_local_timezone(Local).single())
            .map(|local_datetime| Condition::all().add(Column::CreatedDate.lte(local_datetime)))
    }

    /// 分页查询角色
    pub async fn page_by_condition<C>(
        &self,
        req: &SysRolePageRequest,
        conn: &C,
    ) -> Result<PageDataBuilder<RoleModel>>
    where
        C: ConnectionTrait,
    {
        let conditions = Self::build_query_conditions(req);
        let pagination = Pagination::from_one_based(req.page, req.page_size);

        Roles::find()
            .filter(conditions)
            .order_by_desc(Column::CreatedDate)
            .page_builder(conn, &pagination)
            .await
            .context("分页查询角色失败")
    }

    /// 根据ID查询角色
    pub async fn find_by_id<C>(&self, role_id: Uuid, conn: &C) -> Result<Option<RoleModel>>
    where
        C: ConnectionTrait,
    {
        Roles::find_by_id(role_id)
            .one(conn)
            .await
            .context("查询角色失败")
    }

    /// 根据名称查询角色
    pub async fn find_by_name<C>(&self, name: &str, conn: &C) -> Result<Option<RoleModel>>
    where
        C: ConnectionTrait,
    {
        Roles::find()
            .filter(Column::Name.eq(name))
            .one(conn)
            .await
            .context("根据名称查询角色失败")
    }

    /// 查询所有角色
    pub async fn find_all<C>(&self, conn: &C) -> Result<Vec<RoleModel>>
    where
        C: ConnectionTrait,
    {
        Roles::find()
            .order_by_asc(Column::CreatedDate)
            .all(conn)
            .await
            .context("查询所有角色失败")
    }

    /// 根据IDs查询角色列表
    pub async fn find_by_ids<C>(&self, role_ids: Vec<Uuid>, conn: &C) -> Result<Vec<RoleModel>>
    where
        C: ConnectionTrait,
    {
        if role_ids.is_empty() {
            return Ok(Vec::new());
        }

        Roles::find()
            .filter(Column::Id.is_in(role_ids))
            .all(conn)
            .await
            .context("批量查询角色失败")
    }

    /// 查询角色详情包含用户和权限
    pub async fn find_role_with_users_and_permissions<C>(
        &self,
        role_id: Uuid,
        conn: &C,
    ) -> Result<
        Option<(
            RoleModel,
            Vec<crate::domain::system::entities::users::Model>,
            Vec<crate::domain::system::entities::permissions::Model>,
        )>,
    >
    where
        C: ConnectionTrait,
    {
        // 查询角色
        let role = self.find_by_id(role_id, conn).await?;

        if let Some(role) = role {
            // 查询用户
            let users = Users::find()
                .join(
                    JoinType::InnerJoin,
                    crate::domain::system::entities::users::Relation::UserRoles.def(),
                )
                .filter(crate::domain::system::entities::user_roles::Column::RoleId.eq(role_id))
                .all(conn)
                .await
                .context("查询角色用户失败")?;

            // 查询权限
            let permissions = Permissions::find()
                .join(
                    JoinType::InnerJoin,
                    crate::domain::system::entities::permissions::Relation::RolePermissions.def(),
                )
                .filter(
                    crate::domain::system::entities::role_permissions::Column::RoleId.eq(role_id),
                )
                .all(conn)
                .await
                .context("查询角色权限失败")?;

            Ok(Some((role, users, permissions)))
        } else {
            Ok(None)
        }
    }

    /// 检查角色名称是否存在
    pub async fn name_exists<C>(
        &self,
        name: &str,
        exclude_role_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = Roles::find().filter(Column::Name.eq(name));

        if let Some(exclude_id) = exclude_role_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query
            .count(conn)
            .await
            .context("检查角色名称是否存在失败")?;
        Ok(count > 0)
    }

    /// 批量统计角色权限数量
    pub async fn batch_count_permissions_by_role_ids<C>(
        &self,
        role_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<HashMap<Uuid, u64>>
    where
        C: ConnectionTrait,
    {
        if role_ids.is_empty() {
            return Ok(HashMap::new());
        }

        use sea_orm::{FromQueryResult, QuerySelect};

        #[derive(FromQueryResult)]
        struct RolePermissionCount {
            role_id: Uuid,
            permission_count: i64,
        }

        let results = RolePermissions::find()
            .select_only()
            .column_as(
                crate::domain::system::entities::role_permissions::Column::RoleId,
                "role_id",
            )
            .column_as(
                crate::domain::system::entities::role_permissions::Column::RoleId.count(),
                "permission_count",
            )
            .filter(
                crate::domain::system::entities::role_permissions::Column::RoleId
                    .is_in(role_ids.iter().copied()),
            )
            .group_by(crate::domain::system::entities::role_permissions::Column::RoleId)
            .into_model::<RolePermissionCount>()
            .all(conn)
            .await
            .context("批量统计角色权限数量失败")?;

        let mut counts = HashMap::new();

        // 先初始化所有角色为0
        for role_id in role_ids {
            counts.insert(role_id, 0);
        }

        // 然后设置实际统计值
        for result in results {
            counts.insert(result.role_id, result.permission_count as u64);
        }

        Ok(counts)
    }

    /// 创建角色
    pub async fn create<C>(&self, model: ActiveModel, conn: &C) -> Result<RoleModel>
    where
        C: ConnectionTrait,
    {
        model.insert(conn).await.context("创建角色失败")
    }

    /// 更新角色
    pub async fn update<C>(&self, model: ActiveModel, conn: &C) -> Result<RoleModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context("更新角色失败")
    }

    /// 根据ID删除角色
    pub async fn delete_by_id<C>(&self, role_id: Uuid, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        Roles::delete_by_id(role_id)
            .exec(conn)
            .await
            .context("删除角色失败")?;

        Ok(())
    }

    /// 批量删除角色
    pub async fn delete_by_ids<C>(&self, role_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if role_ids.is_empty() {
            return Ok(0);
        }

        let result = Roles::delete_many()
            .filter(Column::Id.is_in(role_ids))
            .exec(conn)
            .await
            .context("批量删除角色失败")?;

        Ok(result.rows_affected)
    }

    /// 统计角色总数
    pub async fn count_all<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        Roles::find().count(conn).await.context("统计角色总数失败")
    }
}

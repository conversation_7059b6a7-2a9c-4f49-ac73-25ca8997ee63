use crate::domain::business::merchant_user_vo::FollowerUserInfo;
use crate::domain::business::merchants::entities::merchant_follower_commission::{
    ActiveModel, Column, CommissionType, Entity as MerchantFollowerCommission,
    Model as MerchantFollowerCommissionModel,
};

use anyhow::{Context, Result};
use lib_core::PageDataBuilder;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use rust_decimal::Decimal;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, ConnectionTrait, EntityTrait, FromQueryResult, JoinType,
    PaginatorTrait, QueryFilter, QueryOrder, QuerySelect, RelationTrait,
};
use uuid::Uuid;

/// 商户跟进人佣金Repository实现
///
/// 提供商户跟进人佣金的数据访问操作，包括：
/// - 基础CRUD操作
/// - 按商户查询佣金配置
/// - 按跟进人查询佣金配置
/// - 佣金类型统计分析
/// - 业务逻辑验证
#[derive(Clone, Service)]
pub struct MerchantFollowerCommissionRepository;

impl MerchantFollowerCommissionRepository {
    // ==================== 基础CRUD操作 ====================

    /// 根据ID查询佣金配置
    pub async fn find_by_id<C>(
        &self,
        commission_id: Uuid,
        conn: &C,
    ) -> Result<Option<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantFollowerCommission::find_by_id(commission_id)
            .one(conn)
            .await
            .context("查询佣金配置失败")
    }

    /// 创建佣金配置
    pub async fn create<C>(
        &self,
        model: ActiveModel,
        conn: &C,
    ) -> Result<MerchantFollowerCommissionModel>
    where
        C: ConnectionTrait,
    {
        model.insert(conn).await.context("创建佣金配置失败")
    }

    /// 更新佣金配置
    pub async fn update<C>(
        &self,
        model: ActiveModel,
        conn: &C,
    ) -> Result<MerchantFollowerCommissionModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context("更新佣金配置失败")
    }

    /// 根据ID删除佣金配置
    pub async fn delete_by_id<C>(&self, commission_id: Uuid, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        MerchantFollowerCommission::delete_by_id(commission_id)
            .exec(conn)
            .await
            .context("删除佣金配置失败")?;

        Ok(())
    }

    /// 批量删除佣金配置
    pub async fn delete_by_ids<C>(&self, commission_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if commission_ids.is_empty() {
            return Ok(0);
        }

        let result = MerchantFollowerCommission::delete_many()
            .filter(Column::Id.is_in(commission_ids))
            .exec(conn)
            .await
            .context("批量删除佣金配置失败")?;

        Ok(result.rows_affected)
    }

    // ==================== 商户相关查询 ====================

    /// 查询商户的所有佣金配置
    pub async fn find_by_merchant_id<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Vec<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantFollowerCommission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .order_by_desc(Column::CreatedDate)
            .all(conn)
            .await
            .context("查询商户佣金配置失败")
    }

    /// 查询商户当前的跟进人
    pub async fn find_current_follower_by_merchant_id<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Option<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantFollowerCommission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .order_by_desc(Column::UpdatedDate)
            .one(conn)
            .await
            .context("查询商户当前跟进人记录失败")
    }

    /// 查询商户跟进人的详细信息（包含用户信息）
    pub async fn find_follower_user_info_by_merchant_id<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Option<FollowerUserInfo>>
    where
        C: ConnectionTrait,
    {
        use crate::domain::business::merchants::entities::merchant_users::Column as MerchantUserColumn;

        let result = MerchantFollowerCommission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .join(JoinType::InnerJoin,
                  crate::domain::business::merchants::entities::merchant_follower_commission::Relation::User.def())
            .select_only()
            // 用户字段
            .column_as(MerchantUserColumn::Id, "id")
            .column_as(MerchantUserColumn::Username, "username")
            .column_as(MerchantUserColumn::RealName, "real_name")
            .column_as(MerchantUserColumn::Phone, "phone")
            .column_as(MerchantUserColumn::Avatar, "avatar")
            .column_as(MerchantUserColumn::Gender, "gender")
            .column_as(MerchantUserColumn::Status, "status")
            .column_as(MerchantUserColumn::LastLoginDate, "last_login_date")
            .column_as(MerchantUserColumn::LastLoginIp, "last_login_ip")
            .column_as(MerchantUserColumn::Remark, "user_remark")
            // 佣金字段
            .column_as(Column::CommissionType, "commission_type")
            .column_as(Column::CommissionValue, "commission_value")
            .column_as(Column::Remark, "commission_remark")
            .order_by_desc(Column::UpdatedDate)
            .into_model::<FollowerUserInfo>()
            .one(conn)
            .await
            .context("查询商户跟进人详细信息失败")?;

        Ok(result)
    }

    /// 分页查询商户的佣金配置
    pub async fn page_by_merchant_id<C>(
        &self,
        merchant_id: i64,
        page: u64,
        page_size: u64,
        conn: &C,
    ) -> Result<PageDataBuilder<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        let pagination = Pagination::from_one_based(Some(page), Some(page_size));

        MerchantFollowerCommission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .order_by_desc(Column::CreatedDate)
            .page_builder(conn, &pagination)
            .await
            .context("分页查询商户佣金配置失败")
    }

    /// 查询商户特定跟进人的佣金配置
    pub async fn find_by_merchant_and_user<C>(
        &self,
        merchant_id: i64,
        user_id: Uuid,
        conn: &C,
    ) -> Result<Option<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantFollowerCommission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::UserId.eq(user_id))
            .one(conn)
            .await
            .context("查询商户跟进人佣金配置失败")
    }

    /// 检查商户和跟进人的佣金配置是否存在
    pub async fn exists_by_merchant_and_user<C>(
        &self,
        merchant_id: i64,
        user_id: Uuid,
        exclude_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = MerchantFollowerCommission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::UserId.eq(user_id));

        if let Some(exclude_id) = exclude_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query
            .count(conn)
            .await
            .context("检查佣金配置是否存在失败")?;

        Ok(count > 0)
    }

    /// 删除商户的所有佣金配置
    pub async fn delete_by_merchant_id<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = MerchantFollowerCommission::delete_many()
            .filter(Column::MerchantId.eq(merchant_id))
            .exec(conn)
            .await
            .context("删除商户佣金配置失败")?;

        Ok(result.rows_affected)
    }

    // ==================== 跟进人相关查询 ====================

    /// 查询跟进人的所有佣金配置
    pub async fn find_by_user_id<C>(
        &self,
        user_id: Uuid,
        conn: &C,
    ) -> Result<Vec<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantFollowerCommission::find()
            .filter(Column::UserId.eq(user_id))
            .order_by_desc(Column::CreatedDate)
            .all(conn)
            .await
            .context("查询跟进人佣金配置失败")
    }

    /// 分页查询跟进人的佣金配置
    pub async fn page_by_user_id<C>(
        &self,
        user_id: Uuid,
        page: u64,
        page_size: u64,
        conn: &C,
    ) -> Result<PageDataBuilder<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        let pagination = Pagination::from_one_based(Some(page), Some(page_size));

        MerchantFollowerCommission::find()
            .filter(Column::UserId.eq(user_id))
            .order_by_desc(Column::CreatedDate)
            .page_builder(conn, &pagination)
            .await
            .context("分页查询跟进人佣金配置失败")
    }

    /// 统计跟进人的佣金配置数量
    pub async fn count_by_user_id<C>(&self, user_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantFollowerCommission::find()
            .filter(Column::UserId.eq(user_id))
            .count(conn)
            .await
            .context("统计跟进人佣金配置数量失败")
    }

    /// 删除跟进人的所有佣金配置
    pub async fn delete_by_user_id<C>(&self, user_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = MerchantFollowerCommission::delete_many()
            .filter(Column::UserId.eq(user_id))
            .exec(conn)
            .await
            .context("删除跟进人佣金配置失败")?;

        Ok(result.rows_affected)
    }

    /// 删除指定商户和跟进人的佣金配置
    pub async fn delete_by_merchant_and_user<C>(
        &self,
        merchant_id: i64,
        user_id: Uuid,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = MerchantFollowerCommission::delete_many()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::UserId.eq(user_id))
            .exec(conn)
            .await
            .context("删除商户跟进人佣金配置失败")?;

        Ok(result.rows_affected)
    }

    // ==================== 佣金类型相关查询 ====================

    /// 查询指定佣金类型的配置
    pub async fn find_by_commission_type<C>(
        &self,
        commission_type: CommissionType,
        conn: &C,
    ) -> Result<Vec<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantFollowerCommission::find()
            .filter(Column::CommissionType.eq(commission_type))
            .order_by_desc(Column::CreatedDate)
            .all(conn)
            .await
            .context("查询指定类型佣金配置失败")
    }

    /// 查询商户指定佣金类型的配置
    pub async fn find_by_merchant_and_type<C>(
        &self,
        merchant_id: i64,
        commission_type: CommissionType,
        conn: &C,
    ) -> Result<Vec<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantFollowerCommission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::CommissionType.eq(commission_type))
            .order_by_desc(Column::CreatedDate)
            .all(conn)
            .await
            .context("查询商户指定类型佣金配置失败")
    }

    /// 统计各佣金类型的使用数量
    pub async fn count_by_commission_types<C>(&self, conn: &C) -> Result<Vec<(CommissionType, u64)>>
    where
        C: ConnectionTrait,
    {
        use sea_orm::FromQueryResult;

        #[derive(FromQueryResult)]
        struct CommissionTypeCount {
            commission_type: CommissionType,
            count: i64,
        }

        let results = MerchantFollowerCommission::find()
            .select_only()
            .column(Column::CommissionType)
            .column_as(Column::Id.count(), "count")
            .group_by(Column::CommissionType)
            .into_model::<CommissionTypeCount>()
            .all(conn)
            .await
            .context("统计佣金类型使用数量失败")?;

        Ok(results
            .into_iter()
            .map(|result| (result.commission_type, result.count as u64))
            .collect())
    }

    // ==================== 佣金值范围查询 ====================

    /// 查询佣金值在指定范围内的配置
    pub async fn find_by_commission_value_range<C>(
        &self,
        min_value: Option<Decimal>,
        max_value: Option<Decimal>,
        conn: &C,
    ) -> Result<Vec<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        let mut query = MerchantFollowerCommission::find();

        if let Some(min) = min_value {
            query = query.filter(Column::CommissionValue.gte(min));
        }

        if let Some(max) = max_value {
            query = query.filter(Column::CommissionValue.lte(max));
        }

        query
            .order_by_desc(Column::CommissionValue)
            .all(conn)
            .await
            .context("按佣金值范围查询失败")
    }

    /// 查询商户佣金值在指定范围内的配置
    pub async fn find_by_merchant_and_value_range<C>(
        &self,
        merchant_id: i64,
        min_value: Option<Decimal>,
        max_value: Option<Decimal>,
        conn: &C,
    ) -> Result<Vec<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        let mut query =
            MerchantFollowerCommission::find().filter(Column::MerchantId.eq(merchant_id));

        if let Some(min) = min_value {
            query = query.filter(Column::CommissionValue.gte(min));
        }

        if let Some(max) = max_value {
            query = query.filter(Column::CommissionValue.lte(max));
        }

        query
            .order_by_desc(Column::CommissionValue)
            .all(conn)
            .await
            .context("按商户和佣金值范围查询失败")
    }

    // ==================== 统计分析方法 ====================

    /// 统计商户的佣金配置数量
    pub async fn count_by_merchant_id<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantFollowerCommission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .count(conn)
            .await
            .context("统计商户佣金配置数量失败")
    }

    /// 获取佣金值的最大值和最小值
    pub async fn get_commission_value_range<C>(
        &self,
        merchant_id: Option<i64>,
        commission_type: Option<CommissionType>,
        conn: &C,
    ) -> Result<(Option<Decimal>, Option<Decimal>)>
    where
        C: ConnectionTrait,
    {
        use sea_orm::FromQueryResult;

        #[derive(FromQueryResult)]
        struct RangeResult {
            min_commission: Option<Decimal>,
            max_commission: Option<Decimal>,
        }

        let mut query = MerchantFollowerCommission::find()
            .select_only()
            .column_as(Column::CommissionValue.min(), "min_commission")
            .column_as(Column::CommissionValue.max(), "max_commission");

        if let Some(merchant_id) = merchant_id {
            query = query.filter(Column::MerchantId.eq(merchant_id));
        }

        if let Some(comm_type) = commission_type {
            query = query.filter(Column::CommissionType.eq(comm_type));
        }

        let result = query
            .into_model::<RangeResult>()
            .one(conn)
            .await
            .context("获取佣金值范围失败")?;

        match result {
            Some(r) => Ok((r.min_commission, r.max_commission)),
            None => Ok((None, None)),
        }
    }

    /// 批量查询佣金配置
    pub async fn find_by_ids<C>(
        &self,
        commission_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        if commission_ids.is_empty() {
            return Ok(Vec::new());
        }

        MerchantFollowerCommission::find()
            .filter(Column::Id.is_in(commission_ids))
            .order_by_desc(Column::CreatedDate)
            .all(conn)
            .await
            .context("批量查询佣金配置失败")
    }

    /// 查询所有佣金配置
    pub async fn find_all<C>(&self, conn: &C) -> Result<Vec<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantFollowerCommission::find()
            .order_by_desc(Column::CreatedDate)
            .all(conn)
            .await
            .context("查询所有佣金配置失败")
    }

    /// 分页查询所有佣金配置
    pub async fn page_all<C>(
        &self,
        page: u64,
        page_size: u64,
        conn: &C,
    ) -> Result<PageDataBuilder<MerchantFollowerCommissionModel>>
    where
        C: ConnectionTrait,
    {
        let pagination = Pagination::from_one_based(Some(page), Some(page_size));

        MerchantFollowerCommission::find()
            .order_by_desc(Column::CreatedDate)
            .page_builder(conn, &pagination)
            .await
            .context("分页查询所有佣金配置失败")
    }
}

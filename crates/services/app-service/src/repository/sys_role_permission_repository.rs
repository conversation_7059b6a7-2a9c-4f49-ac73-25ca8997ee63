use crate::domain::system::entities::role_permissions::{
    ActiveModel, Column, Entity as RolePermissions, Model as RolePermissionModel,
};
use anyhow::{Context, Result};
use lib_core::app::plugin::Service as ServiceTrait;
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, ConnectionTrait, EntityTrait, PaginatorTrait, QueryFilter,
};
use uuid::Uuid;

/// 系统角色权限Repository实现
#[derive(Clone, Service)]
pub struct SysRolePermissionRepository;

impl SysRolePermissionRepository {
    /// 根据角色ID查询权限关联
    pub async fn find_by_role_id<C>(
        &self,
        role_id: Uuid,
        conn: &C,
    ) -> Result<Vec<RolePermissionModel>>
    where
        C: ConnectionTrait,
    {
        RolePermissions::find()
            .filter(Column::RoleId.eq(role_id))
            .all(conn)
            .await
            .context("查询角色权限关联失败")
    }

    /// 根据权限ID查询角色关联
    pub async fn find_by_permission_id<C>(
        &self,
        permission_id: Uuid,
        conn: &C,
    ) -> Result<Vec<RolePermissionModel>>
    where
        C: ConnectionTrait,
    {
        RolePermissions::find()
            .filter(Column::PermissionId.eq(permission_id))
            .all(conn)
            .await
            .context("查询权限角色关联失败")
    }

    /// 根据权限IDs查询角色关联
    pub async fn find_by_permission_ids<C>(
        &self,
        permission_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<RolePermissionModel>>
    where
        C: ConnectionTrait,
    {
        if permission_ids.is_empty() {
            return Ok(Vec::new());
        }

        RolePermissions::find()
            .filter(Column::PermissionId.is_in(permission_ids))
            .all(conn)
            .await
            .context("批量查询权限角色关联失败")
    }

    /// 根据角色IDs查询权限关联
    pub async fn find_by_role_ids<C>(
        &self,
        role_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<RolePermissionModel>>
    where
        C: ConnectionTrait,
    {
        if role_ids.is_empty() {
            return Ok(Vec::new());
        }

        RolePermissions::find()
            .filter(Column::RoleId.is_in(role_ids))
            .all(conn)
            .await
            .context("批量查询角色权限关联失败")
    }

    /// 统计指定权限的角色关联数量
    pub async fn count_by_permission_id<C>(&self, permission_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        RolePermissions::find()
            .filter(Column::PermissionId.eq(permission_id))
            .count(conn)
            .await
            .context("统计权限角色关联数量失败")
    }

    /// 统计指定权限IDs的角色关联数量
    pub async fn count_by_permission_ids<C>(
        &self,
        permission_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if permission_ids.is_empty() {
            return Ok(0);
        }

        RolePermissions::find()
            .filter(Column::PermissionId.is_in(permission_ids))
            .count(conn)
            .await
            .context("统计权限角色关联数量失败")
    }

    /// 统计指定角色的权限数量
    pub async fn count_by_role_id<C>(&self, role_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        RolePermissions::find()
            .filter(Column::RoleId.eq(role_id))
            .count(conn)
            .await
            .context("统计角色权限数量失败")
    }

    /// 检查角色权限关联是否存在
    pub async fn exists_by_role_and_permission<C>(
        &self,
        role_id: Uuid,
        permission_id: Uuid,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let count = RolePermissions::find()
            .filter(Column::RoleId.eq(role_id))
            .filter(Column::PermissionId.eq(permission_id))
            .count(conn)
            .await
            .context("检查角色权限关联失败")?;

        Ok(count > 0)
    }

    /// 创建角色权限关联
    pub async fn create<C>(&self, model: ActiveModel, conn: &C) -> Result<RolePermissionModel>
    where
        C: ConnectionTrait,
    {
        model.insert(conn).await.context("创建角色权限关联失败")
    }

    /// 批量插入角色权限关联
    pub async fn insert_many<C>(&self, models: Vec<ActiveModel>, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        if models.is_empty() {
            return Ok(());
        }

        RolePermissions::insert_many(models)
            .exec(conn)
            .await
            .context("批量插入角色权限关联失败")?;

        Ok(())
    }

    /// 根据角色ID删除权限关联
    pub async fn delete_by_role_id<C>(&self, role_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = RolePermissions::delete_many()
            .filter(Column::RoleId.eq(role_id))
            .exec(conn)
            .await
            .context("删除角色权限关联失败")?;

        Ok(result.rows_affected)
    }

    /// 根据权限ID删除角色关联
    pub async fn delete_by_permission_id<C>(&self, permission_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = RolePermissions::delete_many()
            .filter(Column::PermissionId.eq(permission_id))
            .exec(conn)
            .await
            .context("删除权限角色关联失败")?;

        Ok(result.rows_affected)
    }

    /// 根据权限IDs批量删除角色关联
    pub async fn delete_by_permission_ids<C>(
        &self,
        permission_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if permission_ids.is_empty() {
            return Ok(0);
        }

        let result = RolePermissions::delete_many()
            .filter(Column::PermissionId.is_in(permission_ids))
            .exec(conn)
            .await
            .context("批量删除权限角色关联失败")?;

        Ok(result.rows_affected)
    }

    /// 根据角色IDs批量删除权限关联
    pub async fn delete_by_role_ids<C>(&self, role_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if role_ids.is_empty() {
            return Ok(0);
        }

        let result = RolePermissions::delete_many()
            .filter(Column::RoleId.is_in(role_ids))
            .exec(conn)
            .await
            .context("批量删除角色权限关联失败")?;

        Ok(result.rows_affected)
    }

    /// 删除指定角色的指定权限关联
    pub async fn delete_by_role_and_permission<C>(
        &self,
        role_id: Uuid,
        permission_id: Uuid,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = RolePermissions::delete_many()
            .filter(Column::RoleId.eq(role_id))
            .filter(Column::PermissionId.eq(permission_id))
            .exec(conn)
            .await
            .context("删除角色权限关联失败")?;

        Ok(result.rows_affected)
    }

    /// 删除指定角色的多个权限关联
    pub async fn delete_by_role_and_permissions<C>(
        &self,
        role_id: Uuid,
        permission_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if permission_ids.is_empty() {
            return Ok(0);
        }

        let result = RolePermissions::delete_many()
            .filter(Column::RoleId.eq(role_id))
            .filter(Column::PermissionId.is_in(permission_ids))
            .exec(conn)
            .await
            .context("删除角色权限关联失败")?;

        Ok(result.rows_affected)
    }

    /// 批量统计角色的权限数量
    pub async fn batch_count_permissions_by_role_ids<C>(
        &self,
        role_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<std::collections::HashMap<Uuid, u64>>
    where
        C: ConnectionTrait,
    {
        if role_ids.is_empty() {
            return Ok(std::collections::HashMap::new());
        }

        use sea_orm::{FromQueryResult, QuerySelect};

        #[derive(FromQueryResult)]
        struct RolePermissionCount {
            role_id: Uuid,
            permission_count: i64,
        }

        let results = RolePermissions::find()
            .select_only()
            .column_as(Column::RoleId, "role_id")
            .column_as(Column::RoleId.count(), "permission_count")
            .filter(Column::RoleId.is_in(role_ids.iter().copied()))
            .group_by(Column::RoleId)
            .into_model::<RolePermissionCount>()
            .all(conn)
            .await
            .context("批量统计角色权限数量失败")?;

        let mut counts = std::collections::HashMap::new();

        // 先初始化所有角色为0
        for role_id in role_ids {
            counts.insert(role_id, 0);
        }

        // 然后设置实际统计值
        for result in results {
            counts.insert(result.role_id, result.permission_count as u64);
        }

        Ok(counts)
    }
}

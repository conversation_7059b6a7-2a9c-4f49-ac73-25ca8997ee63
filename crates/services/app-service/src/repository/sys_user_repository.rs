use crate::domain::system::dto::sys_user_request::SysUserPageRequest;
use crate::domain::system::entities::roles::Entity as Roles;
use crate::domain::system::entities::user_roles::Entity as UserRoles;
use crate::domain::system::entities::users::{
    ActiveModel, Column, Entity as Users, Model as UserModel, UserStatus,
};

use anyhow::{Context, Result};
use chrono::{Local, NaiveDate};
use lib_core::PageDataBuilder;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, EntityTrait, PaginatorTrait,
    QueryFilter, QueryOrder, QuerySelect, QueryTrait,
};
use uuid::Uuid;

/// 系统用户Repository实现
#[derive(<PERSON><PERSON>, Service)]
pub struct SysUserRepository;

impl SysUserRepository {
    /// 构建查询条件
    fn build_query_conditions(req: &SysUserPageRequest) -> Condition {
        let mut conditions = Condition::all();

        // 字符串搜索条件
        if let Some(username) = &req.username {
            if !username.trim().is_empty() {
                conditions = conditions.add(Column::Username.contains(username.trim()));
            }
        }

        if let Some(real_name) = &req.real_name {
            if !real_name.trim().is_empty() {
                conditions = conditions.add(Column::RealName.contains(real_name.trim()));
            }
        }

        if let Some(email) = &req.email {
            if !email.trim().is_empty() {
                conditions = conditions.add(Column::Email.contains(email.trim()));
            }
        }

        if let Some(phone) = &req.phone {
            if !phone.trim().is_empty() {
                conditions = conditions.add(Column::Phone.contains(phone.trim()));
            }
        }

        // 精确匹配条件
        if let Some(status) = req.status {
            conditions = conditions.add(Column::Status.eq(status));
        }

        if let Some(gender) = req.gender {
            conditions = conditions.add(Column::Gender.eq(gender));
        }

        // 角色ID过滤 - 使用子查询
        if let Some(role_id) = req.role_id {
            let user_ids_subquery = UserRoles::find()
                .select_only()
                .column(crate::domain::system::entities::user_roles::Column::UserId)
                .filter(crate::domain::system::entities::user_roles::Column::RoleId.eq(role_id));

            conditions = conditions.add(Column::Id.in_subquery(user_ids_subquery.into_query()));
        }

        // 日期范围条件
        if let Some(ref created_start) = req.created_start {
            if !created_start.trim().is_empty() {
                if let Ok(start_date) = NaiveDate::parse_from_str(created_start, "%Y-%m-%d") {
                    if let Some(start_datetime) = start_date.and_hms_opt(0, 0, 0) {
                        if let Some(local_datetime) =
                            start_datetime.and_local_timezone(Local).single()
                        {
                            conditions = conditions.add(Column::CreatedDate.gte(local_datetime));
                        }
                    }
                }
            }
        }

        if let Some(ref created_end) = req.created_end {
            if !created_end.trim().is_empty() {
                if let Ok(end_date) = NaiveDate::parse_from_str(created_end, "%Y-%m-%d") {
                    if let Some(end_datetime) = end_date.and_hms_opt(23, 59, 59) {
                        if let Some(local_datetime) =
                            end_datetime.and_local_timezone(Local).single()
                        {
                            conditions = conditions.add(Column::CreatedDate.lte(local_datetime));
                        }
                    }
                }
            }
        }

        conditions
    }

    /// 分页查询用户
    pub async fn page_by_condition<C>(
        &self,
        req: &SysUserPageRequest,
        conn: &C,
    ) -> Result<PageDataBuilder<UserModel>>
    where
        C: ConnectionTrait,
    {
        let condition = Self::build_query_conditions(req);
        let pagination = Pagination::from_one_based(req.page, req.page_size);

        Users::find()
            .filter(condition)
            .order_by_desc(Column::CreatedDate)
            .page_builder(conn, &pagination)
            .await
            .context("分页查询用户失败")
    }

    /// 分页查询未分配角色的用户
    pub async fn page_unassigned_users<C>(
        &self,
        req: &SysUserPageRequest,
        conn: &C,
    ) -> Result<PageDataBuilder<UserModel>>
    where
        C: ConnectionTrait,
    {
        let mut condition = Self::build_query_conditions(req);

        // 添加未分配角色的条件
        condition = condition.add(
            Column::Id.not_in_subquery(
                UserRoles::find()
                    .select_only()
                    .column(crate::domain::system::entities::user_roles::Column::UserId)
                    .into_query(),
            ),
        );

        let pagination = Pagination::from_one_based(req.page, req.page_size);

        Users::find()
            .filter(condition)
            .order_by_desc(Column::CreatedDate)
            .page_builder(conn, &pagination)
            .await
            .context("分页查询未分配角色用户失败")
    }

    /// 统计未分配角色用户数量
    pub async fn count_unassigned_users<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        Users::find()
            .filter(
                Column::Id.not_in_subquery(
                    UserRoles::find()
                        .select_only()
                        .column(crate::domain::system::entities::user_roles::Column::UserId)
                        .into_query(),
                ),
            )
            .count(conn)
            .await
            .context("统计未分配角色用户数量失败")
    }

    /// 根据ID查询用户
    pub async fn find_by_id<C>(&self, user_id: Uuid, conn: &C) -> Result<Option<UserModel>>
    where
        C: ConnectionTrait,
    {
        Users::find_by_id(user_id)
            .one(conn)
            .await
            .context("查询用户失败")
    }

    /// 根据IDs查询用户列表
    pub async fn find_by_ids<C>(&self, user_ids: Vec<Uuid>, conn: &C) -> Result<Vec<UserModel>>
    where
        C: ConnectionTrait,
    {
        if user_ids.is_empty() {
            return Ok(Vec::new());
        }

        Users::find()
            .filter(Column::Id.is_in(user_ids))
            .all(conn)
            .await
            .context("批量查询用户失败")
    }

    /// 根据用户名查询用户
    pub async fn find_by_username<C>(&self, username: &str, conn: &C) -> Result<Option<UserModel>>
    where
        C: ConnectionTrait,
    {
        Users::find()
            .filter(Column::Username.eq(username))
            .one(conn)
            .await
            .context("根据用户名查询用户失败")
    }

    /// 根据邮箱查询用户
    pub async fn find_by_email<C>(&self, email: &str, conn: &C) -> Result<Option<UserModel>>
    where
        C: ConnectionTrait,
    {
        Users::find()
            .filter(Column::Email.eq(email))
            .one(conn)
            .await
            .context("根据邮箱查询用户失败")
    }

    /// 根据手机号查询用户
    pub async fn find_by_phone<C>(&self, phone: &str, conn: &C) -> Result<Option<UserModel>>
    where
        C: ConnectionTrait,
    {
        Users::find()
            .filter(Column::Phone.eq(phone))
            .one(conn)
            .await
            .context("根据手机号查询用户失败")
    }

    /// 根据微信OpenID查询用户
    pub async fn find_by_wechat_openid<C>(&self, openid: &str, conn: &C) -> Result<Option<UserModel>>
    where
        C: ConnectionTrait,
    {
        Users::find()
            .filter(Column::WechatOpenid.eq(openid))
            .one(conn)
            .await
            .context("根据微信OpenID查询用户失败")
    }

    /// 查询所有活跃用户
    pub async fn find_active_users<C>(&self, conn: &C) -> Result<Vec<UserModel>>
    where
        C: ConnectionTrait,
    {
        Users::find()
            .filter(Column::Status.eq(UserStatus::Active))
            .order_by_asc(Column::Username)
            .all(conn)
            .await
            .context("查询活跃用户失败")
    }

    /// 根据状态查询用户
    pub async fn find_by_status<C>(&self, status: UserStatus, conn: &C) -> Result<Vec<UserModel>>
    where
        C: ConnectionTrait,
    {
        Users::find()
            .filter(Column::Status.eq(status))
            .order_by_desc(Column::CreatedDate)
            .all(conn)
            .await
            .context("根据状态查询用户失败")
    }

    /// 用户认证查询
    pub async fn authenticate_user<C>(&self, username: &str, conn: &C) -> Result<Option<UserModel>>
    where
        C: ConnectionTrait,
    {
        Users::find()
            .filter(
                Condition::all()
                    .add(Column::Username.eq(username))
                    .add(Column::Status.eq(UserStatus::Active)),
            )
            .one(conn)
            .await
            .context("用户认证查询失败")
    }

    /// 搜索用户
    pub async fn search_users<C>(
        &self,
        keyword: &str,
        limit: Option<u64>,
        conn: &C,
    ) -> Result<Vec<UserModel>>
    where
        C: ConnectionTrait,
    {
        let mut query = Users::find()
            .filter(
                Condition::any()
                    .add(Column::Username.contains(keyword))
                    .add(Column::RealName.contains(keyword))
                    .add(Column::Email.contains(keyword)),
            )
            .order_by_asc(Column::Username);

        if let Some(limit) = limit {
            query = query.limit(limit);
        }

        query.all(conn).await.context("搜索用户失败")
    }

    /// 查询最近登录的用户
    pub async fn find_recent_login_users<C>(&self, limit: u64, conn: &C) -> Result<Vec<UserModel>>
    where
        C: ConnectionTrait,
    {
        Users::find()
            .filter(Column::LastLoginDate.is_not_null())
            .filter(Column::Status.eq(UserStatus::Active))
            .order_by_desc(Column::LastLoginDate)
            .limit(limit)
            .all(conn)
            .await
            .context("查询最近登录用户失败")
    }

    /// 检查用户名是否存在
    pub async fn username_exists<C>(
        &self,
        username: &str,
        exclude_user_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = Users::find().filter(Column::Username.eq(username));

        if let Some(exclude_id) = exclude_user_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query.count(conn).await.context("检查用户名是否存在失败")?;
        Ok(count > 0)
    }

    /// 检查邮箱是否存在
    pub async fn email_exists<C>(
        &self,
        email: &str,
        exclude_user_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = Users::find().filter(Column::Email.eq(email));

        if let Some(exclude_id) = exclude_user_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query.count(conn).await.context("检查邮箱是否存在失败")?;
        Ok(count > 0)
    }

    /// 检查手机号是否存在
    pub async fn phone_exists<C>(
        &self,
        phone: &str,
        exclude_user_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = Users::find().filter(Column::Phone.eq(phone));

        if let Some(exclude_id) = exclude_user_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query.count(conn).await.context("检查手机号是否存在失败")?;
        Ok(count > 0)
    }

    /// 统计用户数量（按状态）
    pub async fn count_by_status<C>(&self, status: UserStatus, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        Users::find()
            .filter(Column::Status.eq(status))
            .count(conn)
            .await
            .context("统计用户数量失败")
    }

    /// 创建用户
    pub async fn create<C>(&self, model: ActiveModel, conn: &C) -> Result<UserModel>
    where
        C: ConnectionTrait,
    {
        model.insert(conn).await.context("创建用户失败")
    }

    /// 更新用户
    pub async fn update<C>(&self, model: ActiveModel, conn: &C) -> Result<UserModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context("更新用户失败")
    }

    /// 根据ID删除用户
    pub async fn delete_by_id<C>(&self, user_id: Uuid, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        Users::delete_by_id(user_id)
            .exec(conn)
            .await
            .context("删除用户失败")?;

        Ok(())
    }

    /// 批量删除用户
    pub async fn delete_by_ids<C>(&self, user_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if user_ids.is_empty() {
            return Ok(0);
        }

        let result = Users::delete_many()
            .filter(Column::Id.is_in(user_ids))
            .exec(conn)
            .await
            .context("批量删除用户失败")?;

        Ok(result.rows_affected)
    }

    /// 查询用户详情及其角色
    pub async fn find_user_detail_with_roles<C>(
        &self,
        user_id: Uuid,
        conn: &C,
    ) -> Result<
        Option<(
            UserModel,
            Vec<crate::domain::system::entities::roles::Model>,
        )>,
    >
    where
        C: ConnectionTrait,
    {
        let user = self.find_by_id(user_id, conn).await?;

        if let Some(user) = user {
            let roles = Users::find_by_id(user_id)
                .find_with_related(Roles)
                .all(conn)
                .await
                .context("查询用户角色详情失败")?
                .into_iter()
                .flat_map(|(_, roles)| roles)
                .collect();

            Ok(Some((user, roles)))
        } else {
            Ok(None)
        }
    }
}

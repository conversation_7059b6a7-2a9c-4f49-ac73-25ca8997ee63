//! 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
//! Generated by lib-codegen

use crate::domain::business::merchants::entities::merchant_commission_rules::{
    ActiveModel, Column, Entity as MerchantCommissionRules, Model as MerchantCommissionRulesModel,
};
use anyhow::{Context, Result};
use lib_core::PageDataBuilder;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, ConnectionTrait, EntityTrait, PaginatorTrait, QueryFilter,
    QueryOrder, Set,
};
use uuid::Uuid;

/// 商户门店佣金规则Repository实现
///
/// 提供商户门店佣金规则的数据访问操作，包括：
/// - 基础CRUD操作
/// - 按商户查询佣金规则
/// - 佣金规则管理
/// - 业务逻辑验证
#[derive(Clone, Service)]
pub struct MerchantCommissionRulesRepository;

impl MerchantCommissionRulesRepository {
    // ==================== 基础CRUD操作 ====================

    /// 根据ID查询佣金规则
    pub async fn find_by_id<C>(
        &self,
        rule_id: Uuid,
        conn: &C,
    ) -> Result<Option<MerchantCommissionRulesModel>>
    where
        C: ConnectionTrait,
    {
        MerchantCommissionRules::find_by_id(rule_id)
            .one(conn)
            .await
            .context("查询佣金规则失败")
    }

    /// 创建佣金规则
    pub async fn create<C>(
        &self,
        model: ActiveModel,
        conn: &C,
    ) -> Result<MerchantCommissionRulesModel>
    where
        C: ConnectionTrait,
    {
        model.insert(conn).await.context("创建佣金规则失败")
    }

    /// 更新佣金规则
    pub async fn update<C>(
        &self,
        model: ActiveModel,
        conn: &C,
    ) -> Result<MerchantCommissionRulesModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context("更新佣金规则失败")
    }

    /// 根据ID删除佣金规则
    pub async fn delete_by_id<C>(&self, rule_id: Uuid, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        MerchantCommissionRules::delete_by_id(rule_id)
            .exec(conn)
            .await
            .context("删除佣金规则失败")?;

        Ok(())
    }

    /// 批量删除佣金规则
    pub async fn delete_by_ids<C>(&self, rule_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if rule_ids.is_empty() {
            return Ok(0);
        }

        let result = MerchantCommissionRules::delete_many()
            .filter(Column::Id.is_in(rule_ids))
            .exec(conn)
            .await
            .context("批量删除佣金规则失败")?;

        Ok(result.rows_affected)
    }

    // ==================== 商户相关查询 ====================

    /// 查询商户的所有佣金规则
    pub async fn find_by_merchant_id<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Vec<MerchantCommissionRulesModel>>
    where
        C: ConnectionTrait,
    {
        MerchantCommissionRules::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .order_by_desc(Column::CreatedDate)
            .all(conn)
            .await
            .context("查询商户佣金规则失败")
    }

    /// 检查商户是否已有佣金规则
    pub async fn exists_by_merchant_id<C>(&self, merchant_id: i64, conn: &C) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let count = MerchantCommissionRules::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .count(conn)
            .await
            .context("检查商户佣金规则是否存在失败")?;

        Ok(count > 0)
    }

    /// 检查规则名称是否存在（同一商户下）
    pub async fn exists_rule_name<C>(
        &self,
        merchant_id: i64,
        rule_name: &str,
        exclude_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = MerchantCommissionRules::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::RuleName.eq(rule_name));

        if let Some(exclude_id) = exclude_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query
            .count(conn)
            .await
            .context("检查规则名称是否存在失败")?;

        Ok(count > 0)
    }

    /// 删除商户的所有佣金规则
    pub async fn delete_by_merchant_id<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = MerchantCommissionRules::delete_many()
            .filter(Column::MerchantId.eq(merchant_id))
            .exec(conn)
            .await
            .context("删除商户佣金规则失败")?;

        Ok(result.rows_affected)
    }

    // ==================== 业务查询方法 ====================

    /// 根据规则名称查询（模糊搜索）
    pub async fn find_by_rule_name_like<C>(
        &self,
        merchant_id: Option<i64>,
        rule_name: &str,
        conn: &C,
    ) -> Result<Vec<MerchantCommissionRulesModel>>
    where
        C: ConnectionTrait,
    {
        let mut query =
            MerchantCommissionRules::find().filter(Column::RuleName.contains(rule_name));

        if let Some(merchant_id) = merchant_id {
            query = query.filter(Column::MerchantId.eq(merchant_id));
        }

        query
            .order_by_desc(Column::CreatedDate)
            .all(conn)
            .await
            .context("按规则名称查询失败")
    }

    /// 统计商户的佣金规则数量
    pub async fn count_by_merchant_id<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantCommissionRules::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .count(conn)
            .await
            .context("统计商户佣金规则数量失败")
    }

    /// 批量查询佣金规则
    pub async fn find_by_ids<C>(
        &self,
        rule_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<MerchantCommissionRulesModel>>
    where
        C: ConnectionTrait,
    {
        if rule_ids.is_empty() {
            return Ok(Vec::new());
        }

        MerchantCommissionRules::find()
            .filter(Column::Id.is_in(rule_ids))
            .order_by_desc(Column::CreatedDate)
            .all(conn)
            .await
            .context("批量查询佣金规则失败")
    }

    /// 查询所有佣金规则（系统管理员用）
    pub async fn find_all<C>(&self, conn: &C) -> Result<Vec<MerchantCommissionRulesModel>>
    where
        C: ConnectionTrait,
    {
        MerchantCommissionRules::find()
            .order_by_desc(Column::CreatedDate)
            .all(conn)
            .await
            .context("查询所有佣金规则失败")
    }

    /// 分页查询所有佣金规则（系统管理员用）
    pub async fn page_all<C>(
        &self,
        page: u64,
        page_size: u64,
        conn: &C,
    ) -> Result<PageDataBuilder<MerchantCommissionRulesModel>>
    where
        C: ConnectionTrait,
    {
        let pagination = Pagination::from_one_based(Some(page), Some(page_size));

        MerchantCommissionRules::find()
            .order_by_desc(Column::CreatedDate)
            .page_builder(conn, &pagination)
            .await
            .context("分页查询所有佣金规则失败")
    }

    // ==================== 辅助方法 ====================

    /// 为商户创建默认佣金规则
    pub async fn create_default_rule<C>(
        &self,
        merchant_id: i64,
        base_rate: rust_decimal::Decimal,
        created_by: Uuid,
        conn: &C,
    ) -> Result<MerchantCommissionRulesModel>
    where
        C: ConnectionTrait,
    {
        let default_rule = ActiveModel {
            id: Set(Uuid::now_v7()),
            merchant_id: Set(merchant_id),
            rule_name: Set("默认佣金规则".to_string()),
            base_commission_rate: Set(base_rate),
            tier_rules: Set(None),
            created_by: Set(Some(created_by)),
            updated_by: Set(Some(created_by)),
            remark: Set(Some("系统自动创建的默认佣金规则".to_string())),
            ..Default::default()
        };

        self.create(default_rule, conn).await
    }
}

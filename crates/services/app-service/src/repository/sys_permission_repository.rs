use crate::domain::system::dto::sys_permission_request::SysPermissionPageRequest;
use crate::domain::system::entities::permissions::{
    ActiveModel, Column, Entity as Permissions, Model as PermissionModel,
};
use crate::utils::datetime::DateTimeUtils;
use anyhow::{Context, Result};
use lib_core::app::plugin::Service as ServiceTrait;
use lib_core::response::PageData;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, DatabaseConnection, EntityTrait,
    PaginatorTrait, QueryFilter, QueryOrder, RelationTrait,
};
use uuid::Uuid;

/// 系统权限Repository实现
#[derive(Clone, Service)]
pub struct SysPermissionRepository;

impl SysPermissionRepository {
    /// 构建查询条件
    fn build_query_conditions(req: &SysPermissionPageRequest) -> Condition {
        let mut condition = Condition::all();

        // 菜单名称模糊查询
        if let Some(ref menu_name) = req.menu_name {
            if !menu_name.trim().is_empty() {
                condition = condition.add(Column::MenuName.contains(menu_name.trim()));
            }
        }

        // 权限标识模糊查询
        if let Some(ref perms) = req.perms {
            if !perms.trim().is_empty() {
                condition = condition.add(Column::Perms.contains(perms.trim()));
            }
        }

        // 菜单类型精确匹配
        if let Some(menu_type) = req.menu_type {
            condition = condition.add(Column::MenuType.eq(menu_type));
        }

        // 状态精确匹配
        if let Some(status) = req.status {
            condition = condition.add(Column::Status.eq(status));
        }

        // 创建时间范围查询
        if let Some(ref created_start) = req.created_start {
            if let Ok(start_date) = DateTimeUtils::parse_range_start(created_start) {
                condition = condition.add(Column::CreatedDate.gte(start_date));
            }
        }

        if let Some(ref created_end) = req.created_end {
            if let Ok(end_date) = DateTimeUtils::parse_range_end(created_end) {
                condition = condition.add(Column::CreatedDate.lte(end_date));
            }
        }

        condition
    }

    /// 分页查询权限菜单列表
    pub async fn page_by_condition<C>(
        &self,
        req: &SysPermissionPageRequest,
        conn: &C,
    ) -> Result<PageData<crate::domain::system::vo::SysPermissionListResponse>>
    where
        C: ConnectionTrait,
    {
        let condition = Self::build_query_conditions(req);
        let pagination = Pagination::from_one_based(req.page, req.page_size);

        Permissions::find()
            .filter(condition)
            .order_by_asc(Column::OrderNum)
            .page_as(conn, &pagination)
            .await
            .context("分页查询权限菜单失败")
    }

    /// 根据ID查询权限菜单
    pub async fn find_by_id<C>(
        &self,
        permission_id: Uuid,
        conn: &C,
    ) -> Result<Option<PermissionModel>>
    where
        C: ConnectionTrait,
    {
        Permissions::find_by_id(permission_id)
            .one(conn)
            .await
            .context("查询权限菜单失败")
    }

    /// 查询所有权限菜单
    pub async fn find_all<C>(&self, conn: &C) -> Result<Vec<PermissionModel>>
    where
        C: ConnectionTrait,
    {
        Permissions::find()
            .order_by_asc(Column::OrderNum)
            .all(conn)
            .await
            .context("查询所有权限菜单失败")
    }

    /// 查询正常状态的权限菜单
    pub async fn find_active<C>(&self, conn: &C) -> Result<Vec<PermissionModel>>
    where
        C: ConnectionTrait,
    {
        Permissions::find()
            .filter(Column::Status.eq(0)) // 正常状态
            .order_by_asc(Column::OrderNum)
            .all(conn)
            .await
            .context("查询正常状态权限菜单失败")
    }

    /// 根据父ID查询子菜单
    pub async fn find_by_parent_id<C>(
        &self,
        parent_id: Uuid,
        conn: &C,
    ) -> Result<Vec<PermissionModel>>
    where
        C: ConnectionTrait,
    {
        Permissions::find()
            .filter(Column::ParentId.eq(parent_id))
            .order_by_asc(Column::OrderNum)
            .all(conn)
            .await
            .context("查询子菜单失败")
    }

    /// 根据IDs批量查询权限菜单
    pub async fn find_by_ids<C>(
        &self,
        permission_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<PermissionModel>>
    where
        C: ConnectionTrait,
    {
        if permission_ids.is_empty() {
            return Ok(Vec::new());
        }

        Permissions::find()
            .filter(Column::Id.is_in(permission_ids))
            .all(conn)
            .await
            .context("批量查询权限菜单失败")
    }

    /// 查询菜单类型小于指定值的权限菜单
    pub async fn find_by_menu_type_lt<C>(
        &self,
        menu_type: i32,
        conn: &C,
    ) -> Result<Vec<PermissionModel>>
    where
        C: ConnectionTrait,
    {
        Permissions::find()
            .filter(Column::MenuType.lt(menu_type))
            .filter(Column::Status.eq(0)) // 正常状态
            .order_by_asc(Column::OrderNum)
            .all(conn)
            .await
            .context("查询指定菜单类型权限失败")
    }

    /// 查询用户拥有的权限
    pub async fn find_user_permissions(
        &self,
        user_id: Uuid,
        conn: &DatabaseConnection,
    ) -> Result<Vec<PermissionModel>> {
        Permissions::find_user_permissions(conn, user_id)
            .await
            .context("查询用户权限失败")
    }

    /// 获取用户权限代码列表
    pub async fn get_user_permission_codes<C>(&self, user_id: Uuid, conn: &C) -> Result<Vec<String>>
    where
        C: ConnectionTrait,
    {
        use crate::domain::system::entities::permissions::Entity as Permissions;
        use sea_orm::{JoinType, QuerySelect};

        let permission_codes: Vec<String> = Permissions::find()
            .distinct()
            .select_only()
            .column(crate::domain::system::entities::permissions::Column::Perms)
            .join(
                JoinType::InnerJoin,
                crate::domain::system::entities::permissions::Relation::RolePermissions.def(),
            )
            .join(
                JoinType::InnerJoin,
                crate::domain::system::entities::role_permissions::Relation::Roles.def(),
            )
            .join(
                JoinType::InnerJoin,
                crate::domain::system::entities::roles::Relation::UserRoles.def(),
            )
            .filter(crate::domain::system::entities::user_roles::Column::UserId.eq(user_id))
            .filter(crate::domain::system::entities::permissions::Column::Perms.is_not_null())
            .filter(crate::domain::system::entities::permissions::Column::Status.eq(0))
            .into_tuple::<(Option<String>,)>()
            .all(conn)
            .await
            .context("获取用户权限代码失败")?
            .into_iter()
            .filter_map(|(perm,)| perm)
            .collect();

        Ok(permission_codes)
    }

    /// 检查菜单是否有子菜单
    pub async fn has_children<C>(&self, permission_id: Uuid, conn: &C) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let count = Permissions::find()
            .filter(Column::ParentId.eq(permission_id))
            .count(conn)
            .await
            .context("检查子菜单失败")?;

        Ok(count > 0)
    }

    /// 检查权限标识是否存在
    pub async fn exists_by_perms<C>(
        &self,
        perms: &str,
        exclude_permission_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = Permissions::find().filter(Column::Perms.eq(perms));

        if let Some(exclude_id) = exclude_permission_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query.count(conn).await.context("检查权限标识失败")?;

        Ok(count > 0)
    }

    /// 创建权限菜单
    pub async fn create<C>(&self, model: ActiveModel, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        Permissions::insert(model)
            .exec(conn)
            .await
            .context("创建权限菜单失败")?;

        Ok(())
    }

    /// 更新权限菜单
    pub async fn update<C>(&self, model: ActiveModel, conn: &C) -> Result<PermissionModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context("更新权限菜单失败")
    }

    /// 根据ID删除权限菜单
    pub async fn delete_by_id<C>(&self, permission_id: Uuid, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        Permissions::delete_by_id(permission_id)
            .exec(conn)
            .await
            .context("删除权限菜单失败")?;

        Ok(())
    }

    /// 批量删除权限菜单
    pub async fn delete_by_ids<C>(&self, permission_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = Permissions::delete_many()
            .filter(Column::Id.is_in(permission_ids))
            .exec(conn)
            .await
            .context("批量删除权限菜单失败")?;

        Ok(result.rows_affected)
    }

    /// 统计指定父ID的子菜单数量
    pub async fn count_by_parent_id<C>(&self, parent_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        Permissions::find()
            .filter(Column::ParentId.eq(parent_id))
            .count(conn)
            .await
            .context("统计子菜单数量失败")
    }

    /// 统计指定父IDs的子菜单数量
    pub async fn count_by_parent_ids<C>(&self, parent_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if parent_ids.is_empty() {
            return Ok(0);
        }

        Permissions::find()
            .filter(Column::ParentId.is_in(parent_ids))
            .count(conn)
            .await
            .context("统计子菜单数量失败")
    }
}

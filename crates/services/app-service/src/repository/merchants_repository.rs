use crate::domain::business::merchants::entities::merchant_categories;
use crate::domain::business::merchants::entities::merchants::{
    ActiveModel, Column, Entity as Merchants, MerchantStatus, Model as MerchantsModel,
};
use crate::domain::system::dto::sys_merchant_request::SysMerchantPageRequest;
use crate::utils::datetime::DateTimeUtils;
use anyhow::{Context, Result};
use lib_core::PageDataBuilder;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_data::pagination::Pagination;
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, EntityTrait, PaginatorTrait,
    QueryFilter, QueryOrder,
};

/// 商户Repository实现
#[derive(Clone, Service)]
pub struct MerchantsRepository;

impl MerchantsRepository {
    /// 构建查询条件
    fn build_query_conditions(req: &SysMerchantPageRequest) -> Condition {
        let mut condition = Condition::all();

        // 商户名称搜索
        if let Some(ref merchant_name) = req.merchant_name {
            if !merchant_name.trim().is_empty() {
                condition = condition.add(Column::MerchantName.contains(merchant_name.trim()));
            }
        }

        // 商户编码搜索
        if let Some(ref merchant_code) = req.merchant_code {
            if !merchant_code.trim().is_empty() {
                condition = condition.add(Column::MerchantCode.contains(merchant_code.trim()));
            }
        }

        // 联系电话搜索
        if let Some(ref phone) = req.phone {
            if !phone.trim().is_empty() {
                condition = condition.add(Column::Phone.contains(phone.trim()));
            }
        }

        // 邮箱
        if let Some(ref email) = req.email {
            if !email.trim().is_empty() {
                condition = condition.add(Column::Email.contains(email.trim()));
            }
        }

        // 分类ID过滤
        if let Some(category_id) = req.category_id {
            condition = condition.add(Column::CategoryId.eq(category_id));
        }

        // 状态过滤
        if let Some(status) = req.status {
            condition = condition.add(Column::Status.eq(status));
        }

        // 创建时间范围过滤
        if let Some(ref created_start) = req.created_start {
            if !created_start.trim().is_empty() {
                if let Ok(start_datetime) = DateTimeUtils::parse_range_start(created_start) {
                    condition =
                        condition.add(Column::CreatedDate.gte(start_datetime.fixed_offset()));
                }
            }
        }

        if let Some(ref created_end) = req.created_end {
            if !created_end.trim().is_empty() {
                if let Ok(end_datetime) = DateTimeUtils::parse_range_end(created_end) {
                    condition = condition.add(Column::CreatedDate.lte(end_datetime.fixed_offset()));
                }
            }
        }

        condition
    }

    /// 分页查询商户（带商户分类关联）
    pub async fn page_with_categories<C>(
        &self,
        req: &SysMerchantPageRequest,
        conn: &C,
    ) -> Result<PageDataBuilder<(MerchantsModel, Option<merchant_categories::Model>)>>
    where
        C: ConnectionTrait,
    {
        let condition = Self::build_query_conditions(req);
        let pagination = Pagination::from_one_based(req.page, req.page_size);

        let paginator = Merchants::find()
            .filter(condition)
            .find_also_related(merchant_categories::Entity)
            .order_by_desc(Column::CreatedDate)
            .paginate(conn, pagination.size);

        let merchants = paginator
            .fetch_page(pagination.page)
            .await
            .context("分页查询商户失败")?;

        let total = paginator.num_items().await.context("查询总记录数失败")?;

        Ok(PageDataBuilder::new(
            merchants,
            total,
            pagination.page,
            pagination.size,
        ))
    }

    /// 根据ID查询商户
    pub async fn find_by_id<C>(&self, merchant_id: i64, conn: &C) -> Result<Option<MerchantsModel>>
    where
        C: ConnectionTrait,
    {
        Merchants::find_by_id(merchant_id)
            .one(conn)
            .await
            .context("查询商户失败")
    }

    /// 根据ID查询商户详情（带商户分类关联）
    pub async fn find_by_id_with_category<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Option<(MerchantsModel, Option<merchant_categories::Model>)>>
    where
        C: ConnectionTrait,
    {
        Merchants::find_by_id(merchant_id)
            .find_also_related(merchant_categories::Entity)
            .one(conn)
            .await
            .context("查询商户详情失败")
    }

    /// 创建商户
    pub async fn create<C>(
        &self,
        model: ActiveModel,
        conn: &C,
    ) -> Result<sea_orm::InsertResult<ActiveModel>>
    where
        C: ConnectionTrait,
    {
        Merchants::insert(model)
            .exec(conn)
            .await
            .context("创建商户失败")
    }

    /// 更新商户
    pub async fn update<C>(&self, model: ActiveModel, conn: &C) -> Result<MerchantsModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context("更新商户失败")
    }

    /// 根据ID删除商户
    pub async fn delete_by_id<C>(&self, merchant_id: i64, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        Merchants::delete_by_id(merchant_id)
            .exec(conn)
            .await
            .context("删除商户失败")?;

        Ok(())
    }

    /// 批量删除商户
    pub async fn delete_by_ids<C>(&self, merchant_ids: Vec<i64>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = Merchants::delete_many()
            .filter(Column::Id.is_in(merchant_ids))
            .exec(conn)
            .await
            .context("批量删除商户失败")?;

        Ok(result.rows_affected)
    }

    /// 查询正常营业的商户选择项
    pub async fn find_active_select_items<C>(&self, conn: &C) -> Result<Vec<MerchantsModel>>
    where
        C: ConnectionTrait,
    {
        Merchants::find()
            .filter(Column::Status.eq(MerchantStatus::Active))
            .order_by_asc(Column::MerchantName)
            .all(conn)
            .await
            .context("查询商户选择项失败")
    }

    /// 查询正常营业的商户基础信息（支持名称搜索）
    pub async fn find_active_basic_list<C>(
        &self,
        merchant_name: Option<String>,
        conn: &C,
    ) -> Result<Vec<MerchantsModel>>
    where
        C: ConnectionTrait,
    {
        let mut condition = Condition::all();
        condition = condition.add(Column::Status.eq(MerchantStatus::Active));

        if let Some(ref name) = merchant_name {
            if !name.trim().is_empty() {
                condition = condition.add(Column::MerchantName.contains(name.trim()));
            }
        }

        Merchants::find()
            .filter(condition)
            .order_by_asc(Column::MerchantName)
            .all(conn)
            .await
            .context("查询商户基础信息失败")
    }

    /// 统计商户总数
    pub async fn count_total<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        Merchants::find()
            .count(conn)
            .await
            .context("统计商户总数失败")
    }

    /// 统计正常营业商户数
    pub async fn count_active<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        Merchants::find()
            .filter(Column::Status.eq(MerchantStatus::Active as i32))
            .count(conn)
            .await
            .context("统计正常营业商户数失败")
    }

    /// 统计临时关闭商户数
    pub async fn count_suspended<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        Merchants::find()
            .filter(Column::Status.eq(MerchantStatus::Suspended as i32))
            .count(conn)
            .await
            .context("统计临时关闭商户数失败")
    }

    /// 统计永久关闭商户数
    pub async fn count_closed<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        Merchants::find()
            .filter(Column::Status.eq(MerchantStatus::Closed as i32))
            .count(conn)
            .await
            .context("统计永久关闭商户数失败")
    }

    /// 检查商户编码是否存在
    pub async fn exists_by_code<C>(
        &self,
        merchant_code: &str,
        exclude_id: Option<i64>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = Merchants::find().filter(Column::MerchantCode.eq(merchant_code));

        if let Some(exclude_id) = exclude_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query
            .count(conn)
            .await
            .context("检查商户编码是否存在失败")?;

        Ok(count > 0)
    }

    /// 检查商户名称是否存在
    pub async fn exists_by_name<C>(
        &self,
        merchant_name: &str,
        exclude_id: Option<i64>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = Merchants::find().filter(Column::MerchantName.eq(merchant_name));

        if let Some(exclude_id) = exclude_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query
            .count(conn)
            .await
            .context("检查商户名称是否存在失败")?;

        Ok(count > 0)
    }
}

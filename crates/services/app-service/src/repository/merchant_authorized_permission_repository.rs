use crate::domain::business::merchants::entities::merchant_authorized_permissions::{
    ActiveModel, Column, Entity as MerchantAuthorizedPermission,
    MerchantAuthorizedPermissionsStatus, Model as MerchantAuthorizedPermissionModel,
};
use crate::domain::business::merchants::entities::merchant_role_permissions::Entity as MerchantRolePermission;
use crate::domain::business::merchants::entities::system_permission_templates::Entity as SystemPermissionTemplate;
use crate::domain::system::dto::sys_merchant_authorized_permission_request::SysMerchantAuthorizedPermissionPageRequest;
use crate::utils::datetime::DateTimeUtils;
use anyhow::{Context, Result};
use lib_core::PageDataBuilder;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, EntityTrait, Pa<PERSON>atorT<PERSON>t,
    Query<PERSON>ilter, QueryOrder,
};
use uuid::Uuid;

/// 商户授权权限Repository实现
#[derive(Clone, Service)]
pub struct MerchantAuthorizedPermissionRepository;

impl MerchantAuthorizedPermissionRepository {
    /// 构建查询条件
    fn build_query_conditions(req: &SysMerchantAuthorizedPermissionPageRequest) -> Condition {
        let mut condition = Condition::all();

        // 商户ID过滤
        if let Some(merchant_id) = req.merchant_id {
            condition = condition.add(Column::MerchantId.eq(merchant_id));
        }

        // 权限模板ID过滤
        if let Some(permission_template_id) = req.permission_template_id {
            condition = condition.add(Column::PermissionTemplateId.eq(permission_template_id));
        }

        // 状态过滤
        if let Some(status) = req.status {
            condition = condition.add(Column::Status.eq(status));
        }

        // 授权时间范围过滤
        if let Some(ref authorized_start) = req.authorized_start {
            if !authorized_start.trim().is_empty() {
                if let Ok(start_datetime) = DateTimeUtils::parse_range_start(authorized_start) {
                    condition =
                        condition.add(Column::AuthorizedDate.gte(start_datetime.fixed_offset()));
                }
            }
        }

        if let Some(ref authorized_end) = req.authorized_end {
            if !authorized_end.trim().is_empty() {
                if let Ok(end_datetime) = DateTimeUtils::parse_range_end(authorized_end) {
                    condition =
                        condition.add(Column::AuthorizedDate.lte(end_datetime.fixed_offset()));
                }
            }
        }

        // 创建时间范围过滤
        if let Some(ref created_start) = req.created_start {
            if !created_start.trim().is_empty() {
                if let Ok(start_datetime) = DateTimeUtils::parse_range_start(created_start) {
                    condition =
                        condition.add(Column::AuthorizedDate.gte(start_datetime.fixed_offset()));
                }
            }
        }

        if let Some(ref created_end) = req.created_end {
            if !created_end.trim().is_empty() {
                if let Ok(end_datetime) = DateTimeUtils::parse_range_end(created_end) {
                    condition =
                        condition.add(Column::AuthorizedDate.lte(end_datetime.fixed_offset()));
                }
            }
        }

        condition
    }

    /// 分页查询商户授权权限
    pub async fn page_by_condition<C>(
        &self,
        req: &SysMerchantAuthorizedPermissionPageRequest,
        conn: &C,
    ) -> Result<PageDataBuilder<MerchantAuthorizedPermissionModel>, String>
    where
        C: ConnectionTrait,
    {
        let condition = Self::build_query_conditions(req);
        let pagination = Pagination::from_one_based(req.page, req.page_size);

        let data_builder = MerchantAuthorizedPermission::find()
            .filter(condition)
            .order_by_desc(Column::AuthorizedDate)
            .page_builder(conn, &pagination)
            .await
            .map_err(|e| e.to_string())?;

        Ok(data_builder)
    }

    /// 根据ID查询商户授权权限
    pub async fn find_by_id<C>(
        &self,
        id: Uuid,
        conn: &C,
    ) -> Result<Option<MerchantAuthorizedPermissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find_by_id(id)
            .one(conn)
            .await
            .context("查询商户授权权限失败")
    }

    /// 根据商户ID查询所有授权权限
    pub async fn find_by_merchant_id<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Vec<MerchantAuthorizedPermissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .all(conn)
            .await
            .context("查询商户授权权限失败")
    }

    /// 根据商户ID查询有效的授权权限
    pub async fn find_effective_by_merchant_id<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Vec<MerchantAuthorizedPermissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantAuthorizedPermissionsStatus::Enabled))
            .all(conn)
            .await
            .context("查询商户有效权限失败")
    }

    /// 根据商户ID查询正常状态的授权权限
    pub async fn find_active_by_merchant_id<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Vec<MerchantAuthorizedPermissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantAuthorizedPermissionsStatus::Enabled)) // 0 表示正常状态
            .all(conn)
            .await
            .context("查询商户正常状态授权权限失败")
    }

    /// 根据商户IDs批量查询授权权限
    pub async fn find_by_merchant_ids<C>(
        &self,
        merchant_ids: Vec<i64>,
        conn: &C,
    ) -> Result<Vec<MerchantAuthorizedPermissionModel>>
    where
        C: ConnectionTrait,
    {
        if merchant_ids.is_empty() {
            return Ok(Vec::new());
        }

        MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.is_in(merchant_ids))
            .filter(Column::Status.eq(MerchantAuthorizedPermissionsStatus::Enabled)) // 只查询正常状态权限
            .all(conn)
            .await
            .context("批量查询商户授权权限失败")
    }

    /// 根据商户ID和权限模板IDs查询授权权限
    pub async fn find_by_merchant_and_template_ids<C>(
        &self,
        merchant_id: i64,
        template_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<MerchantAuthorizedPermissionModel>>
    where
        C: ConnectionTrait,
    {
        if template_ids.is_empty() {
            return Ok(Vec::new());
        }

        MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::PermissionTemplateId.is_in(template_ids))
            .filter(Column::Status.eq(MerchantAuthorizedPermissionsStatus::Enabled)) // 只查询正常状态权限
            .all(conn)
            .await
            .context("根据商户ID和权限模板IDs查询授权权限失败")
    }

    /// 根据商户ID查询授权权限（包含权限模板信息）
    pub async fn find_with_templates_by_merchant_id<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<
        Vec<(
            MerchantAuthorizedPermissionModel,
            Vec<crate::domain::business::merchants::entities::system_permission_templates::Model>,
        )>,
    >
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantAuthorizedPermissionsStatus::Enabled as i32))
            .find_with_related(SystemPermissionTemplate)
            .all(conn)
            .await
            .context("查询商户授权权限及模板信息失败")
    }

    /// 根据商户ID和权限模板ID查询
    pub async fn find_by_merchant_and_templates<C>(
        &self,
        merchant_id: i64,
        template_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<MerchantAuthorizedPermissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::PermissionTemplateId.is_in(template_ids))
            .all(conn)
            .await
            .context("查询商户权限失败")
    }

    /// 根据商户ID和权限模板ID查询（包含权限模板信息）
    pub async fn find_with_templates_by_merchant_and_templates<C>(
        &self,
        merchant_id: i64,
        template_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<
        Vec<(
            MerchantAuthorizedPermissionModel,
            Vec<crate::domain::business::merchants::entities::system_permission_templates::Model>,
        )>,
    >
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::PermissionTemplateId.is_in(template_ids))
            .find_with_related(SystemPermissionTemplate)
            .all(conn)
            .await
            .context("查询商户权限及模板信息失败")
    }

    /// 根据IDs查询授权权限列表
    pub async fn find_by_ids<C>(
        &self,
        ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<MerchantAuthorizedPermissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find()
            .filter(Column::Id.is_in(ids))
            .all(conn)
            .await
            .context("查询授权权限失败")
    }

    /// 检查是否存在指定的权限模板授权
    pub async fn exists_by_merchant_and_templates<C>(
        &self,
        merchant_id: i64,
        template_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let count = MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::PermissionTemplateId.is_in(template_ids))
            .count(conn)
            .await
            .context("检查权限授权状态失败")?;

        Ok(count > 0)
    }

    /// 检查权限是否可以删除（没有被角色关联）
    pub async fn is_deletable<C>(&self, id: Uuid, conn: &C) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let count = MerchantRolePermission::find()
            .filter(crate::domain::business::merchants::entities::merchant_role_permissions::Column::AuthorizedPermissionId.eq(id))
            .count(conn)
            .await
            .context("检查权限关联角色失败")?;

        Ok(count == 0)
    }

    /// 批量插入授权权限
    pub async fn insert_many<C>(&self, models: Vec<ActiveModel>, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::insert_many(models)
            .exec(conn)
            .await
            .context("批量插入授权权限失败")?;

        Ok(())
    }

    /// 更新授权权限
    pub async fn update<C>(
        &self,
        model: ActiveModel,
        conn: &C,
    ) -> Result<MerchantAuthorizedPermissionModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context("更新授权权限失败")
    }

    /// 根据ID删除授权权限
    pub async fn delete_by_id<C>(&self, id: Uuid, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::delete_by_id(id)
            .exec(conn)
            .await
            .context("删除授权权限失败")?;

        Ok(())
    }

    /// 批量删除授权权限
    pub async fn delete_by_ids<C>(&self, ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = MerchantAuthorizedPermission::delete_many()
            .filter(Column::Id.is_in(ids))
            .exec(conn)
            .await
            .context("批量删除授权权限失败")?;

        Ok(result.rows_affected)
    }

    /// 根据商户ID删除所有权限
    pub async fn delete_by_merchant_id<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = MerchantAuthorizedPermission::delete_many()
            .filter(Column::MerchantId.eq(merchant_id))
            .exec(conn)
            .await
            .context("删除商户权限失败")?;

        Ok(result.rows_affected)
    }

    /// 根据商户ID和权限模板ID删除
    pub async fn delete_by_merchant_and_templates<C>(
        &self,
        merchant_id: i64,
        template_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = MerchantAuthorizedPermission::delete_many()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::PermissionTemplateId.is_in(template_ids))
            .exec(conn)
            .await
            .context("删除商户权限失败")?;

        Ok(result.rows_affected)
    }

    /// 统计商户授权权限数量
    pub async fn count_by_merchant_id<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .count(conn)
            .await
            .context("统计授权权限数量失败")
    }

    /// 统计商户有效授权权限数量
    pub async fn count_active_by_merchant_id<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantAuthorizedPermissionsStatus::Enabled as i32))
            .count(conn)
            .await
            .context("统计有效权限数量失败")
    }

    /// 统计指定权限模板的授权数量
    pub async fn count_by_template_id<C>(&self, template_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find()
            .filter(Column::PermissionTemplateId.eq(template_id))
            .count(conn)
            .await
            .context("统计权限模板授权数量失败")
    }

    /// 统计指定商户和权限模板的授权数量
    pub async fn count_by_merchant_and_template<C>(
        &self,
        merchant_id: i64,
        template_id: Uuid,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantAuthorizedPermission::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::PermissionTemplateId.eq(template_id))
            .filter(Column::Status.eq(MerchantAuthorizedPermissionsStatus::Enabled as i32))
            .count(conn)
            .await
            .context("统计商户权限授权数量失败")
    }

    /// 批量查询权限模板信息
    pub async fn find_templates_by_ids<C>(
        &self,
        template_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<crate::domain::business::merchants::entities::system_permission_templates::Model>>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::find()
            .filter(crate::domain::business::merchants::entities::system_permission_templates::Column::Id.is_in(template_ids))
            .all(conn)
            .await
            .context("查询权限模板失败")
    }
}

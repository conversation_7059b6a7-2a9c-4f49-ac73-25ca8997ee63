use crate::domain::business::merchants::entities::system_permission_templates::{
    ActiveModel, Column, Entity as SystemPermissionTemplate, Model as SystemPermissionTemplateModel,
};
use crate::domain::system::dto::sys_permission_template_request::SysPermissionTemplatePageRequest;
use anyhow::{Context, Result};
use lib_core::PageDataBuilder;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, EntityTrait, PaginatorTrait,
    QueryFilter, QueryOrder, QuerySelect,
};
use uuid::Uuid;

/// 系统权限模板Repository实现
#[derive(Clone, Service)]
pub struct SystemPermissionTemplatesRepository;

impl SystemPermissionTemplatesRepository {
    /// 构建查询条件
    fn build_query_conditions(req: &SysPermissionTemplatePageRequest) -> Condition {
        let mut condition = Condition::all();

        // 权限名称搜索
        if let Some(ref permission_name) = req.permission_name {
            if !permission_name.trim().is_empty() {
                condition = condition.add(Column::PermissionName.contains(permission_name.trim()));
            }
        }

        // 权限编码搜索
        if let Some(ref permission_code) = req.permission_code {
            if !permission_code.trim().is_empty() {
                condition = condition.add(Column::PermissionCode.contains(permission_code.trim()));
            }
        }

        // 父权限ID过滤
        if let Some(parent_id) = req.parent_id {
            condition = condition.add(Column::ParentId.eq(parent_id));
        }

        // 权限类型过滤
        if let Some(permission_type) = req.permission_type {
            condition = condition.add(Column::PermissionType.eq(permission_type));
        }

        // 菜单状态过滤
        if let Some(visible) = req.visible {
            condition = condition.add(Column::Visible.eq(visible));
        }

        condition
    }

    /// 分页查询权限模板
    pub async fn page_by_condition<C>(
        &self,
        req: &SysPermissionTemplatePageRequest,
        conn: &C,
    ) -> Result<PageDataBuilder<SystemPermissionTemplateModel>>
    where
        C: ConnectionTrait,
    {
        let condition = Self::build_query_conditions(req);
        let pagination = Pagination::from_one_based(Some(req.page), Some(req.page_size));

        SystemPermissionTemplate::find()
            .filter(condition)
            .order_by_asc(Column::OrderNum)
            .order_by_asc(Column::PermissionName)
            .page_builder(conn, &pagination)
            .await
            .context("分页查询权限模板失败")
    }

    /// 查询所有权限模板
    pub async fn find_all<C>(&self, conn: &C) -> Result<Vec<SystemPermissionTemplateModel>>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::find()
            .order_by_asc(Column::OrderNum)
            .order_by_asc(Column::PermissionName)
            .all(conn)
            .await
            .context("查询所有权限模板失败")
    }

    /// 查询显示的权限模板
    pub async fn find_visible<C>(&self, conn: &C) -> Result<Vec<SystemPermissionTemplateModel>>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::find()
            .filter(Column::Visible.eq(0))
            .order_by_asc(Column::OrderNum)
            .order_by_asc(Column::PermissionName)
            .all(conn)
            .await
            .context("查询显示的权限模板失败")
    }

    /// 根据IDs查询权限模板
    pub async fn find_by_ids<C>(
        &self,
        template_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<SystemPermissionTemplateModel>>
    where
        C: ConnectionTrait,
    {
        if template_ids.is_empty() {
            return Ok(Vec::new());
        }

        SystemPermissionTemplate::find()
            .filter(Column::Id.is_in(template_ids))
            .order_by_asc(Column::OrderNum)
            .all(conn)
            .await
            .context("根据IDs查询权限模板失败")
    }

    /// 根据ID查询权限模板
    pub async fn find_by_id<C>(
        &self,
        template_id: Uuid,
        conn: &C,
    ) -> Result<Option<SystemPermissionTemplateModel>>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::find_by_id(template_id)
            .one(conn)
            .await
            .context("查询权限模板失败")
    }

    /// 根据父级ID查询子权限模板
    pub async fn find_by_parent_id<C>(
        &self,
        parent_id: Option<Uuid>,
        conn: &C,
    ) -> Result<Vec<SystemPermissionTemplateModel>>
    where
        C: ConnectionTrait,
    {
        let mut query = SystemPermissionTemplate::find();

        if let Some(parent_id) = parent_id {
            query = query.filter(Column::ParentId.eq(parent_id));
        } else {
            query = query.filter(Column::ParentId.is_null());
        }

        query
            .order_by_asc(Column::OrderNum)
            .order_by_asc(Column::PermissionName)
            .all(conn)
            .await
            .context("根据父级ID查询子权限模板失败")
    }

    /// 根据权限类型查询权限模板
    pub async fn find_by_permission_type<C>(
        &self,
        permission_type: i32,
        conn: &C,
    ) -> Result<Vec<SystemPermissionTemplateModel>>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::find()
            .filter(Column::PermissionType.eq(permission_type))
            .order_by_asc(Column::OrderNum)
            .all(conn)
            .await
            .context("根据权限类型查询权限模板失败")
    }

    /// 根据权限编码查询权限模板
    pub async fn find_by_permission_code<C>(
        &self,
        permission_code: &str,
        conn: &C,
    ) -> Result<Option<SystemPermissionTemplateModel>>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::find()
            .filter(Column::PermissionCode.eq(permission_code))
            .one(conn)
            .await
            .context("根据权限编码查询权限模板失败")
    }

    /// 检查权限模板是否存在
    pub async fn exists_by_id<C>(&self, template_id: Uuid, conn: &C) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let count = SystemPermissionTemplate::find()
            .filter(Column::Id.eq(template_id))
            .count(conn)
            .await
            .context("检查权限模板是否存在失败")?;

        Ok(count > 0)
    }

    /// 检查权限编码是否存在
    pub async fn exists_by_permission_code<C>(
        &self,
        permission_code: &str,
        exclude_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut condition = Condition::all().add(Column::PermissionCode.eq(permission_code));

        if let Some(id) = exclude_id {
            condition = condition.add(Column::Id.ne(id));
        }

        let count = SystemPermissionTemplate::find()
            .filter(condition)
            .count(conn)
            .await
            .context("检查权限编码是否存在失败")?;

        Ok(count > 0)
    }

    /// 检查是否有子权限
    pub async fn has_children<C>(&self, template_id: Uuid, conn: &C) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let count = SystemPermissionTemplate::find()
            .filter(Column::ParentId.eq(template_id))
            .count(conn)
            .await
            .context("检查子权限失败")?;

        Ok(count > 0)
    }

    /// 创建权限模板
    pub async fn create<C>(&self, model: ActiveModel, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::insert(model)
            .exec(conn)
            .await
            .context("创建权限模板失败")?;

        Ok(())
    }

    /// 更新权限模板
    pub async fn update<C>(
        &self,
        model: ActiveModel,
        conn: &C,
    ) -> Result<SystemPermissionTemplateModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context("更新权限模板失败")
    }

    /// 根据ID删除权限模板
    pub async fn delete_by_id<C>(&self, template_id: Uuid, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::delete_by_id(template_id)
            .exec(conn)
            .await
            .context("删除权限模板失败")?;

        Ok(())
    }

    /// 批量删除权限模板
    pub async fn delete_by_ids<C>(&self, template_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if template_ids.is_empty() {
            return Ok(0);
        }

        let result = SystemPermissionTemplate::delete_many()
            .filter(Column::Id.is_in(template_ids))
            .exec(conn)
            .await
            .context("批量删除权限模板失败")?;

        Ok(result.rows_affected)
    }

    /// 统计权限模板总数
    pub async fn count_all<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::find()
            .count(conn)
            .await
            .context("统计权限模板总数失败")
    }

    /// 统计显示的权限模板数量
    pub async fn count_visible<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::find()
            .filter(Column::Visible.eq(0))
            .count(conn)
            .await
            .context("统计显示权限模板数量失败")
    }

    /// 统计隐藏的权限模板数量
    pub async fn count_hidden<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::find()
            .filter(Column::Visible.eq(1))
            .count(conn)
            .await
            .context("统计隐藏权限模板数量失败")
    }

    /// 根据权限类型统计数量
    pub async fn count_by_permission_type<C>(&self, permission_type: i32, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::find()
            .filter(Column::PermissionType.eq(permission_type))
            .count(conn)
            .await
            .context("根据权限类型统计数量失败")
    }

    /// 根据父ID统计子权限数量
    pub async fn count_by_parent_id<C>(&self, parent_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        SystemPermissionTemplate::find()
            .filter(Column::ParentId.eq(parent_id))
            .count(conn)
            .await
            .context("统计子权限数量失败")
    }

    /// 根据父IDs批量统计子权限数量
    pub async fn count_by_parent_ids<C>(&self, parent_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if parent_ids.is_empty() {
            return Ok(0);
        }

        SystemPermissionTemplate::find()
            .filter(Column::ParentId.is_in(parent_ids))
            .count(conn)
            .await
            .context("批量统计子权限数量失败")
    }

    /// 根据权限模板ID查询权限编码列表
    /// 单一职责：只负责查询权限编码
    pub async fn find_permission_codes_by_template_ids<C>(
        &self,
        template_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<String>>
    where
        C: ConnectionTrait,
    {
        if template_ids.is_empty() {
            return Ok(Vec::new());
        }

        let permission_codes: Vec<String> = SystemPermissionTemplate::find()
            .filter(Column::Id.is_in(template_ids))
            .select_only()
            .column(Column::PermissionCode)
            .into_tuple()
            .all(conn)
            .await
            .context("查询权限编码失败")?;

        Ok(permission_codes)
    }
}

use crate::domain::business::merchants::entities::{
    merchant_authorized_permissions,
    merchant_role_permissions::{
        ActiveModel, Column, Entity as MerchantRolePermission, Model as MerchantRolePermissionModel,
    },
};
use anyhow::{Context, Result, ensure};
use lib_core::app::plugin::Service as ServiceTrait;
use lib_macros::Service;
use sea_orm::{
    ColumnTrait, ConnectionTrait, EntityTrait, PaginatorTrait, QueryFilter, QuerySelect,
};
use uuid::Uuid;

/// 商户角色权限Repository实现
#[derive(Clone, Service)]
pub struct MerchantRolePermissionsRepository;

impl MerchantRolePermissionsRepository {
    /// 查询角色权限关联
    pub async fn find_by_role_id<C>(
        &self,
        role_id: Uuid,
        conn: &C,
    ) -> Result<Vec<MerchantRolePermissionModel>>
    where
        C: ConnectionTrait,
    {
        MerchantRolePermission::find()
            .filter(Column::RoleId.eq(role_id))
            .all(conn)
            .await
            .context("查询角色权限关联失败")
    }

    /// 查询指定授权权限的角色关联数量
    pub async fn count_by_authorized_permission_id<C>(
        &self,
        authorized_permission_id: Uuid,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantRolePermission::find()
            .filter(Column::AuthorizedPermissionId.eq(authorized_permission_id))
            .count(conn)
            .await
            .context("统计权限关联角色数量失败")
    }

    /// 查询指定授权权限的角色关联
    pub async fn find_by_authorized_permission_ids<C>(
        &self,
        authorized_permission_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<MerchantRolePermissionModel>>
    where
        C: ConnectionTrait,
    {
        ensure!(
            !authorized_permission_ids.is_empty(),
            "授权权限ID列表不能为空"
        );

        MerchantRolePermission::find()
            .filter(Column::AuthorizedPermissionId.is_in(authorized_permission_ids))
            .all(conn)
            .await
            .context("查询权限关联角色失败")
    }

    /// 删除角色权限关联
    pub async fn delete_by_role_id<C>(&self, role_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = MerchantRolePermission::delete_many()
            .filter(Column::RoleId.eq(role_id))
            .exec(conn)
            .await
            .context("删除角色权限关联失败")?;

        Ok(result.rows_affected)
    }

    /// 批量删除角色权限关联
    pub async fn delete_by_role_ids<C>(&self, role_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        ensure!(!role_ids.is_empty(), "角色ID列表不能为空");

        let result = MerchantRolePermission::delete_many()
            .filter(Column::RoleId.is_in(role_ids))
            .exec(conn)
            .await
            .context("批量删除角色权限关联失败")?;

        Ok(result.rows_affected)
    }

    /// 删除指定授权权限的角色关联
    pub async fn delete_by_authorized_permission_ids<C>(
        &self,
        authorized_permission_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        ensure!(
            !authorized_permission_ids.is_empty(),
            "授权权限ID列表不能为空"
        );

        let result = MerchantRolePermission::delete_many()
            .filter(Column::AuthorizedPermissionId.is_in(authorized_permission_ids))
            .exec(conn)
            .await
            .context("删除权限关联角色失败")?;

        Ok(result.rows_affected)
    }

    /// 批量插入角色权限关联
    pub async fn insert_many<C>(&self, permissions: Vec<ActiveModel>, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        if permissions.is_empty() {
            return Ok(());
        }

        MerchantRolePermission::insert_many(permissions)
            .exec(conn)
            .await
            .context("批量插入角色权限关联失败")?;

        Ok(())
    }

    /// 根据角色ID查询权限模板ID列表
    /// 单一职责：只负责查询角色对应的权限模板ID
    pub async fn find_permission_template_ids_by_role_ids<C>(
        &self,
        role_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<Uuid>>
    where
        C: ConnectionTrait,
    {
        if role_ids.is_empty() {
            return Ok(Vec::new());
        }

        let template_ids: Vec<Uuid> = MerchantRolePermission::find()
            .filter(Column::RoleId.is_in(role_ids))
            .inner_join(merchant_authorized_permissions::Entity)
            .select_only()
            .column(merchant_authorized_permissions::Column::PermissionTemplateId)
            .into_tuple()
            .all(conn)
            .await
            .context("查询角色权限模板ID失败")?;

        Ok(template_ids)
    }
}

use crate::domain::system::entities::user_roles::{
    ActiveModel, Column, Entity as UserRoles, Model as UserRoleModel,
};
use anyhow::{Context, Result};
use lib_core::app::plugin::Service as ServiceTrait;
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, ConnectionTrait, EntityTrait, PaginatorTrait, QueryFilter,
    RelationTrait,
};
use std::collections::HashMap;
use uuid::Uuid;

/// 系统用户角色Repository实现
#[derive(Clone, Service)]
pub struct SysUserRoleRepository;

impl SysUserRoleRepository {
    /// 根据用户ID查询角色关联
    pub async fn find_by_user_id<C>(&self, user_id: Uuid, conn: &C) -> Result<Vec<UserRoleModel>>
    where
        C: ConnectionTrait,
    {
        UserRoles::find()
            .filter(Column::UserId.eq(user_id))
            .all(conn)
            .await
            .context("查询用户角色关联失败")
    }

    /// 根据用户ID获取角色编码列表
    pub async fn get_user_role_codes<C>(&self, user_id: Uuid, conn: &C) -> Result<Vec<String>>
    where
        C: ConnectionTrait,
    {
        use crate::domain::system::entities::roles::Entity as Roles;
        use sea_orm::{JoinType, QuerySelect};

        let role_codes: Vec<String> = Roles::find()
            .select_only()
            .column(crate::domain::system::entities::roles::Column::RoleCode)
            .join(
                JoinType::InnerJoin,
                crate::domain::system::entities::roles::Relation::UserRoles.def(),
            )
            .filter(Column::UserId.eq(user_id))
            .filter(crate::domain::system::entities::roles::Column::RoleCode.is_not_null())
            .into_tuple::<(Option<String>,)>()
            .all(conn)
            .await
            .context("查询用户角色编码失败")?
            .into_iter()
            .filter_map(|(role_code,)| role_code)
            .collect();

        Ok(role_codes)
    }

    /// 根据用户ID获取角色信息列表
    pub async fn get_user_roles<C>(
        &self,
        user_id: Uuid,
        conn: &C,
    ) -> Result<Vec<crate::domain::system::vo::SysRoleSimpleResponse>>
    where
        C: ConnectionTrait,
    {
        use crate::domain::system::entities::roles::Entity as Roles;
        use sea_orm::{JoinType, QuerySelect};

        let roles: Vec<crate::domain::system::vo::SysRoleSimpleResponse> = Roles::find()
            .select_only()
            .columns([
                crate::domain::system::entities::roles::Column::Id,
                crate::domain::system::entities::roles::Column::Name,
                crate::domain::system::entities::roles::Column::RoleCode,
                crate::domain::system::entities::roles::Column::Description,
            ])
            .join(
                JoinType::InnerJoin,
                crate::domain::system::entities::roles::Relation::UserRoles.def(),
            )
            .filter(Column::UserId.eq(user_id))
            .into_tuple::<(Uuid, String, Option<String>, Option<String>)>()
            .all(conn)
            .await
            .context("查询用户角色失败")?
            .into_iter()
            .map(|(id, name, role_code, description)| {
                crate::domain::system::vo::SysRoleSimpleResponse {
                    id,
                    name,
                    role_code,
                    description,
                }
            })
            .collect();

        Ok(roles)
    }

    /// 根据角色ID查询用户关联
    pub async fn find_by_role_id<C>(&self, role_id: Uuid, conn: &C) -> Result<Vec<UserRoleModel>>
    where
        C: ConnectionTrait,
    {
        UserRoles::find()
            .filter(Column::RoleId.eq(role_id))
            .all(conn)
            .await
            .context("查询角色用户关联失败")
    }

    /// 根据用户IDs查询角色关联
    pub async fn find_by_user_ids<C>(
        &self,
        user_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<UserRoleModel>>
    where
        C: ConnectionTrait,
    {
        if user_ids.is_empty() {
            return Ok(Vec::new());
        }

        UserRoles::find()
            .filter(Column::UserId.is_in(user_ids))
            .all(conn)
            .await
            .context("批量查询用户角色关联失败")
    }

    /// 根据角色IDs查询用户关联
    pub async fn find_by_role_ids<C>(
        &self,
        role_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<UserRoleModel>>
    where
        C: ConnectionTrait,
    {
        if role_ids.is_empty() {
            return Ok(Vec::new());
        }

        UserRoles::find()
            .filter(Column::RoleId.is_in(role_ids))
            .all(conn)
            .await
            .context("批量查询角色用户关联失败")
    }

    /// 统计用户角色关联数量
    pub async fn count_by_user_id<C>(&self, user_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        UserRoles::find()
            .filter(Column::UserId.eq(user_id))
            .count(conn)
            .await
            .context("统计用户角色关联数量失败")
    }

    /// 统计角色用户关联数量
    pub async fn count_by_role_id<C>(&self, role_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        UserRoles::find()
            .filter(Column::RoleId.eq(role_id))
            .count(conn)
            .await
            .context("统计角色用户关联数量失败")
    }

    /// 批量统计角色用户数量
    pub async fn batch_count_users_by_role_ids<C>(
        &self,
        role_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<HashMap<Uuid, u64>>
    where
        C: ConnectionTrait,
    {
        if role_ids.is_empty() {
            return Ok(HashMap::new());
        }

        use sea_orm::{FromQueryResult, QuerySelect};

        #[derive(FromQueryResult)]
        struct RoleUserCount {
            role_id: Uuid,
            user_count: i64,
        }

        let results = UserRoles::find()
            .select_only()
            .column_as(Column::RoleId, "role_id")
            .column_as(Column::RoleId.count(), "user_count")
            .filter(Column::RoleId.is_in(role_ids.iter().copied()))
            .group_by(Column::RoleId)
            .into_model::<RoleUserCount>()
            .all(conn)
            .await
            .context("批量统计角色用户数量失败")?;

        let mut counts = HashMap::new();

        // 先初始化所有角色为0
        for role_id in role_ids {
            counts.insert(role_id, 0);
        }

        // 然后设置实际统计值
        for result in results {
            counts.insert(result.role_id, result.user_count as u64);
        }

        Ok(counts)
    }

    /// 检查用户角色关联是否存在
    pub async fn exists_by_user_and_role<C>(
        &self,
        user_id: Uuid,
        role_id: Uuid,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let count = UserRoles::find()
            .filter(Column::UserId.eq(user_id))
            .filter(Column::RoleId.eq(role_id))
            .count(conn)
            .await
            .context("检查用户角色关联失败")?;

        Ok(count > 0)
    }

    /// 创建用户角色关联
    pub async fn create<C>(&self, model: ActiveModel, conn: &C) -> Result<UserRoleModel>
    where
        C: ConnectionTrait,
    {
        model.insert(conn).await.context("创建用户角色关联失败")
    }

    /// 批量插入用户角色关联
    pub async fn insert_many<C>(&self, models: Vec<ActiveModel>, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        if models.is_empty() {
            return Ok(());
        }

        UserRoles::insert_many(models)
            .exec(conn)
            .await
            .context("批量插入用户角色关联失败")?;

        Ok(())
    }

    /// 根据用户ID删除角色关联
    pub async fn delete_by_user_id<C>(&self, user_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = UserRoles::delete_many()
            .filter(Column::UserId.eq(user_id))
            .exec(conn)
            .await
            .context("删除用户角色关联失败")?;

        Ok(result.rows_affected)
    }

    /// 根据角色ID删除用户关联
    pub async fn delete_by_role_id<C>(&self, role_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = UserRoles::delete_many()
            .filter(Column::RoleId.eq(role_id))
            .exec(conn)
            .await
            .context("删除角色用户关联失败")?;

        Ok(result.rows_affected)
    }

    /// 根据用户IDs批量删除角色关联
    pub async fn delete_by_user_ids<C>(&self, user_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if user_ids.is_empty() {
            return Ok(0);
        }

        let result = UserRoles::delete_many()
            .filter(Column::UserId.is_in(user_ids))
            .exec(conn)
            .await
            .context("批量删除用户角色关联失败")?;

        Ok(result.rows_affected)
    }

    /// 根据角色IDs批量删除用户关联
    pub async fn delete_by_role_ids<C>(&self, role_ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if role_ids.is_empty() {
            return Ok(0);
        }

        let result = UserRoles::delete_many()
            .filter(Column::RoleId.is_in(role_ids))
            .exec(conn)
            .await
            .context("批量删除角色用户关联失败")?;

        Ok(result.rows_affected)
    }

    /// 删除特定用户角色关联
    pub async fn delete_by_user_and_role<C>(
        &self,
        user_id: Uuid,
        role_id: Uuid,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = UserRoles::delete_many()
            .filter(Column::UserId.eq(user_id))
            .filter(Column::RoleId.eq(role_id))
            .exec(conn)
            .await
            .context("删除用户角色关联失败")?;

        Ok(result.rows_affected)
    }
}

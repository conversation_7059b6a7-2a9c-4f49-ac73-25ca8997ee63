use crate::domain::business::merchants::entities::merchant_categories::{
    ActiveModel, Column, Entity as MerchantCategory, MerchantCategoryStatus,
    Model as MerchantCategoryModel,
};
use crate::domain::system::dto::sys_merchant_category_request::SysMerchantCategoryPageRequest;
use anyhow::{Context, Result};
use lib_core::PageDataBuilder;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, EntityTrait, PaginatorTrait,
    QueryFilter, QueryOrder,
};
use uuid::Uuid;

/// 商户分类Repository实现
#[derive(Clone, Service)]
pub struct MerchantCategoryRepository;

impl MerchantCategoryRepository {
    /// 构建查询条件
    fn build_query_conditions(req: &SysMerchantCategoryPageRequest) -> Condition {
        let mut condition = Condition::all();

        // 分类名称模糊查询
        if let Some(ref category_name) = req.category_name {
            if !category_name.trim().is_empty() {
                condition = condition.add(Column::CategoryName.contains(category_name.trim()));
            }
        }

        // 分类编码模糊查询
        if let Some(ref category_code) = req.category_code {
            if !category_code.trim().is_empty() {
                condition = condition.add(Column::CategoryCode.contains(category_code.trim()));
            }
        }

        // 状态精确匹配
        if let Some(status) = req.status {
            condition = condition.add(Column::Status.eq(status));
        }

        // 创建时间范围查询
        if let Some(ref created_start) = req.created_start {
            if let Ok(start_date) = chrono::DateTime::parse_from_str(
                &format!("{} 00:00:00 +08:00", created_start),
                "%Y-%m-%d %H:%M:%S %z",
            ) {
                condition = condition.add(Column::CreatedDate.gte(start_date));
            }
        }

        if let Some(ref created_end) = req.created_end {
            if let Ok(end_date) = chrono::DateTime::parse_from_str(
                &format!("{} 23:59:59 +08:00", created_end),
                "%Y-%m-%d %H:%M:%S %z",
            ) {
                condition = condition.add(Column::CreatedDate.lte(end_date));
            }
        }

        condition
    }

    /// 分页查询商户分类列表
    pub async fn page_by_condition<C>(
        &self,
        req: &SysMerchantCategoryPageRequest,
        conn: &C,
    ) -> Result<PageDataBuilder<MerchantCategoryModel>>
    where
        C: ConnectionTrait,
    {
        let condition = Self::build_query_conditions(req);
        let pagination = Pagination::from_one_based(req.page, req.page_size);

        MerchantCategory::find()
            .filter(condition)
            .order_by_asc(Column::SortOrder)
            .order_by_asc(Column::CategoryName)
            .page_builder(conn, &pagination)
            .await
            .with_context(|| format!("分页查询商户分类失败: {:?}", req))
    }

    /// 根据ID查询商户分类
    pub async fn find_by_id<C>(&self, id: Uuid, conn: &C) -> Result<Option<MerchantCategoryModel>>
    where
        C: ConnectionTrait,
    {
        MerchantCategory::find_by_id(id)
            .one(conn)
            .await
            .context("查询商户分类失败")
    }

    /// 根据分类名称查询（排除指定ID）
    pub async fn find_by_name<C>(
        &self,
        category_name: &str,
        exclude_id: Option<Uuid>,
        conn: &C,
    ) -> Result<Option<MerchantCategoryModel>>
    where
        C: ConnectionTrait,
    {
        let mut query = MerchantCategory::find().filter(Column::CategoryName.eq(category_name));

        if let Some(exclude_id) = exclude_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        query.one(conn).await.context("根据名称查询商户分类失败")
    }

    /// 根据分类编码查询（排除指定ID）
    pub async fn find_by_code<C>(
        &self,
        category_code: &str,
        exclude_id: Option<Uuid>,
        conn: &C,
    ) -> Result<Option<MerchantCategoryModel>>
    where
        C: ConnectionTrait,
    {
        let mut query = MerchantCategory::find().filter(Column::CategoryCode.eq(category_code));

        if let Some(exclude_id) = exclude_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        query.one(conn).await.context("根据编码查询商户分类失败")
    }

    /// 查询所有启用状态的分类
    pub async fn find_all_enabled<C>(&self, conn: &C) -> Result<Vec<MerchantCategoryModel>>
    where
        C: ConnectionTrait,
    {
        MerchantCategory::find()
            .filter(Column::Status.eq(MerchantCategoryStatus::Enabled))
            .order_by_asc(Column::SortOrder)
            .order_by_asc(Column::CategoryName)
            .all(conn)
            .await
            .context("查询启用状态的商户分类失败")
    }

    /// 查询所有分类
    pub async fn find_all<C>(&self, conn: &C) -> Result<Vec<MerchantCategoryModel>>
    where
        C: ConnectionTrait,
    {
        MerchantCategory::find()
            .order_by_asc(Column::SortOrder)
            .order_by_asc(Column::CategoryName)
            .all(conn)
            .await
            .context("查询所有商户分类失败")
    }

    /// 创建商户分类
    pub async fn create<C>(&self, model: ActiveModel, conn: &C) -> Result<MerchantCategoryModel>
    where
        C: ConnectionTrait,
    {
        model.insert(conn).await.context("创建商户分类失败")
    }

    /// 更新商户分类
    pub async fn update<C>(&self, model: ActiveModel, conn: &C) -> Result<MerchantCategoryModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context("更新商户分类失败")
    }

    /// 根据ID删除商户分类
    pub async fn delete_by_id<C>(&self, id: Uuid, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        MerchantCategory::delete_by_id(id)
            .exec(conn)
            .await
            .context("删除商户分类失败")?;

        Ok(())
    }

    /// 检查分类名称是否存在
    pub async fn exists_by_name<C>(
        &self,
        category_name: &str,
        exclude_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = MerchantCategory::find().filter(Column::CategoryName.eq(category_name));

        if let Some(exclude_id) = exclude_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query
            .count(conn)
            .await
            .context("检查分类名称是否存在失败")?;

        Ok(count > 0)
    }

    /// 检查分类编码是否存在
    pub async fn exists_by_code<C>(
        &self,
        category_code: &str,
        exclude_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let mut query = MerchantCategory::find().filter(Column::CategoryCode.eq(category_code));

        if let Some(exclude_id) = exclude_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query
            .count(conn)
            .await
            .context("检查分类编码是否存在失败")?;

        Ok(count > 0)
    }

    /// 检查分类下是否有商户
    pub async fn has_merchants<C>(&self, category_id: Uuid, conn: &C) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let count = crate::domain::business::merchants::entities::merchants::Entity::find()
            .filter(
                crate::domain::business::merchants::entities::merchants::Column::CategoryId
                    .eq(category_id),
            )
            .count(conn)
            .await
            .context("查询分类下商户数量失败")?;

        Ok(count > 0)
    }

    /// 统计分类下的商户数量
    pub async fn count_merchants<C>(&self, category_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        crate::domain::business::merchants::entities::merchants::Entity::find()
            .filter(
                crate::domain::business::merchants::entities::merchants::Column::CategoryId
                    .eq(category_id),
            )
            .count(conn)
            .await
            .context("统计分类下商户数量失败")
    }

    /// 统计分类下的启用商户数量
    pub async fn count_active_merchants<C>(&self, category_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        crate::domain::business::merchants::entities::merchants::Entity::find()
            .filter(
                crate::domain::business::merchants::entities::merchants::Column::CategoryId
                    .eq(category_id),
            )
            .filter(crate::domain::business::merchants::entities::merchants::Column::Status.eq(1))
            .count(conn)
            .await
            .context("统计分类下启用商户数量失败")
    }
}

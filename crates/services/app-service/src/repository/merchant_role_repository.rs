use crate::domain::business::merchants::entities::merchant_role_permissions::Entity as MerchantRolePermission;
use crate::domain::business::merchants::entities::merchant_roles::{
    ActiveModel, Column, Entity as MerchantRole, MerchantRoleStatus, MerchantRoleType,
    Model as MerchantRoleModel,
};
use crate::domain::business::merchants::entities::merchant_user_roles::Entity as MerchantUserRole;
use crate::domain::system::dto::sys_merchant_role_request::SysMerchantRolePageRequest;
use crate::utils::datetime::DateTimeUtils;
use anyhow::{Context, Result, ensure};
use lib_core::PageDataBuilder;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, EntityT<PERSON>t, PaginatorTrait,
    Query<PERSON>ilter, QueryOrder,
};
use uuid::Uuid;

/// 商户角色Repository实现
#[derive(Clone, Service)]
pub struct MerchantRoleRepository;

impl MerchantRoleRepository {
    /// 构建查询条件
    fn build_query_conditions(req: &SysMerchantRolePageRequest) -> Condition {
        let mut condition = Condition::all();

        // 商户ID条件
        condition = condition.add(Column::MerchantId.eq(req.merchant_id));

        // 角色名称模糊查询
        if let Some(role_name) = &req.role_name {
            if !role_name.trim().is_empty() {
                condition = condition.add(Column::RoleName.contains(role_name.trim()));
            }
        }

        // 角色编码模糊查询
        if let Some(role_code) = &req.role_code {
            if !role_code.trim().is_empty() {
                condition = condition.add(Column::RoleCode.contains(role_code.trim()));
            }
        }

        // 角色类型精确匹配
        if let Some(role_type) = req.role_type {
            condition = condition.add(Column::RoleType.eq(role_type));
        }

        // 状态精确匹配
        if let Some(status) = req.status {
            condition = condition.add(Column::Status.eq(status));
        }

        // 是否默认精确匹配
        if let Some(is_default) = req.is_default {
            condition = condition.add(Column::IsDefault.eq(is_default));
        }

        // 创建时间范围查询
        if let Some(created_start) = &req.created_start {
            if let Ok(start_time) = DateTimeUtils::parse_range_start(created_start) {
                condition = condition.add(Column::CreatedDate.gte(start_time));
            }
        }

        if let Some(created_end) = &req.created_end {
            if let Ok(end_time) = DateTimeUtils::parse_range_end(created_end) {
                condition = condition.add(Column::CreatedDate.lte(end_time));
            }
        }

        condition
    }

    /// 分页查询商户角色
    pub async fn page_by_condition<C>(
        &self,
        req: &SysMerchantRolePageRequest,
        conn: &C,
    ) -> Result<PageDataBuilder<MerchantRoleModel>>
    where
        C: ConnectionTrait,
    {
        let condition = Self::build_query_conditions(req);
        let pagination = Pagination::from_one_based(req.page, req.page_size);

        MerchantRole::find()
            .filter(condition)
            .order_by_desc(Column::CreatedDate)
            .page_builder(conn, &pagination)
            .await
            .context("分页查询商户角色失败")
    }

    /// 根据ID查询商户角色
    pub async fn find_by_id<C>(&self, id: Uuid, conn: &C) -> Result<Option<MerchantRoleModel>>
    where
        C: ConnectionTrait,
    {
        MerchantRole::find_by_id(id)
            .one(conn)
            .await
            .context("查询商户角色失败")
    }

    /// 根据商户ID查询所有角色
    pub async fn find_by_merchant_id<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Vec<MerchantRoleModel>>
    where
        C: ConnectionTrait,
    {
        MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .all(conn)
            .await
            .context("查询商户角色列表失败")
    }

    /// 根据商户ID查询启用的角色
    pub async fn find_enabled_by_merchant_id<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Vec<MerchantRoleModel>>
    where
        C: ConnectionTrait,
    {
        MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantRoleStatus::Enabled))
            .order_by_asc(Column::RoleType)
            .order_by_asc(Column::RoleName)
            .all(conn)
            .await
            .context("查询启用角色列表失败")
    }

    /// 根据IDs查询角色列表
    pub async fn find_by_ids<C>(&self, ids: Vec<Uuid>, conn: &C) -> Result<Vec<MerchantRoleModel>>
    where
        C: ConnectionTrait,
    {
        ensure!(!ids.is_empty(), "角色ID列表不能为空");

        MerchantRole::find()
            .filter(Column::Id.is_in(ids))
            .all(conn)
            .await
            .context("批量查询角色失败")
    }

    /// 根据IDs和商户ID查询角色列表
    pub async fn find_by_ids_and_merchant<C>(
        &self,
        ids: Vec<Uuid>,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Vec<MerchantRoleModel>>
    where
        C: ConnectionTrait,
    {
        ensure!(!ids.is_empty(), "角色ID列表不能为空");

        MerchantRole::find()
            .filter(Column::Id.is_in(ids))
            .filter(Column::MerchantId.eq(merchant_id))
            .all(conn)
            .await
            .context("批量查询角色失败")
    }

    /// 创建角色
    pub async fn create<C>(&self, model: ActiveModel, conn: &C) -> Result<MerchantRoleModel>
    where
        C: ConnectionTrait,
    {
        model.insert(conn).await.context("创建商户角色失败")
    }

    /// 更新角色
    pub async fn update<C>(&self, model: ActiveModel, conn: &C) -> Result<MerchantRoleModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context("更新商户角色失败")
    }

    /// 根据ID删除角色
    pub async fn delete_by_id<C>(&self, id: Uuid, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        MerchantRole::delete_by_id(id)
            .exec(conn)
            .await
            .context("删除商户角色失败")?;

        Ok(())
    }

    /// 批量删除角色
    pub async fn delete_by_ids<C>(&self, ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        ensure!(!ids.is_empty(), "删除的角色ID列表不能为空");

        let result = MerchantRole::delete_many()
            .filter(Column::Id.is_in(ids))
            .exec(conn)
            .await
            .context("批量删除商户角色失败")?;

        Ok(result.rows_affected)
    }

    /// 检查角色名称是否存在
    pub async fn exists_by_name<C>(
        &self,
        merchant_id: i64,
        role_name: &str,
        exclude_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        ensure!(!role_name.trim().is_empty(), "角色名称不能为空");

        let mut query = MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::RoleName.eq(role_name));

        if let Some(exclude_id) = exclude_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query
            .count(conn)
            .await
            .context("检查角色名称是否存在失败")?;

        Ok(count > 0)
    }

    /// 检查角色编码是否存在
    pub async fn exists_by_code<C>(
        &self,
        merchant_id: i64,
        role_code: &str,
        exclude_id: Option<Uuid>,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        ensure!(!role_code.trim().is_empty(), "角色编码不能为空");

        let mut query = MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::RoleCode.eq(role_code));

        if let Some(exclude_id) = exclude_id {
            query = query.filter(Column::Id.ne(exclude_id));
        }

        let count = query
            .count(conn)
            .await
            .context("检查角色编码是否存在失败")?;

        Ok(count > 0)
    }

    /// 检查角色是否可以删除（没有分配给用户）
    pub async fn is_deletable<C>(&self, role_id: Uuid, conn: &C) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let count = MerchantUserRole::find()
            .filter(
                crate::domain::business::merchants::entities::merchant_user_roles::Column::RoleId
                    .eq(role_id),
            )
            .count(conn)
            .await
            .context("检查角色是否可删除失败")?;

        Ok(count == 0)
    }

    /// 检查是否有默认角色
    pub async fn count_default_roles<C>(&self, ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        ensure!(!ids.is_empty(), "角色ID列表不能为空");

        MerchantRole::find()
            .filter(Column::Id.is_in(ids))
            .filter(Column::IsDefault.eq(true))
            .count(conn)
            .await
            .context("检查默认角色数量失败")
    }

    /// 检查是否有角色已分配给用户
    pub async fn count_assigned_roles<C>(&self, ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        ensure!(!ids.is_empty(), "角色ID列表不能为空");

        MerchantUserRole::find()
            .filter(
                crate::domain::business::merchants::entities::merchant_user_roles::Column::RoleId
                    .is_in(ids),
            )
            .count(conn)
            .await
            .context("检查角色分配情况失败")
    }

    /// 统计商户角色数量
    pub async fn count_by_merchant_id<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .count(conn)
            .await
            .context("统计商户角色总数失败")
    }

    /// 统计管理员角色数量
    pub async fn count_admin_roles<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::RoleType.eq(MerchantRoleType::SystemCustom))
            .count(conn)
            .await
            .context("统计管理员角色数失败")
    }

    /// 统计自定义角色数量
    pub async fn count_custom_roles<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::RoleType.eq(MerchantRoleType::MerchantCustom))
            .count(conn)
            .await
            .context("统计自定义角色数失败")
    }

    /// 统计启用角色数量
    pub async fn count_active_roles<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantRoleStatus::Enabled))
            .count(conn)
            .await
            .context("统计启用角色数失败")
    }

    /// 统计禁用角色数量
    pub async fn count_disabled_roles<C>(&self, merchant_id: i64, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantRoleStatus::Disabled))
            .count(conn)
            .await
            .context("统计禁用角色数失败")
    }

    /// 查询商户管理员角色
    pub async fn find_admin_role<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Option<MerchantRoleModel>>
    where
        C: ConnectionTrait,
    {
        MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::RoleType.eq(MerchantRoleType::SystemCustom))
            .filter(Column::IsDefault.eq(false))
            .one(conn)
            .await
            .context("查询管理员角色失败")
    }

    /// 查询商户启用的管理员角色
    pub async fn find_enabled_admin_role<C>(
        &self,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Option<MerchantRoleModel>>
    where
        C: ConnectionTrait,
    {
        MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::RoleType.eq(MerchantRoleType::SystemCustom))
            .filter(Column::Status.eq(MerchantRoleStatus::Enabled))
            .one(conn)
            .await
            .context("查询启用的管理员角色失败")
    }

    /// 批量查询商户的管理员角色
    pub async fn find_admin_roles_by_merchant_ids<C>(
        &self,
        merchant_ids: Vec<i64>,
        conn: &C,
    ) -> Result<Vec<MerchantRoleModel>>
    where
        C: ConnectionTrait,
    {
        ensure!(!merchant_ids.is_empty(), "商户ID列表不能为空");

        MerchantRole::find()
            .filter(Column::MerchantId.is_in(merchant_ids))
            .filter(Column::RoleType.eq(MerchantRoleType::SystemCustom))
            .filter(Column::Status.eq(MerchantRoleStatus::Enabled))
            .all(conn)
            .await
            .context("批量查询管理员角色失败")
    }

    /// 生成角色编码
    pub async fn generate_role_code<C>(&self, merchant_id: i64, conn: &C) -> Result<String>
    where
        C: ConnectionTrait,
    {
        let max_role = MerchantRole::find()
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::RoleCode.is_not_null())
            .order_by_desc(Column::RoleCode)
            .one(conn)
            .await
            .context("查询最大角色编码失败")?;

        let sequence = match max_role {
            Some(role) if role.role_code.is_some() => {
                // 提取编码中的数字部分
                let code = role.role_code.unwrap();
                if let Some(num_str) = code.strip_prefix(&format!("M{}_R", merchant_id)) {
                    num_str.parse::<i32>().unwrap_or(0) + 1
                } else {
                    1
                }
            }
            _ => 1,
        };

        let final_code = format!("M{}_R{:03}", merchant_id, sequence);
        Ok(final_code)
    }

    /// 删除角色权限关联
    pub async fn delete_role_permissions<C>(&self, role_id: Uuid, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        let result = MerchantRolePermission::delete_many()
            .filter(crate::domain::business::merchants::entities::merchant_role_permissions::Column::RoleId.eq(role_id))
            .exec(conn)
            .await
            .context("删除角色权限关联失败")?;

        Ok(result.rows_affected)
    }

    /// 批量删除角色权限关联
    pub async fn delete_role_permissions_by_role_ids<C>(
        &self,
        role_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        ensure!(!role_ids.is_empty(), "角色ID列表不能为空");

        let result = MerchantRolePermission::delete_many()
            .filter(crate::domain::business::merchants::entities::merchant_role_permissions::Column::RoleId.is_in(role_ids))
            .exec(conn)
            .await
            .context("批量删除角色权限关联失败")?;

        Ok(result.rows_affected)
    }

    /// 删除指定权限的角色关联
    pub async fn delete_role_permissions_by_permission_ids<C>(
        &self,
        role_id: Uuid,
        permission_ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        ensure!(!permission_ids.is_empty(), "权限ID列表不能为空");

        let result = MerchantRolePermission::delete_many()
            .filter(crate::domain::business::merchants::entities::merchant_role_permissions::Column::RoleId.eq(role_id))
            .filter(crate::domain::business::merchants::entities::merchant_role_permissions::Column::AuthorizedPermissionId.is_in(permission_ids))
            .exec(conn)
            .await
            .context("删除指定权限的角色关联失败")?;

        Ok(result.rows_affected)
    }

    /// 查询角色权限关联
    pub async fn find_role_permissions<C>(
        &self,
        role_id: Uuid,
        conn: &C,
    ) -> Result<Vec<crate::domain::business::merchants::entities::merchant_role_permissions::Model>>
    where
        C: ConnectionTrait,
    {
        MerchantRolePermission::find()
            .filter(crate::domain::business::merchants::entities::merchant_role_permissions::Column::RoleId.eq(role_id))
            .all(conn)
            .await
            .context("查询角色权限关联失败")
    }

    /// 批量插入角色权限关联
    pub async fn insert_role_permissions<C>(
        &self,
        permissions: Vec<
            crate::domain::business::merchants::entities::merchant_role_permissions::ActiveModel,
        >,
        conn: &C,
    ) -> Result<()>
    where
        C: ConnectionTrait,
    {
        if permissions.is_empty() {
            return Ok(());
        }

        crate::domain::business::merchants::entities::merchant_role_permissions::Entity::insert_many(permissions)
            .exec(conn)
            .await
            .context("批量插入角色权限关联失败")?;

        Ok(())
    }
}

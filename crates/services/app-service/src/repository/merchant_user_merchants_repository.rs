use crate::domain::business::merchants::entities::merchant_roles::role_code;
use crate::domain::business::merchants::entities::{
    merchant_authorized_permissions, merchant_categories, merchant_role_permissions,
    merchant_roles,
    merchant_user_merchants::{
        ActiveModel, Column, Entity as MerchantUserMerchants, MerchantUserJoinType,
        MerchantUserMerchantStatus, Model as MerchantUserMerchantsModel,
    },
    merchant_user_roles, merchant_users, merchants, system_permission_templates,
};
use crate::utils::datetime::DateTimeUtils;
use anyhow::{Context, Result};
use lib_core::app::plugin::Service as ServiceTrait;
use lib_macros::Service;
use sea_orm::{ActiveModelTrait, ColumnTrait, ConnectionTrait, EntityTrait, PaginatorTrait, QueryFilter, QuerySelect, RelationTrait, Set};
use std::collections::{HashMap, HashSet};
use uuid::Uuid;

/// 用户商户关系Repository实现
#[derive(<PERSON><PERSON>, Service)]
pub struct MerchantUserMerchantsRepository;

impl MerchantUserMerchantsRepository {
    /// 获取用户在指定商户下的角色ID列表
    pub async fn get_user_merchant_role_ids<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Vec<Uuid>>
    where
        C: ConnectionTrait,
    {
        use sea_orm::{JoinType, QuerySelect};

        // 使用正确的关联查询，通过JOIN获取角色ID
        let role_ids: Vec<Uuid> = merchant_user_roles::Entity::find()
            .join(
                JoinType::InnerJoin,
                merchant_user_roles::Relation::MerchantUserMerchants.def(),
            )
            .join(
                JoinType::InnerJoin,
                merchant_user_roles::Relation::MerchantRoles.def(),
            )
            .filter(Column::UserId.eq(user_id))
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantUserMerchantStatus::Active))
            .select_only()
            .column(merchant_roles::Column::Id)
            .into_tuple()
            .all(conn)
            .await
            .context("查询用户商户角色ID失败")?;

        Ok(role_ids)
    }
}

impl MerchantUserMerchantsRepository {
    /// 查询用户关联的所有商户ID
    pub async fn get_user_merchant_ids<C>(&self, user_id: Uuid, conn: &C) -> Result<Vec<i64>>
    where
        C: ConnectionTrait,
    {
        let merchant_ids = MerchantUserMerchants::find()
            .filter(Column::UserId.eq(user_id))
            .filter(Column::Status.eq(MerchantUserMerchantStatus::Active))
            .all(conn)
            .await
            .context("查询用户关联商户失败")?
            .into_iter()
            .map(|um| um.merchant_id)
            .collect::<HashSet<_>>()
            .into_iter()
            .collect();

        Ok(merchant_ids)
    }

    /// 查询用户在指定商户的权限信息
    pub async fn get_user_merchant_permissions<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        conn: &C,
    ) -> Result<(Vec<String>, Vec<String>)>
    where
        C: ConnectionTrait,
    {
        // 1. 先获取用户在该商户的关系
        let user_merchant = MerchantUserMerchants::find()
            .filter(Column::UserId.eq(user_id))
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantUserMerchantStatus::Active))
            .one(conn)
            .await
            .context("查询用户商户关系失败")?;

        let user_merchant = match user_merchant {
            Some(um) => um,
            None => return Ok((Vec::new(), Vec::new())),
        };

        // 2. 查询用户角色
        let user_roles = merchant_user_roles::Entity::find()
            .filter(merchant_user_roles::Column::UserMerchantId.eq(user_merchant.id))
            .find_with_related(merchant_roles::Entity)
            .all(conn)
            .await
            .context("查询用户商户角色失败")?;

        // 3. 使用 HashSet 高效收集角色信息
        let (role_codes, role_ids): (HashSet<String>, HashSet<Uuid>) =
            user_roles.into_iter().flat_map(|(_, roles)| roles).fold(
                (HashSet::new(), HashSet::new()),
                |(mut codes, mut ids), role| {
                    ids.insert(role.id);
                    if let Some(code) = role.role_code {
                        codes.insert(code);
                    }
                    (codes, ids)
                },
            );

        // 4. 如果没有角色，直接返回空结果
        if role_ids.is_empty() {
            return Ok((role_codes.into_iter().collect(), Vec::new()));
        }

        // 5. 查询角色的权限模板ID
        let template_ids: HashSet<Uuid> = merchant_role_permissions::Entity::find()
            .filter(merchant_role_permissions::Column::RoleId.is_in(role_ids))
            .find_with_related(merchant_authorized_permissions::Entity)
            .all(conn)
            .await
            .context("查询角色权限失败")?
            .into_iter()
            .flat_map(|(_, auth_perms)| auth_perms)
            .map(|auth_perm| auth_perm.permission_template_id)
            .collect();

        // 6. 如果没有权限模板，返回角色代码和空权限
        if template_ids.is_empty() {
            return Ok((role_codes.into_iter().collect(), Vec::new()));
        }

        // 7. 查询权限模板，直接获取权限代码
        let permission_codes: Vec<String> = system_permission_templates::Entity::find()
            .filter(system_permission_templates::Column::Id.is_in(template_ids))
            .all(conn)
            .await
            .context("查询权限模板失败")?
            .into_iter()
            .map(|template| template.permission_code)
            .collect::<HashSet<_>>() // 使用 HashSet 去重
            .into_iter()
            .collect();

        Ok((role_codes.into_iter().collect(), permission_codes))
    }

    /// 检查用户是否有管理员角色
    pub async fn check_user_admin_role<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        // 1. 先获取用户在该商户的关系
        let user_merchant = MerchantUserMerchants::find()
            .filter(Column::UserId.eq(user_id))
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantUserMerchantStatus::Active))
            .one(conn)
            .await
            .context("查询用户商户关系失败")?;

        let user_merchant = match user_merchant {
            Some(um) => um,
            None => return Ok(false),
        };

        // 2. 检查是否有管理员角色
        let admin_role = merchant_user_roles::Entity::find()
            .filter(merchant_user_roles::Column::UserMerchantId.eq(user_merchant.id))
            .inner_join(merchant_roles::Entity)
            .filter(
                merchant_roles::Column::RoleCode
                    .eq(Some(role_code::SYSTEM_CUSTOM_ADMIN_ROLE.to_string())),
            )
            .one(conn)
            .await
            .context("检查用户管理员角色失败")?;

        Ok(admin_role.is_some())
    }

    /// 查询用户完整商户列表信息
    /// 包含商户基本信息、分类信息和用户在各商户下的角色信息
    pub async fn get_user_merchant_list<C>(
        &self,
        user_id: Uuid,
        include_disabled: bool,
        conn: &C,
    ) -> Result<
        Vec<(
            merchants::Model,
            Option<merchant_categories::Model>,
            Vec<merchant_roles::Model>,
        )>,
    >
    where
        C: ConnectionTrait,
    {
        // 1. 查询用户关联的所有商户关系
        let user_merchants_query = MerchantUserMerchants::find()
            .filter(Column::UserId.eq(user_id))
            .filter(Column::Status.eq(MerchantUserMerchantStatus::Active));

        let user_merchants = user_merchants_query
            .all(conn)
            .await
            .context("查询用户商户关系失败")?;

        if user_merchants.is_empty() {
            return Ok(Vec::new());
        }

        let merchant_ids: Vec<i64> = user_merchants.iter().map(|um| um.merchant_id).collect();
        let user_merchant_ids: Vec<Uuid> = user_merchants.iter().map(|um| um.id).collect();

        // 2. 查询商户基本信息及分类信息
        let mut merchant_query =
            merchants::Entity::find().filter(merchants::Column::Id.is_in(merchant_ids));

        // 如果不包含已禁用的商户，则过滤状态
        if !include_disabled {
            merchant_query = merchant_query
                .filter(merchants::Column::Status.eq(merchants::MerchantStatus::Active));
        }

        let merchants_with_categories = merchant_query
            .find_also_related(merchant_categories::Entity)
            .all(conn)
            .await
            .context("查询商户及分类信息失败")?;

        // 3. 查询用户在各商户下的角色信息
        let user_roles = merchant_user_roles::Entity::find()
            .filter(merchant_user_roles::Column::UserMerchantId.is_in(user_merchant_ids))
            .find_with_related(merchant_roles::Entity)
            .all(conn)
            .await
            .context("查询用户角色信息失败")?;

        // 4. 按商户ID分组角色信息
        let mut merchant_roles_map: HashMap<i64, Vec<merchant_roles::Model>> = HashMap::new();
        for (_, roles) in user_roles {
            for role in roles {
                merchant_roles_map
                    .entry(role.merchant_id)
                    .or_insert_with(Vec::new)
                    .push(role);
            }
        }

        // 5. 组装最终结果
        let result = merchants_with_categories
            .into_iter()
            .map(|(merchant, category)| {
                let roles = merchant_roles_map
                    .get(&merchant.id)
                    .cloned()
                    .unwrap_or_default();
                (merchant, category, roles)
            })
            .collect();

        Ok(result)
    }

    /// 创建用户商户关系
    pub async fn create_user_merchant_relation<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        join_type: MerchantUserJoinType,
        status: MerchantUserMerchantStatus,
        created_by: Option<Uuid>,
        conn: &C,
    ) -> Result<MerchantUserMerchantsModel>
    where
        C: ConnectionTrait,
    {
        let active_model = ActiveModel {
            id: Set(Uuid::now_v7()),
            user_id: Set(user_id),
            merchant_id: Set(merchant_id),
            status: Set(status),
            join_type: Set(join_type),
            join_date: Set(DateTimeUtils::now_local().fixed_offset()),
            leave_date: Set(None),
            created_date: Set(DateTimeUtils::now_local().fixed_offset()),
            updated_date: Set(DateTimeUtils::now_local().fixed_offset()),
            created_by: Set(created_by),
            updated_by: Set(created_by),
            remark: Set(None),
        };

        active_model
            .insert(conn)
            .await
            .context("创建用户商户关系失败")
    }

    /// 更新用户商户关系状态
    pub async fn update_user_merchant_status<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        status: MerchantUserMerchantStatus,
        updated_by: Option<Uuid>,
        conn: &C,
    ) -> Result<Option<MerchantUserMerchantsModel>>
    where
        C: ConnectionTrait,
    {
        let user_merchant = MerchantUserMerchants::find()
            .filter(Column::UserId.eq(user_id))
            .filter(Column::MerchantId.eq(merchant_id))
            .one(conn)
            .await
            .context("查询用户商户关系失败")?;

        if let Some(um) = user_merchant {
            let mut active_model: ActiveModel = um.into();
            active_model.status = Set(status);
            active_model.updated_date = Set(DateTimeUtils::now_local().fixed_offset());
            active_model.updated_by = Set(updated_by);

            if status == MerchantUserMerchantStatus::Left
                || status == MerchantUserMerchantStatus::Removed
            {
                active_model.leave_date = Set(Some(DateTimeUtils::now_local().fixed_offset()));
            }

            let updated = active_model
                .update(conn)
                .await
                .context("更新用户商户关系状态失败")?;

            Ok(Some(updated))
        } else {
            Ok(None)
        }
    }

    /// 检查用户是否属于某个商户
    pub async fn user_belongs_to_merchant<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let count = MerchantUserMerchants::find()
            .filter(Column::UserId.eq(user_id))
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantUserMerchantStatus::Active))
            .count(conn)
            .await
            .context("检查用户是否属于商户失败")?;

        Ok(count > 0)
    }

    /// 查询商户的所有用户
    pub async fn find_merchant_users<C>(
        &self,
        merchant_id: i64,
        include_inactive: bool,
        conn: &C,
    ) -> Result<Vec<merchant_users::Model>>
    where
        C: ConnectionTrait,
    {
        let mut query = MerchantUserMerchants::find().filter(Column::MerchantId.eq(merchant_id));

        if !include_inactive {
            query = query.filter(Column::Status.eq(MerchantUserMerchantStatus::Active));
        }

        let user_merchants = query
            .find_with_related(merchant_users::Entity)
            .all(conn)
            .await
            .context("查询商户用户失败")?;

        let users = user_merchants
            .into_iter()
            .flat_map(|(_, users)| users)
            .collect();

        Ok(users)
    }

    /// 获取用户商户关系ID
    pub async fn get_user_merchant_relation_id<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Option<Uuid>>
    where
        C: ConnectionTrait,
    {
        let user_merchant = MerchantUserMerchants::find()
            .filter(Column::UserId.eq(user_id))
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantUserMerchantStatus::Active))
            .one(conn)
            .await
            .context("查询用户商户关系失败")?;

        Ok(user_merchant.map(|um| um.id))
    }

    // ==================== 单一职责原则的新方法 ====================

    /// 获取用户在指定商户下的角色编码列表
    /// 单一职责：只负责获取角色编码
    /// 性能优化：只查询需要的字段
    pub async fn get_user_merchant_role_codes<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Vec<String>>
    where
        C: ConnectionTrait,
    {
        use sea_orm::{JoinType, QuerySelect};

        // 使用正确的关联查询，通过JOIN获取角色编码
        let role_codes: Vec<Option<String>> = merchant_user_roles::Entity::find()
            .join(
                JoinType::InnerJoin,
                merchant_user_roles::Relation::MerchantUserMerchants.def(),
            )
            .join(
                JoinType::InnerJoin,
                merchant_user_roles::Relation::MerchantRoles.def(),
            )
            .filter(Column::UserId.eq(user_id))
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantUserMerchantStatus::Active))
            .select_only()
            .column(merchant_roles::Column::RoleCode)
            .into_tuple()
            .all(conn)
            .await
            .context("查询用户商户角色编码失败")?;

        // 过滤掉None值并去重
        let result: Vec<String> = role_codes
            .into_iter()
            .filter_map(|code| code)
            .collect::<HashSet<_>>()
            .into_iter()
            .collect();

        Ok(result)
    }

    /// 获取用户在指定商户下的完整角色信息
    /// 单一职责：只负责获取角色详情
    /// 用于需要完整角色信息的场景（如用户个人信息页面）
    pub async fn get_user_merchant_roles_with_details<C>(
        &self,
        user_id: Uuid,
        merchant_id: i64,
        conn: &C,
    ) -> Result<Vec<merchant_roles::Model>>
    where
        C: ConnectionTrait,
    {
        use sea_orm::JoinType;

        // 使用正确的关联查询，获取完整的角色信息
        let roles: Vec<merchant_roles::Model> = merchant_user_roles::Entity::find()
            .join(
                JoinType::InnerJoin,
                merchant_user_roles::Relation::MerchantUserMerchants.def(),
            )
            .join(
                JoinType::InnerJoin,
                merchant_user_roles::Relation::MerchantRoles.def(),
            )
            .filter(Column::UserId.eq(user_id))
            .filter(Column::MerchantId.eq(merchant_id))
            .filter(Column::Status.eq(MerchantUserMerchantStatus::Active))
            .select_only()
            .columns([
                merchant_roles::Column::Id,
                merchant_roles::Column::MerchantId,
                merchant_roles::Column::RoleCode,
                merchant_roles::Column::RoleName,
                merchant_roles::Column::RoleType,
                merchant_roles::Column::IsDefault,
                merchant_roles::Column::DataScope,
                merchant_roles::Column::RoleDescription,
                merchant_roles::Column::Status,
                merchant_roles::Column::CreatedDate,
                merchant_roles::Column::UpdatedDate,
                merchant_roles::Column::CreatedBy,
                merchant_roles::Column::UpdatedBy,
                merchant_roles::Column::Remark,
            ])
            .into_model::<merchant_roles::Model>()
            .all(conn)
            .await
            .context("查询用户商户角色详情失败")?;

        // 使用HashSet去重（基于角色ID）
        let mut unique_roles = std::collections::HashMap::new();
        for role in roles {
            unique_roles.insert(role.id, role);
        }

        Ok(unique_roles.into_values().collect())
    }
}

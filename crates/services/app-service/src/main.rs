use app_service::controller;
use axum::{middleware, Router};
use lib_auth::middleware::ip_resolver_middleware::ip_resolver_middleware;
use lib_cache::RedisPlugin;
use lib_core::app::app::App;
use lib_core::response::codes;
use lib_core::ApiResult;
use lib_data::DataPlugin;
use lib_job::{JobConfigurator, JobPlugin};
use lib_macros::auto_config;
use lib_store::StoragePlugin;
use lib_web::{WebConfigurator, WebPlugin};
use lib_wechat::WechatPlugin;
use tower_http::trace::TraceLayer;

#[auto_config(JobConfigurator)]
#[tokio::main]
async fn main() {
    // 公开路由
    let public_router = controller::create_public_routes();

    // 受保护路由
    let protected_router = controller::create_protected_routes();

    // 合并路由，应用全局中间件
    let app_router = Router::new()
        .merge(public_router) // 公开路由，没有JWT中间件
        .merge(protected_router) // 受保护路由，已经有JWT中间件
        .layer(middleware::from_fn(ip_resolver_middleware)) // 全局IP解析中间件
        .layer(TraceLayer::new_for_http()); // 全局日志中间件

    App::new()
        .add_plugin(JobPlugin) // 任务插件
        .add_plugin(RedisPlugin) // Redis插件
        .add_plugin(DataPlugin) // Data插件
        .add_plugin(StoragePlugin) // 添加存储
        .add_plugin(WebPlugin) // Web插件
        .add_plugin(WechatPlugin) // Wechat插件
        .add_router(app_router.fallback(|| async {
            ApiResult::<()>::error(codes::BAD_REQUEST, "资源不存在".to_string())
        })) // 添加路由
        .run()
        .await;
}

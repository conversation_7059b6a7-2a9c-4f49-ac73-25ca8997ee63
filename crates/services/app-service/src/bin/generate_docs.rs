use app_service::controller::business::merchants::merchant_user_auth_controller::MerchantUserAuthApiDoc;
use std::fs;
use std::path::Path;

fn main() {
    println!("开始生成 OpenAPI 文档...");

    // 创建 docs 目录（如果不存在）
    let docs_dir = Path::new("docs");
    if !docs_dir.exists() {
        fs::create_dir_all(docs_dir).expect("无法创建 docs 目录");
    }

    // 生成 JSON 格式文档
    let json_path = docs_dir.join("openapi.json");
    let json_content = MerchantUserAuthApiDoc::get_openapi_json();
    fs::write(&json_path, json_content).expect("无法写入 JSON 文档");
    println!("✅ JSON 文档已生成: {}", json_path.display());

    // 生成 YAML 格式文档
    let yaml_path = docs_dir.join("openapi.yaml");
    let yaml_content = MerchantUserAuthApiDoc::get_openapi_yaml();
    fs::write(&yaml_path, yaml_content).expect("无法写入 YAML 文档");
    println!("✅ YAML 文档已生成: {}", yaml_path.display());

    println!("文档生成完成！");
}

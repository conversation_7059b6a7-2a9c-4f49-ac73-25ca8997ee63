use lazy_static::lazy_static;
use regex::Regex;
use ::validator::ValidationError;

lazy_static! {
    /// 手机号码验证正则 - 支持11位数字，1开头
    pub static ref PHONE_REGEX: Regex = Regex::new(r"^1[3-9]\d{9}$").unwrap();

    /// 身份证号码验证正则 - 18位，最后一位可以是X
    pub static ref ID_CARD_REGEX: Regex = Regex::new(r"^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$").unwrap();

    /// 邮箱验证正则 - 更严格的邮箱格式
    pub static ref EMAIL_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$").unwrap();

    /// 用户名验证正则 - 字母、数字、下划线，3-20位
    pub static ref USERNAME_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9_]{3,20}$").unwrap();

    /// 商户编码验证正则 - 字母、数字、下划线、连字符，2-50位
    pub static ref MERCHANT_CODE_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9_-]{2,50}$").unwrap();

    /// 角色编码验证正则 - 大写字母、数字、下划线，2-50位
    pub static ref ROLE_CODE_REGEX: Regex = Regex::new(r"^[A-Z0-9_]{2,50}$").unwrap();

    /// 营业执照号验证正则 - 统一社会信用代码，18位
    pub static ref BUSINESS_LICENSE_REGEX: Regex = Regex::new(r"^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$").unwrap();

    /// URL验证正则 - 支持http/https
    pub static ref URL_REGEX: Regex = Regex::new(r"^https?://[^\s/$.?#].[^\s]*$").unwrap();

    /// 密码强度验证正则 - 至少包含字母和数字，6-255位
    pub static ref PASSWORD_REGEX: Regex = Regex::new(r"^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,255}$").unwrap();

    /// IPv4地址验证正则
    pub static ref IPV4_REGEX: Regex = Regex::new(r"^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$").unwrap();

    /// 版本号验证正则 - 语义化版本，如 1.0.0
    pub static ref VERSION_REGEX: Regex = Regex::new(r"^\d+\.\d+\.\d+$").unwrap();
}

/// 验证工具函数
pub mod validator {
    use super::*;

    /// 验证手机号码
    pub fn validate_phone(phone: &str) -> bool {
        PHONE_REGEX.is_match(phone)
    }

    /// 验证身份证号码
    pub fn validate_id_card(id_card: &str) -> bool {
        ID_CARD_REGEX.is_match(id_card)
    }

    /// 验证邮箱地址
    pub fn validate_email(email: &str) -> bool {
        EMAIL_REGEX.is_match(email)
    }

    /// 验证用户名
    pub fn validate_username(username: &str) -> bool {
        USERNAME_REGEX.is_match(username)
    }

    /// 验证商户编码
    pub fn validate_merchant_code(code: &str) -> bool {
        MERCHANT_CODE_REGEX.is_match(code)
    }

    /// 验证角色编码
    pub fn validate_role_code(code: &str) -> bool {
        ROLE_CODE_REGEX.is_match(code)
    }

    /// 验证营业执照号
    pub fn validate_business_license(license: &str) -> bool {
        BUSINESS_LICENSE_REGEX.is_match(license)
    }

    /// 验证URL
    pub fn validate_url(url: &str) -> bool {
        URL_REGEX.is_match(url)
    }

    /// 验证密码强度
    pub fn validate_password(password: &str) -> bool {
        PASSWORD_REGEX.is_match(password)
    }

    /// 验证IPv4地址
    pub fn validate_ipv4(ip: &str) -> bool {
        IPV4_REGEX.is_match(ip)
    }

    /// 验证版本号
    pub fn validate_version(version: &str) -> bool {
        VERSION_REGEX.is_match(version)
    }

    /// 验证经度 (-180.0 到 180.0)
    pub fn validate_longitude(longitude: f64) -> bool {
        longitude >= -180.0 && longitude <= 180.0
    }

    /// 验证纬度 (-90.0 到 90.0)
    pub fn validate_latitude(latitude: f64) -> bool {
        latitude >= -90.0 && latitude <= 90.0
    }

    /// 验证百分比 (0.0 到 1.0)
    pub fn validate_percentage(percentage: f64) -> bool {
        percentage >= 0.0 && percentage <= 1.0
    }
}

/// 自定义验证器 - 用于validator crate

/// 自定义手机号验证器
pub fn validate_phone_custom(phone: &str) -> Result<(), ValidationError> {
    if validator::validate_phone(phone) {
        Ok(())
    } else {
        Err(ValidationError::new("手机号码格式不正确"))
    }
}

/// 自定义身份证号验证器
pub fn validate_id_card_custom(id_card: &str) -> Result<(), ValidationError> {
    if validator::validate_id_card(id_card) {
        Ok(())
    } else {
        Err(ValidationError::new("身份证号码格式不正确"))
    }
}

/// 自定义用户名验证器
pub fn validate_username_custom(username: &str) -> Result<(), ValidationError> {
    if validator::validate_username(username) {
        Ok(())
    } else {
        Err(ValidationError::new(
            "用户名只能包含字母、数字、下划线，长度3-20位",
        ))
    }
}

/// 自定义商户编码验证器
pub fn validate_merchant_code_custom(code: &str) -> Result<(), ValidationError> {
    if validator::validate_merchant_code(code) {
        Ok(())
    } else {
        Err(ValidationError::new(
            "商户编码只能包含字母、数字、下划线、连字符，长度2-50位",
        ))
    }
}

/// 自定义角色编码验证器
pub fn validate_role_code_custom(code: &str) -> Result<(), ValidationError> {
    if validator::validate_role_code(code) {
        Ok(())
    } else {
        Err(ValidationError::new(
            "角色编码只能包含大写字母、数字、下划线，长度2-50位",
        ))
    }
}

/// 自定义营业执照号验证器
pub fn validate_business_license_custom(license: &str) -> Result<(), ValidationError> {
    if validator::validate_business_license(license) {
        Ok(())
    } else {
        Err(ValidationError::new(
            "营业执照号格式不正确，请输入18位统一社会信用代码",
        ))
    }
}

/// 自定义密码强度验证器
pub fn validate_password_custom(password: &str) -> Result<(), ValidationError> {
    if validator::validate_password(password) {
        Ok(())
    } else {
        Err(ValidationError::new("密码必须包含字母和数字，长度6-255位"))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_phone_validation() {
        assert!(validator::validate_phone("**********1"));
        assert!(validator::validate_phone("***********"));
        assert!(!validator::validate_phone("***********")); // 不是1开头的有效号段
        assert!(!validator::validate_phone("**********")); // 长度不够
        assert!(!validator::validate_phone("**********11")); // 长度超出
    }

    #[test]
    fn test_email_validation() {
        assert!(validator::validate_email("<EMAIL>"));
        assert!(validator::validate_email("<EMAIL>"));
        assert!(!validator::validate_email("invalid-email"));
        assert!(!validator::validate_email("@domain.com"));
    }

    #[test]
    fn test_username_validation() {
        assert!(validator::validate_username("user123"));
        assert!(validator::validate_username("test_user"));
        assert!(!validator::validate_username("us")); // 太短
        assert!(!validator::validate_username("user-name")); // 包含连字符
        assert!(!validator::validate_username("用户名")); // 包含中文
    }

    #[test]
    fn test_coordinate_validation() {
        assert!(validator::validate_longitude(116.4074));
        assert!(validator::validate_latitude(39.9042));
        assert!(!validator::validate_longitude(181.0));
        assert!(!validator::validate_latitude(91.0));
    }

    #[test]
    fn test_percentage_validation() {
        assert!(validator::validate_percentage(0.5));
        assert!(validator::validate_percentage(0.0));
        assert!(validator::validate_percentage(1.0));
        assert!(!validator::validate_percentage(1.1));
        assert!(!validator::validate_percentage(-0.1));
    }
}

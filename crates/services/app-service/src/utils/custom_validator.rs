use axum::extract::FromRequestParts;
use axum::http::Uri;
use axum::http::request::Parts;
use axum::{
    Json,
    extract::{FromRequest, Path, Request, path::ErrorKind, rejection::PathRejection},
    response::{IntoResponse, Response},
};
use lib_core::response::ApiResult;
use serde::de::DeserializeOwned;
use std::future::Future;
use tracing;
use validator::Validate;

/// 自定义验证提取器，返回我们自己的API响应格式
#[derive(Debug, <PERSON><PERSON>, Copy, Default)]
pub struct ValidJson<T>(pub T);

impl<T> std::ops::Deref for Valid<PERSON>son<T> {
    type Target = T;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl<S, T> FromRequest<S> for ValidJson<T>
where
    T: DeserializeOwned + Validate + Send + 'static,
    S: Send + Sync,
{
    type Rejection = Response;

    fn from_request(
        req: Request,
        state: &S,
    ) -> impl Future<Output = Result<Self, Self::Rejection>> + Send {
        async move {
            // 先使用标准的Json提取器
            match Json::<T>::from_request(req, state).await {
                Ok(Json(data)) => {
                    // 进行验证
                    if let Err(errors) = data.validate() {
                        let error_message = format_validation_errors(&errors);
                        let error_response = ApiResult::<()>::error(400, error_message);
                        return Err(Json(error_response).into_response());
                    }
                    Ok(ValidJson(data))
                }
                Err(rejection) => {
                    // JSON解析失败，记录详细错误信息用于调试
                    tracing::error!("JSON解析失败: {:?}", rejection);

                    // 提供用户友好的错误消息
                    let error_message = format_json_error(&rejection);
                    let error_response = ApiResult::<()>::error(400, error_message);
                    Err(Json(error_response).into_response())
                }
            }
        }
    }
}

/// 自定义Path提取器，提供友好且精确的URL参数解析错误信息
#[derive(Debug, Clone, Copy, Default)]
pub struct MyPath<T>(pub T);

impl<T> std::ops::Deref for MyPath<T> {
    type Target = T;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl<T, S> FromRequestParts<S> for MyPath<T>
where
    T: DeserializeOwned + Send,
    S: Send + Sync,
{
    type Rejection = Response;

    async fn from_request_parts(parts: &mut Parts, state: &S) -> Result<Self, Self::Rejection> {
        match Path::<T>::from_request_parts(parts, state).await {
            Ok(Path(data)) => Ok(MyPath(data)),
            Err(rejection) => {
                // Path解析失败，记录详细错误信息用于调试
                tracing::error!("Path参数解析失败: {:?}", rejection);

                // 生成用户友好的错误消息
                let error_message = format_path_error_detailed(&rejection);
                let error_response = ApiResult::<()>::error(400, error_message);
                Err(Json(error_response).into_response())
            }
        }
    }
}

/// 格式化Path解析错误消息（精确版本，基于ErrorKind）
fn format_path_error_detailed(rejection: &PathRejection) -> String {
    match rejection {
        PathRejection::FailedToDeserializePathParams(inner) => {
            let kind = inner.kind();
            match kind {
                ErrorKind::WrongNumberOfParameters { got, expected } => {
                    format!(
                        "路径参数数量不匹配，期望 {} 个参数，实际获得 {} 个",
                        expected, got
                    )
                }

                ErrorKind::ParseErrorAtKey {
                    key,
                    value,
                    expected_type,
                } => format_parse_error_at_key(key, value, expected_type),

                ErrorKind::ParseErrorAtIndex {
                    index,
                    value,
                    expected_type,
                } => {
                    format!(
                        "路径参数位置 {} 的值 '{}' 无法解析为 {}，请提供正确格式的值",
                        index, value, expected_type
                    )
                }

                ErrorKind::ParseError {
                    value,
                    expected_type,
                } => {
                    format!(
                        "路径参数值 '{}' 无法解析为 {}，请检查参数格式",
                        value, expected_type
                    )
                }

                ErrorKind::InvalidUtf8InPathParam { key } => {
                    format!(
                        "路径参数 '{}' 包含无效的UTF-8字符，请使用正确的字符编码",
                        key
                    )
                }

                ErrorKind::UnsupportedType { name } => {
                    format!("服务器内部错误：不支持的参数类型 '{}'", name)
                }

                ErrorKind::Message(msg) => {
                    format!("路径参数解析错误: {}", msg)
                }

                _ => {
                    format!("路径参数解析失败: {}", kind)
                }
            }
        }
        PathRejection::MissingPathParams(error) => {
            format!("服务器配置错误，缺少路径参数定义: {}", error)
        }
        _ => {
            format!("路径参数处理失败: {}", rejection)
        }
    }
}

/// 专门处理带键名的解析错误，提供针对性的错误信息
fn format_parse_error_at_key(key: &str, value: &str, expected_type: &str) -> String {
    // 根据期望类型提供特定的错误信息
    match expected_type {
        typ if typ.contains("Uuid") || typ.contains("uuid") => {
            if value.is_empty() {
                format!("路径参数 '{}' 不能为空，请提供有效的UUID", key)
            } else if value.len() != 36 && value.len() != 32 {
                format!(
                    "路径参数 '{}' 的UUID长度错误，请提供36位带连字符或32位无连字符的UUID格式",
                    key
                )
            } else if value.chars().any(|c| !c.is_ascii_hexdigit() && c != '-') {
                format!(
                    "路径参数 '{}' 包含无效字符，UUID只能包含数字、字母a-f和连字符",
                    key
                )
            } else {
                format!("路径参数 '{}' 的UUID格式错误，请提供正确的UUID格式", key)
            }
        }

        typ if typ.contains("u32")
            || typ.contains("i32")
            || typ.contains("u64")
            || typ.contains("i64") =>
        {
            if value.is_empty() {
                format!("路径参数 '{}' 不能为空，请提供有效的数字", key)
            } else if value.chars().any(|c| !c.is_ascii_digit() && c != '-') {
                format!("路径参数 '{}' 包含无效字符，只能包含数字", key)
            } else {
                format!("路径参数 '{}' 的数值超出范围或格式错误", key)
            }
        }

        typ if typ.contains("String") || typ.contains("str") => {
            if value.is_empty() {
                format!("路径参数 '{}' 不能为空", key)
            } else {
                format!("路径参数 '{}' 格式错误", key)
            }
        }

        _ => {
            format!(
                "路径参数 '{}' 的值 '{}' 无法解析为 {}，请检查参数格式",
                key, value, expected_type
            )
        }
    }
}

/// 格式化验证错误消息
fn format_validation_errors(errors: &validator::ValidationErrors) -> String {
    let mut messages = Vec::new();

    for (field, field_errors) in errors.field_errors() {
        for error in field_errors {
            let message = error
                .message
                .as_ref()
                .map(|m| m.to_string())
                .unwrap_or_else(|| match error.code.as_ref() {
                    "required" => "此字段为必填项".to_string(),
                    "email" => "请输入有效的邮箱地址".to_string(),
                    "url" => "请输入有效的URL地址".to_string(),
                    "phone" => "请输入有效的手机号码".to_string(),
                    "credit_card" => "请输入有效的信用卡号".to_string(),
                    "length" => {
                        if let Some(min) = error.params.get("min") {
                            if let Some(max) = error.params.get("max") {
                                format!("长度必须在 {} 到 {} 之间", min, max)
                            } else {
                                format!("长度至少为 {}", min)
                            }
                        } else if let Some(max) = error.params.get("max") {
                            format!("长度不能超过 {}", max)
                        } else {
                            "长度不符合要求".to_string()
                        }
                    }
                    "range" => {
                        if let Some(min) = error.params.get("min") {
                            if let Some(max) = error.params.get("max") {
                                format!("值必须在 {} 到 {} 之间", min, max)
                            } else {
                                format!("值必须大于等于 {}", min)
                            }
                        } else if let Some(max) = error.params.get("max") {
                            format!("值必须小于等于 {}", max)
                        } else {
                            "值超出范围".to_string()
                        }
                    }
                    "must_match" => "字段值不匹配".to_string(),
                    "contains" => "必须包含指定内容".to_string(),
                    "does_not_contain" => "不能包含指定内容".to_string(),
                    "regex" => "格式不正确".to_string(),
                    "custom" => "自定义验证失败".to_string(),
                    _ => "无效的值".to_string(),
                });

            messages.push(format!("{}: {}", field, message));
        }
    }

    messages.join("; ")
}

/// 自定义Query提取器，提供友好的错误消息，特别是处理空字符串转换为整数的问题
#[derive(Debug, Clone, Copy, Default)]
pub struct MyQuery<T>(pub T);

impl<T> std::ops::Deref for MyQuery<T> {
    type Target = T;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl<T, S> FromRequestParts<S> for MyQuery<T>
where
    T: DeserializeOwned,
    S: Send + Sync,
{
    type Rejection = Response;

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        Self::try_from_uri(&parts.uri)
    }
}

impl<T> MyQuery<T>
where
    T: DeserializeOwned,
{
    pub fn try_from_uri(value: &Uri) -> Result<Self, Response> {
        let query = value.query().unwrap_or_default();

        // 预处理查询字符串，智能处理空值参数
        let processed_query = preprocess_query_string(query);

        let deserializer =
            serde_urlencoded::Deserializer::new(form_urlencoded::parse(processed_query.as_bytes()));

        match serde_path_to_error::deserialize(deserializer) {
            Ok(params) => Ok(MyQuery(params)),
            Err(err) => {
                // 生成友好的错误消息
                let error_message = format_query_error(&err, query);
                let error_response = ApiResult::<()>::error(400, error_message);
                Err(Json(error_response).into_response())
            }
        }
    }
}

/// 预处理查询字符串，智能处理空值参数
fn preprocess_query_string(query: &str) -> String {
    if query.is_empty() {
        return String::new();
    }

    let mut processed_params = Vec::new();

    for pair in query.split('&') {
        if pair.is_empty() {
            continue;
        }

        if let Some((_key, _value)) = pair.split_once('=') {
            // 对于其他字段，保留原始值
            processed_params.push(pair);
        } else {
            // 没有等号的参数（只有key），检查是否为空
            if !pair.is_empty() {
                // 对于没有值的参数，我们跳过它们
                continue;
            }
        }
    }

    processed_params.join("&")
}

/// 格式化查询参数错误消息
fn format_query_error(
    err: &serde_path_to_error::Error<serde_urlencoded::de::Error>,
    _query: &str,
) -> String {
    let path = err.path().to_string();
    let inner_error = err.inner();

    // 尝试解析错误类型
    let error_msg = inner_error.to_string();

    if error_msg.contains("cannot parse integer from empty string") {
        format!("参数 '{}' 的值不能为空，请提供有效的数字", path)
    } else if error_msg.contains("invalid digit found in string") {
        format!("参数 '{}' 的值格式不正确，请提供有效的数字", path)
    } else if error_msg.contains("number too large") {
        format!("参数 '{}' 的值过大", path)
    } else if error_msg.contains("number too small") {
        format!("参数 '{}' 的值过小", path)
    } else if error_msg.contains("invalid type") {
        format!("参数 '{}' 的类型不正确", path)
    } else {
        // 对于其他错误，提供通用的错误消息
        format!("查询参数解析错误: {}，请检查参数格式", path)
    }
}

/// 格式化JSON解析错误消息，提供精确且用户友好的错误信息
fn format_json_error(rejection: &axum::extract::rejection::JsonRejection) -> String {
    use axum::extract::rejection::JsonRejection;

    match rejection {
        JsonRejection::JsonDataError(err) => {
            let error_msg = err.to_string();

            // 解析serde_json错误消息并转换为友好的中文提示
            if error_msg.contains("invalid type:") {
                if let Some(type_error) = extract_type_error_simple(&error_msg) {
                    match type_error {
                        SimpleTypeError::StringToNumber {
                            field,
                            value,
                            expected_type,
                        } => {
                            format!(
                                "字段 '{}' 的值 '{}' 应该是{}类型，不能是字符串",
                                field,
                                value,
                                get_type_name_chinese(&expected_type)
                            )
                        }
                        SimpleTypeError::NumberToString { field, value } => {
                            format!(
                                "字段 '{}' 的值 {} 应该是字符串类型，不能是数字",
                                field, value
                            )
                        }
                        SimpleTypeError::BooleanError { field, value } => {
                            format!(
                                "字段 '{}' 的值 '{}' 应该是布尔类型（true或false）",
                                field, value
                            )
                        }
                        SimpleTypeError::ArrayError { field } => {
                            format!("字段 '{}' 应该是数组类型", field)
                        }
                        SimpleTypeError::ObjectError { field } => {
                            format!("字段 '{}' 应该是对象类型", field)
                        }
                        SimpleTypeError::OtherTypeError {
                            field,
                            actual,
                            expected,
                        } => {
                            format!(
                                "字段 '{}' 的类型错误：期望 {}，实际 {}",
                                field,
                                get_type_name_chinese(&expected),
                                get_type_name_chinese(&actual)
                            )
                        }
                    }
                } else {
                    format!("数据类型错误：{}", error_msg)
                }
            } else if error_msg.contains("missing field") {
                if let Some(field) = extract_field_name(&error_msg, "missing field") {
                    format!("缺少必填字段：'{}'", field)
                } else {
                    format!("缺少必填字段")
                }
            } else if error_msg.contains("unknown field") {
                if let Some(field) = extract_field_name(&error_msg, "unknown field") {
                    format!("包含未知字段：'{}'，请检查字段名是否正确", field)
                } else {
                    format!("包含未知字段")
                }
            } else if error_msg.contains("expected") && error_msg.contains("at line") {
                // 处理JSON格式错误
                if error_msg.contains("expected `,` or `}`") {
                    format!("JSON格式错误：缺少逗号或右花括号，请检查JSON结构")
                } else if error_msg.contains("expected value") {
                    format!("JSON格式错误：期望一个值，请检查是否有多余的逗号")
                } else {
                    format!("JSON格式错误：{}", error_msg)
                }
            } else if error_msg.contains("duplicate field") {
                if let Some(field) = extract_field_name(&error_msg, "duplicate field") {
                    format!("重复的字段：'{}'", field)
                } else {
                    format!("存在重复字段")
                }
            } else {
                format!("JSON数据解析错误：{}", error_msg)
            }
        }
        JsonRejection::JsonSyntaxError(err) => {
            let error_msg = err.to_string();
            if error_msg.contains("expected") && error_msg.contains("at line") {
                format!("JSON语法错误：{}，请检查JSON格式是否正确", error_msg)
            } else {
                format!("JSON语法错误：{}，请检查JSON格式", error_msg)
            }
        }
        JsonRejection::MissingJsonContentType(_) => {
            "请求头错误：Content-Type应该设置为application/json".to_string()
        }
        JsonRejection::BytesRejection(_) => "请求体读取失败：数据过大或网络错误".to_string(),
        _ => {
            format!("JSON处理失败：{}", rejection)
        }
    }
}

/// 简化的类型错误信息枚举
#[derive(Debug)]
enum SimpleTypeError {
    StringToNumber {
        field: String,
        value: String,
        expected_type: String,
    },
    NumberToString {
        field: String,
        value: String,
    },
    BooleanError {
        field: String,
        value: String,
    },
    ArrayError {
        field: String,
    },
    ObjectError {
        field: String,
    },
    OtherTypeError {
        field: String,
        actual: String,
        expected: String,
    },
}

/// 使用简单字符串解析提取类型错误信息
fn extract_type_error_simple(error_msg: &str) -> Option<SimpleTypeError> {
    // 寻找 "field: invalid type: string \"value\", expected i32" 模式
    if let Some(colon_pos) = error_msg.find(": invalid type:") {
        let field_part = &error_msg[..colon_pos];

        // 从字段部分提取字段名（可能包含前缀，如空格）
        let field = if let Some(space_pos) = field_part.rfind(' ') {
            field_part[space_pos + 1..].to_string()
        } else {
            field_part.to_string()
        };

        // 提取类型信息部分
        let type_part = &error_msg[colon_pos + 15..]; // 跳过 ": invalid type: "

        if let Some(expected_pos) = type_part.find(", expected ") {
            let actual_part = &type_part[..expected_pos].trim(); // 去除前后空格
            let expected_part = &type_part[expected_pos + 11..]; // 跳过 ", expected "

            // 提取期望类型 (可能包含 " at line" 等后缀)
            let expected_type = if let Some(at_pos) = expected_part.find(" at ") {
                expected_part[..at_pos].to_string()
            } else {
                expected_part.trim().to_string()
            };

            // 解析实际类型和值
            if actual_part.starts_with("string \"") && actual_part.ends_with("\"") {
                let value = actual_part[8..actual_part.len() - 1].to_string();

                // 判断期望类型
                if expected_type.contains("i32")
                    || expected_type.contains("u32")
                    || expected_type.contains("i64")
                    || expected_type.contains("u64")
                    || expected_type.contains("f32")
                    || expected_type.contains("f64")
                {
                    return Some(SimpleTypeError::StringToNumber {
                        field,
                        value,
                        expected_type,
                    });
                } else if expected_type == "bool" {
                    return Some(SimpleTypeError::BooleanError { field, value });
                }
            } else if actual_part.starts_with("number") && expected_type == "string" {
                // 提取数字值
                let value = if let Some(space_pos) = actual_part.find(' ') {
                    actual_part[space_pos + 1..].to_string()
                } else {
                    "数字".to_string()
                };
                return Some(SimpleTypeError::NumberToString { field, value });
            } else if actual_part.starts_with("string") {
                // 没有引号的字符串类型错误
                return Some(SimpleTypeError::OtherTypeError {
                    field,
                    actual: "string".to_string(),
                    expected: expected_type,
                });
            }

            // 其他类型错误
            let actual_type = if let Some(space_pos) = actual_part.find(' ') {
                actual_part[..space_pos].to_string()
            } else {
                actual_part.to_string()
            };

            if expected_type.contains("array") || expected_type.contains("Vec") {
                return Some(SimpleTypeError::ArrayError { field });
            } else if expected_type.contains("object") || expected_type.contains("struct") {
                return Some(SimpleTypeError::ObjectError { field });
            } else {
                return Some(SimpleTypeError::OtherTypeError {
                    field,
                    actual: actual_type,
                    expected: expected_type,
                });
            }
        }
    }

    None
}

/// 从错误消息中提取字段名（简化版本）
fn extract_field_name(error_msg: &str, pattern: &str) -> Option<String> {
    if let Some(start_pos) = error_msg.find(pattern) {
        let after_pattern = &error_msg[start_pos + pattern.len()..];

        // 寻找 `field_name` 模式
        if let Some(backtick_start) = after_pattern.find('`') {
            if let Some(backtick_end) = after_pattern[backtick_start + 1..].find('`') {
                let field_name =
                    &after_pattern[backtick_start + 1..backtick_start + 1 + backtick_end];
                return Some(field_name.to_string());
            }
        }
    }
    None
}

/// 获取类型的中文名称
fn get_type_name_chinese(type_name: &str) -> &str {
    match type_name {
        "i32" | "u32" | "i64" | "u64" => "整数",
        "f32" | "f64" => "小数",
        "bool" => "布尔值",
        "string" | "String" => "字符串",
        typ if typ.contains("array") || typ.contains("Vec") => "数组",
        typ if typ.contains("object") || typ.contains("struct") => "对象",
        _ => type_name,
    }
}

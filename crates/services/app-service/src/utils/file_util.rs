use anyhow::{anyhow, Result};
use axum::body::Bytes;
use axum::extract::Multipart;
use chrono::Utc;
use std::path::Path;
use uuid::Uuid;

/// 文件信息结构体
#[derive(Debug, <PERSON>lone)]
pub struct FileInfo {
    /// 文件名
    pub file_name: String,
    /// 文件数据
    pub file_data: Bytes,
    /// 内容类型
    pub content_type: Option<String>,
    /// 文件扩展名
    pub extension: Option<String>,
    /// 文件大小（字节）
    pub file_size: usize,
    /// 表单字段名
    pub field_name: String,
}

/// 多文件上传结果
#[derive(Debug, Clone)]
pub struct MultiFileUploadInfo {
    /// 文件列表
    pub files: Vec<FileInfo>,
    /// 其他表单字段
    pub form_fields: std::collections::HashMap<String, String>,
}

/// 文件操作工具类
pub struct FileUtil;

impl FileUtil {
    /// 从文件名获取扩展名
    ///
    /// # 参数
    /// * `filename` - 文件名
    ///
    /// # 返回
    /// * `Option<String>` - 如果文件有扩展名，则返回扩展名（不包含点），否则返回 None
    pub fn get_extension(filename: &str) -> Option<String> {
        Path::new(filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .map(|s| s.to_lowercase())
    }

    /// 生成文件存储路径（安全版本）
    ///
    /// # 参数
    /// * `prefix` - 可选的路径前缀
    /// * `extension` - 可选的文件扩展名（不含点）
    ///
    /// # 返回
    /// * `String` - 生成的文件路径
    /// 
    /// # 安全特性
    /// * 使用双重UUID确保路径不可预测
    /// * 移除时间信息避免路径推断
    /// * 使用哈希分片减少单目录文件数量
    pub fn generate_file_path(prefix: Option<&str>, extension: Option<&str>) -> String {
        let uuid1 = Uuid::new_v4().to_string();
        let uuid2 = Uuid::new_v4().to_string();
        
        // 使用UUID的前两个字符作为分片目录，减少单目录文件数量
        let shard1 = &uuid1[0..2];
        let shard2 = &uuid1[2..4];
        
        let extension = extension.map(|ext| format!(".{}", ext)).unwrap_or_default();
        
        // 最终文件名使用第二个UUID确保唯一性
        let filename = format!("{}{}", uuid2, extension);

        // 如果有前缀，则使用 prefix/shard1/shard2/uuid2.ext 格式
        // 如果没有前缀，则使用 shard1/shard2/uuid2.ext 格式
        if let Some(prefix) = prefix {
            format!("{}/{}/{}/{}", prefix, shard1, shard2, filename)
        } else {
            format!("{}/{}/{}", shard1, shard2, filename)
        }
    }

    /// 生成带时间戳的文件存储路径（仅在确实需要时间信息时使用）
    ///
    /// # 参数
    /// * `prefix` - 可选的路径前缀
    /// * `extension` - 可选的文件扩展名（不含点）
    ///
    /// # 返回
    /// * `String` - 生成的文件路径
    /// 
    /// # 安全警告
    /// * 此方法包含时间信息，可能被恶意推断，请谨慎使用
    /// * 建议仅在确实需要按时间组织文件时使用
    pub fn generate_file_path_with_timestamp(prefix: Option<&str>, extension: Option<&str>) -> String {
        let uuid = Uuid::new_v4().to_string();
        let date = chrono::Local::now().format("%Y/%m/%d").to_string();
        
        let extension = extension.map(|ext| format!(".{}", ext)).unwrap_or_default();

        // 如果有前缀，则使用 prefix/date/uuid.ext 格式
        // 如果没有前缀，则使用 date/uuid.ext 格式
        if let Some(prefix) = prefix {
            format!("{}/{}/{}{}", prefix, date, uuid, extension)
        } else {
            format!("{}/{}{}", date, uuid, extension)
        }
    }

    /// 获取MIME类型
    ///
    /// # 参数
    /// * `filename` - 文件名
    ///
    /// # 返回
    /// * `String` - MIME类型
    pub fn get_content_type(filename: &str) -> String {
        if let Some(ext) = Self::get_extension(filename) {
            ContentType::from_extension(&ext).mime_type().to_string()
        } else {
            ContentType::OctetStream.mime_type().to_string()
        }
    }

    /// 获取内容类型枚举
    ///
    /// # 参数
    /// * `filename` - 文件名
    ///
    /// # 返回
    /// * `ContentType` - 内容类型枚举
    pub fn get_content_type_enum(filename: &str) -> ContentType {
        if let Some(ext) = Self::get_extension(filename) {
            ContentType::from_extension(&ext)
        } else {
            ContentType::OctetStream
        }
    }

    /// 验证文件大小
    ///
    /// # 参数
    /// * `size` - 文件大小（字节）
    /// * `max_size` - 最大允许大小（字节）
    ///
    /// # 返回
    /// * `Result<()>` - 如果文件大小合法则返回 Ok，否则返回 Err
    pub fn validate_file_size(size: usize, max_size: usize) -> Result<()> {
        if size > max_size {
            return Err(anyhow!(
                "文件大小超过限制：{:.2} MB，最大允许 {:.2} MB",
                size as f64 / 1024.0 / 1024.0,
                max_size as f64 / 1024.0 / 1024.0
            ));
        }
        Ok(())
    }

    /// 验证文件类型
    ///
    /// # 参数
    /// * `filename` - 文件名
    /// * `allowed_extensions` - 允许的文件扩展名列表
    ///
    /// # 返回
    /// * `Result<()>` - 如果文件类型合法则返回 Ok，否则返回 Err
    pub fn validate_file_type(filename: &str, allowed_extensions: &[&str]) -> Result<()> {
        if let Some(ext) = Self::get_extension(filename) {
            if !allowed_extensions.contains(&ext.as_str()) {
                return Err(anyhow!("不支持的文件类型: {}", ext));
            }
        }
        Ok(())
    }

    /// 获取当前时间的友好显示格式（中文）
    pub fn current_time_friendly() -> String {
        // 使用东八区（北京时间）
        use chrono::TimeZone;
        let china_timezone = chrono::FixedOffset::east_opt(8 * 3600).unwrap(); // UTC+8
        let local_time = china_timezone.from_utc_datetime(&Utc::now().naive_utc());
        local_time.format("%Y年%m月%d日 %H:%M:%S").to_string()
    }

    /// 获取当前时间戳（ISO 8601格式）- 保留原方法用于其他需要ISO格式的场景
    pub fn current_time_iso8601() -> String {
        // 使用东八区（北京时间）
        use chrono::TimeZone;
        let china_timezone = chrono::FixedOffset::east_opt(8 * 3600).unwrap(); // UTC+8
        china_timezone
            .from_utc_datetime(&Utc::now().naive_utc())
            .to_rfc3339()
    }

    /// 从文件路径构建对象键
    ///
    /// # 参数
    /// * `filename` - 原始文件名
    /// * `prefix` - 可选的路径前缀
    ///
    /// # 返回
    /// * `String` - 构建的对象键
    pub fn build_object_key(filename: &str, prefix: Option<&str>) -> String {
        let extension = Self::get_extension(filename);
        Self::generate_file_path(prefix, extension.as_deref())
    }

    /// 获取文件大小的可读表示
    ///
    /// # 参数
    /// * `size_in_bytes` - 文件大小（字节）
    ///
    /// # 返回
    /// * `String` - 格式化后的文件大小
    pub fn get_readable_file_size(size_in_bytes: u64) -> String {
        const KB: f64 = 1024.0;
        const MB: f64 = KB * 1024.0;
        const GB: f64 = MB * 1024.0;

        let size = size_in_bytes as f64;

        if size < KB {
            format!("{:.0} B", size)
        } else if size < MB {
            format!("{:.2} KB", size / KB)
        } else if size < GB {
            format!("{:.2} MB", size / MB)
        } else {
            format!("{:.2} GB", size / GB)
        }
    }

    /// 从 Multipart 表单中提取文件信息
    ///
    /// # 参数
    /// * `multipart` - Axum Multipart 表单
    /// * `allowed_file_fields` - 允许的文件字段名列表（可选，如果为None则接受所有文件字段）
    /// * `max_file_size` - 单个文件最大大小限制（可选，默认50MB）
    /// * `allowed_extensions` - 允许的文件扩展名列表（可选）
    ///
    /// # 返回
    /// * `Result<MultiFileUploadInfo>` - 提取的文件和表单信息
    pub async fn extract_files_from_multipart(
        mut multipart: Multipart,
        allowed_file_fields: Option<&[&str]>,
        max_file_size: Option<usize>,
        allowed_extensions: Option<&[&str]>,
    ) -> Result<MultiFileUploadInfo> {
        let mut files = Vec::new();
        let mut form_fields = std::collections::HashMap::new();
        let max_size = max_file_size.unwrap_or(50 * 1024 * 1024); // 默认50MB

        while let Some(field) = multipart
            .next_field()
            .await
            .map_err(|e| anyhow!("处理multipart字段失败: {}", e))?
        {
            let field_name = field.name().unwrap_or("file").to_string();

            // 检查是否为文件字段
            if let Some(file_name) = field.file_name() {
                // 验证字段名是否允许
                if let Some(allowed_fields) = allowed_file_fields {
                    if !allowed_fields.contains(&field_name.as_str()) {
                        return Err(anyhow!("不允许的文件字段: {}", field_name));
                    }
                }

                let file_name = file_name.to_string();
                let content_type = field.content_type().map(|s| s.to_string());

                // 获取文件数据
                let file_data = field
                    .bytes()
                    .await
                    .map_err(|e| anyhow!("读取文件数据失败: {}", e))?;

                // 先计算文件大小，避免后续移动所有权后无法访问
                let file_size = file_data.len();

                // 验证文件大小
                if file_size > max_size {
                    return Err(anyhow!(
                        "文件 {} 大小超过限制: {:.2} MB，最大允许 {:.2} MB",
                        file_name,
                        file_size as f64 / 1024.0 / 1024.0,
                        max_size as f64 / 1024.0 / 1024.0
                    ));
                }

                // 获取文件扩展名
                let extension = Self::get_extension(&file_name);

                // 验证文件类型
                if let Some(allowed_exts) = allowed_extensions {
                    if let Some(ext) = &extension {
                        if !allowed_exts.contains(&ext.as_str()) {
                            return Err(anyhow!("不支持的文件类型: {}", ext));
                        }
                    } else {
                        return Err(anyhow!("无法确定文件类型: {}", file_name));
                    }
                }

                let file_info = FileInfo {
                    file_name,
                    file_data,
                    content_type,
                    extension,
                    file_size,
                    field_name,
                };

                files.push(file_info);
            } else {
                // 处理普通表单字段
                let value = field
                    .text()
                    .await
                    .map_err(|e| anyhow!("读取表单字段值失败: {}", e))?;
                form_fields.insert(field_name, value);
            }
        }

        Ok(MultiFileUploadInfo { files, form_fields })
    }

    /// 从 Multipart 表单中提取单个文件
    ///
    /// # 参数
    /// * `multipart` - Axum Multipart 表单
    /// * `file_field_name` - 文件字段名（可选，默认为"file"）
    /// * `max_file_size` - 最大文件大小限制（可选，默认50MB）
    /// * `allowed_extensions` - 允许的文件扩展名列表（可选）
    ///
    /// # 返回
    /// * `Result<FileInfo>` - 提取的文件信息
    pub async fn extract_single_file_from_multipart(
        mut multipart: Multipart,
        file_field_name: Option<&str>,
        max_file_size: Option<usize>,
        allowed_extensions: Option<&[&str]>,
    ) -> Result<FileInfo> {
        let target_field = file_field_name.unwrap_or("file");
        let max_size = max_file_size.unwrap_or(50 * 1024 * 1024); // 默认50MB

        while let Some(field) = multipart
            .next_field()
            .await
            .map_err(|e| anyhow!("处理multipart字段失败: {}", e))?
        {
            let field_name = field.name().unwrap_or("").to_string();

            // 检查字段名称是否匹配
            if field_name != target_field {
                continue;
            }

            // 只处理目标文件字段
            if let Some(file_name) = field.file_name() {
                let file_name = file_name.to_string();
                let content_type = field.content_type().map(|s| s.to_string());

                // 获取文件数据
                let file_data = field
                    .bytes()
                    .await
                    .map_err(|e| anyhow!("读取文件数据失败: {}", e))?;

                // 先计算文件大小，避免后续移动所有权后无法访问
                let file_size = file_data.len();

                // 验证文件大小
                if file_size > max_size {
                    return Err(anyhow!(
                        "文件大小超过限制: {:.2} MB，最大允许 {:.2} MB",
                        file_size as f64 / 1024.0 / 1024.0,
                        max_size as f64 / 1024.0 / 1024.0
                    ));
                }

                // 获取文件扩展名
                let extension = Self::get_extension(&file_name);

                // 验证文件类型
                if let Some(allowed_exts) = allowed_extensions {
                    if let Some(ext) = &extension {
                        if !allowed_exts.contains(&ext.as_str()) {
                            return Err(anyhow!("不支持的文件类型: {}", ext));
                        }
                    } else {
                        return Err(anyhow!("无法确定文件类型: {}", file_name));
                    }
                }

                return Ok(FileInfo {
                    file_name,
                    file_data,
                    content_type,
                    extension,
                    file_size,
                    field_name,
                });
            } else {
                return Err(anyhow!("字段 {} 不包含文件", target_field));
            }
        }

        Err(anyhow!("未找到文件字段: {}", target_field))
    }

    /// 验证文件信息
    ///
    /// # 参数
    /// * `file_info` - 文件信息
    /// * `max_file_size` - 最大文件大小限制（可选）
    /// * `allowed_extensions` - 允许的文件扩展名列表（可选）
    ///
    /// # 返回
    /// * `Result<()>` - 验证结果
    pub fn validate_file_info(
        file_info: &FileInfo,
        max_file_size: Option<usize>,
        allowed_extensions: Option<&[&str]>,
    ) -> Result<()> {
        // 验证文件大小
        if let Some(max_size) = max_file_size {
            if file_info.file_size > max_size {
                return Err(anyhow!(
                    "文件 {} 大小超过限制: {:.2} MB，最大允许 {:.2} MB",
                    file_info.file_name,
                    file_info.file_size as f64 / 1024.0 / 1024.0,
                    max_size as f64 / 1024.0 / 1024.0
                ));
            }
        }

        // 验证文件类型
        if let Some(allowed_exts) = allowed_extensions {
            if let Some(ext) = &file_info.extension {
                if !allowed_exts.contains(&ext.as_str()) {
                    return Err(anyhow!("不支持的文件类型: {}", ext));
                }
            } else {
                return Err(anyhow!("无法确定文件类型: {}", file_info.file_name));
            }
        }

        Ok(())
    }
}

/// 常用文件类型常量
pub struct FileTypes;

impl FileTypes {
    /// 图片文件扩展名
    pub const IMAGE_EXTENSIONS: [&'static str; 6] = ["jpg", "jpeg", "png", "gif", "webp", "svg"];

    /// 文档文件扩展名
    pub const DOCUMENT_EXTENSIONS: [&'static str; 7] =
        ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx"];

    /// 音频文件扩展名
    pub const AUDIO_EXTENSIONS: [&'static str; 3] = ["mp3", "wav", "ogg"];

    /// 视频文件扩展名
    pub const VIDEO_EXTENSIONS: [&'static str; 5] = ["mp4", "avi", "mov", "wmv", "webm"];

    /// 压缩文件扩展名
    pub const ARCHIVE_EXTENSIONS: [&'static str; 5] = ["zip", "rar", "tar", "gz", "7z"];

    /// 文本文件扩展名
    pub const TEXT_EXTENSIONS: [&'static str; 5] = ["txt", "html", "htm", "css", "js"];

    /// 所有支持的文件扩展名
    pub fn all_supported_extensions() -> Vec<&'static str> {
        let mut all = Vec::new();
        all.extend_from_slice(&Self::IMAGE_EXTENSIONS);
        all.extend_from_slice(&Self::DOCUMENT_EXTENSIONS);
        all.extend_from_slice(&Self::AUDIO_EXTENSIONS);
        all.extend_from_slice(&Self::VIDEO_EXTENSIONS);
        all.extend_from_slice(&Self::ARCHIVE_EXTENSIONS);
        all.extend_from_slice(&Self::TEXT_EXTENSIONS);
        all
    }
}

/// 文件内容类型枚举
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum ContentType {
    // 图片类型
    Jpeg,
    Png,
    Gif,
    Webp,
    Svg,

    // 文档类型
    Pdf,
    Word,
    WordX,
    Excel,
    ExcelX,
    PowerPoint,
    PowerPointX,

    // 文本类型
    PlainText,
    Html,
    Css,
    JavaScript,
    Json,
    Xml,

    // 压缩文件类型
    Zip,
    Rar,
    Tar,
    GZip,
    SevenZip,

    // 音频类型
    Mp3,
    Wav,
    Ogg,

    // 视频类型
    Mp4,
    Avi,
    QuickTime,
    Wmv,
    Webm,

    // 默认二进制类型
    OctetStream,
}

impl ContentType {
    /// 从文件扩展名获取内容类型
    pub fn from_extension(extension: &str) -> Self {
        match extension.to_lowercase().as_str() {
            // 图片类型
            "jpg" | "jpeg" => ContentType::Jpeg,
            "png" => ContentType::Png,
            "gif" => ContentType::Gif,
            "webp" => ContentType::Webp,
            "svg" => ContentType::Svg,

            // 文档类型
            "pdf" => ContentType::Pdf,
            "doc" => ContentType::Word,
            "docx" => ContentType::WordX,
            "xls" => ContentType::Excel,
            "xlsx" => ContentType::ExcelX,
            "ppt" => ContentType::PowerPoint,
            "pptx" => ContentType::PowerPointX,

            // 文本类型
            "txt" => ContentType::PlainText,
            "html" | "htm" => ContentType::Html,
            "css" => ContentType::Css,
            "js" => ContentType::JavaScript,
            "json" => ContentType::Json,
            "xml" => ContentType::Xml,

            // 压缩文件类型
            "zip" => ContentType::Zip,
            "rar" => ContentType::Rar,
            "tar" => ContentType::Tar,
            "gz" | "gzip" => ContentType::GZip,
            "7z" => ContentType::SevenZip,

            // 音频类型
            "mp3" => ContentType::Mp3,
            "wav" => ContentType::Wav,
            "ogg" => ContentType::Ogg,

            // 视频类型
            "mp4" => ContentType::Mp4,
            "avi" => ContentType::Avi,
            "mov" => ContentType::QuickTime,
            "wmv" => ContentType::Wmv,
            "webm" => ContentType::Webm,

            // 默认
            _ => ContentType::OctetStream,
        }
    }

    /// 获取MIME类型字符串
    pub fn mime_type(&self) -> &'static str {
        match self {
            // 图片类型
            ContentType::Jpeg => "image/jpeg",
            ContentType::Png => "image/png",
            ContentType::Gif => "image/gif",
            ContentType::Webp => "image/webp",
            ContentType::Svg => "image/svg+xml",

            // 文档类型
            ContentType::Pdf => "application/pdf",
            ContentType::Word => "application/msword",
            ContentType::WordX => {
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            }
            ContentType::Excel => "application/vnd.ms-excel",
            ContentType::ExcelX => {
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            }
            ContentType::PowerPoint => "application/vnd.ms-powerpoint",
            ContentType::PowerPointX => {
                "application/vnd.openxmlformats-officedocument.presentationml.presentation"
            }

            // 文本类型
            ContentType::PlainText => "text/plain",
            ContentType::Html => "text/html",
            ContentType::Css => "text/css",
            ContentType::JavaScript => "application/javascript",
            ContentType::Json => "application/json",
            ContentType::Xml => "application/xml",

            // 压缩文件类型
            ContentType::Zip => "application/zip",
            ContentType::Rar => "application/x-rar-compressed",
            ContentType::Tar => "application/x-tar",
            ContentType::GZip => "application/gzip",
            ContentType::SevenZip => "application/x-7z-compressed",

            // 音频类型
            ContentType::Mp3 => "audio/mpeg",
            ContentType::Wav => "audio/wav",
            ContentType::Ogg => "audio/ogg",

            // 视频类型
            ContentType::Mp4 => "video/mp4",
            ContentType::Avi => "video/x-msvideo",
            ContentType::QuickTime => "video/quicktime",
            ContentType::Wmv => "video/x-ms-wmv",
            ContentType::Webm => "video/webm",

            // 默认
            ContentType::OctetStream => "application/octet-stream",
        }
    }
}

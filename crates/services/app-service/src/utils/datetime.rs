use chrono::{
    DateTime, Datelike, Local, NaiveDate, NaiveDateTime, NaiveTime, ParseError, TimeZone, Timelike, Utc,
};
use std::fmt;

/// 日期时间工具类
///
/// 提供常用的日期时间操作，包括：
/// - 获取当前时间（本地时间/UTC时间）
/// - 时间格式化
/// - 字符串解析为时间
/// - 常用时间计算
pub struct DateTimeUtils;

/// 自定义日期时间错误类型
#[derive(Debug)]
pub enum DateTimeError {
    ParseError(ParseError),
    InvalidFormat(String),
}

impl fmt::Display for DateTimeError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            DateTimeError::ParseError(e) => write!(f, "日期解析错误: {}", e),
            DateTimeError::InvalidFormat(s) => write!(f, "无效的日期格式: {}", s),
        }
    }
}

impl std::error::Error for DateTimeError {}

impl From<ParseError> for DateTimeError {
    fn from(error: ParseError) -> Self {
        DateTimeError::ParseError(error)
    }
}

impl DateTimeUtils {    
    // ==================== 获取当前时间 ====================

    /// 获取当前本地时间
    pub fn now_local() -> DateTime<Local> {
        Local::now()
    }

    /// 获取当前UTC时间
    pub fn now_utc() -> DateTime<Utc> {
        Utc::now()
    }

    /// 获取今天的日期（本地时区）
    pub fn today() -> NaiveDate {
        Local::now().date_naive()
    }

    /// 获取当前时间（本地时区，仅时分秒）
    pub fn current_time() -> NaiveTime {
        Local::now().time()
    }

    /// 获取当前北京时间（东八区，FixedOffset）
    pub fn now_china_offset() -> chrono::DateTime<chrono::FixedOffset> {
        let local = chrono::Local::now();
        let china_offset = chrono::FixedOffset::east_opt(8 * 3600).unwrap();
        local.with_timezone(&china_offset)
    }

    // ==================== 时间格式化 ====================

    /// 格式化为标准日期时间字符串 (YYYY-MM-DD HH:MM:SS)
    pub fn format_datetime(dt: &DateTime<Local>) -> String {
        dt.format("%Y-%m-%d %H:%M:%S").to_string()
    }

    /// 格式化为日期字符串 (YYYY-MM-DD)
    pub fn format_date(dt: &DateTime<Local>) -> String {
        dt.format("%Y-%m-%d").to_string()
    }

    /// 格式化为时间字符串 (HH:MM:SS)
    pub fn format_time(dt: &DateTime<Local>) -> String {
        dt.format("%H:%M:%S").to_string()
    }

    /// 格式化为中文日期时间字符串
    pub fn format_chinese_datetime(dt: &DateTime<Local>) -> String {
        dt.format("%Y年%m月%d日 %H:%M:%S").to_string()
    }

    /// 自定义格式化
    pub fn format_custom(dt: &DateTime<Local>, format: &str) -> String {
        dt.format(format).to_string()
    }

    // ==================== 字符串解析 ====================

    /// 解析标准日期时间字符串 (YYYY-MM-DD HH:MM:SS)
    pub fn parse_datetime(date_str: &str) -> Result<DateTime<Local>, DateTimeError> {
        let naive_dt = NaiveDateTime::parse_from_str(date_str, "%Y-%m-%d %H:%M:%S")?;
        Ok(Local
            .from_local_datetime(&naive_dt)
            .single()
            .ok_or_else(|| DateTimeError::InvalidFormat(date_str.to_string()))?)
    }
    

    /// 解析时间字符串 (HH:MM:SS)
    pub fn parse_time(time_str: &str) -> Result<NaiveTime, DateTimeError> {
        Ok(NaiveTime::parse_from_str(time_str, "%H:%M:%S")?)
    }

    /// 解析时间字符串 (HH:MM)
    pub fn parse_time_hm(time_str: &str) -> Result<NaiveTime, DateTimeError> {
        Ok(NaiveTime::parse_from_str(time_str, "%H:%M")?)
    }

    /// 智能解析 - 尝试多种格式
    pub fn parse_smart(date_str: &str) -> Result<DateTime<Local>, DateTimeError> {
        // 常见格式列表
        let formats = vec![
            "%Y-%m-%d %H:%M:%S",     // 2024-01-15 14:30:00
            "%Y-%m-%d %H:%M",        // 2024-01-15 14:30
            "%Y-%m-%d",              // 2024-01-15
            "%Y/%m/%d %H:%M:%S",     // 2024/01/15 14:30:00
            "%Y/%m/%d %H:%M",        // 2024/01/15 14:30
            "%Y/%m/%d",              // 2024/01/15
            "%d-%m-%Y %H:%M:%S",     // 15-01-2024 14:30:00
            "%d/%m/%Y %H:%M:%S",     // 15/01/2024 14:30:00
            "%Y年%m月%d日 %H:%M:%S", // 2024年01月15日 14:30:00
            "%Y年%m月%d日",          // 2024年01月15日
        ];

        for format in formats {
            if let Ok(naive_dt) = NaiveDateTime::parse_from_str(date_str, format) {
                if let Some(local_dt) = Local.from_local_datetime(&naive_dt).single() {
                    return Ok(local_dt);
                }
            }

            // 尝试仅日期格式
            if let Ok(naive_date) = NaiveDate::parse_from_str(date_str, format) {
                let naive_dt = naive_date
                    .and_hms_opt(0, 0, 0)
                    .ok_or_else(|| DateTimeError::InvalidFormat(date_str.to_string()))?;
                if let Some(local_dt) = Local.from_local_datetime(&naive_dt).single() {
                    return Ok(local_dt);
                }
            }
        }

        Err(DateTimeError::InvalidFormat(format!(
            "无法解析日期字符串: {}",
            date_str
        )))
    }

    // ==================== 时间计算 ====================

    /// 获取指定天数前的日期
    pub fn days_ago(days: i64) -> DateTime<Local> {
        Local::now() - chrono::Duration::days(days)
    }

    /// 获取指定天数后的日期
    pub fn days_later(days: i64) -> DateTime<Local> {
        Local::now() + chrono::Duration::days(days)
    }

    /// 获取本周开始时间（周一00:00:00）
    pub fn start_of_week() -> DateTime<Local> {
        let now = Local::now();
        let weekday = now.weekday().num_days_from_monday();
        let start_of_day = now.date_naive().and_hms_opt(0, 0, 0).unwrap();
        Local.from_local_datetime(&start_of_day).single().unwrap()
            - chrono::Duration::days(weekday as i64)
    }

    /// 获取本月开始时间（1号00:00:00）
    pub fn start_of_month() -> DateTime<Local> {
        let now = Local::now();
        let first_day = now.date_naive().with_day(1).unwrap();
        let start_of_day = first_day.and_hms_opt(0, 0, 0).unwrap();
        Local.from_local_datetime(&start_of_day).single().unwrap()
    }

    /// 获取本年开始时间（1月1日00:00:00）
    pub fn start_of_year() -> DateTime<Local> {
        let now = Local::now();
        let first_day = NaiveDate::from_ymd_opt(now.year(), 1, 1).unwrap();
        let start_of_day = first_day.and_hms_opt(0, 0, 0).unwrap();
        Local.from_local_datetime(&start_of_day).single().unwrap()
    }

    /// 计算两个日期之间的天数差
    pub fn days_between(start: &DateTime<Local>, end: &DateTime<Local>) -> i64 {
        (end.date_naive() - start.date_naive()).num_days()
    }

    /// 判断是否为今天
    pub fn is_today(dt: &DateTime<Local>) -> bool {
        dt.date_naive() == Local::now().date_naive()
    }

    /// 判断是否为本周
    pub fn is_this_week(dt: &DateTime<Local>) -> bool {
        let start_of_week = Self::start_of_week();
        let end_of_week = start_of_week + chrono::Duration::days(7);
        *dt >= start_of_week && *dt < end_of_week
    }

    /// 判断是否为本月
    pub fn is_this_month(dt: &DateTime<Local>) -> bool {
        let now = Local::now();
        dt.year() == now.year() && dt.month() == now.month()
    }

    // ==================== 便捷方法 ====================

    /// 获取友好的时间描述（如：刚刚、5分钟前、昨天等）
    pub fn friendly_time(dt: &DateTime<Local>) -> String {
        let now = Local::now();
        let duration = now.signed_duration_since(*dt);

        if duration.num_seconds() < 60 {
            "刚刚".to_string()
        } else if duration.num_minutes() < 60 {
            format!("{}分钟前", duration.num_minutes())
        } else if duration.num_hours() < 24 {
            format!("{}小时前", duration.num_hours())
        } else if duration.num_days() == 1 {
            "昨天".to_string()
        } else if duration.num_days() < 7 {
            format!("{}天前", duration.num_days())
        } else if duration.num_days() < 30 {
            format!("{}周前", duration.num_days() / 7)
        } else if duration.num_days() < 365 {
            format!("{}个月前", duration.num_days() / 30)
        } else {
            format!("{}年前", duration.num_days() / 365)
        }
    }

    /// 时间戳转换为本地时间
    pub fn timestamp_to_local(timestamp: i64) -> Result<DateTime<Local>, DateTimeError> {
        let utc_dt = DateTime::from_timestamp(timestamp, 0)
            .ok_or_else(|| DateTimeError::InvalidFormat(format!("无效的时间戳: {}", timestamp)))?;
        Ok(utc_dt.with_timezone(&Local))
    }

    /// 本地时间转换为时间戳
    pub fn local_to_timestamp(dt: &DateTime<Local>) -> i64 {
        dt.timestamp()
    }

    // ==================== 便利方法：范围查询专用 ====================

    /// 解析日期字符串为当天开始时间 (YYYY-MM-DD -> YYYY-MM-DD 00:00:00)
    pub fn parse_date_start(date_str: &str) -> Result<DateTime<Local>, DateTimeError> {
        Self::parse_datetime(&format!("{} 00:00:00", date_str))
    }

    /// 解析日期字符串为当天结束时间 (YYYY-MM-DD -> YYYY-MM-DD 23:59:59)
    pub fn parse_date_end(date_str: &str) -> Result<DateTime<Local>, DateTimeError> {
        Self::parse_datetime(&format!("{} 23:59:59", date_str))
    }

    /// 智能解析日期范围的开始时间
    /// 如果传入完整的日期时间，直接解析；如果只有日期，则设为当天00:00:00
    pub fn parse_range_start(date_str: &str) -> Result<DateTime<Local>, DateTimeError> {
        // 先尝试智能解析
        if let Ok(dt) = Self::parse_smart(date_str) {
            // 如果解析成功但是时间为00:00:00，说明可能只传入了日期
            let time = dt.time();
            if time.hour() == 0 && time.minute() == 0 && time.second() == 0 {
                return Ok(dt);
            }
            return Ok(dt);
        }

        // 如果智能解析失败，尝试作为纯日期解析
        Self::parse_date_start(date_str)
    }

    /// 智能解析日期范围的结束时间
    /// 如果传入完整的日期时间，直接解析；如果只有日期，则设为当天23:59:59
    pub fn parse_range_end(date_str: &str) -> Result<DateTime<Local>, DateTimeError> {
        // 先尝试智能解析
        if let Ok(dt) = Self::parse_smart(date_str) {
            // 如果解析成功但是时间为00:00:00，说明可能只传入了日期，需要设为当天结束
            let time = dt.time();
            if time.hour() == 0 && time.minute() == 0 && time.second() == 0 {
                return Self::parse_date_end(&DateTimeUtils::format_date(&dt));
            }
            return Ok(dt);
        }

        // 如果智能解析失败，尝试作为纯日期解析
        Self::parse_date_end(date_str)
    }

    /// 获取当前时间的友好显示（用于日志和调试）
    pub fn now_display() -> String {
        Self::format_datetime(&Local::now())
    }

    /// 获取当前日期的友好显示
    pub fn today_display() -> String {
        Self::format_date(&Local::now())
    }

    // ==================== 通用时间转换方法 ====================

    /// 安全地将任意时区的 DateTime 转换为本地时间并格式化
    pub fn format_to_local_string<Tz: TimeZone>(dt: DateTime<Tz>) -> String 
    where 
        Tz::Offset: fmt::Display 
    {
        let local_dt = dt.with_timezone(&Local);
        local_dt.format("%Y-%m-%d %H:%M:%S").to_string()
    }
    
    /// 将 Option<DateTime<Tz>> 转换为 Option<String>
    pub fn format_optional_to_local<Tz: TimeZone>(dt_opt: Option<DateTime<Tz>>) -> Option<String>
    where 
        Tz::Offset: fmt::Display 
    {
        dt_opt.map(|dt| Self::format_to_local_string(dt))
    }

    /// 自定义格式将任意时区的 DateTime 转换为本地时间并格式化
    pub fn format_to_local_custom<Tz: TimeZone>(dt: DateTime<Tz>, format: &str) -> String 
    where 
        Tz::Offset: fmt::Display 
    {
        let local_dt = dt.with_timezone(&Local);
        local_dt.format(format).to_string()
    }

    /// 将任意时区的 DateTime 转换为本地 DateTime
    pub fn to_local<Tz: TimeZone>(dt: DateTime<Tz>) -> DateTime<Local>
    where 
        Tz::Offset: fmt::Display 
    {
        dt.with_timezone(&Local)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::{FixedOffset, TimeZone};

    #[test]
    fn test_format_to_local_string() {
        // 测试 UTC 时间转换为本地时间字符串
        let utc_time = Utc.with_ymd_and_hms(2024, 1, 15, 10, 30, 0).unwrap();
        let result = DateTimeUtils::format_to_local_string(utc_time);
        
        // 验证格式正确（具体时间会根据本地时区而变化）
        assert!(result.contains("2024-01-15"));
        assert!(result.len() == 19); // "YYYY-MM-DD HH:MM:SS" 长度为19
    }

    #[test]
    fn test_format_to_local_string_with_fixed_offset() {
        // 测试固定偏移时区转换
        let offset = FixedOffset::east_opt(8 * 3600).unwrap(); // 东八区
        let offset_time = offset.with_ymd_and_hms(2024, 1, 15, 18, 30, 0).unwrap();
        let result = DateTimeUtils::format_to_local_string(offset_time);
        
        assert!(result.contains("2024-01-15"));
        assert!(result.len() == 19);
    }

    #[test]
    fn test_format_optional_to_local_some() {
        // 测试 Some(DateTime) 的转换
        let utc_time = Utc.with_ymd_and_hms(2024, 1, 15, 10, 30, 0).unwrap();
        let result = DateTimeUtils::format_optional_to_local(Some(utc_time));
        
        assert!(result.is_some());
        let formatted = result.unwrap();
        assert!(formatted.contains("2024-01-15"));
        assert!(formatted.len() == 19);
    }

    #[test]
    fn test_format_optional_to_local_none() {
        // 测试 None 的处理
        let result: Option<String> = DateTimeUtils::format_optional_to_local(None::<DateTime<Utc>>);
        assert!(result.is_none());
    }

    #[test]
    fn test_format_to_local_custom() {
        // 测试自定义格式
        let utc_time = Utc.with_ymd_and_hms(2024, 1, 15, 10, 30, 0).unwrap();
        let result = DateTimeUtils::format_to_local_custom(utc_time, "%Y/%m/%d %H:%M");
        
        assert!(result.contains("2024/01/15"));
        assert!(result.len() == 16); // "YYYY/MM/DD HH:MM" 长度为16
    }

    #[test]
    fn test_to_local() {
        // 测试时区转换
        let utc_time = Utc.with_ymd_and_hms(2024, 1, 15, 10, 30, 0).unwrap();
        let local_time = DateTimeUtils::to_local(utc_time);
        
        // 验证转换后的类型是 DateTime<Local>
        assert_eq!(local_time.date_naive(), utc_time.date_naive());
    }

    #[test]
    fn test_format_consistency() {
        // 测试格式化的一致性
        let utc_time = Utc.with_ymd_and_hms(2024, 1, 15, 10, 30, 0).unwrap();
        
        // 使用不同方法格式化同一时间
        let method1 = DateTimeUtils::format_to_local_string(utc_time);
        let method2 = DateTimeUtils::format_to_local_custom(utc_time, "%Y-%m-%d %H:%M:%S");
        let method3 = DateTimeUtils::format_optional_to_local(Some(utc_time)).unwrap();
        
        // 三种方法应该产生相同的结果
        assert_eq!(method1, method2);
        assert_eq!(method1, method3);
    }

    #[test]
    fn test_different_timezones() {
        // 测试不同时区的转换
        let utc_time = Utc.with_ymd_and_hms(2024, 1, 15, 12, 0, 0).unwrap();
        let east8 = FixedOffset::east_opt(8 * 3600).unwrap();
        let west5 = FixedOffset::west_opt(5 * 3600).unwrap();
        
        let utc_result = DateTimeUtils::format_to_local_string(utc_time);
        let east8_result = DateTimeUtils::format_to_local_string(utc_time.with_timezone(&east8));
        let west5_result = DateTimeUtils::format_to_local_string(utc_time.with_timezone(&west5));
        
        // 所有结果都应该是有效的时间字符串格式
        assert!(utc_result.len() == 19);
        assert!(east8_result.len() == 19);
        assert!(west5_result.len() == 19);
        
        // 验证日期部分相同（因为都是同一个时刻）
        assert!(utc_result.starts_with("2024-01-15"));
        assert!(east8_result.starts_with("2024-01-15"));
        assert!(west5_result.starts_with("2024-01-15"));
    }
}

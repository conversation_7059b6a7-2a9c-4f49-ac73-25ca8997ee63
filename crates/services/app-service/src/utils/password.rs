use argon2::{
    Algorithm, Argon2, Params, Version,
    password_hash::{PasswordHash, PasswordHasher, PasswordVerifier, SaltString, rand_core::OsRng},
};
use tracing::{error, info};

/// 密码工具模块
/// 提供密码加密和验证功能

/// Argon2id 参数配置
/// 平衡性能和安全性的生产环境参数
fn get_default_argon2_params() -> Params {
    // 平衡参数：64KB内存，2次迭代，1个并行度
    // 验证时间约100-200ms，安全性适中
    Params::new(64, 2, 1, None).expect("Valid Argon2 parameters")
}

/// 对密码进行加密
///
/// 使用平衡的 Argon2id 参数：
/// - 内存：64KB
/// - 迭代：2次
/// - 并行：1线程
/// - 预期验证时间：100-200ms
///
/// # 参数级别参考：
/// - 开发环境：`Params::new(8, 1, 1, None)`     // 8KB内存，1次迭代，~10-50ms
/// - 生产环境：`Params::new(64, 2, 1, None)`    // 64KB内存，2次迭代，~100-200ms
/// - 高安全：`Params::new(19456, 2, 1, None)`   // 19MB内存，2次迭代，~300-500ms
///
/// # 参数
/// * `password` - 原始密码
///
/// # 返回
/// * `Result<String, String>` - 成功返回加密后的密码，失败返回错误信息
pub fn hash_password(password: &str) -> Result<String, String> {
    let params = get_default_argon2_params();
    let argon2 = Argon2::new(Algorithm::Argon2id, Version::V0x13, params);

    // 生成随机盐
    let salt = SaltString::generate(&mut OsRng);

    // 哈希密码
    match argon2.hash_password(password.as_bytes(), &salt) {
        Ok(password_hash) => {
            info!("密码加密成功 (Argon2id, m=64, t=2, p=1)");
            Ok(password_hash.to_string())
        }
        Err(err) => {
            error!("密码加密失败: {}", err);
            Err(format!("密码加密失败: {}", err))
        }
    }
}

/// 验证密码是否匹配
///
/// 重要：Argon2验证时会自动使用密码哈希中嵌入的参数，
/// 但为了确保一致性，我们仍然使用相同的参数配置
///
/// # 参数
/// * `password` - 原始密码
/// * `hashed_password` - 加密后的密码
///
/// # 返回
/// * `Result<bool, String>` - 成功返回是否匹配，失败返回错误信息
pub fn verify_password(password: &str, hashed_password: &str) -> Result<bool, String> {
    // 解析密码哈希字符串
    let parsed_hash = PasswordHash::new(hashed_password)
        .map_err(|e| format!("Invalid password hash format: {}", e))?;

    // 创建 Argon2 验证器 - 使用与加密时相同的参数
    let params = get_default_argon2_params();
    let argon2 = Argon2::new(Algorithm::Argon2id, Version::V0x13, params);

    // 验证密码
    match argon2.verify_password(password.as_bytes(), &parsed_hash) {
        Ok(_) => Ok(true),
        Err(argon2::password_hash::Error::Password) => Ok(false),
        Err(err) => {
            error!("密码验证失败: {}", err);
            Err(format!("密码验证失败: {}", err))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_password_hash_and_verify() {
        let password = "123456";
        let hashed = hash_password(password).unwrap();
        println!("Hashed password: {}", hashed);

        // 验证成功
        let result = verify_password(password, &hashed).unwrap();
        assert!(result);

        // 验证失败
        let result = verify_password("wrong_password", &hashed).unwrap();
        assert!(!result);
    }

    #[test]
    fn test_argon2_parameters() {
        let password = "test_password";
        let hashed = hash_password(password).unwrap();

        // 验证使用的是平衡的 Argon2id 参数
        assert!(hashed.contains("m=64")); // 64KB内存
        assert!(hashed.contains("t=2")); // 2次迭代
        assert!(hashed.contains("p=1")); // 1个并行度
        assert!(hashed.starts_with("$argon2id$v=19$")); // Argon2id v19
    }

    #[test]
    fn test_multiple_hashes_different() {
        let password = "123456";
        let hash1 = hash_password(password).unwrap();
        let hash2 = hash_password(password).unwrap();
        println!("Hash 1: {}, Hash 2: {}", hash1, hash2);

        // 相同密码的两次哈希应该不同（因为盐不同）
        assert_ne!(hash1, hash2);

        // 但都应该能验证成功
        assert!(verify_password(password, &hash1).unwrap());
        assert!(verify_password(password, &hash2).unwrap());
    }
}

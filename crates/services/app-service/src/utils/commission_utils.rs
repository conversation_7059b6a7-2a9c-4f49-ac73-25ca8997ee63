use rust_decimal::Decimal;
use std::str::FromStr;
use validator::ValidationError;

/// 佣金转换工具类
/// 
/// 提供统一的百分比转换、验证和处理功能，支持多种输入格式：
/// - 百分比格式：3%、3.5%、0.5%
/// - 小数格式：0.03、0.035、0.005
/// - 整数格式（自动转换为百分比）：3、5、10
pub struct CommissionUtils;

/// 佣金类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum CommissionType {
    /// 百分比佣金 - 按照百分比计算佣金
    Percentage,
    /// 固定金额佣金 - 按照固定金额计算佣金
    FixedAmount,
}

/// 佣金转换结果
#[derive(Debug, Clone, PartialEq)]
pub struct CommissionResult {
    /// 转换后的decimal值
    pub value: Decimal,
    /// 原始输入字符串
    pub original: String,
    /// 佣金类型
    pub commission_type: CommissionType,
}

impl CommissionUtils {
    /// 解析平台佣金百分比
    /// 
    /// 输入格式支持：
    /// - "3%" -> 0.03
    /// - "3.5%" -> 0.035
    /// - "0.03" -> 0.03
    /// - "3" -> 0.03 (自动转换为百分比)
    /// 
    /// # Arguments
    /// * `input` - 输入的百分比字符串
    /// 
    /// # Returns
    /// * `Result<Decimal, String>` - 转换后的decimal值或错误信息
    pub fn parse_platform_commission(input: &str) -> Result<Decimal, String> {
        let trimmed = input.trim();
        
        if trimmed.is_empty() {
            return Err("佣金比例不能为空".to_string());
        }

        // 处理百分比格式 (如 "3%", "3.5%")
        if trimmed.ends_with('%') {
            let percentage_str = &trimmed[..trimmed.len() - 1];
            let percentage = percentage_str.parse::<f64>()
                .map_err(|_| "百分比格式不正确".to_string())?;
                
            if percentage < 0.0 || percentage > 100.0 {
                return Err("平台佣金比例必须在0%-100%之间".to_string());
            }
            
            let decimal_value = Decimal::from_str(&format!("{:.4}", percentage / 100.0))
                .map_err(|_| "转换decimal失败".to_string())?;
            
            return Ok(decimal_value);
        }

        // 处理小数格式 (如 "0.03", "0.035")
        let decimal_value = Decimal::from_str(trimmed)
            .map_err(|_| "数值格式不正确".to_string())?;
            
        // 判断是否为小数格式 (0-1之间) 还是整数格式
        if decimal_value >= Decimal::from(0) && decimal_value <= Decimal::from(1) {
            // 小数格式，直接使用
            Ok(decimal_value)
        } else if decimal_value > Decimal::from(1) && decimal_value <= Decimal::from(100) {
            // 检查是否为整数（避免1.5这样的小数）
            if decimal_value.fract() == Decimal::from(0) {
                // 整数格式，转换为百分比
                let percentage_decimal = decimal_value / Decimal::from(100);
                Ok(percentage_decimal)
            } else {
                Err("大于1的数值必须是整数或使用百分比格式（如3%）".to_string())
            }
        } else {
            Err("平台佣金比例必须在0-100之间".to_string())
        }
    }

    /// 解析跟进人佣金
    /// 
    /// 根据佣金类型解析不同格式的佣金值
    /// 
    /// # Arguments
    /// * `input` - 输入的佣金字符串
    /// * `commission_type` - 佣金类型 (1=百分比, 2=固定金额)
    /// 
    /// # Returns
    /// * `Result<Decimal, String>` - 转换后的decimal值或错误信息
    pub fn parse_follower_commission(input: &str, commission_type: i32) -> Result<Decimal, String> {
        let trimmed = input.trim();
        
        if trimmed.is_empty() {
            return Err("佣金值不能为空".to_string());
        }

        match commission_type {
            1 => {
                // 百分比类型
                Self::parse_platform_commission(input)
            }
            2 => {
                // 固定金额类型
                Self::parse_fixed_amount(input)
            }
            _ => Err("无效的佣金类型".to_string())
        }
    }

    /// 解析固定金额佣金
    /// 
    /// # Arguments
    /// * `input` - 输入的金额字符串
    /// 
    /// # Returns
    /// * `Result<Decimal, String>` - 转换后的decimal值或错误信息
    pub fn parse_fixed_amount(input: &str) -> Result<Decimal, String> {
        let trimmed = input.trim();
        
        if trimmed.is_empty() {
            return Err("金额不能为空".to_string());
        }

        let decimal_value = Decimal::from_str(trimmed)
            .map_err(|_| "金额格式不正确".to_string())?;
            
        if decimal_value < Decimal::from(0) {
            return Err("金额不能为负数".to_string());
        }
        
        if decimal_value > Decimal::from(999999) {
            return Err("金额不能超过999999".to_string());
        }

        Ok(decimal_value)
    }

    /// 验证平台佣金比例
    /// 
    /// 用于validator的自定义验证函数
    /// 
    /// # Arguments
    /// * `rate_str` - 佣金比例字符串
    /// 
    /// # Returns
    /// * `Result<(), ValidationError>` - 验证结果
    pub fn validate_platform_commission(rate_str: &str) -> Result<(), ValidationError> {
        match Self::parse_platform_commission(rate_str) {
            Ok(_) => Ok(()),
            Err(msg) => {
                let mut error = ValidationError::new("commission_rate_invalid");
                error.message = Some(msg.into());
                Err(error)
            }
        }
    }

    /// 验证跟进人佣金
    /// 
    /// 用于validator的自定义验证函数
    /// 
    /// # Arguments
    /// * `commission_value` - 佣金值字符串
    /// * `commission_type` - 佣金类型
    /// 
    /// # Returns
    /// * `Result<(), ValidationError>` - 验证结果
    pub fn validate_follower_commission(commission_value: &str, commission_type: i32) -> Result<(), ValidationError> {
        match Self::parse_follower_commission(commission_value, commission_type) {
            Ok(_) => Ok(()),
            Err(msg) => {
                let mut error = ValidationError::new("follower_commission_invalid");
                error.message = Some(msg.into());
                Err(error)
            }
        }
    }

    /// 格式化佣金比例为百分比显示
    /// 
    /// # Arguments
    /// * `decimal_value` - decimal值
    /// 
    /// # Returns
    /// * `String` - 格式化后的百分比字符串
    pub fn format_percentage(decimal_value: Decimal) -> String {
        let percentage = decimal_value * Decimal::from(100);
        format!("{:.2}%", percentage)
    }

    /// 格式化固定金额显示
    /// 
    /// # Arguments
    /// * `decimal_value` - decimal值
    /// 
    /// # Returns
    /// * `String` - 格式化后的金额字符串
    pub fn format_fixed_amount(decimal_value: Decimal) -> String {
        format!("{:.2}", decimal_value)
    }
}
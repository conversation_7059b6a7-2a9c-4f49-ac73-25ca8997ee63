use serde::Serialize;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;

// ==================== 角色响应相关 VO ====================

/// 角色列表响应VO (用于分页查询)
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "系统管理员",
    "description": "拥有系统全部权限",
    "user_count": 5,
    "permission_count": 20,
    "created_date": "2023-01-01 00:00:00",
    "updated_date": "2023-05-24 10:30:00",
    "remark": "系统默认角色"
}))]
pub struct SysRoleListResponse {
    /// 角色ID
    pub id: Uuid,
    /// 角色名称
    pub name: String,
    /// 角色描述
    pub description: Option<String>,
    /// 拥有该角色的用户数量
    pub user_count: u64,
    /// 角色拥有的权限数量
    pub permission_count: u64,
    /// 创建时间
    pub created_date: String,
    /// 更新时间
    pub updated_date: String,
    /// 备注
    pub remark: Option<String>,
}

/// 角色详情响应VO (包含完整信息)
#[derive(Debug, Serialize, ToSchema)]
pub struct SysRoleDetailResponse {
    /// 角色ID
    pub id: Uuid,
    /// 角色名称
    pub name: String,
    /// 角色描述
    pub description: Option<String>,
    /// 创建时间
    pub created_date: String,
    /// 更新时间
    pub updated_date: String,
    /// 创建人ID
    pub created_by: Option<Uuid>,
    /// 更新人ID
    pub updated_by: Option<Uuid>,
    /// 备注
    pub remark: Option<String>,
    /// 角色拥有的权限列表
    pub permissions: Vec<SysRolePermissionVo>,
    /// 拥有该角色的用户列表
    pub users: Vec<SysRoleUserVo>,
}

/// 角色权限详情VO
#[derive(Debug, Serialize, ToSchema)]
pub struct SysRolePermissionVo {
    /// 权限ID
    pub id: Uuid,
    /// 权限名称
    pub menu_name: String,
    /// 权限标识
    pub perms: Option<String>,
    /// 权限类型 1:目录，2:菜单，3:按钮
    pub menu_type: Option<i32>,
    /// 父级权限ID
    pub parent_id: Option<Uuid>,
}

/// 角色用户详情VO
#[derive(Debug, Serialize, ToSchema)]
pub struct SysRoleUserVo {
    /// 用户ID
    pub id: Uuid,
    /// 用户名
    pub username: String,
    /// 真实姓名
    pub real_name: Option<String>,
    /// 邮箱
    pub email: Option<String>,
    /// 用户状态 1:启用 2:禁用 3:锁定
    pub status: i32,
}

/// 角色简单信息VO（用于下拉选择等场景）
#[derive(Debug, Serialize, ToSchema)]
pub struct SysRoleSimpleResponse {
    /// 角色ID
    pub id: Uuid,
    /// 角色名称
    pub name: String,
    /// 角色编码
    pub role_code: Option<String>,
    /// 角色描述
    pub description: Option<String>,
}


/// 角色简单信息VO（用于下拉选择等场景）
#[derive(Debug, Serialize, ToSchema)]
pub struct SysRoleSimpleAndUserCountResponse {
    /// 角色ID
    pub id: Uuid,
    /// 角色名称
    pub name: String,
    // 该角色下用户数量
    pub user_count: i32,
    /// 角色描述
    pub description: Option<String>,
}

// ============================================================================
// 实体类转 VO - 转换方法
// ============================================================================

use crate::domain::system::entities::roles::Model as RoleModel;
use crate::utils::datetime::DateTimeUtils;

impl From<RoleModel> for SysRoleListResponse {
    fn from(model: RoleModel) -> Self {
        Self {
            id: model.id,
            name: model.name,
            description: model.description,
            user_count: 0,       // 默认为0，需要单独设置
            permission_count: 0, // 默认为0，需要单独设置
            created_date: DateTimeUtils::format_datetime(&model.created_date),
            updated_date: DateTimeUtils::format_datetime(&model.updated_date),
            remark: model.remark,
        }
    }
}

impl From<RoleModel> for SysRoleDetailResponse {
    fn from(model: RoleModel) -> Self {
        Self {
            id: model.id,
            name: model.name,
            description: model.description,
            created_date: DateTimeUtils::format_datetime(&model.created_date),
            updated_date: DateTimeUtils::format_datetime(&model.updated_date),
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
            permissions: vec![], // 默认为空，需要单独填充
            users: vec![],       // 默认为空，需要单独填充
        }
    }
}

impl From<RoleModel> for SysRoleSimpleResponse {
    fn from(model: RoleModel) -> Self {
        Self {
            id: model.id,
            name: model.name,
            description: model.description,
            role_code: model.role_code,
        }
    }
}

// ============================================================================
// 便捷构造函数
// ============================================================================

impl SysRoleListResponse {
    /// 设置用户数量
    pub fn with_user_count(mut self, user_count: u64) -> Self {
        self.user_count = user_count;
        self
    }

    /// 设置权限数量
    pub fn with_permission_count(mut self, permission_count: u64) -> Self {
        self.permission_count = permission_count;
        self
    }

    /// 同时设置用户数和权限数
    pub fn with_counts(mut self, user_count: u64, permission_count: u64) -> Self {
        self.user_count = user_count;
        self.permission_count = permission_count;
        self
    }
}

impl SysRoleDetailResponse {
    /// 填充权限信息
    pub fn with_permissions(mut self, permissions: Vec<SysRolePermissionVo>) -> Self {
        self.permissions = permissions;
        self
    }

    /// 填充用户信息
    pub fn with_users(mut self, users: Vec<SysRoleUserVo>) -> Self {
        self.users = users;
        self
    }

    /// 同时填充权限和用户信息
    pub fn with_permissions_and_users(
        mut self,
        permissions: Vec<SysRolePermissionVo>,
        users: Vec<SysRoleUserVo>,
    ) -> Self {
        self.permissions = permissions;
        self.users = users;
        self
    }
}

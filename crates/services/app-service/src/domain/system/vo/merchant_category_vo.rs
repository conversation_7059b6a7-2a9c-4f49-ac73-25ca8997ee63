use bon::Builder;
use sea_orm::ActiveEnum;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;

/// 商户分类列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "category_name": "餐饮",
    "category_code": "CATERING",
    "sort_order": 1,
    "status": 1,
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00"
}))]
pub struct SysMerchantCategoryListResponse {
    /// 分类ID
    pub id: Uuid,

    /// 分类名称
    pub category_name: String,

    /// 分类编码
    pub category_code: Option<String>,

    /// 分类描述
    pub description: Option<String>,

    /// 排序字段
    pub sort_order: Option<i32>,

    /// 状态（1启用 2禁用）
    pub status: i32,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,
}

/// 商户分类详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "category_name": "餐饮",
    "category_code": "CATERING",
    "description": "餐饮类商户分类",
    "sort_order": 1,
    "status": 1,
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00",
    "created_by": "550e8400-e29b-41d4-a716-************",
    "updated_by": "550e8400-e29b-41d4-a716-************",
    "remark": "餐饮类商户分类备注"
}))]
pub struct SysMerchantCategoryDetailResponse {
    /// 分类ID
    pub id: Uuid,

    /// 分类名称
    pub category_name: String,

    /// 分类编码
    pub category_code: Option<String>,

    /// 分类描述
    pub description: Option<String>,

    /// 排序字段
    pub sort_order: Option<i32>,

    /// 状态（1启用 2禁用）
    pub status: i32,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,

    /// 创建人ID
    pub created_by: Option<Uuid>,

    /// 更新人ID
    pub updated_by: Option<Uuid>,

    /// 备注
    pub remark: Option<String>,
}

/// 商户分类选择项（用于下拉选择）
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "category_name": "餐饮",
    "category_code": "CATERING",
    "sort_order": 1
}))]
pub struct SysMerchantCategorySelectItem {
    /// 分类ID
    pub id: Uuid,

    /// 分类名称
    pub category_name: String,

    /// 分类编码
    pub category_code: Option<String>,

    /// 排序字段
    pub sort_order: Option<i32>,
}

/// 商户分类统计信息
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "category_id": "550e8400-e29b-41d4-a716-************",
    "category_name": "餐饮",
    "merchant_count": 25,
    "active_merchant_count": 20,
    "inactive_merchant_count": 5
}))]
pub struct SysMerchantCategoryStatsResponse {
    /// 分类ID
    pub category_id: Uuid,

    /// 分类名称
    pub category_name: String,

    /// 商户总数
    pub merchant_count: i32,

    /// 活跃商户数
    pub active_merchant_count: i32,

    /// 非活跃商户数
    pub inactive_merchant_count: i32,
}

// ============================================================================
// 实体映射函数
// ============================================================================

use crate::domain::business::merchants::entities::merchant_categories::Model as MerchantCategoryModel;

impl From<MerchantCategoryModel> for SysMerchantCategoryListResponse {
    fn from(model: MerchantCategoryModel) -> Self {
        Self {
            id: model.id,
            category_name: model.category_name,
            category_code: model.category_code,
            description: model.description,
            sort_order: model.sort_order,
            status: model.status.to_value(),
            created_date: format_datetime(&model.created_date),
            updated_date: format_datetime(&model.updated_date),
        }
    }
}

impl From<MerchantCategoryModel> for SysMerchantCategoryDetailResponse {
    fn from(model: MerchantCategoryModel) -> Self {
        Self {
            id: model.id,
            category_name: model.category_name,
            category_code: model.category_code,
            description: model.description,
            sort_order: model.sort_order,
            status: model.status.to_value(),
            created_date: format_datetime(&model.created_date),
            updated_date: format_datetime(&model.updated_date),
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
        }
    }
}

impl From<MerchantCategoryModel> for SysMerchantCategorySelectItem {
    fn from(model: MerchantCategoryModel) -> Self {
        Self {
            id: model.id,
            category_name: model.category_name,
            category_code: model.category_code,
            sort_order: model.sort_order,
        }
    }
}

impl SysMerchantCategoryStatsResponse {
    /// 从商户分类模型和统计数据创建
    pub fn from_model_with_stats(
        model: MerchantCategoryModel,
        merchant_count: i32,
        active_merchant_count: i32,
    ) -> Self {
        let inactive_merchant_count = merchant_count - active_merchant_count;

        Self::builder()
            .category_id(model.id)
            .category_name(model.category_name)
            .merchant_count(merchant_count)
            .active_merchant_count(active_merchant_count)
            .inactive_merchant_count(inactive_merchant_count)
            .build()
    }
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 格式化日期时间
fn format_datetime(dt: &chrono::DateTime<chrono::FixedOffset>) -> String {
    dt.format("%Y-%m-%d %H:%M:%S").to_string()
}

// ============================================================================
// 便捷构造函数
// ============================================================================

impl SysMerchantCategoryListResponse {
    /// 快速创建列表响应
    pub fn new_simple(
        id: Uuid,
        category_name: String,
        status: i32,
        created_date: String,
        updated_date: String,
    ) -> Self {
        Self {
            id,
            category_name,
            category_code: None,
            description: None,
            sort_order: None,
            status,
            created_date,
            updated_date,
        }
    }
}

impl SysMerchantCategorySelectItem {
    /// 快速创建选择项
    pub fn new_simple(id: Uuid, category_name: String) -> Self {
        Self {
            id,
            category_name,
            category_code: None,
            sort_order: None,
        }
    }
}

impl SysMerchantCategoryStatsResponse {
    /// 快速创建统计响应
    pub fn new_simple(
        category_id: Uuid,
        category_name: String,
        merchant_count: i32,
        active_merchant_count: i32,
    ) -> Self {
        let inactive_merchant_count = merchant_count - active_merchant_count;

        Self::builder()
            .category_id(category_id)
            .category_name(category_name)
            .merchant_count(merchant_count)
            .active_merchant_count(active_merchant_count)
            .inactive_merchant_count(inactive_merchant_count)
            .build()
    }
}

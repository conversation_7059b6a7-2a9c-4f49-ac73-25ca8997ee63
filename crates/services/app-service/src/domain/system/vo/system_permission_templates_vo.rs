use crate::utils::datetime::DateTimeUtils;
use bon::Builder;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;

/// 系统权限模板列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "parent_id": "550e8400-e29b-41d4-a716-************",
    "order_num": 1,
    "path": "/order",
    "component": "OrderView",
    "permission_type": 1,
    "permission_type_desc": "目录",
    "visible": 0,
    "visible_desc": "显示",
    "icon": "order-icon",
    "description": "订单查看权限",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00"
}))]
pub struct SystemPermissionTemplateListResponse {
    /// 权限模板ID
    pub id: Uuid,

    /// 权限名称
    pub permission_name: String,

    /// 权限编码
    pub permission_code: String,

    /// 父权限ID
    pub parent_id: Option<Uuid>,

    /// 显示顺序
    pub order_num: Option<i32>,

    /// 路由地址
    pub path: Option<String>,

    /// 组件路径
    pub component: Option<String>,

    /// 权限类型
    pub permission_type: Option<i32>,

    /// 权限类型描述
    pub permission_type_desc: String,

    /// 菜单状态
    pub visible: Option<i32>,

    /// 菜单状态描述
    pub visible_desc: String,

    /// 权限图标
    pub icon: Option<String>,

    /// 权限描述
    pub description: Option<String>,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,

    /// 子权限列表（用于树形结构）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub children: Option<Vec<SystemPermissionTemplateListResponse>>,
}

/// 系统权限模板详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "parent_id": "550e8400-e29b-41d4-a716-************",
    "order_num": 1,
    "path": "/order",
    "component": "OrderView",
    "query": "status=1",
    "is_frame": 1,
    "is_cache": 0,
    "permission_type": 1,
    "permission_type_desc": "目录",
    "visible": 0,
    "visible_desc": "显示",
    "icon": "order-icon",
    "description": "订单查看权限",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00",
    "created_by": "550e8400-e29b-41d4-a716-************",
    "updated_by": "550e8400-e29b-41d4-a716-************",
    "remark": "系统权限模板"
}))]
pub struct SystemPermissionTemplateDetailResponse {
    /// 权限模板ID
    pub id: Uuid,

    /// 权限名称
    pub permission_name: String,

    /// 权限编码
    pub permission_code: String,

    /// 父权限ID
    pub parent_id: Option<Uuid>,

    /// 显示顺序
    pub order_num: Option<i32>,

    /// 路由地址
    pub path: Option<String>,

    /// 组件路径
    pub component: Option<String>,

    /// 路由参数
    pub query: Option<String>,

    /// 是否为外链
    pub is_frame: Option<i32>,

    /// 是否缓存
    pub is_cache: Option<i32>,

    /// 权限类型
    pub permission_type: Option<i32>,

    /// 权限类型描述
    pub permission_type_desc: String,

    /// 菜单状态
    pub visible: Option<i32>,

    /// 菜单状态描述
    pub visible_desc: String,

    /// 权限图标
    pub icon: Option<String>,

    /// 权限描述
    pub description: Option<String>,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,

    /// 创建人ID
    pub created_by: Option<Uuid>,

    /// 更新人ID
    pub updated_by: Option<Uuid>,

    /// 备注
    pub remark: Option<String>,
}

/// 系统权限模板选择项
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "permission_type": 1,
    "permission_type_desc": "目录"
}))]
pub struct SystemPermissionTemplateSelectItem {
    /// 权限模板ID
    pub id: Uuid,

    /// 权限名称
    pub permission_name: String,

    /// 权限编码
    pub permission_code: String,

    /// 权限类型
    pub permission_type: Option<i32>,

    /// 权限类型描述
    pub permission_type_desc: String,
}

/// 系统权限模板树形节点
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "parent_id": null,
    "permission_type": 1,
    "order_num": 1,
    "children": []
}))]
pub struct SystemPermissionTemplateTreeNode {
    /// 权限模板ID
    pub id: Uuid,

    /// 权限名称
    pub permission_name: String,

    /// 权限编码
    pub permission_code: String,

    /// 父权限ID
    pub parent_id: Option<Uuid>,

    /// 权限类型
    pub permission_type: Option<i32>,

    /// 显示顺序
    pub order_num: Option<i32>,

    /// 子权限列表
    pub children: Vec<SystemPermissionTemplateTreeNode>,
}

// ============================================================================
// 实体类转 VO - 转换方法
// ============================================================================

use crate::domain::business::merchants::entities::system_permission_templates::Model as SystemPermissionTemplateModel;

impl From<SystemPermissionTemplateModel> for SystemPermissionTemplateListResponse {
    fn from(model: SystemPermissionTemplateModel) -> Self {
        Self {
            id: model.id,
            permission_name: model.permission_name,
            permission_code: model.permission_code,
            parent_id: model.parent_id,
            order_num: model.order_num,
            path: model.path,
            component: model.component,
            permission_type: model.permission_type,
            permission_type_desc: get_permission_type_desc(model.permission_type),
            visible: model.visible,
            visible_desc: get_visible_desc(model.visible),
            icon: model.icon,
            description: model.description,
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            updated_date: DateTimeUtils::format_datetime(
                &model.updated_date.with_timezone(&chrono::Local),
            ),
            children: None,
        }
    }
}

impl From<SystemPermissionTemplateModel> for SystemPermissionTemplateDetailResponse {
    fn from(model: SystemPermissionTemplateModel) -> Self {
        Self {
            id: model.id,
            permission_name: model.permission_name,
            permission_code: model.permission_code,
            parent_id: model.parent_id,
            order_num: model.order_num,
            path: model.path,
            component: model.component,
            query: model.query,
            is_frame: model.is_frame,
            is_cache: model.is_cache,
            permission_type: model.permission_type,
            permission_type_desc: get_permission_type_desc(model.permission_type),
            visible: model.visible,
            visible_desc: get_visible_desc(model.visible),
            icon: model.icon,
            description: model.description,
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            updated_date: DateTimeUtils::format_datetime(
                &model.updated_date.with_timezone(&chrono::Local),
            ),
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
        }
    }
}

impl From<SystemPermissionTemplateModel> for SystemPermissionTemplateSelectItem {
    fn from(model: SystemPermissionTemplateModel) -> Self {
        Self {
            id: model.id,
            permission_name: model.permission_name,
            permission_code: model.permission_code,
            permission_type: model.permission_type,
            permission_type_desc: get_permission_type_desc(model.permission_type),
        }
    }
}

impl From<SystemPermissionTemplateModel> for SystemPermissionTemplateTreeNode {
    fn from(model: SystemPermissionTemplateModel) -> Self {
        Self {
            id: model.id,
            permission_name: model.permission_name,
            permission_code: model.permission_code,
            parent_id: model.parent_id,
            permission_type: model.permission_type,
            order_num: model.order_num,
            children: Vec::new(),
        }
    }
}

// ============================================================================
// 便捷构造函数
// ============================================================================

impl SystemPermissionTemplateListResponse {
    /// 设置子权限列表
    pub fn with_children(mut self, children: Vec<SystemPermissionTemplateListResponse>) -> Self {
        self.children = Some(children);
        self
    }
}

impl SystemPermissionTemplateTreeNode {
    /// 设置子权限列表
    pub fn with_children(mut self, children: Vec<SystemPermissionTemplateTreeNode>) -> Self {
        self.children = children;
        self
    }
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 获取权限类型描述
fn get_permission_type_desc(permission_type: Option<i32>) -> String {
    match permission_type {
        Some(1) => "目录".to_string(),
        Some(2) => "菜单".to_string(),
        Some(3) => "按钮".to_string(),
        _ => "未知".to_string(),
    }
}

/// 获取可见状态描述
fn get_visible_desc(visible: Option<i32>) -> String {
    match visible {
        Some(0) => "显示".to_string(),
        Some(1) => "隐藏".to_string(),
        _ => "未知".to_string(),
    }
} 
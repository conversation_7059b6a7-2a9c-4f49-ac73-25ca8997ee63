use crate::domain::system::vo::SysRoleSimpleResponse;
use bon::Builder;
use serde::Serialize;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;
// ==================== 用户响应相关 VO ====================

/// 登录成功返回信息
#[derive(Debug, Serialize, ToSchema, Builder)]
#[schema(example = json!({
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}))]
pub struct SysUserLoginResponse {
    /// 短token 用于请求资源，过期时间非常短
    pub access_token: String,
    /// 长token 用于刷新access_token，过期时间长
    pub refresh_token: String,
}

/// 系统用户基本信息 角色信息 权限
#[derive(Debug, Serialize, ToSchema)]
pub struct SysUserVo {
    /// id
    pub id: Uuid,
    /// 用户名
    pub username: String,
    /// 密码
    pub password: String,
    /// 真实姓名
    pub real_name: Option<String>,
    /// 昵称
    pub phone: Option<String>,
    /// 邮箱
    pub email: Option<String>,
    /// 头像地址
    pub avatar: Option<String>,
    /// 性别
    pub gender: Option<i32>,
    // 状态 1:启用 2:禁用 3:锁定
    pub status: i32,
    /// 权限
    pub permissions: Vec<String>,
    /// 角色
    pub role_ids: Vec<String>,
}

/// 刷新token响应结构体
#[derive(Debug, Serialize, ToSchema, Builder)]
pub struct RefreshTokenResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub expires_in: u64,
}

/// 用户个人信息响应结构体
#[derive(Debug, Serialize, ToSchema)]
pub struct UserProfileResponse {
    /// 用户ID
    pub id: Uuid,
    /// 用户名
    pub username: String,
    /// 真实姓名
    pub real_name: Option<String>,
    /// 手机号
    pub phone: Option<String>,
    /// 邮箱
    pub email: Option<String>,
    /// 头像地址
    pub avatar: Option<String>,
    /// 性别 1:男 2:女 3:未知
    pub gender: Option<i32>,
    /// 状态 1:启用 2:禁用 3:锁定
    pub status: i32,
    /// 最后登录时间
    pub last_login_date: Option<String>,
    /// 最后登录IP
    pub last_login_ip: Option<String>,
    /// 是否已绑定微信
    pub wechat_bound: bool,
    /// 微信昵称
    pub wechat_nickname: Option<String>,
    /// 微信头像
    pub wechat_avatar: Option<String>,
    /// 权限代码集合
    pub permissions: Vec<String>,
    /// 角色简单信息集合
    pub roles: Vec<SysRoleSimpleResponse>,
}

/// 用户列表响应VO (用于分页查询)
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
    "username": "admin",
    "real_name": "系统管理员",
    "phone": "138****1234",
    "email": "a***@example.com",
    "gender": 1,
    "status": 1,
    "last_login_date": "2023-05-24 10:30:00",
    "last_login_ip": "***********",
    "created_date": "2023-01-01 00:00:00",
    "updated_date": "2023-05-24 10:30:00"
}))]
pub struct SysUserListResponse {
    /// 用户ID
    pub id: Uuid,
    /// 用户名
    pub username: String,
    /// 头像
    pub avatar: Option<String>,
    /// 真实姓名
    pub real_name: Option<String>,
    /// 手机号 (脱敏)
    pub phone: Option<String>,
    /// 邮箱 (脱敏)
    pub email: Option<String>,
    /// 性别 1:男 2:女 3:未知
    pub gender: Option<i32>,
    /// 状态 1:启用 2:禁用 3:锁定
    pub status: i32,
    /// 最后登录时间
    pub last_login_date: Option<String>,
    /// 最后登录IP
    pub last_login_ip: Option<String>,
    /// 创建时间
    pub created_date: String,
    /// 更新时间
    pub updated_date: String,
}

/// 未分配角色用户数量响应VO
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "unassigned_user_count": 25
}))]
pub struct SysUnassignedUserCountResponse {
    /// 未分配角色的用户总数量
    pub unassigned_user_count: u64,
}

/// 用户详情响应VO (包含完整信息)
#[derive(Debug, Serialize, ToSchema)]
pub struct SysUserDetailResponse {
    /// 用户ID
    pub id: Uuid,
    /// 用户名
    pub username: String,
    /// 真实姓名
    pub real_name: Option<String>,
    /// 手机号
    pub phone: Option<String>,
    /// 邮箱
    pub email: Option<String>,
    /// 头像地址
    pub avatar: Option<String>,
    /// 性别 1:男 2:女 3:未知
    pub gender: Option<i32>,
    /// 状态 1:启用 2:禁用 3:锁定
    pub status: i32,
    /// 最后登录时间
    pub last_login_date: Option<String>,
    /// 最后登录IP
    pub last_login_ip: Option<String>,
    /// 创建时间
    pub created_date: String,
    /// 更新时间
    pub updated_date: String,
    /// 创建人ID
    pub created_by: Option<Uuid>,
    /// 更新人ID
    pub updated_by: Option<Uuid>,
    /// 备注
    pub remark: Option<String>,
    /// 用户角色详情列表
    pub roles: Vec<SysUserRoleDetailVo>,
}

/// 用户角色详情VO
#[derive(Debug, Serialize, ToSchema)]
pub struct SysUserRoleDetailVo {
    /// 角色ID
    pub id: Uuid,
    /// 角色名称
    pub name: String,
    /// 角色描述
    pub description: Option<String>,
    /// 创建时间
    pub created_date: String,
    /// 更新时间
    pub updated_date: String,
    /// 备注
    pub remark: Option<String>,
}

// ============================================================================
// 实体类转 VO - 转换方法
// ============================================================================

use crate::domain::system::entities::roles::Model as RoleModel;
use crate::domain::system::entities::users::Model as UserModel;
use crate::utils::datetime::DateTimeUtils;

impl From<UserModel> for SysUserListResponse {
    fn from(model: UserModel) -> Self {
        Self {
            id: model.id,
            username: model.username,
            avatar: model.avatar,
            real_name: model.real_name,
            // phone: model.phone.map(|p| mask_phone(&p)),
            phone: model.phone,
            email: model.email.map(|e| mask_email(&e)),
            gender: model.gender,
            status: model.status,
            last_login_date: model
                .last_login_date
                .map(|dt| DateTimeUtils::format_datetime(&dt)),
            last_login_ip: model.last_login_ip,
            created_date: DateTimeUtils::format_datetime(&model.created_date),
            updated_date: DateTimeUtils::format_datetime(&model.updated_date),
        }
    }
}

impl From<UserModel> for SysUserDetailResponse {
    fn from(model: UserModel) -> Self {
        Self {
            id: model.id,
            username: model.username,
            real_name: model.real_name,
            phone: model.phone,
            email: model.email,
            avatar: model.avatar,
            gender: model.gender,
            status: model.status,
            last_login_date: model
                .last_login_date
                .map(|dt| DateTimeUtils::format_datetime(&dt)),
            last_login_ip: model.last_login_ip,
            created_date: DateTimeUtils::format_datetime(&model.created_date),
            updated_date: DateTimeUtils::format_datetime(&model.updated_date),
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
            roles: vec![], // 默认为空，需要单独填充
        }
    }
}

impl From<RoleModel> for SysUserRoleDetailVo {
    fn from(model: RoleModel) -> Self {
        Self {
            id: model.id,
            name: model.name,
            description: model.description,
            created_date: DateTimeUtils::format_datetime(&model.created_date),
            updated_date: DateTimeUtils::format_datetime(&model.updated_date),
            remark: model.remark,
        }
    }
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 脱敏手机号
fn mask_phone(phone: &str) -> String {
    if phone.len() >= 11 {
        format!("{}****{}", &phone[..3], &phone[7..])
    } else {
        phone.to_string()
    }
}

/// 脱敏邮箱
fn mask_email(email: &str) -> String {
    if let Some(at_pos) = email.find('@') {
        let username = &email[..at_pos];
        let domain = &email[at_pos..];
        if username.len() > 3 {
            format!("{}***{}", &username[..2], domain)
        } else {
            format!("***{}", domain)
        }
    } else {
        email.to_string()
    }
}

// ============================================================================
// 便捷构造函数
// ============================================================================

impl SysUserDetailResponse {
    /// 填充角色信息
    pub fn with_roles(mut self, roles: Vec<SysUserRoleDetailVo>) -> Self {
        self.roles = roles;
        self
    }
}

impl UserProfileResponse {
    /// 从用户模型和权限角色信息创建用户档案响应
    pub fn from_user_with_permissions_and_roles(
        user: UserModel,
        permissions: Vec<String>,
        roles: Vec<SysRoleSimpleResponse>,
    ) -> Self {
        Self {
            id: user.id,
            username: user.username,
            real_name: user.real_name,
            phone: user.phone,
            email: user.email,
            avatar: user.avatar,
            gender: user.gender,
            status: user.status,
            last_login_date: user
                .last_login_date
                .map(|dt| DateTimeUtils::format_datetime(&dt)),
            last_login_ip: user.last_login_ip,
            wechat_bound: user.wechat_openid.is_some(),
            wechat_nickname: user.wechat_nickname,
            wechat_avatar: user.wechat_avatar,
            permissions,
            roles,
        }
    }
}

use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;

/// 权限菜单列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "menu_name": "系统管理",
    "parent_id": null,
    "order_num": 1,
    "path": "/system",
    "component": "Layout",
    "menu_type": 0,
    "status": 0,
    "perms": null,
    "icon": "system",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00"
}))]
pub struct SysPermissionListResponse {
    /// 权限ID
    pub id: Uuid,
    
    /// 菜单名称
    pub menu_name: String,
    
    /// 父级ID
    pub parent_id: Option<Uuid>,
    
    /// 显示顺序
    pub order_num: Option<i32>,
    
    /// 路由地址
    pub path: Option<String>,
    
    /// 组件路径
    pub component: Option<String>,
    
    /// 菜单类型（1目录 2菜单 3按钮）
    pub menu_type: Option<i32>,
    
    /// 菜单状态（0正常 1停用）
    pub status: Option<i32>,
    
    /// 权限标识
    pub perms: Option<String>,
    
    /// 菜单图标
    pub icon: Option<String>,
    
    /// 创建时间
    pub created_date: String,
    
    /// 更新时间
    pub updated_date: String,
}

/// 权限菜单详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "menu_name": "系统管理",
    "parent_id": null,
    "parent_name": null,
    "order_num": 1,
    "path": "/system",
    "component": "Layout",
    "query": null,
    "is_frame": 1,
    "is_cache": 0,
    "menu_type": 0,
    "visible": 0,
    "status": 0,
    "perms": null,
    "icon": "system",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00",
    "created_by": "550e8400-e29b-41d4-a716-************",
    "updated_by": "550e8400-e29b-41d4-a716-************",
    "remark": "系统管理目录",
    "children": []
}))]
pub struct SysPermissionDetailResponse {
    /// 权限ID
    pub id: Uuid,
    
    /// 菜单名称
    pub menu_name: String,
    
    /// 父级ID
    pub parent_id: Option<Uuid>,
    
    /// 父级菜单名称（如果有）
    pub parent_name: Option<String>,
    
    /// 显示顺序
    pub order_num: Option<i32>,
    
    /// 路由地址
    pub path: Option<String>,
    
    /// 组件路径
    pub component: Option<String>,
    
    /// 路由参数
    pub query: Option<String>,
    
    /// 是否为外链（0是 1否）
    pub is_frame: Option<i32>,
    
    /// 是否缓存（0缓存 1不缓存）
    pub is_cache: Option<i32>,
    
    /// 菜单类型（1目录 2菜单 3按钮）
    pub menu_type: Option<i32>,
    
    /// 显示状态（0显示 1隐藏）
    pub visible: Option<i32>,
    
    /// 菜单状态（0正常 1停用）
    pub status: Option<i32>,
    
    /// 权限标识
    pub perms: Option<String>,
    
    /// 菜单图标
    pub icon: Option<String>,
    
    /// 创建时间
    pub created_date: String,
    
    /// 更新时间
    pub updated_date: String,
    
    /// 创建人ID
    pub created_by: Option<Uuid>,
    
    /// 更新人ID
    pub updated_by: Option<Uuid>,
    
    /// 备注
    pub remark: Option<String>,
    
    /// 子菜单列表 
    #[schema(no_recursion)]
    pub children: Option<Box<Vec<SysPermissionTreeNode>>>,
}

/// 权限菜单树节点
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "menu_name": "系统管理",
    "parent_id": null,
    "order_num": 1,
    "path": "/system",
    "component": "Layout",
    "menu_type": 0,
    "status": 0,
    "perms": null,
    "icon": "system",
    "children": []
}))]
pub struct SysPermissionTreeNode {
    /// 权限ID
    pub id: Uuid,
    
    /// 菜单名称
    pub menu_name: String,
    
    /// 父级ID
    pub parent_id: Option<Uuid>,
    
    /// 显示顺序
    pub order_num: Option<i32>,
    
    /// 路由地址
    pub path: Option<String>,
    
    /// 组件路径
    pub component: Option<String>,
    
    /// 菜单类型（1目录 2菜单 3按钮）
    pub menu_type: Option<i32>,
    
    /// 菜单状态（0正常 1停用）
    pub status: Option<i32>,
    
    /// 权限标识
    pub perms: Option<String>,
    
    /// 菜单图标
    pub icon: Option<String>,
    
    /// 子菜单
    #[schema(no_recursion)]
    pub children: Option<Box<Vec<SysPermissionTreeNode>>>,
}

/// 权限菜单选择项（用于下拉选择）
#[derive(Debug, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "menu_name": "系统管理",
    "parent_id": null,
    "order_num": 1,
    "menu_type": 0
}))]
pub struct SysPermissionSelectItem {
    /// 权限ID
    pub id: Uuid,
    
    /// 菜单名称
    pub menu_name: String,
    
    /// 父级ID
    pub parent_id: Option<Uuid>,
    
    /// 显示顺序
    pub order_num: Option<i32>,
    
    /// 菜单类型（1目录 2菜单 3按钮）
    pub menu_type: Option<i32>,
}

/// 路由菜单项（用于前端生成动态路由）
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[schema(example = json!({
    "name": "System",
    "path": "/system",
    "hidden": false,
    "redirect": null,
    "component": "Layout",
    "always_show": true,
    "perms": null,
    "menu_type": 1,
    "meta": {
        "title": "系统管理",
        "icon": "system",
        "no_cache": false,
        "link": null
    },
    "children": []
}))]
pub struct RouterVo {
    /// 路由名称
    pub name: Option<String>,
    
    /// 路由地址
    pub path: String,
    
    /// 是否隐藏
    pub hidden: bool,
    
    /// 重定向地址
    pub redirect: Option<String>,
    
    /// 组件
    pub component: Option<String>,
    
    /// 是否总是显示
    pub always_show: Option<bool>,
    
    /// 权限标识
    pub perms: Option<String>,
    
    /// 菜单类型（1目录 2菜单 3按钮）
    pub menu_type: Option<i32>,
    
    /// 路由元信息
    pub meta: RouterMeta,
    
    /// 子路由
    #[schema(no_recursion)]
    pub children: Option<Box<Vec<RouterVo>>>,
}

/// 路由元信息
#[derive(Debug, Serialize, Deserialize, Clone, ToSchema)]
#[schema(example = json!({
    "title": "系统管理",
    "icon": "system",
    "no_cache": false,
    "link": null
}))]
pub struct RouterMeta {
    /// 标题
    pub title: String,
    
    /// 图标
    pub icon: Option<String>,
    
    /// 是否缓存
    pub no_cache: bool,
    
    /// 链接地址
    pub link: Option<String>,
} 

// ============================================================================
// 实体类转 VO - 转换方法
// ============================================================================

use crate::domain::system::entities::permissions::Model as PermissionModel;
use crate::utils::datetime::DateTimeUtils;

impl From<PermissionModel> for SysPermissionListResponse {
    fn from(model: PermissionModel) -> Self {
        Self {
            id: model.id,
            menu_name: model.menu_name,
            parent_id: model.parent_id,
            order_num: model.order_num,
            path: model.path,
            component: model.component,
            menu_type: model.menu_type,
            status: model.status,
            perms: model.perms,
            icon: model.icon,
            created_date: DateTimeUtils::format_datetime(&model.created_date),
            updated_date: DateTimeUtils::format_datetime(&model.updated_date),
        }
    }
}

impl From<PermissionModel> for SysPermissionDetailResponse {
    fn from(model: PermissionModel) -> Self {
        Self {
            id: model.id,
            menu_name: model.menu_name,
            parent_id: model.parent_id,
            parent_name: None, // 需要单独设置
            order_num: model.order_num,
            path: model.path,
            component: model.component,
            query: model.query,
            is_frame: model.is_frame,
            is_cache: model.is_cache,
            menu_type: model.menu_type,
            visible: model.visible,
            status: model.status,
            perms: model.perms,
            icon: model.icon,
            created_date: DateTimeUtils::format_datetime(&model.created_date),
            updated_date: DateTimeUtils::format_datetime(&model.updated_date),
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
            children: None, // 需要单独设置
        }
    }
}

impl From<PermissionModel> for SysPermissionTreeNode {
    fn from(model: PermissionModel) -> Self {
        Self {
            id: model.id,
            menu_name: model.menu_name,
            parent_id: model.parent_id,
            order_num: model.order_num,
            path: model.path,
            component: model.component,
            menu_type: model.menu_type,
            status: model.status,
            perms: model.perms,
            icon: model.icon,
            children: None, // 需要单独设置
        }
    }
}

impl SysPermissionTreeNode {
    /// 构建菜单树形结构
    /// 
    /// # 参数
    /// - `permissions`: 权限菜单实体列表
    /// 
    /// # 返回
    /// 返回构建好的树形结构根节点列表
    pub fn build_tree(
        permissions: Vec<crate::domain::system::entities::permissions::Model>,
    ) -> Vec<Self> {
        let mut id_to_perm = std::collections::HashMap::new();
        let mut root_nodes = Vec::new();
        let mut parent_to_children = std::collections::HashMap::new();

        // 第一遍循环：收集所有节点并建立父子关系映射
        for perm in &permissions {
            id_to_perm.insert(perm.id, perm);

            if let Some(parent_id) = perm.parent_id {
                parent_to_children
                    .entry(parent_id)
                    .or_insert_with(Vec::new)
                    .push(perm.id);
            }
        }

        // 第二遍循环：构建树形结构
        for perm in &permissions {
            if perm.parent_id.is_none() {
                // 创建根节点
                let mut node = Self {
                    id: perm.id,
                    menu_name: perm.menu_name.clone(),
                    parent_id: None,
                    order_num: perm.order_num,
                    path: perm.path.clone(),
                    component: perm.component.clone(),
                    menu_type: perm.menu_type,
                    status: perm.status,
                    perms: perm.perms.clone(),
                    icon: perm.icon.clone(),
                    children: Some(Box::new(Vec::new())),
                };

                // 递归添加子节点
                Self::add_children(&mut node, &parent_to_children, &id_to_perm);

                root_nodes.push(node);
            }
        }

        // 按order_num排序
        root_nodes.sort_by(|a, b| {
            let a_order = a.order_num.unwrap_or(999);
            let b_order = b.order_num.unwrap_or(999);
            a_order.cmp(&b_order)
        });

        root_nodes
    }

    /// 递归添加子节点
    fn add_children(
        parent: &mut Self,
        parent_to_children: &std::collections::HashMap<uuid::Uuid, Vec<uuid::Uuid>>,
        id_to_perm: &std::collections::HashMap<
            uuid::Uuid,
            &crate::domain::system::entities::permissions::Model,
        >,
    ) {
        if let Some(children_ids) = parent_to_children.get(&parent.id) {
            let mut children_nodes = Vec::new();

            for &child_id in children_ids {
                if let Some(&child_perm) = id_to_perm.get(&child_id) {
                    let mut child_node = Self {
                        id: child_perm.id,
                        menu_name: child_perm.menu_name.clone(),
                        parent_id: child_perm.parent_id,
                        order_num: child_perm.order_num,
                        path: child_perm.path.clone(),
                        component: child_perm.component.clone(),
                        menu_type: child_perm.menu_type,
                        status: child_perm.status,
                        perms: child_perm.perms.clone(),
                        icon: child_perm.icon.clone(),
                        children: Some(Box::new(Vec::new())),
                    };

                    // 递归添加子节点的子节点
                    Self::add_children(&mut child_node, parent_to_children, id_to_perm);

                    children_nodes.push(child_node);
                }
            }

            // 对子节点按order_num排序
            children_nodes.sort_by(|a, b| {
                let a_order = a.order_num.unwrap_or(999);
                let b_order = b.order_num.unwrap_or(999);
                a_order.cmp(&b_order)
            });

            // 设置父节点的子节点列表
            if let Some(children) = &mut parent.children {
                *children = Box::from(children_nodes);
            }
        }
    }
}

impl From<PermissionModel> for SysPermissionSelectItem {
    fn from(model: PermissionModel) -> Self {
        Self {
            id: model.id,
            menu_name: model.menu_name,
            parent_id: model.parent_id,
            order_num: model.order_num,
            menu_type: model.menu_type,
        }
    }
}

impl From<PermissionModel> for RouterVo {
    fn from(model: PermissionModel) -> Self {
        let hidden = model.visible.unwrap_or(0) == 1; // 1表示隐藏
        let no_cache = model.is_cache.unwrap_or(0) == 1; // 1表示不缓存
        let path = model.path.clone();
        
        Self {
            name: generate_route_name(&model.menu_name, &path),
            path: path.clone().unwrap_or_default(),
            hidden,
            redirect: None, // 需要根据业务逻辑设置
            component: model.component,
            always_show: None, // 需要根据业务逻辑设置
            perms: model.perms,
            menu_type: model.menu_type,
            meta: RouterMeta {
                title: model.menu_name,
                icon: model.icon,
                no_cache,
                link: if model.is_frame.unwrap_or(1) == 0 { path } else { None },
            },
            children: None, // 需要单独设置
        }
    }
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 生成路由名称
/// 根据菜单名称和路径生成合适的路由名称
fn generate_route_name(_menu_name: &str, path: &Option<String>) -> Option<String> {
    match path {
        Some(p) if !p.is_empty() => {
            // 将路径转换为驼峰命名的路由名称
            let name = p
                .trim_start_matches('/')
                .split('/')
                .filter(|s| !s.is_empty())
                .map(|s| {
                    let mut chars = s.chars();
                    match chars.next() {
                        None => String::new(),
                        Some(first) => first.to_uppercase().collect::<String>() + chars.as_str(),
                    }
                })
                .collect::<Vec<String>>()
                .join("");
            if name.is_empty() {
                None
            } else {
                Some(name)
            }
        }
        _ => None,
    }
} 
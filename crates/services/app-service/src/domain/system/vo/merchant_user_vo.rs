use crate::utils::datetime::DateTimeUtils;
use bon::Builder;
use sea_orm::ActiveEnum;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;

/// 商户用户列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "username": "z<PERSON><PERSON>",
    "real_name": "张三",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "gender": 1,
    "gender_desc": "男",
    "status": 1,
    "status_desc": "启用",
    "last_login_date": "2023-01-01 10:00:00",
    "last_login_ip": "*************",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00"
}))]
pub struct SysMerchantUserListResponse {
    /// 用户ID
    pub id: Uuid,

    /// 用户名
    pub username: String,

    /// 真实姓名
    pub real_name: String,

    /// 手机号码
    pub phone: String,

    /// 邮箱地址
    pub email: Option<String>,

    /// 头像URL
    pub avatar: Option<String>,

    /// 性别（1男 2女 3未知）
    pub gender: Option<i32>,

    /// 性别描述
    pub gender_desc: String,

    /// 状态（1启用 2禁用 3锁定）
    pub status: i32,

    /// 状态描述
    pub status_desc: String,

    /// 最后登录时间
    pub last_login_date: Option<String>,

    /// 最后登录IP
    pub last_login_ip: Option<String>,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,
}

/// 商户用户详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "username": "zhangsan",
    "real_name": "张三",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "gender": 1,
    "gender_desc": "男",
    "id_card": "110***********1234",
    "status": 1,
    "status_desc": "启用",
    "last_login_date": "2023-01-01 10:00:00",
    "last_login_ip": "*************",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00",
    "created_by": "550e8400-e29b-41d4-a716-************",
    "updated_by": "550e8400-e29b-41d4-a716-************",
    "remark": "优秀员工",
    "roles": [
        {
            "role_id": "550e8400-e29b-41d4-a716-************",
            "role_name": "商户管理员",
            "role_code": "MERCHANT_ADMIN",
            "role_type": 1,
            "role_type_desc": "管理员角色",
            "commission_rate": "0.0200",
            "status": 1,
            "status_desc": "启用"
        }
    ]
}))]
pub struct SysMerchantUserDetailResponse {
    /// 用户ID
    pub id: Uuid,

    /// 用户名
    pub username: String,

    /// 真实姓名
    pub real_name: String,

    /// 手机号码
    pub phone: String,

    /// 邮箱地址
    pub email: Option<String>,

    /// 头像URL
    pub avatar: Option<String>,

    /// 性别（1男 2女 3未知）
    pub gender: Option<i32>,

    /// 性别描述
    pub gender_desc: String,

    /// 身份证号（脱敏）
    pub id_card: Option<String>,

    /// 状态（1启用 2禁用 3锁定）
    pub status: i32,

    /// 状态描述
    pub status_desc: String,

    /// 最后登录时间
    pub last_login_date: Option<String>,

    /// 最后登录IP
    pub last_login_ip: Option<String>,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,

    /// 创建人ID
    pub created_by: Option<Uuid>,

    /// 更新人ID
    pub updated_by: Option<Uuid>,

    /// 备注
    pub remark: Option<String>,

    /// 用户的角色列表
    pub roles: Vec<SysMerchantUserRoleInfo>,
}

/// 商户用户选择项（用于下拉选择）
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "username": "zhangsan",
    "real_name": "张三",
    "phone": "13800138000",
    "status": 1
}))]
pub struct SysMerchantUserSelectItem {
    /// 用户ID
    pub id: Uuid,

    /// 用户名
    pub username: String,

    /// 真实姓名
    pub real_name: String,

    /// 手机号码
    pub phone: String,

    /// 状态（1启用 2禁用 3锁定）
    pub status: i32,
}

/// 商户用户基础信息响应（用于简单查询）
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "username": "zhangsan",
    "real_name": "张三",
    "phone": "13800138000",
    "status": 1,
    "status_desc": "启用"
}))]
pub struct SysMerchantUserBasicResponse {
    /// 用户ID
    pub id: Uuid,

    /// 用户名
    pub username: String,

    /// 真实姓名
    pub real_name: String,

    /// 手机号码
    pub phone: String,

    /// 状态（1启用 2禁用 3锁定）
    pub status: i32,

    /// 状态描述
    pub status_desc: String,
}

/// 商户用户统计信息响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "total_count": 100,
    "active_count": 85,
    "disabled_count": 10,
    "locked_count": 5,
    "by_gender": [
        {
            "gender": 1,
            "gender_desc": "男",
            "user_count": 60
        },
        {
            "gender": 2,
            "gender_desc": "女",
            "user_count": 40
        }
    ]
}))]
pub struct SysMerchantUserStatsResponse {
    /// 用户总数
    pub total_count: i32,

    /// 启用用户数
    pub active_count: i32,

    /// 禁用用户数
    pub disabled_count: i32,

    /// 锁定用户数
    pub locked_count: i32,

    /// 按性别统计
    pub by_gender: Vec<SysMerchantUserGenderStats>,
}

/// 商户用户性别统计
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
pub struct SysMerchantUserGenderStats {
    /// 性别（1男 2女 3未知）
    pub gender: Option<i32>,

    /// 性别描述
    pub gender_desc: String,

    /// 用户数量
    pub user_count: i32,
}

/// 商户用户登录信息响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "user_id": "550e8400-e29b-41d4-a716-************",
    "username": "zhangsan",
    "real_name": "张三",
    "avatar": "https://example.com/avatar.jpg",
    "merchant_ids": [1001, 1002],
    "default_merchant_id": 1001,
    "last_login_date": "2023-01-01 10:00:00",
    "last_login_ip": "*************"
}))]
pub struct SysMerchantUserLoginResponse {
    /// 用户ID
    pub user_id: Uuid,

    /// 用户名
    pub username: String,

    /// 真实姓名
    pub real_name: String,

    /// 头像URL
    pub avatar: Option<String>,

    /// 关联的商户ID列表
    pub merchant_ids: Vec<i64>,

    /// 默认商户ID
    pub default_merchant_id: Option<i64>,

    /// 最后登录时间
    pub last_login_date: Option<String>,

    /// 最后登录IP
    pub last_login_ip: Option<String>,
}

/// 商户用户角色信息响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "user_id": "550e8400-e29b-41d4-a716-************",
    "username": "zhangsan",
    "real_name": "张三",
    "merchant_id": 1001,
    "merchant_name": "张三餐厅",
    "roles": [
        {
            "role_id": "550e8400-e29b-41d4-a716-************",
            "role_name": "商户管理员",
            "role_code": "MERCHANT_ADMIN"
        }
    ]
}))]
pub struct SysMerchantUserRoleResponse {
    /// 用户ID
    pub user_id: Uuid,

    /// 用户名
    pub username: String,

    /// 真实姓名
    pub real_name: String,

    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: String,

    /// 角色列表
    pub roles: Vec<SysMerchantUserRoleInfo>,
}

/// 商户用户角色信息
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "role_code": "MERCHANT_ADMIN",
    "role_type": 1,
    "role_type_desc": "管理员角色",
    "commission_rate": "0.0200",
    "status": 1,
    "status_desc": "启用"
}))]
pub struct SysMerchantUserRoleInfo {
    /// 角色ID
    pub role_id: Uuid,

    /// 角色名称
    pub role_name: String,

    /// 角色编码
    pub role_code: Option<String>,

    /// 角色类型（1管理员角色 2自定义角色）
    pub role_type: i32,

    /// 角色类型描述
    pub role_type_desc: String,

    /// 状态（0禁用 1启用）
    pub status: i32,

    /// 状态描述
    pub status_desc: String,
}

// ============================================================================
// 实体类转 VO - 转换方法
// ============================================================================

use crate::domain::business::merchants::entities::merchant_users::LoginInfo;
use crate::domain::business::merchants::entities::merchant_users::{
    MerchantUserGender, Model as MerchantUserModel,
};

impl From<MerchantUserModel> for SysMerchantUserListResponse {
    fn from(model: MerchantUserModel) -> Self {
        Self {
            id: model.id,
            username: model.username,
            real_name: model.real_name,
            phone: model.phone,
            email: model.email,
            avatar: model.avatar,
            gender: Some(
                model
                    .gender
                    .unwrap_or(MerchantUserGender::Unknown)
                    .to_value(),
            ),
            gender_desc: get_gender_desc(Some(
                model
                    .gender
                    .unwrap_or(MerchantUserGender::Unknown)
                    .to_value(),
            )),
            status: model.status.to_value(),
            status_desc: get_status_desc(model.status.to_value()),
            last_login_date: model
                .last_login_date
                .map(|dt| DateTimeUtils::format_datetime(&dt.with_timezone(&chrono::Local))),
            last_login_ip: model.last_login_ip,
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            updated_date: DateTimeUtils::format_datetime(
                &model.updated_date.with_timezone(&chrono::Local),
            ),
        }
    }
}

impl From<MerchantUserModel> for SysMerchantUserDetailResponse {
    fn from(model: MerchantUserModel) -> Self {
        Self {
            id: model.id,
            username: model.username,
            real_name: model.real_name,
            phone: model.phone,
            email: model.email,
            avatar: model.avatar,
            gender: Some(
                model
                    .gender
                    .unwrap_or(MerchantUserGender::Unknown)
                    .to_value(),
            ),
            gender_desc: get_gender_desc(Some(
                model
                    .gender
                    .unwrap_or(MerchantUserGender::Unknown)
                    .to_value(),
            )),
            id_card: model.id_card.map(mask_id_card),
            status: model.status.to_value(),
            status_desc: get_status_desc(model.status.to_value()),
            last_login_date: model
                .last_login_date
                .map(|dt| DateTimeUtils::format_datetime(&dt.with_timezone(&chrono::Local))),
            last_login_ip: model.last_login_ip,
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            updated_date: DateTimeUtils::format_datetime(
                &model.updated_date.with_timezone(&chrono::Local),
            ),
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
            roles: vec![],
        }
    }
}

impl From<MerchantUserModel> for SysMerchantUserSelectItem {
    fn from(model: MerchantUserModel) -> Self {
        Self {
            id: model.id,
            username: model.username,
            real_name: model.real_name,
            phone: model.phone,
            status: model.status.to_value(),
        }
    }
}

impl From<MerchantUserModel> for SysMerchantUserBasicResponse {
    fn from(model: MerchantUserModel) -> Self {
        Self {
            id: model.id,
            username: model.username,
            real_name: model.real_name,
            phone: model.phone,
            status: model.status.to_value(),
            status_desc: get_status_desc(model.status.to_value()),
        }
    }
}

impl From<LoginInfo> for SysMerchantUserLoginResponse {
    fn from(login_info: LoginInfo) -> Self {
        let user = login_info.user;
        Self {
            user_id: user.id,
            username: user.username,
            real_name: user.real_name,
            avatar: user.avatar,
            merchant_ids: login_info.merchant_ids,
            default_merchant_id: login_info.default_merchant_id,
            last_login_date: user
                .last_login_date
                .map(|dt| DateTimeUtils::format_datetime(&dt.with_timezone(&chrono::Local))),
            last_login_ip: user.last_login_ip,
        }
    }
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 获取状态描述
fn get_status_desc(status: i32) -> String {
    match status {
        1 => "启用".to_string(),
        2 => "禁用".to_string(),
        3 => "锁定".to_string(),
        _ => "未知状态".to_string(),
    }
}

/// 获取性别描述
fn get_gender_desc(gender: Option<i32>) -> String {
    match gender {
        Some(1) => "男".to_string(),
        Some(2) => "女".to_string(),
        Some(3) | None => "未知".to_string(),
        _ => "未知".to_string(),
    }
}

/// 身份证号脱敏处理
fn mask_id_card(id_card: String) -> String {
    if id_card.len() < 6 {
        return id_card;
    }

    let prefix = &id_card[..3];
    let suffix = &id_card[id_card.len() - 4..];
    let stars = "*".repeat(id_card.len() - 7);

    format!("{}{}{}", prefix, stars, suffix)
}

// ============================================================================
// 便捷构造函数
// ============================================================================

impl SysMerchantUserRoleResponse {
    /// 创建用户角色信息响应
    pub fn new(
        user: MerchantUserModel,
        merchant_id: i64,
        merchant_name: String,
        roles: Vec<SysMerchantUserRoleInfo>,
    ) -> Self {
        Self {
            user_id: user.id,
            username: user.username,
            real_name: user.real_name,
            merchant_id,
            merchant_name,
            roles,
        }
    }
}

impl SysMerchantUserStatsResponse {
    /// 创建统计信息响应
    pub fn new(
        total_count: i32,
        active_count: i32,
        disabled_count: i32,
        locked_count: i32,
        by_gender: Vec<SysMerchantUserGenderStats>,
    ) -> Self {
        Self {
            total_count,
            active_count,
            disabled_count,
            locked_count,
            by_gender,
        }
    }
}

impl SysMerchantUserDetailResponse {
    /// 填充角色信息
    pub fn with_roles(mut self, roles: Vec<SysMerchantUserRoleInfo>) -> Self {
        self.roles = roles;
        self
    }
}

impl SysMerchantUserRoleInfo {
    /// 从用户角色关联和角色信息创建角色信息
    pub fn new(
        role_id: Uuid,
        role_name: String,
        role_code: Option<String>,
        role_type: i32,
        status: i32,
    ) -> Self {
        Self {
            role_id,
            role_name,
            role_code,
            role_type,
            role_type_desc: get_role_type_desc(role_type),
            status,
            status_desc: get_role_assignment_status_desc(status),
        }
    }
}

/// 获取角色类型描述
fn get_role_type_desc(role_type: i32) -> String {
    match role_type {
        1 => "管理员角色".to_string(),
        2 => "自定义角色".to_string(),
        _ => "未知角色".to_string(),
    }
}

/// 获取角色分配状态描述
fn get_role_assignment_status_desc(status: i32) -> String {
    match status {
        0 => "禁用".to_string(),
        1 => "启用".to_string(),
        _ => "未知状态".to_string(),
    }
}

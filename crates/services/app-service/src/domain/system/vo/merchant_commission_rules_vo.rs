//! 商户门店佣金规则响应VO
//! Generated by lib-codegen

use crate::domain::business::merchants::entities::merchant_commission_rules::{
    CommissionTier, ComparisonOperator, ConditionType, LogicType,
    Model as MerchantCommissionRulesModel, TierCondition, TierRules,
};
use derive_more::{Constructor, From, Into};
use rust_decimal::Decimal;
use sea_orm::prelude::DateTimeWithTimeZone;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// ============================================================================
// 基础响应结构
// ============================================================================

/// 商户佣金规则列表响应
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into, utoipa::ToSchema)]
pub struct MerchantCommissionRulesListResponse {
    /// 规则ID
    pub id: Uuid,
    /// 商户ID
    pub merchant_id: i64,
    /// 规则名称
    pub rule_name: String,
    /// 基础佣金比例（字符串格式）
    pub base_commission_rate: String,
    /// 基础佣金比例百分比字符串（如："5.00%"）
    pub base_commission_rate_percent: String,
    /// 是否有阶梯规则
    pub has_tier_rules: bool,
    /// 阶梯规则数量
    pub tier_count: i32,
    /// 创建时间
    pub created_date: String,
    /// 更新时间
    pub updated_date: String,
    /// 备注信息
    pub remark: Option<String>,
}

/// 商户佣金规则详情响应
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into, utoipa::ToSchema)]
pub struct MerchantCommissionRulesDetailResponse {
    /// 规则ID
    pub id: Uuid,
    /// 商户ID
    pub merchant_id: i64,
    /// 规则名称
    pub rule_name: String,
    /// 基础佣金比例（字符串格式）
    pub base_commission_rate: String,
    /// 基础佣金比例百分比字符串
    pub base_commission_rate_percent: String,
    /// 阶梯佣金规则
    pub tier_rules: Option<TierRulesResponse>,
    /// 创建时间
    pub created_date: String,
    /// 更新时间
    pub updated_date: String,
    /// 创建人ID
    pub created_by: Option<Uuid>,
    /// 更新人ID
    pub updated_by: Option<Uuid>,
    /// 备注信息
    pub remark: Option<String>,
}

/// 商户佣金规则基础信息响应
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into, utoipa::ToSchema)]
pub struct MerchantCommissionRulesBasicResponse {
    /// 规则ID
    pub id: Uuid,
    /// 规则名称
    pub rule_name: String,
    /// 基础佣金比例（字符串格式）
    pub base_commission_rate: String,
    /// 基础佣金比例百分比字符串
    pub base_commission_rate_percent: String,
    /// 是否有阶梯规则
    pub has_tier_rules: bool,
}

/// 商户佣金规则选择项
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into, utoipa::ToSchema)]
pub struct MerchantCommissionRulesSelectItem {
    /// 规则ID
    pub id: Uuid,
    /// 规则名称
    pub rule_name: String,
    /// 基础佣金比例百分比字符串
    pub base_commission_rate_percent: String,
}

// ============================================================================
// 阶梯佣金相关响应结构
// ============================================================================

/// 阶梯佣金规则响应
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into, utoipa::ToSchema)]
pub struct TierRulesResponse {
    /// 阶梯规则列表
    pub tiers: Vec<CommissionTierResponse>,
    /// 阶梯总数
    pub total_tiers: i32,
}

/// 单个佣金阶梯响应
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into, utoipa::ToSchema)]
pub struct CommissionTierResponse {
    /// 阶梯名称
    pub tier_name: String,
    /// 逻辑关系类型
    pub logic_type: LogicType,
    /// 逻辑关系类型描述
    pub logic_type_desc: String,
    /// 判断条件列表
    pub conditions: Vec<TierConditionResponse>,
    /// 佣金比例（字符串格式）
    pub commission_rate: String,
    /// 佣金比例百分比字符串
    pub commission_rate_percent: String,
    /// 阶梯描述
    pub description: Option<String>,
    /// 条件描述（人类可读的条件说明）
    pub condition_description: String,
}

/// 阶梯条件响应
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into, utoipa::ToSchema)]
pub struct TierConditionResponse {
    /// 条件类型
    pub condition_type: ConditionType,
    /// 条件类型描述
    pub condition_type_desc: String,
    /// 阈值（字符串格式）
    pub threshold: String,
    /// 阈值格式化字符串
    pub threshold_formatted: String,
    /// 操作符
    pub operator: ComparisonOperator,
    /// 操作符描述
    pub operator_desc: String,
    /// 完整条件描述
    pub full_description: String,
}

// ============================================================================
// 统计相关响应结构
// ============================================================================

/// 商户佣金规则统计响应
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into, utoipa::ToSchema)]
pub struct MerchantCommissionRulesStatsResponse {
    /// 总规则数
    pub total_rules: u64,
    /// 有阶梯规则的数量
    pub tier_rules_count: u64,
    /// 仅基础佣金的数量
    pub basic_only_count: u64,
    /// 平均基础佣金比例（字符串格式）
    pub avg_base_commission_rate: String,
    /// 最高基础佣金比例（字符串格式）
    pub max_base_commission_rate: String,
    /// 最低基础佣金比例（字符串格式）
    pub min_base_commission_rate: String,
}

// ============================================================================
// 佣金计算相关响应结构
// ============================================================================

/// 佣金计算结果响应
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into, utoipa::ToSchema)]
pub struct CommissionCalculateResponse {
    /// 适用的佣金比例（字符串格式）
    pub applicable_rate: String,
    /// 适用的佣金比例百分比字符串
    pub applicable_rate_percent: String,
    /// 计算的佣金金额（字符串格式）
    pub commission_amount: String,
    /// 计算的佣金金额格式化字符串
    pub commission_amount_formatted: String,
    /// 使用的规则名称
    pub rule_name: String,
    /// 匹配的阶梯名称（如果有）
    pub matched_tier_name: Option<String>,
    /// 是否使用了阶梯规则
    pub used_tier_rule: bool,
    /// 计算说明
    pub calculation_description: String,
}

/// 佣金预览响应
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into, utoipa::ToSchema)]
pub struct CommissionPreviewResponse {
    /// 基础佣金（字符串格式）
    pub base_commission: String,
    /// 基础佣金百分比字符串
    pub base_commission_percent: String,
    /// 可能的阶梯佣金列表
    pub tier_commissions: Vec<TierCommissionPreview>,
}

/// 阶梯佣金预览
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into, utoipa::ToSchema)]
pub struct TierCommissionPreview {
    /// 阶梯名称
    pub tier_name: String,
    /// 阶梯佣金比例（字符串格式）
    pub commission_rate: String,
    /// 阶梯佣金比例百分比字符串
    pub commission_rate_percent: String,
    /// 需要满足的条件描述
    pub required_conditions: String,
    /// 是否当前已满足
    pub is_currently_met: bool,
}

// ============================================================================
// 转换实现
// ============================================================================

impl From<MerchantCommissionRulesModel> for MerchantCommissionRulesListResponse {
    fn from(model: MerchantCommissionRulesModel) -> Self {
        let tier_count = model
            .tier_rules
            .as_ref()
            .map(|tr| tr.tiers.len() as i32)
            .unwrap_or(0);

        Self {
            id: model.id,
            merchant_id: model.merchant_id,
            rule_name: model.rule_name,
            base_commission_rate: model.base_commission_rate.to_string(),
            base_commission_rate_percent: format_percentage(model.base_commission_rate.to_string()),
            has_tier_rules: model.tier_rules.is_some(),
            tier_count,
            created_date: format_datetime(model.created_date),
            updated_date: format_datetime(model.updated_date),
            remark: model.remark,
        }
    }
}

impl From<MerchantCommissionRulesModel> for MerchantCommissionRulesDetailResponse {
    fn from(model: MerchantCommissionRulesModel) -> Self {
        Self {
            id: model.id,
            merchant_id: model.merchant_id,
            rule_name: model.rule_name.clone(),
            base_commission_rate: model.base_commission_rate.to_string(),
            base_commission_rate_percent: format_percentage(model.base_commission_rate.to_string()),
            tier_rules: model.tier_rules.map(|tr| tr.into()),
            created_date: format_datetime(model.created_date),
            updated_date: format_datetime(model.updated_date),
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
        }
    }
}

impl From<MerchantCommissionRulesModel> for MerchantCommissionRulesBasicResponse {
    fn from(model: MerchantCommissionRulesModel) -> Self {
        Self {
            id: model.id,
            rule_name: model.rule_name,
            base_commission_rate: model.base_commission_rate.to_string(),
            base_commission_rate_percent: format_percentage(model.base_commission_rate.to_string()),
            has_tier_rules: model.tier_rules.is_some(),
        }
    }
}

impl From<MerchantCommissionRulesModel> for MerchantCommissionRulesSelectItem {
    fn from(model: MerchantCommissionRulesModel) -> Self {
        Self {
            id: model.id,
            rule_name: model.rule_name,
            base_commission_rate_percent: format_percentage(model.base_commission_rate.to_string()),
        }
    }
}

impl From<TierRules> for TierRulesResponse {
    fn from(tier_rules: TierRules) -> Self {
        let total_tiers = tier_rules.tiers.len() as i32;
        Self {
            tiers: tier_rules
                .tiers
                .into_iter()
                .map(|tier| tier.into())
                .collect(),
            total_tiers,
        }
    }
}

impl From<CommissionTier> for CommissionTierResponse {
    fn from(tier: CommissionTier) -> Self {
        let logic_type_desc = match tier.logic_type {
            LogicType::Or => "满足任一条件".to_string(),
            LogicType::And => "必须全部满足".to_string(),
        };

        let condition_description =
            create_condition_description(&tier.conditions, &tier.logic_type);

        Self {
            tier_name: tier.tier_name,
            logic_type: tier.logic_type,
            logic_type_desc,
            conditions: tier
                .conditions
                .into_iter()
                .map(|cond| cond.into())
                .collect(),
            commission_rate: tier.commission_rate.to_string(),
            commission_rate_percent: format_percentage(tier.commission_rate.to_string()),
            description: tier.description,
            condition_description,
        }
    }
}

impl From<TierCondition> for TierConditionResponse {
    fn from(condition: TierCondition) -> Self {
        let condition_type_desc = match condition.condition_type {
            ConditionType::SalesAmount => "年销售额".to_string(),
            ConditionType::CustomerCount => "客户数量".to_string(),
        };

        let operator_desc = match condition.operator {
            ComparisonOperator::GreaterThanOrEqual => "大于等于".to_string(),
            ComparisonOperator::GreaterThan => "大于".to_string(),
            ComparisonOperator::LessThanOrEqual => "小于等于".to_string(),
            ComparisonOperator::LessThan => "小于".to_string(),
            ComparisonOperator::Equal => "等于".to_string(),
        };

        let threshold_formatted = match condition.condition_type {
            ConditionType::SalesAmount => format_currency(condition.threshold.to_string()),
            ConditionType::CustomerCount => format!("{}个", condition.threshold),
        };

        let full_description = format!(
            "{} {} {}",
            condition_type_desc, operator_desc, threshold_formatted
        );

        Self {
            condition_type: condition.condition_type,
            condition_type_desc,
            threshold: condition.threshold.to_string(),
            threshold_formatted,
            operator: condition.operator,
            operator_desc,
            full_description,
        }
    }
}

// ============================================================================
// 工具函数
// ============================================================================

/// 格式化百分比
fn format_percentage(decimal_str: String) -> String {
    if let Ok(decimal) = decimal_str.parse::<Decimal>() {
        format!("{:.2}%", decimal * Decimal::from(100))
    } else {
        format!("{}%", decimal_str)
    }
}

/// 格式化货币
fn format_currency(amount_str: String) -> String {
    if let Ok(amount) = amount_str.parse::<Decimal>() {
        format!("¥{:.2}", amount)
    } else {
        format!("¥{}", amount_str)
    }
}

/// 格式化日期时间
fn format_datetime(dt: DateTimeWithTimeZone) -> String {
    dt.format("%Y-%m-%d %H:%M:%S").to_string()
}

/// 创建条件描述
fn create_condition_description(conditions: &[TierCondition], logic_type: &LogicType) -> String {
    if conditions.is_empty() {
        return "无条件".to_string();
    }

    let connector = match logic_type {
        LogicType::Or => " 或 ",
        LogicType::And => " 且 ",
    };

    conditions
        .iter()
        .map(|cond| {
            let type_desc = match cond.condition_type {
                ConditionType::SalesAmount => "年销售额",
                ConditionType::CustomerCount => "客户数",
            };
            let op_desc = match cond.operator {
                ComparisonOperator::GreaterThanOrEqual => "≥",
                ComparisonOperator::GreaterThan => ">",
                ComparisonOperator::LessThanOrEqual => "≤",
                ComparisonOperator::LessThan => "<",
                ComparisonOperator::Equal => "=",
            };
            let threshold_str = match cond.condition_type {
                ConditionType::SalesAmount => format_currency(cond.threshold.to_string()),
                ConditionType::CustomerCount => format!("{}个", cond.threshold),
            };
            format!("{}{}{}", type_desc, op_desc, threshold_str)
        })
        .collect::<Vec<_>>()
        .join(connector)
}

use crate::domain::business::merchants::entities::merchants::Location;
use crate::utils::datetime::DateTimeUtils;
use bon::Builder;
use chrono::Datelike;
use sea_orm::ActiveEnum;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;

/// 商户列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": 1,
    "merchant_name": "张三餐厅",
    "merchant_code": "M001",
    "category_id": "550e8400-e29b-41d4-a716-************",
    "category_name": "餐饮",
    "phone": "***********",
    "email": "<EMAIL>",
    "address": "北京市朝阳区xxx街道xxx号",
    "platform_commission_rate": "0.0500",
    "follower_name": "李四",
    "follower_phone": "***********",
    "status": 1,
    "status_desc": "启用",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00"
}))]
pub struct SysMerchantListResponse {
    /// 商户ID
    pub id: i64,

    /// 商户名称
    pub merchant_name: String,

    // 商户logo 头像
    pub avatar: Option<String>,

    /// 商户编码
    pub merchant_code: String,

    /// 分类ID
    pub category_id: Option<Uuid>,

    /// 分类名称
    pub category_name: Option<String>,

    /// 联系电话
    pub phone: Option<String>,

    /// 邮箱地址
    pub email: Option<String>,

    /// 详细地址
    pub address: Option<String>,

    /// 平台佣金比例
    pub platform_commission_rate: Option<String>,

    /// 状态（1正常营业 2临时关闭 3永久关闭）
    pub status: i32,

    /// 状态描述
    pub status_desc: String,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,
}

/// 商户详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": 1,
    "merchant_name": "张三餐厅",
    "merchant_code": "M001",
    "category_id": "550e8400-e29b-41d4-a716-************",
    "category_name": "餐饮",
    "phone": "***********",
    "email": "<EMAIL>",
    "address": "北京市朝阳区xxx街道xxx号",
    "location": {"longitude": 116.4470, "latitude": 39.9200},
    "business_license": "91110000MA001234567",
    "license_photo": "https://example.com/license.jpg",
    "avatar": "https://example.com/avatar.jpg",
    "description": "专业的中式餐厅",
    "platform_commission_rate": "0.0500",
    "follower_id": "550e8400-e29b-41d4-a716-************",
    "follower_name": "李四",
    "follower_phone": "***********",
    "follower_commission_rate": "0.0200",
    "status": 1,
    "status_desc": "启用",
    "auto_clear_date": 15,
    "sort_order": 1,
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00",
    "created_by": "550e8400-e29b-41d4-a716-************",
    "updated_by": "550e8400-e29b-41d4-a716-************",
    "remark": "优质商户"
}))]
pub struct SysMerchantDetailResponse {
    /// 商户ID
    pub id: i64,

    /// 商户名称
    pub merchant_name: String,

    /// 商户编码
    pub merchant_code: String,

    /// 分类ID
    pub category_id: Option<Uuid>,

    /// 分类名称
    pub category_name: Option<String>,

    /// 联系电话
    pub phone: Option<String>,

    /// 邮箱地址
    pub email: Option<String>,

    /// 详细地址
    pub address: Option<String>,

    /// 经纬度坐标
    pub location: Option<Location>,

    /// 营业执照号
    pub business_license: Option<String>,

    /// 营业执照照片URL
    pub license_photo: Option<String>,

    /// 商户头像URL
    pub avatar: Option<String>,

    /// 商户描述
    pub description: Option<String>,

    /// 平台佣金比例
    pub platform_commission_rate: Option<String>,

    /// 状态（1正常营业 2临时关闭 3永久关闭）
    pub status: i32,

    /// 状态描述
    pub status_desc: String,

    /// 自动结算日期
    pub auto_clear_date: Option<i32>,

    /// 排序字段
    pub sort_order: Option<i32>,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,

    /// 创建人ID
    pub created_by: Option<Uuid>,

    /// 更新人ID
    pub updated_by: Option<Uuid>,

    /// 备注
    pub remark: Option<String>,
}

/// 商户选择项（用于下拉选择）
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": 1,
    "merchant_name": "张三餐厅",
    "merchant_code": "M001",
    "contact_person": "张三",
    "contact_phone": "***********",
    "merchant_level": 2,
    "status": 1
}))]
pub struct SysMerchantSelectItem {
    /// 商户ID
    pub id: i64,

    /// 商户名称
    pub merchant_name: String,

    /// 商户编码
    pub merchant_code: String,

    /// 分类名称
    pub category_name: Option<String>,

    /// 状态（1启用 0禁用）
    pub status: i32,
}

/// 商户统计信息响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "total_count": 100,
    "active_count": 85,
    "inactive_count": 15,
    "by_category": [
        {
            "category_id": "550e8400-e29b-41d4-a716-************",
            "category_name": "餐饮",
            "merchant_count": 45
        },
        {
            "category_id": "550e8400-e29b-41d4-a716-446655440001",
            "category_name": "零售",
            "merchant_count": 30
        }
    ]
}))]
pub struct SysMerchantStatsResponse {
    /// 商户总数
    pub total_count: i32,

    /// 启用商户数
    pub active_count: i32,

    /// 禁用商户数
    pub inactive_count: i32,

    /// 按分类统计
    pub by_category: Vec<SysMerchantCategoryStats>,
}

/// 商户分类统计
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
pub struct SysMerchantCategoryStats {
    /// 分类ID
    pub category_id: Option<Uuid>,

    /// 分类名称
    pub category_name: Option<String>,

    /// 商户数量
    pub merchant_count: i32,
}

/// 商户佣金信息响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "platform_commission_rate": "0.0500",
    "follower_commission_rate": "0.0200",
    "total_commission_rate": "0.0700"
}))]
pub struct SysMerchantCommissionResponse {
    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: String,

    /// 平台佣金比例
    pub platform_commission_rate: Option<String>,

    /// 总佣金比例
    pub total_commission_rate: Option<String>,
}

/// 商户跟进人信息响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "follower_id": "550e8400-e29b-41d4-a716-************",
    "follower_name": "李四",
    "follower_phone": "***********",
    "follower_commission_rate": "0.0200"
}))]
pub struct SysMerchantFollowerResponse {
    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: String,
}

/// 商户位置响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "address": "北京市朝阳区xxx街道xxx号",
    "location": {"longitude": 116.4470, "latitude": 39.9200}
}))]
pub struct SysMerchantLocationResponse {
    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: String,

    /// 详细地址
    pub address: Option<String>,

    /// 经纬度坐标
    pub location: Option<Location>,
}

/// 商户基础信息响应（用于简单查询）
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": 1,
    "merchant_name": "张三餐厅",
    "merchant_code": "M001",
    "phone": "***********",
    "status": 1,
    "status_desc": "启用"
}))]
pub struct SysMerchantBasicResponse {
    /// 商户ID
    pub id: i64,

    /// 商户名称
    pub merchant_name: String,

    /// 商户编码
    pub merchant_code: String,

    /// 联系电话
    pub phone: Option<String>,

    /// 状态（1正常营业 2临时关闭 3永久关闭）
    pub status: i32,

    /// 状态描述
    pub status_desc: String,
}

// ============================================================================
// 实体类转 VO - 转换方法
// ============================================================================

use crate::domain::business::merchants::entities::merchants::Model as MerchantModel;

impl From<MerchantModel> for SysMerchantListResponse {
    fn from(model: MerchantModel) -> Self {
        Self {
            id: model.id,
            merchant_name: model.merchant_name,
            merchant_code: model.merchant_code,
            avatar: model.avatar,
            category_id: model.category_id,
            category_name: None, // 需要从关联查询中获取
            phone: model.phone,
            email: model.email,
            address: model.address,
            platform_commission_rate: model.platform_commission_rate.map(|d| d.to_string()),
            status: model.status.to_value(),
            status_desc: get_merchant_status_desc(model.status.to_value()),
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            updated_date: DateTimeUtils::format_datetime(
                &model.updated_date.with_timezone(&chrono::Local),
            ),
        }
    }
}

impl From<MerchantModel> for SysMerchantDetailResponse {
    fn from(model: MerchantModel) -> Self {
        Self {
            id: model.id,
            merchant_name: model.merchant_name,
            merchant_code: model.merchant_code,
            category_id: model.category_id,
            category_name: None, // 需要从关联查询中获取
            phone: model.phone,
            email: model.email,
            address: model.address,
            location: model.location.and_then(|l| {
                // 尝试从JSON中解析Location对象
                if let Some(location_obj) = l.as_object() {
                    // 如果是JSON对象，尝试解析为Location
                    if let (Some(lng), Some(lat)) = (
                        location_obj.get("longitude").and_then(|v| v.as_f64()),
                        location_obj.get("latitude").and_then(|v| v.as_f64()),
                    ) {
                        Some(Location {
                            longitude: lng,
                            latitude: lat,
                        })
                    } else {
                        None
                    }
                } else if let Some(location_str) = l.as_str() {
                    // 如果是字符串格式，尝试解析为Location
                    Location::from_string(location_str).ok()
                } else {
                    None
                }
            }),
            business_license: model.business_license,
            license_photo: model.license_photo,
            avatar: model.avatar,
            description: model.description,
            platform_commission_rate: model.platform_commission_rate.map(|d| d.to_string()),
            status: model.status.to_value(),
            status_desc: get_merchant_status_desc(model.status.to_value()),
            auto_clear_date: model.auto_clear_date.map(|d| d.day() as i32),
            sort_order: model.sort_order,
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            updated_date: DateTimeUtils::format_datetime(
                &model.updated_date.with_timezone(&chrono::Local),
            ),
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
        }
    }
}

impl From<MerchantModel> for SysMerchantSelectItem {
    fn from(model: MerchantModel) -> Self {
        Self {
            id: model.id,
            merchant_name: model.merchant_name,
            merchant_code: model.merchant_code,
            category_name: None, // 需要从关联查询中获取
            status: model.status.to_value(),
        }
    }
}

impl From<MerchantModel> for SysMerchantBasicResponse {
    fn from(model: MerchantModel) -> Self {
        Self {
            id: model.id,
            merchant_name: model.merchant_name,
            merchant_code: model.merchant_code,
            phone: model.phone,
            status: model.status.to_value(),
            status_desc: get_merchant_status_desc(model.status.to_value()),
        }
    }
}

impl From<MerchantModel> for SysMerchantCommissionResponse {
    fn from(model: MerchantModel) -> Self {
        let platform_rate = model
            .platform_commission_rate
            .as_ref()
            .map(|d| d.to_string());

        let total_rate = match &model.platform_commission_rate {
            Some(p) => Some(p.to_string()),
            None => None,
        };

        Self {
            merchant_id: model.id,
            merchant_name: model.merchant_name,
            platform_commission_rate: platform_rate,
            total_commission_rate: total_rate,
        }
    }
}

impl From<MerchantModel> for SysMerchantFollowerResponse {
    fn from(model: MerchantModel) -> Self {
        Self {
            merchant_id: model.id,
            merchant_name: model.merchant_name,
        }
    }
}

impl From<MerchantModel> for SysMerchantLocationResponse {
    fn from(model: MerchantModel) -> Self {
        Self {
            merchant_id: model.id,
            merchant_name: model.merchant_name,
            address: model.address,
            location: model.location.and_then(|l| {
                // 尝试从JSON中解析Location对象
                if let Some(location_obj) = l.as_object() {
                    // 如果是JSON对象，尝试解析为Location
                    if let (Some(lng), Some(lat)) = (
                        location_obj.get("longitude").and_then(|v| v.as_f64()),
                        location_obj.get("latitude").and_then(|v| v.as_f64()),
                    ) {
                        Some(Location {
                            longitude: lng,
                            latitude: lat,
                        })
                    } else {
                        None
                    }
                } else if let Some(location_str) = l.as_str() {
                    // 如果是字符串格式，尝试解析为Location
                    Location::from_string(location_str).ok()
                } else {
                    None
                }
            }),
        }
    }
}

// ============================================================================
// 便捷构造函数
// ============================================================================

impl SysMerchantListResponse {
    /// 创建带分类名称的列表响应
    pub fn with_category_name(mut self, category_name: Option<String>) -> Self {
        self.category_name = category_name;
        self
    }
}

impl SysMerchantDetailResponse {
    /// 创建带分类名称的详情响应
    pub fn with_category_name(mut self, category_name: Option<String>) -> Self {
        self.category_name = category_name;
        self
    }
}

impl SysMerchantSelectItem {
    /// 创建带分类名称的选择项
    pub fn with_category_name(mut self, category_name: Option<String>) -> Self {
        self.category_name = category_name;
        self
    }
}

/// 获取商户状态描述
fn get_merchant_status_desc(status: i32) -> String {
    match status {
        1 => "正常营业".to_string(),
        2 => "临时关闭".to_string(),
        3 => "永久关闭".to_string(),
        _ => "未知状态".to_string(),
    }
}

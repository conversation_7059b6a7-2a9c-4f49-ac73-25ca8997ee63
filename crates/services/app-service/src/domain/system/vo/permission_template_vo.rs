use chrono::{DateTime, FixedOffset};
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;

use crate::domain::business::merchants::entities::system_permission_templates;

/// 系统权限模板列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Clone)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "用户管理",
    "permission_code": "system:user:list",
    "parent_id": "550e8400-e29b-41d4-a716-************",
    "parent_name": "系统管理",
    "order_num": 1,
    "path": "/system/user",
    "component": "system/user/index",
    "permission_type": 2,
    "permission_type_desc": "菜单",
    "visible": 0,
    "visible_desc": "显示",
    "icon": "user",
    "description": "系统用户管理功能",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00"
}))]
pub struct SystemPermissionTemplateListResponse {
    /// 权限模板ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub id: Uuid,
    
    /// 权限名称
    #[schema(example = "用户管理")]
    pub permission_name: String,
    
    /// 权限编码
    #[schema(example = "system:user:list")]
    pub permission_code: String,
    
    /// 父权限ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub parent_id: Option<Uuid>,
    
    /// 父权限名称
    #[schema(example = "系统管理")]
    pub parent_name: Option<String>,
    
    /// 显示顺序
    #[schema(example = 1)]
    pub order_num: i32,
    
    /// 路由地址
    #[schema(example = "/system/user")]
    pub path: Option<String>,
    
    /// 组件路径
    #[schema(example = "system/user/index")]
    pub component: Option<String>,
    
    /// 权限类型
    #[schema(example = 2)]
    pub permission_type: i32,
    
    /// 权限类型描述
    #[schema(example = "菜单")]
    pub permission_type_desc: String,
    
    /// 菜单状态
    #[schema(example = 0)]
    pub visible: i32,
    
    /// 菜单状态描述
    #[schema(example = "显示")]
    pub visible_desc: String,
    
    /// 权限图标
    #[schema(example = "user")]
    pub icon: Option<String>,
    
    /// 权限描述
    #[schema(example = "系统用户管理功能")]
    pub description: Option<String>,
    
    /// 创建时间
    #[schema(example = "2023-01-01 10:00:00")]
    pub created_date: String,
    
    /// 更新时间
    #[schema(example = "2023-01-01 10:00:00")]
    pub updated_date: String,
}

/// 系统权限模板详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "用户管理",
    "permission_code": "system:user:list",
    "parent_id": "550e8400-e29b-41d4-a716-************",
    "parent_name": "系统管理",
    "order_num": 1,
    "path": "/system/user",
    "component": "system/user/index",
    "query": "{\"tab\":\"basic\"}",
    "is_frame": 1,
    "is_frame_desc": "否",
    "is_cache": 0,
    "is_cache_desc": "缓存",
    "permission_type": 2,
    "permission_type_desc": "菜单",
    "visible": 0,
    "visible_desc": "显示",
    "icon": "user",
    "description": "系统用户管理功能",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00",
    "created_by": "550e8400-e29b-41d4-a716-************",
    "updated_by": "550e8400-e29b-41d4-a716-************",
    "remark": "基础权限模板"
}))]
pub struct SystemPermissionTemplateDetailResponse {
    /// 权限模板ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub id: Uuid,
    
    /// 权限名称
    #[schema(example = "用户管理")]
    pub permission_name: String,
    
    /// 权限编码
    #[schema(example = "system:user:list")]
    pub permission_code: String,
    
    /// 父权限ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub parent_id: Option<Uuid>,
    
    /// 父权限名称
    #[schema(example = "系统管理")]
    pub parent_name: Option<String>,
    
    /// 显示顺序
    #[schema(example = 1)]
    pub order_num: i32,
    
    /// 路由地址
    #[schema(example = "/system/user")]
    pub path: Option<String>,
    
    /// 组件路径
    #[schema(example = "system/user/index")]
    pub component: Option<String>,
    
    /// 路由参数
    #[schema(example = "{\"tab\":\"basic\"}")]
    pub query: Option<String>,
    
    /// 是否为外链
    #[schema(example = 1)]
    pub is_frame: i32,
    
    /// 是否为外链描述
    #[schema(example = "否")]
    pub is_frame_desc: String,
    
    /// 是否缓存
    #[schema(example = 0)]
    pub is_cache: i32,
    
    /// 是否缓存描述
    #[schema(example = "缓存")]
    pub is_cache_desc: String,
    
    /// 权限类型
    #[schema(example = 2)]
    pub permission_type: i32,
    
    /// 权限类型描述
    #[schema(example = "菜单")]
    pub permission_type_desc: String,
    
    /// 菜单状态
    #[schema(example = 0)]
    pub visible: i32,
    
    /// 菜单状态描述
    #[schema(example = "显示")]
    pub visible_desc: String,
    
    /// 权限图标
    #[schema(example = "user")]
    pub icon: Option<String>,
    
    /// 权限描述
    #[schema(example = "系统用户管理功能")]
    pub description: Option<String>,
    
    /// 创建时间
    #[schema(example = "2023-01-01 10:00:00")]
    pub created_date: String,
    
    /// 更新时间
    #[schema(example = "2023-01-01 10:00:00")]
    pub updated_date: String,
    
    /// 创建人ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub created_by: Option<Uuid>,
    
    /// 更新人ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub updated_by: Option<Uuid>,
    
    /// 备注
    #[schema(example = "基础权限模板")]
    pub remark: Option<String>,
}

/// 系统权限模板树形节点
#[derive(Debug, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "label": "系统管理",
    "permission_code": "system",
    "parent_id": null,
    "permission_type": 1,
    "visible": 0,
    "icon": "system",
    "order_num": 1,
    "children": [
        {
            "id": "550e8400-e29b-41d4-a716-************",
            "label": "用户管理",
            "permission_code": "system:user:list",
            "parent_id": "550e8400-e29b-41d4-a716-************",
            "permission_type": 2,
            "visible": 0,
            "icon": "user",
            "order_num": 1,
            "children": []
        }
    ]
}))]
pub struct SystemPermissionTemplateTreeNode {
    /// 权限模板ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub id: Uuid,
    
    /// 权限名称
    #[schema(example = "系统管理")]
    pub label: String,
    
    /// 权限编码
    #[schema(example = "system")]
    pub permission_code: String,
    
    /// 父权限ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub parent_id: Option<Uuid>,
    
    /// 权限类型
    #[schema(example = 1)]
    pub permission_type: i32,
    
    /// 菜单状态
    #[schema(example = 0)]
    pub visible: i32,
    
    /// 权限图标
    #[schema(example = "system")]
    pub icon: Option<String>,
    
    /// 排序
    #[schema(example = 1)]
    pub order_num: i32,
    
    /// 子节点
    #[schema(no_recursion)]
    pub children: Vec<SystemPermissionTemplateTreeNode>,
}

/// 系统权限模板选择项
#[derive(Debug, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "用户管理",
    "permission_code": "system:user:list",
    "permission_type": 2,
    "parent_id": "550e8400-e29b-41d4-a716-************",
    "visible": 0
}))]
pub struct SystemPermissionTemplateSelectItem {
    /// 权限模板ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub id: Uuid,
    
    /// 权限名称
    #[schema(example = "用户管理")]
    pub permission_name: String,
    
    /// 权限编码
    #[schema(example = "system:user:list")]
    pub permission_code: String,
    
    /// 权限类型
    #[schema(example = 2)]
    pub permission_type: i32,
    
    /// 父权限ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub parent_id: Option<Uuid>,
    
    /// 状态（0显示 1隐藏）
    #[schema(example = 0)]
    pub visible: i32,
}

/// 权限模板统计信息
#[derive(Debug, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "total_count": 50,
    "directory_count": 8,
    "menu_count": 32,
    "button_count": 10,
    "visible_count": 45,
    "hidden_count": 5
}))]
pub struct SystemPermissionTemplateStatsResponse {
    /// 总权限数
    #[schema(example = 50)]
    pub total_count: i32,
    
    /// 目录数量
    #[schema(example = 8)]
    pub directory_count: i32,
    
    /// 菜单数量
    #[schema(example = 32)]
    pub menu_count: i32,
    
    /// 按钮数量
    #[schema(example = 10)]
    pub button_count: i32,
    
    /// 显示数量
    #[schema(example = 45)]
    pub visible_count: i32,
    
    /// 隐藏数量
    #[schema(example = 5)]
    pub hidden_count: i32,
}

// ============================================================================
// 转换实现
// ============================================================================

impl From<system_permission_templates::Model> for SystemPermissionTemplateListResponse {
    fn from(model: system_permission_templates::Model) -> Self {
        Self {
            id: model.id,
            permission_name: model.permission_name,
            permission_code: model.permission_code,
            parent_id: model.parent_id,
            parent_name: None, // 需要通过联表查询获取
            order_num: model.order_num.unwrap_or(0),
            path: model.path,
            component: model.component,
            permission_type: model.permission_type.unwrap_or(2),
            permission_type_desc: get_permission_type_desc(model.permission_type.unwrap_or(2)),
            visible: model.visible.unwrap_or(0),
            visible_desc: get_visible_desc(model.visible.unwrap_or(0)),
            icon: model.icon,
            description: model.description,
            created_date: format_datetime(&model.created_date),
            updated_date: format_datetime(&model.updated_date),
        }
    }
}

impl From<system_permission_templates::Model> for SystemPermissionTemplateDetailResponse {
    fn from(model: system_permission_templates::Model) -> Self {
        Self {
            id: model.id,
            permission_name: model.permission_name,
            permission_code: model.permission_code,
            parent_id: model.parent_id,
            parent_name: None, // 需要通过联表查询获取
            order_num: model.order_num.unwrap_or(0),
            path: model.path,
            component: model.component,
            query: model.query,
            is_frame: model.is_frame.unwrap_or(1),
            is_frame_desc: get_is_frame_desc(model.is_frame.unwrap_or(1)),
            is_cache: model.is_cache.unwrap_or(0),
            is_cache_desc: get_is_cache_desc(model.is_cache.unwrap_or(0)),
            permission_type: model.permission_type.unwrap_or(2),
            permission_type_desc: get_permission_type_desc(model.permission_type.unwrap_or(2)),
            visible: model.visible.unwrap_or(0),
            visible_desc: get_visible_desc(model.visible.unwrap_or(0)),
            icon: model.icon,
            description: model.description,
            created_date: format_datetime(&model.created_date),
            updated_date: format_datetime(&model.updated_date),
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
        }
    }
}

impl From<system_permission_templates::Model> for SystemPermissionTemplateSelectItem {
    fn from(model: system_permission_templates::Model) -> Self {
        Self {
            id: model.id,
            permission_name: model.permission_name,
            permission_code: model.permission_code,
            permission_type: model.permission_type.unwrap_or(2),
            parent_id: model.parent_id,
            visible: model.visible.unwrap_or(0),
        }
    }
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 获取权限类型描述
fn get_permission_type_desc(permission_type: i32) -> String {
    match permission_type {
        1 => "目录".to_string(),
        2 => "菜单".to_string(),
        3 => "按钮".to_string(),
        _ => "未知".to_string(),
    }
}

/// 获取菜单状态描述
fn get_visible_desc(visible: i32) -> String {
    match visible {
        0 => "显示".to_string(),
        1 => "隐藏".to_string(),
        _ => "未知".to_string(),
    }
}

/// 获取是否外链描述
fn get_is_frame_desc(is_frame: i32) -> String {
    match is_frame {
        0 => "是".to_string(),
        1 => "否".to_string(),
        _ => "未知".to_string(),
    }
}

/// 获取是否缓存描述
fn get_is_cache_desc(is_cache: i32) -> String {
    match is_cache {
        0 => "缓存".to_string(),
        1 => "不缓存".to_string(),
        _ => "未知".to_string(),
    }
}

/// 格式化日期时间
fn format_datetime(dt: &DateTime<FixedOffset>) -> String {
    dt.format("%Y-%m-%d %H:%M:%S").to_string()
}

impl SystemPermissionTemplateTreeNode {
    /// 构建权限模板树形结构
    pub fn build_permission_tree(
        templates: Vec<system_permission_templates::Model>,
    ) -> Vec<SystemPermissionTemplateTreeNode> {
        let mut map = std::collections::HashMap::new();

        // 构建映射
        for template in templates {
            let node = SystemPermissionTemplateTreeNode {
                id: template.id,
                label: template.permission_name.clone(),
                permission_code: template.permission_code.clone(),
                parent_id: template.parent_id,
                permission_type: template.permission_type.unwrap_or(2),
                visible: template.visible.unwrap_or(0),
                icon: template.icon,
                order_num: template.order_num.unwrap_or(0),
                children: Vec::new(),
            };
            map.insert(template.id, node);
        }

        // 构建树形结构
        let mut roots = Vec::new();
        let mut children_map: std::collections::HashMap<
            uuid::Uuid,
            Vec<SystemPermissionTemplateTreeNode>,
        > = std::collections::HashMap::new();

        // 分离根节点和子节点
        for (_id, node) in map {
            if let Some(parent_id) = node.parent_id {
                children_map
                    .entry(parent_id)
                    .or_insert_with(Vec::new)
                    .push(node);
            } else {
                roots.push(node);
            }
        }

        // 递归构建子节点
        fn build_children(
            node: &mut SystemPermissionTemplateTreeNode,
            children_map: &mut std::collections::HashMap<
                uuid::Uuid,
                Vec<SystemPermissionTemplateTreeNode>,
            >,
        ) {
            if let Some(mut children) = children_map.remove(&node.id) {
                children.sort_by(|a, b| a.order_num.cmp(&b.order_num));
                for mut child in children {
                    build_children(&mut child, children_map);
                    node.children.push(child);
                }
            }
        }

        // 为每个根节点构建子树
        for mut root in &mut roots {
            build_children(&mut root, &mut children_map);
        }

        // 排序根节点
        roots.sort_by(|a, b| a.order_num.cmp(&b.order_num));

        roots
    }
} 
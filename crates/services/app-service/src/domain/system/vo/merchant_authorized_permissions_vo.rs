use crate::utils::datetime::DateTimeUtils;
use bon::Builder;
use sea_orm::ActiveEnum;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;

/// 商户授权权限列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "permission_template_id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "permission_type": 1,
    "permission_type_desc": "目录",
    "status": 0,
    "status_desc": "正常",
    "authorized_date": "2023-01-01 10:00:00",
    "authorized_by_name": "系统管理员",
    "remark": "商户订单管理权限"
}))]
pub struct MerchantAuthorizedPermissionListResponse {
    /// 授权记录ID
    pub id: Uuid,

    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: Option<String>,

    /// 系统权限模板ID
    pub permission_template_id: Uuid,

    /// 权限名称
    pub permission_name: Option<String>,

    /// 权限编码
    pub permission_code: Option<String>,

    /// 权限类型
    pub permission_type: Option<i32>,

    /// 权限类型描述
    pub permission_type_desc: String,

    /// 授权状态
    pub status: i32,

    /// 授权状态描述
    pub status_desc: String,

    /// 授权时间
    pub authorized_date: String,

    /// 授权人姓名
    pub authorized_by_name: Option<String>,

    /// 备注
    pub remark: Option<String>,
}

/// 商户授权权限详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "permission_template_id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "permission_type": 1,
    "permission_type_desc": "目录",
    "permission_description": "订单查看权限",
    "status": 0,
    "status_desc": "正常",
    "authorized_date": "2023-01-01 10:00:00",
    "created_by": "550e8400-e29b-41d4-a716-************",
    "authorized_by_name": "系统管理员",
    "updated_by": "550e8400-e29b-41d4-a716-************",
    "updated_by_name": "超级管理员",
    "remark": "商户订单管理权限"
}))]
pub struct MerchantAuthorizedPermissionDetailResponse {
    /// 授权记录ID
    pub id: Uuid,

    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: Option<String>,

    /// 系统权限模板ID
    pub permission_template_id: Uuid,

    /// 权限名称
    pub permission_name: Option<String>,

    /// 权限编码
    pub permission_code: Option<String>,

    /// 权限类型
    pub permission_type: Option<i32>,

    /// 权限类型描述
    pub permission_type_desc: String,

    /// 权限描述
    pub permission_description: Option<String>,

    /// 授权状态
    pub status: i32,

    /// 授权状态描述
    pub status_desc: String,

    /// 授权时间
    pub authorized_date: String,

    /// 创建人ID
    pub created_by: Uuid,

    /// 授权人姓名
    pub authorized_by_name: Option<String>,

    /// 更新人ID
    pub updated_by: Option<Uuid>,

    /// 更新人姓名
    pub updated_by_name: Option<String>,

    /// 备注
    pub remark: Option<String>,
}

/// 商户授权权限选择项
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "permission_template_id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "status": 0
}))]
pub struct MerchantAuthorizedPermissionSelectItem {
    /// 授权记录ID
    pub id: Uuid,

    /// 系统权限模板ID
    pub permission_template_id: Uuid,

    /// 权限名称
    pub permission_name: Option<String>,

    /// 权限编码
    pub permission_code: Option<String>,

    /// 授权状态
    pub status: i32,
}

/// 商户权限统计响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "total_permissions": 50,
    "authorized_permissions": 30,
    "active_permissions": 25,
    "suspended_permissions": 5
}))]
pub struct MerchantPermissionStatsResponse {
    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: String,

    /// 总权限数量
    pub total_permissions: i32,

    /// 已授权权限数量
    pub authorized_permissions: i32,

    /// 正常权限数量
    pub active_permissions: i32,

    /// 停用权限数量
    pub suspended_permissions: i32,
}

/// 商户授权权限树形节点
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "authorized_permission_id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "系统管理",
    "permission_code": "system",
    "permission_type": 1,
    "permission_type_desc": "目录",
    "parent_id": null,
    "status": 0,
    "status_desc": "正常",
    "authorized_date": "2023-01-01 10:00:00",
    "order_num": 1,
    "children": []
}))]
pub struct MerchantAuthorizedPermissionTreeNode {
    /// 权限模板ID
    pub id: Uuid,

    /// 商户授权权限ID（如果已授权）
    pub authorized_permission_id: Option<Uuid>,

    /// 权限名称
    pub permission_name: String,

    /// 权限编码
    pub permission_code: String,

    /// 权限类型
    pub permission_type: i32,

    /// 权限类型描述
    pub permission_type_desc: String,

    /// 父权限ID
    pub parent_id: Option<Uuid>,

    /// 是否已授权
    pub is_authorized: bool,

    /// 授权状态（仅当已授权时有效）
    pub status: Option<i32>,

    /// 授权状态描述
    pub status_desc: Option<String>,
    
    /// 图标
    pub icon: Option<String>,

    /// 授权时间（仅当已授权时有效）
    pub authorized_date: Option<String>,

    /// 排序号
    pub order_num: i32,

    /// 子权限列表
    #[schema(no_recursion)]
    pub children: Vec<MerchantAuthorizedPermissionTreeNode>,
}

/// 商户授权权限树形结构响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "tree": [
        {
            "id": "550e8400-e29b-41d4-a716-************",
            "authorized_permission_id": "550e8400-e29b-41d4-a716-************",
            "permission_name": "系统管理",
            "permission_code": "system",
            "permission_type": 1,
            "permission_type_desc": "目录",
            "parent_id": null,
            "is_authorized": true,
            "status": 0,
            "status_desc": "正常",
            "authorized_date": "2023-01-01 10:00:00",
            "order_num": 1,
            "children": []
        }
    ]
}))]
pub struct MerchantAuthorizedPermissionTreeResponse {
    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: String,

    /// 权限树形结构
    #[schema(no_recursion)]
    pub tree: Vec<MerchantAuthorizedPermissionTreeNode>,
}

// ============================================================================
// 实体类转 VO - 转换方法
// ============================================================================

use crate::domain::business::merchants::entities::merchant_authorized_permissions::{Model as MerchantAuthorizedPermissionModel, Model};
use crate::domain::system::vo::merchant_role_permissions_vo::get_permission_type_desc;

impl From<MerchantAuthorizedPermissionModel> for MerchantAuthorizedPermissionListResponse {
    fn from(model: MerchantAuthorizedPermissionModel) -> Self {
        Self {
            id: model.id,
            merchant_id: model.merchant_id,
            merchant_name: None, // 需要从关联查询中获取
            permission_template_id: model.permission_template_id,
            permission_name: None, // 需要从关联查询中获取
            permission_code: None, // 需要从关联查询中获取
            permission_type: None, // 需要从关联查询中获取
            permission_type_desc: "未知".to_string(), // 需要从关联查询中获取
            status: model.status as i32,
            status_desc: Model::get_status_desc(model.status),
            authorized_date: DateTimeUtils::format_datetime(
                &model.authorized_date.with_timezone(&chrono::Local),
            ),
            authorized_by_name: None, // 需要从关联查询中获取
            remark: model.remark,
        }
    }
}

impl From<MerchantAuthorizedPermissionModel> for MerchantAuthorizedPermissionDetailResponse {
    fn from(model: MerchantAuthorizedPermissionModel) -> Self {
        Self {
            id: model.id,
            merchant_id: model.merchant_id,
            merchant_name: None, // 需要从关联查询中获取
            permission_template_id: model.permission_template_id,
            permission_name: None, // 需要从关联查询中获取
            permission_code: None, // 需要从关联查询中获取
            permission_type: None, // 需要从关联查询中获取
            permission_type_desc: "未知".to_string(), // 需要从关联查询中获取
            permission_description: None, // 需要从关联查询中获取
            status: model.status.to_value(),
            status_desc: Model::get_status_desc(model.status),
            authorized_date: DateTimeUtils::format_datetime(
                &model.authorized_date.with_timezone(&chrono::Local),
            ),
            created_by: model.created_by,
            authorized_by_name: None, // 需要从关联查询中获取
            updated_by: model.updated_by,
            updated_by_name: None, // 需要从关联查询中获取
            remark: model.remark,
        }
    }
}

impl From<MerchantAuthorizedPermissionModel> for MerchantAuthorizedPermissionSelectItem {
    fn from(model: MerchantAuthorizedPermissionModel) -> Self {
        Self {
            id: model.id,
            permission_template_id: model.permission_template_id,
            permission_name: None, // 需要从关联查询中获取
            permission_code: None, // 需要从关联查询中获取
            status: model.status.to_value(),
        }
    }
}

// ============================================================================
// 便捷构造函数
// ============================================================================

impl MerchantAuthorizedPermissionListResponse {
    /// 设置商户信息
    pub fn with_merchant_info(mut self, merchant_name: Option<String>) -> Self {
        self.merchant_name = merchant_name;
        self
    }

    /// 设置权限信息
    pub fn with_permission_info(
        mut self,
        permission_name: Option<String>,
        permission_code: Option<String>,
        permission_type: Option<i32>,
    ) -> Self {
        self.permission_name = permission_name;
        self.permission_code = permission_code;
        self.permission_type = permission_type;
        self.permission_type_desc = get_permission_type_desc(permission_type);
        self
    }

    /// 设置授权人信息
    pub fn with_authorized_by_name(mut self, authorized_by_name: Option<String>) -> Self {
        self.authorized_by_name = authorized_by_name;
        self
    }
}

impl MerchantAuthorizedPermissionDetailResponse {
    /// 设置商户信息
    pub fn with_merchant_info(mut self, merchant_name: Option<String>) -> Self {
        self.merchant_name = merchant_name;
        self
    }

    /// 设置权限信息
    pub fn with_permission_info(
        mut self,
        permission_name: Option<String>,
        permission_code: Option<String>,
        permission_type: Option<i32>,
        permission_description: Option<String>,
    ) -> Self {
        self.permission_name = permission_name;
        self.permission_code = permission_code;
        self.permission_type = permission_type;
        self.permission_type_desc = get_permission_type_desc(permission_type);
        self.permission_description = permission_description;
        self
    }

    /// 设置用户信息
    pub fn with_user_info(
        mut self,
        authorized_by_name: Option<String>,
        updated_by_name: Option<String>,
    ) -> Self {
        self.authorized_by_name = authorized_by_name;
        self.updated_by_name = updated_by_name;
        self
    }
}

impl MerchantAuthorizedPermissionSelectItem {
    /// 设置权限信息
    pub fn with_permission_info(
        mut self,
        permission_name: Option<String>,
        permission_code: Option<String>,
    ) -> Self {
        self.permission_name = permission_name;
        self.permission_code = permission_code;
        self
    }
}
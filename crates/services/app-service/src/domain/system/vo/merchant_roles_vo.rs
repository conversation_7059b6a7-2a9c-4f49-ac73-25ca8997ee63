use crate::utils::datetime::DateTimeUtils;
use bon::Builder;
use sea_orm::ActiveEnum;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;

/// 商户角色列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "role_code": "MERCHANT_ADMIN",
    "role_name": "商户管理员",
    "role_type": 1,
    "role_type_desc": "管理员角色",
    "is_default": true,
    "data_scope": 1,
    "data_scope_desc": "商户全部数据",
    "role_description": "商户管理员，拥有商户内所有权限",
    "status": 1,
    "status_desc": "启用",
    "user_count": 3,
    "permission_count": 25,
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00"
}))]
pub struct MerchantRoleListResponse {
    /// 角色ID
    pub id: Uuid,

    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: Option<String>,

    /// 角色编码
    pub role_code: Option<String>,

    /// 角色名称
    pub role_name: String,

    /// 角色类型
    pub role_type: i32,

    /// 角色类型描述
    pub role_type_desc: String,

    /// 是否默认角色
    pub is_default: bool,

    /// 数据范围
    pub data_scope: i32,

    /// 数据范围描述
    pub data_scope_desc: String,

    /// 角色描述
    pub role_description: Option<String>,

    /// 角色状态
    pub status: i32,

    /// 角色状态描述
    pub status_desc: String,

    /// 关联用户数量
    #[serde(skip_serializing_if = "Option::is_none")]
    pub user_count: Option<i32>,

    /// 关联权限数量
    #[serde(skip_serializing_if = "Option::is_none")]
    pub permission_count: Option<i32>,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,
}

/// 商户角色详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "role_code": "MERCHANT_ADMIN",
    "role_name": "商户管理员",
    "role_type": 1,
    "role_type_desc": "管理员角色",
    "is_default": true,
    "data_scope": 1,
    "data_scope_desc": "商户全部数据",
    "role_description": "商户管理员，拥有商户内所有权限",
    "status": 1,
    "status_desc": "启用",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00",
    "created_by": "550e8400-e29b-41d4-a716-************",
    "updated_by": "550e8400-e29b-41d4-a716-************",
    "created_by_name": "张三",
    "updated_by_name": "李四",
    "remark": "系统默认管理员角色"
}))]
pub struct MerchantRoleDetailResponse {
    /// 角色ID
    pub id: Uuid,

    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: Option<String>,

    /// 角色编码
    pub role_code: Option<String>,

    /// 角色名称
    pub role_name: String,

    /// 角色类型
    pub role_type: i32,

    /// 角色类型描述
    pub role_type_desc: String,

    /// 是否默认角色
    pub is_default: bool,

    /// 数据范围
    pub data_scope: i32,

    /// 数据范围描述
    pub data_scope_desc: String,

    /// 角色描述
    pub role_description: Option<String>,

    /// 角色状态
    pub status: i32,

    /// 角色状态描述
    pub status_desc: String,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,

    /// 创建人ID
    pub created_by: Option<Uuid>,

    /// 更新人ID
    pub updated_by: Option<Uuid>,

    /// 创建人姓名
    pub created_by_name: Option<String>,

    /// 更新人姓名
    pub updated_by_name: Option<String>,

    /// 备注
    pub remark: Option<String>,
}

/// 商户角色选择项
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "role_code": "MERCHANT_ADMIN",
    "role_name": "商户管理员",
    "role_type": 1,
    "role_type_desc": "管理员角色",
    "status": 1
}))]
pub struct MerchantRoleSelectItem {
    /// 角色ID
    pub id: Uuid,

    /// 角色编码
    pub role_code: Option<String>,

    /// 角色名称
    pub role_name: String,

    /// 角色类型
    pub role_type: i32,

    /// 角色类型描述
    pub role_type_desc: String,

    /// 角色状态
    pub status: i32,
}

/// 商户角色基础信息响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "role_type": 1,
    "role_type_desc": "管理员角色",
    "is_default": true,
    "status": 1,
    "status_desc": "启用"
}))]
pub struct MerchantRoleBasicResponse {
    /// 角色ID
    pub id: Uuid,

    /// 角色名称
    pub role_name: String,

    /// 角色类型
    pub role_type: i32,

    /// 角色类型描述
    pub role_type_desc: String,

    /// 是否默认角色
    pub is_default: bool,

    /// 角色状态
    pub status: i32,

    /// 角色状态描述
    pub status_desc: String,
}

/// 商户角色统计响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "total_roles": 5,
    "admin_roles": 1,
    "custom_roles": 4,
    "active_roles": 4,
    "disabled_roles": 1
}))]
pub struct MerchantRoleStatsResponse {
    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: String,

    /// 角色总数
    pub total_roles: i32,

    /// 管理员角色数
    pub admin_roles: i32,

    /// 自定义角色数
    pub custom_roles: i32,

    /// 启用角色数
    pub active_roles: i32,

    /// 禁用角色数
    pub disabled_roles: i32,
}

// ============================================================================
// 实体类转 VO - 转换方法
// ============================================================================

use crate::domain::business::merchants::entities::merchant_roles::{
    MerchantRoleDataScope, MerchantRoleType, Model as MerchantRoleModel,
};

impl From<MerchantRoleModel> for MerchantRoleListResponse {
    fn from(model: MerchantRoleModel) -> Self {
        Self {
            id: model.id,
            merchant_id: model.merchant_id,
            merchant_name: None, // 需要从关联查询中获取
            role_code: model.role_code,
            role_name: model.role_name,
            role_type: model.role_type.to_value(),
            role_type_desc: get_role_type_desc(model.role_type.to_value()),
            is_default: model.is_default,
            data_scope: model.data_scope.to_value(),
            data_scope_desc: get_data_scope_desc(model.data_scope.to_value()),
            role_description: model.role_description,
            status: model.status.to_value(),
            status_desc: get_role_status_desc(model.status.to_value()),
            user_count: None,
            permission_count: None,
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            updated_date: DateTimeUtils::format_datetime(
                &model.updated_date.with_timezone(&chrono::Local),
            ),
        }
    }
}

impl From<MerchantRoleModel> for MerchantRoleDetailResponse {
    fn from(model: MerchantRoleModel) -> Self {
        Self {
            id: model.id,
            merchant_id: model.merchant_id,
            merchant_name: None, // 需要从关联查询中获取
            role_code: model.role_code,
            role_name: model.role_name,
            role_type: model.role_type.to_value(),
            role_type_desc: get_role_type_desc(model.role_type.to_value()),
            is_default: model.is_default,
            data_scope: model.data_scope.to_value(),
            data_scope_desc: get_data_scope_desc(model.data_scope.to_value()),
            role_description: model.role_description,
            status: model.status.to_value(),
            status_desc: get_role_status_desc(model.status.to_value()),
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            updated_date: DateTimeUtils::format_datetime(
                &model.updated_date.with_timezone(&chrono::Local),
            ),
            created_by: model.created_by,
            updated_by: model.updated_by,
            created_by_name: None, // 需要从关联查询中获取
            updated_by_name: None, // 需要从关联查询中获取
            remark: model.remark,
        }
    }
}

impl From<MerchantRoleModel> for MerchantRoleSelectItem {
    fn from(model: MerchantRoleModel) -> Self {
        Self {
            id: model.id,
            role_code: model.role_code,
            role_name: model.role_name,
            role_type: model.role_type.to_value(),
            role_type_desc: get_role_type_desc(model.role_type.to_value()),
            status: model.status.to_value(),
        }
    }
}

impl From<MerchantRoleModel> for MerchantRoleBasicResponse {
    fn from(model: MerchantRoleModel) -> Self {
        Self {
            id: model.id,
            role_name: model.role_name,
            role_type: model.role_type.to_value(),
            role_type_desc: get_role_type_desc(model.role_type.to_value()),
            is_default: model.is_default,
            status: model.status.to_value(),
            status_desc: get_role_status_desc(model.status.to_value()),
        }
    }
}

// ============================================================================
// 便捷构造函数
// ============================================================================

impl MerchantRoleListResponse {
    /// 设置商户信息
    pub fn with_merchant_name(mut self, merchant_name: Option<String>) -> Self {
        self.merchant_name = merchant_name;
        self
    }

    /// 设置统计信息
    pub fn with_stats(mut self, user_count: Option<i32>, permission_count: Option<i32>) -> Self {
        self.user_count = user_count;
        self.permission_count = permission_count;
        self
    }
}

impl MerchantRoleDetailResponse {
    /// 设置商户信息
    pub fn with_merchant_name(mut self, merchant_name: Option<String>) -> Self {
        self.merchant_name = merchant_name;
        self
    }

    /// 设置用户信息
    pub fn with_user_info(
        mut self,
        created_by_name: Option<String>,
        updated_by_name: Option<String>,
    ) -> Self {
        self.created_by_name = created_by_name;
        self.updated_by_name = updated_by_name;
        self
    }
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 获取角色类型描述
fn get_role_type_desc(role_type: i32) -> String {
    match MerchantRoleType::try_from_value(&role_type).unwrap_or(MerchantRoleType::MerchantCustom) {
        MerchantRoleType::SystemCustom => "系统定义".to_string(),
        MerchantRoleType::MerchantCustom => "商户定义".to_string(),
    }
}

/// 获取数据范围描述
fn get_data_scope_desc(data_scope: i32) -> String {
    match MerchantRoleDataScope::try_from_value(&data_scope)
        .unwrap_or(MerchantRoleDataScope::PersonalData)
    {
        MerchantRoleDataScope::AllData => "商户全部数据".to_string(),
        MerchantRoleDataScope::PersonalData => "个人数据".to_string(),
    }
}

/// 获取角色状态描述
fn get_role_status_desc(status: i32) -> String {
    match status {
        0 => "禁用".to_string(),
        1 => "启用".to_string(),
        _ => "未知".to_string(),
    }
}

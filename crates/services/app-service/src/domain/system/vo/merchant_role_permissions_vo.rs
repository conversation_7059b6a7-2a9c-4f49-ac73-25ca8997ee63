use crate::utils::datetime::DateTimeUtils;
use bon::Builder;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;

/// 商户角色权限列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "role_type": 1,
    "role_type_desc": "管理员角色",
    "authorized_permission_id": "550e8400-e29b-41d4-a716-************",
    "permission_template_id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "permission_type": 1,
    "permission_type_desc": "目录",
    "created_date": "2023-01-01 10:00:00",
    "assigned_by_name": "张三"
}))]
pub struct MerchantRolePermissionListResponse {
    /// 分配记录ID
    pub id: Uuid,

    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: Option<String>,

    /// 角色ID
    pub role_id: Uuid,

    /// 角色名称
    pub role_name: Option<String>,

    /// 角色类型
    pub role_type: Option<i32>,

    /// 角色类型描述
    pub role_type_desc: String,

    /// 商户授权权限ID
    pub authorized_permission_id: Uuid,

    /// 系统权限模板ID
    pub permission_template_id: Option<Uuid>,

    /// 权限名称
    pub permission_name: Option<String>,

    /// 权限编码
    pub permission_code: Option<String>,

    /// 权限类型
    pub permission_type: Option<i32>,

    /// 权限类型描述
    pub permission_type_desc: String,

    /// 分配时间
    pub created_date: String,

    /// 分配人姓名
    pub assigned_by_name: Option<String>,
}

/// 商户角色权限详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "role_code": "MERCHANT_ADMIN",
    "role_type": 1,
    "role_type_desc": "管理员角色",
    "authorized_permission_id": "550e8400-e29b-41d4-a716-************",
    "permission_template_id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "permission_type": 1,
    "permission_type_desc": "目录",
    "permission_description": "订单查看权限",
    "permission_path": "/order",
    "permission_component": "OrderView",
    "created_date": "2023-01-01 10:00:00",
    "created_by": "550e8400-e29b-41d4-a716-************",
    "assigned_by_name": "张三",
    "remark": "商户管理员默认权限"
}))]
pub struct MerchantRolePermissionDetailResponse {
    /// 分配记录ID
    pub id: Uuid,

    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: Option<String>,

    /// 角色ID
    pub role_id: Uuid,

    /// 角色名称
    pub role_name: Option<String>,

    /// 角色编码
    pub role_code: Option<String>,

    /// 角色类型
    pub role_type: Option<i32>,

    /// 角色类型描述
    pub role_type_desc: String,

    /// 商户授权权限ID
    pub authorized_permission_id: Uuid,

    /// 系统权限模板ID
    pub permission_template_id: Option<Uuid>,

    /// 权限名称
    pub permission_name: Option<String>,

    /// 权限编码
    pub permission_code: Option<String>,

    /// 权限类型
    pub permission_type: Option<i32>,

    /// 权限类型描述
    pub permission_type_desc: String,

    /// 权限描述
    pub permission_description: Option<String>,

    /// 权限路径
    pub permission_path: Option<String>,

    /// 权限组件
    pub permission_component: Option<String>,

    /// 分配时间
    pub created_date: String,

    /// 创建人ID
    pub created_by: Option<Uuid>,

    /// 分配人姓名
    pub assigned_by_name: Option<String>,

    /// 备注
    pub remark: Option<String>,
}

/// 角色权限分配信息响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "role_type": 1,
    "role_type_desc": "管理员角色",
    "permissions": [
        {
            "permission_id": "550e8400-e29b-41d4-a716-************",
            "permission_name": "订单管理",
            "permission_code": "order:view",
            "permission_type": 1,
            "permission_type_desc": "目录"
        }
    ]
}))]
pub struct RolePermissionAssignmentResponse {
    /// 角色ID
    pub role_id: Uuid,

    /// 角色名称
    pub role_name: String,

    /// 角色类型
    pub role_type: i32,

    /// 角色类型描述
    pub role_type_desc: String,

    /// 分配的权限列表
    pub permissions: Vec<RolePermissionInfo>,
}

/// 角色权限信息
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
pub struct RolePermissionInfo {
    /// 权限ID（授权权限ID）
    pub permission_id: Uuid,

    /// 权限名称
    pub permission_name: String,

    /// 权限编码
    pub permission_code: String,

    /// 权限类型
    pub permission_type: i32,

    /// 权限类型描述
    pub permission_type_desc: String,
}

/// 权限角色分配信息响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "permission_id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "permission_type": 1,
    "permission_type_desc": "目录",
    "roles": [
        {
            "role_id": "550e8400-e29b-41d4-a716-************",
            "role_name": "商户管理员",
            "role_type": 1,
            "role_type_desc": "管理员角色"
        }
    ]
}))]
pub struct PermissionRoleAssignmentResponse {
    /// 权限ID（授权权限ID）
    pub permission_id: Uuid,

    /// 权限名称
    pub permission_name: String,

    /// 权限编码
    pub permission_code: String,

    /// 权限类型
    pub permission_type: i32,

    /// 权限类型描述
    pub permission_type_desc: String,

    /// 分配的角色列表
    pub roles: Vec<PermissionRoleInfo>,
}

/// 权限角色信息
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
pub struct PermissionRoleInfo {
    /// 角色ID
    pub role_id: Uuid,

    /// 角色名称
    pub role_name: String,

    /// 角色类型
    pub role_type: i32,

    /// 角色类型描述
    pub role_type_desc: String,
}

/// 商户角色权限树形结构响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "permission_tree": [
        {
            "permission_id": "550e8400-e29b-41d4-a716-************",
            "permission_name": "订单管理",
            "permission_code": "order",
            "permission_type": 1,
            "has_permission": true,
            "children": [
                {
                    "permission_id": "550e8400-e29b-41d4-a716-************",
                    "permission_name": "订单查看",
                    "permission_code": "order:view",
                    "permission_type": 2,
                    "has_permission": true,
                    "children": []
                }
            ]
        }
    ]
}))]
pub struct RolePermissionTreeResponse {
    /// 角色ID
    pub role_id: Uuid,

    /// 角色名称
    pub role_name: String,

    /// 权限树
    pub permission_tree: Vec<PermissionTreeNode>,
}

/// 权限树节点
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
pub struct PermissionTreeNode {
    /// 权限ID（授权权限ID）
    pub permission_id: Uuid,

    /// 权限名称
    pub permission_name: String,

    /// 权限编码
    pub permission_code: String,

    /// 权限类型
    pub permission_type: i32,

    /// 是否拥有该权限
    pub has_permission: bool,

    /// 子权限列表
    pub children: Vec<PermissionTreeNode>,
}

/// 商户角色权限统计响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "total_authorized_permissions": 50,
    "assigned_permissions": 25,
    "unassigned_permissions": 25,
    "by_permission_type": [
        {
            "permission_type": 1,
            "permission_type_desc": "目录",
            "permission_count": 10
        },
        {
            "permission_type": 2,
            "permission_type_desc": "菜单",
            "permission_count": 15
        },
        {
            "permission_type": 3,
            "permission_type_desc": "按钮",
            "permission_count": 25
        }
    ]
}))]
pub struct RolePermissionStatsResponse {
    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: String,

    /// 角色ID
    pub role_id: Uuid,

    /// 角色名称
    pub role_name: String,

    /// 总授权权限数
    pub total_authorized_permissions: i32,

    /// 已分配权限数
    pub assigned_permissions: i32,

    /// 未分配权限数
    pub unassigned_permissions: i32,

    /// 按权限类型统计
    pub by_permission_type: Vec<PermissionTypeStats>,
}

/// 权限类型统计
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
pub struct PermissionTypeStats {
    /// 权限类型
    pub permission_type: i32,

    /// 权限类型描述
    pub permission_type_desc: String,

    /// 权限数量
    pub permission_count: i32,
}

// ============================================================================
// 实体类转 VO - 转换方法
// ============================================================================

use crate::domain::business::merchants::entities::merchant_role_permissions::Model as MerchantRolePermissionModel;

impl From<MerchantRolePermissionModel> for MerchantRolePermissionListResponse {
    fn from(model: MerchantRolePermissionModel) -> Self {
        Self {
            id: model.id,
            merchant_id: model.merchant_id,
            merchant_name: None, // 需要从关联查询中获取
            role_id: model.role_id,
            role_name: None, // 需要从关联查询中获取
            role_type: None, // 需要从关联查询中获取
            role_type_desc: "未知".to_string(), // 需要从关联查询中获取
            authorized_permission_id: model.authorized_permission_id,
            permission_template_id: None, // 需要从关联查询中获取
            permission_name: None, // 需要从关联查询中获取
            permission_code: None, // 需要从关联查询中获取
            permission_type: None, // 需要从关联查询中获取
            permission_type_desc: "未知".to_string(), // 需要从关联查询中获取
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            assigned_by_name: None, // 需要从关联查询中获取
        }
    }
}

impl From<MerchantRolePermissionModel> for MerchantRolePermissionDetailResponse {
    fn from(model: MerchantRolePermissionModel) -> Self {
        Self {
            id: model.id,
            merchant_id: model.merchant_id,
            merchant_name: None, // 需要从关联查询中获取
            role_id: model.role_id,
            role_name: None, // 需要从关联查询中获取
            role_code: None, // 需要从关联查询中获取
            role_type: None, // 需要从关联查询中获取
            role_type_desc: "未知".to_string(), // 需要从关联查询中获取
            authorized_permission_id: model.authorized_permission_id,
            permission_template_id: None, // 需要从关联查询中获取
            permission_name: None, // 需要从关联查询中获取
            permission_code: None, // 需要从关联查询中获取
            permission_type: None, // 需要从关联查询中获取
            permission_type_desc: "未知".to_string(), // 需要从关联查询中获取
            permission_description: None, // 需要从关联查询中获取
            permission_path: None, // 需要从关联查询中获取
            permission_component: None, // 需要从关联查询中获取
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            created_by: model.created_by,
            assigned_by_name: None, // 需要从关联查询中获取
            remark: model.remark,
        }
    }
}

// ============================================================================
// 便捷构造函数
// ============================================================================

impl MerchantRolePermissionListResponse {
    /// 设置商户信息
    pub fn with_merchant_info(mut self, merchant_name: Option<String>) -> Self {
        self.merchant_name = merchant_name;
        self
    }

    /// 设置角色信息
    pub fn with_role_info(
        mut self,
        role_name: Option<String>,
        role_type: Option<i32>,
    ) -> Self {
        self.role_name = role_name;
        self.role_type = role_type;
        self.role_type_desc = get_role_type_desc(role_type);
        self
    }

    /// 设置权限信息
    pub fn with_permission_info(
        mut self,
        permission_template_id: Option<Uuid>,
        permission_name: Option<String>,
        permission_code: Option<String>,
        permission_type: Option<i32>,
    ) -> Self {
        self.permission_template_id = permission_template_id;
        self.permission_name = permission_name;
        self.permission_code = permission_code;
        self.permission_type = permission_type;
        self.permission_type_desc = get_permission_type_desc(permission_type);
        self
    }

    /// 设置分配人信息
    pub fn with_assigned_by_name(mut self, assigned_by_name: Option<String>) -> Self {
        self.assigned_by_name = assigned_by_name;
        self
    }
}

impl MerchantRolePermissionDetailResponse {
    /// 设置商户信息
    pub fn with_merchant_info(mut self, merchant_name: Option<String>) -> Self {
        self.merchant_name = merchant_name;
        self
    }

    /// 设置角色信息
    pub fn with_role_info(
        mut self,
        role_name: Option<String>,
        role_code: Option<String>,
        role_type: Option<i32>,
    ) -> Self {
        self.role_name = role_name;
        self.role_code = role_code;
        self.role_type = role_type;
        self.role_type_desc = get_role_type_desc(role_type);
        self
    }

    /// 设置权限信息
    pub fn with_permission_info(
        mut self,
        permission_template_id: Option<Uuid>,
        permission_name: Option<String>,
        permission_code: Option<String>,
        permission_type: Option<i32>,
        permission_description: Option<String>,
        permission_path: Option<String>,
        permission_component: Option<String>,
    ) -> Self {
        self.permission_template_id = permission_template_id;
        self.permission_name = permission_name;
        self.permission_code = permission_code;
        self.permission_type = permission_type;
        self.permission_type_desc = get_permission_type_desc(permission_type);
        self.permission_description = permission_description;
        self.permission_path = permission_path;
        self.permission_component = permission_component;
        self
    }

    /// 设置分配人信息
    pub fn with_assigned_by_name(mut self, assigned_by_name: Option<String>) -> Self {
        self.assigned_by_name = assigned_by_name;
        self
    }
}

impl PermissionTreeNode {
    /// 设置子权限列表
    pub fn with_children(mut self, children: Vec<PermissionTreeNode>) -> Self {
        self.children = children;
        self
    }
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 获取角色类型描述
pub fn get_role_type_desc(role_type: Option<i32>) -> String {
    match role_type {
        Some(1) => "管理员角色".to_string(),
        Some(2) => "自定义角色".to_string(),
        _ => "未知".to_string(),
    }
}

/// 获取权限类型描述
pub fn get_permission_type_desc(permission_type: Option<i32>) -> String {
    match permission_type {
        Some(1) => "目录".to_string(),
        Some(2) => "菜单".to_string(),
        Some(3) => "按钮".to_string(),
        _ => "未知".to_string(),
    }
}

// ============================================================================
// 新增树形结构 VO 定义
// ============================================================================

/// 角色权限树形节点（详细信息）
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "authorized_permission_id": "550e8400-e29b-41d4-a716-************",
    "permission_template_id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "parent_id": null,
    "order_num": 1,
    "path": "/order",
    "component": "order/index",
    "permission_type": 1,
    "permission_type_desc": "目录",
    "visible": 0,
    "visible_desc": "显示",
    "icon": "order",
    "description": "订单管理模块",
    "has_permission": true,
    "status": 0,
    "status_desc": "正常",
    "children": []
}))]
pub struct RolePermissionTreeNode {
    /// 权限模板ID
    pub id: Uuid,
    
    /// 商户授权权限ID（如果已授权给商户）
    pub authorized_permission_id: Uuid,
    
    /// 系统权限模板ID
    pub permission_template_id: Uuid,
    
    /// 权限名称
    pub permission_name: String,
    
    /// 权限编码
    pub permission_code: String,
    
    /// 父权限ID
    pub parent_id: Uuid,
    
    /// 显示顺序
    pub order_num: i32,
    
    /// 路由地址
    pub path: String,
    
    /// 组件路径
    pub component: String,
    
    /// 权限类型（1-目录，2-菜单，3-按钮）
    pub permission_type: i32,
    
    /// 权限类型描述
    pub permission_type_desc: String,
    
    /// 菜单状态（0-显示，1-隐藏）
    pub visible: i32,
    
    /// 菜单状态描述
    pub visible_desc: String,
    
    /// 权限图标
    pub icon: String,
    
    /// 权限描述
    pub description: String,
    
    /// 是否拥有该权限
    pub has_permission: bool,
    
    /// 授权状态（0-正常，1-停用）
    pub status: i32,
    
    /// 授权状态描述
    pub status_desc: String,
    
    /// 子权限列表
    #[schema(no_recursion)]
    pub children: Vec<RolePermissionTreeNode>,
}

/// 角色权限树形结构响应（详细版本）
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "role_type": 1,
    "role_type_desc": "管理员角色",
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "total_permissions": 50,
    "assigned_permissions": 25,
    "permission_tree": [
        {
            "id": "550e8400-e29b-41d4-a716-************",
            "permission_name": "订单管理",
            "permission_code": "order",
            "permission_type": 1,
            "has_permission": true,
            "children": []
        }
    ]
}))]
pub struct RolePermissionTreeDetailResponse {
    /// 角色ID
    pub role_id: Uuid,
    
    /// 角色名称
    pub role_name: String,
    
    /// 角色类型
    pub role_type: i32,
    
    /// 角色类型描述
    pub role_type_desc: String,
    
    /// 商户ID
    pub merchant_id: i64,
    
    /// 商户名称
    pub merchant_name: Option<String>,
    
    /// 总权限数
    pub total_permissions: i32,
    
    /// 已分配权限数
    pub assigned_permissions: i32,
    
    /// 权限树形结构
    #[schema(no_recursion)]
    pub permission_tree: Vec<RolePermissionTreeNode>,
}

/// 可分配权限树形节点
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "authorized_permission_id": "550e8400-e29b-41d4-a716-************",
    "permission_template_id": "550e8400-e29b-41d4-a716-************",
    "permission_name": "订单管理",
    "permission_code": "order:view",
    "parent_id": null,
    "order_num": 1,
    "path": "/order",
    "component": "order/index",
    "permission_type": 1,
    "permission_type_desc": "目录",
    "visible": 0,
    "visible_desc": "显示",
    "icon": "order",
    "description": "订单管理模块",
    "is_authorized": true,
    "is_assigned": false,
    "authorized_status": 0,
    "authorized_status_desc": "正常",
    "authorized_date": "2023-01-01 10:00:00",
    "children": []
}))]
pub struct AvailablePermissionTreeNode {
    /// 权限模板ID
    pub id: Uuid,
    
    /// 商户授权权限ID（如果已授权给商户）
    pub authorized_permission_id: Option<Uuid>,
    
    /// 系统权限模板ID
    pub permission_template_id: Uuid,
    
    /// 权限名称
    pub permission_name: String,
    
    /// 权限编码
    pub permission_code: String,
    
    /// 父权限ID
    pub parent_id: Option<Uuid>,
    
    /// 显示顺序
    pub order_num: i32,
    
    /// 路由地址
    pub path: Option<String>,
    
    /// 组件路径
    pub component: Option<String>,
    
    /// 权限类型（1-目录，2-菜单，3-按钮）
    pub permission_type: i32,
    
    /// 权限类型描述
    pub permission_type_desc: String,
    
    /// 菜单状态（0-显示，1-隐藏）
    pub visible: i32,
    
    /// 菜单状态描述
    pub visible_desc: String,
    
    /// 权限图标
    pub icon: Option<String>,
    
    /// 权限描述
    pub description: Option<String>,
    
    /// 是否已授权给商户
    pub is_authorized: bool,
    
    /// 是否已分配给角色
    pub is_assigned: bool,
    
    /// 授权状态（0-正常，1-停用）
    pub authorized_status: Option<i32>,
    
    /// 授权状态描述
    pub authorized_status_desc: Option<String>,
    
    /// 授权时间
    pub authorized_date: Option<String>,
    
    /// 子权限列表
    #[schema(no_recursion)]
    pub children: Vec<AvailablePermissionTreeNode>,
}

/// 可分配权限树形结构响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "total_authorized": 50,
    "already_assigned": 25,
    "available_count": 25,
    "permission_tree": []
}))]
pub struct AvailablePermissionTreeResponse {
    /// 角色ID
    pub role_id: Uuid,
    
    /// 角色名称
    pub role_name: String,
    
    /// 商户ID
    pub merchant_id: i64,
    
    /// 商户名称
    pub merchant_name: Option<String>,
    
    /// 商户总授权权限数
    pub total_authorized: i32,
    
    /// 已分配给角色的权限数
    pub already_assigned: i32,
    
    /// 可分配权限数
    pub available_count: i32,
    
    /// 可分配权限树形结构
    #[schema(no_recursion)]
    pub permission_tree: Vec<AvailablePermissionTreeNode>,
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 获取菜单状态描述
pub fn get_visible_desc(visible: i32) -> String {
    match visible {
        0 => "显示".to_string(),
        1 => "隐藏".to_string(),
        _ => "未知".to_string(),
    }
}

/// 获取授权状态描述
pub fn get_authorized_status_desc(status: Option<i32>) -> Option<String> {
    status.map(|s| match s {
        0 => "正常".to_string(),
        1 => "停用".to_string(),
        _ => "未知".to_string(),
    })
}

// ============================================================================
// RolePermissionTreeNode 树形结构构建方法
// ============================================================================

impl RolePermissionTreeNode {
    /// 构建角色权限树形结构
    /// 
    /// # 参数
    /// - `permission_templates`: 权限模板列表
    /// - `auth_map`: 授权权限映射 (permission_template_id -> authorized_permission)
    /// - `assigned_permission_ids`: 已分配给角色的权限ID列表
    /// 
    /// # 返回
    /// 返回构建好的树形结构根节点列表
    pub fn build_permission_tree(
        permission_templates: Vec<crate::domain::business::merchants::entities::system_permission_templates::Model>,
        auth_map: std::collections::HashMap<uuid::Uuid, crate::domain::business::merchants::entities::merchant_authorized_permissions::Model>,
        assigned_permission_ids: Vec<uuid::Uuid>,
    ) -> Vec<Self> {
        let mut node_map: std::collections::HashMap<uuid::Uuid, Self> = std::collections::HashMap::new();

        // 第一遍：创建所有节点
        for template in permission_templates {
            if let Some(auth_permission) = auth_map.get(&template.id) {
                let has_permission = assigned_permission_ids.contains(&auth_permission.id);

                let node = Self::builder()
                    .id(template.id)
                    .authorized_permission_id(auth_permission.id)
                    .permission_template_id(template.id)
                    .permission_name(template.permission_name.clone())
                    .permission_code(template.permission_code.clone())
                    .parent_id(template.parent_id.unwrap_or(uuid::Uuid::nil()))
                    .order_num(template.order_num.unwrap_or(0))
                    .path(template.path.clone().unwrap_or_default())
                    .component(template.component.clone().unwrap_or_default())
                    .permission_type(template.permission_type.unwrap_or(2))
                    .permission_type_desc(get_permission_type_desc(Some(
                        template.permission_type.unwrap_or(2),
                    )))
                    .visible(template.visible.unwrap_or(0))
                    .visible_desc(get_visible_desc(template.visible.unwrap_or(0)))
                    .icon(template.icon.clone().unwrap_or_default())
                    .description(template.description.clone().unwrap_or_default())
                    .has_permission(has_permission)
                    .status(auth_permission.status as i32)
                    .status_desc(
                        get_authorized_status_desc(Some(auth_permission.status as i32))
                            .unwrap_or_default()
                    )
                    .children(Vec::new())
                    .build();

                node_map.insert(template.id, node);
            }
        }

        // 第二遍：构建树形结构
        let mut root_nodes = Vec::new();
        let mut children_map: std::collections::HashMap<uuid::Uuid, Vec<Self>> = std::collections::HashMap::new();

        // 分离根节点和子节点
        for (_id, node) in node_map {
            if node.parent_id != uuid::Uuid::nil() {
                children_map
                    .entry(node.parent_id)
                    .or_insert_with(Vec::new)
                    .push(node);
            } else {
                root_nodes.push(node);
            }
        }

        // 递归构建树形结构
        fn build_tree_recursive(
            nodes: &mut Vec<RolePermissionTreeNode>,
            children_map: &mut std::collections::HashMap<uuid::Uuid, Vec<RolePermissionTreeNode>>,
        ) {
            for node in nodes {
                if let Some(mut children) = children_map.remove(&node.id) {
                    // 按order_num排序
                    children.sort_by(|a, b| {
                        a.order_num
                            .cmp(&b.order_num)
                            .then_with(|| a.permission_type.cmp(&b.permission_type))
                            .then_with(|| a.permission_name.cmp(&b.permission_name))
                    });
                    
                    build_tree_recursive(&mut children, children_map);
                    node.children = children;
                }
            }
        }

        build_tree_recursive(&mut root_nodes, &mut children_map);

        // 处理可能遗留的孤儿节点（父节点不存在的节点）
        for (_, mut orphan_nodes) in children_map {
            build_tree_recursive(&mut orphan_nodes, &mut std::collections::HashMap::new());
            root_nodes.extend(orphan_nodes);
        }

        // 对根节点进行排序
        root_nodes.sort_by(|a, b| {
            a.order_num
                .cmp(&b.order_num)
                .then_with(|| a.permission_type.cmp(&b.permission_type))
                .then_with(|| a.permission_name.cmp(&b.permission_name))
        });

        root_nodes
    }
}

// ============================================================================
// AvailablePermissionTreeNode 树形结构构建方法
// ============================================================================

impl AvailablePermissionTreeNode {
    /// 构建可分配权限树形结构
    /// 
    /// # 参数
    /// - `template_map`: 权限模板映射 (template_id -> template)
    /// - `auth_map`: 授权权限映射 (permission_template_id -> authorized_permission)
    /// - `assigned_permission_ids`: 已分配给角色的权限ID列表
    /// 
    /// # 返回
    /// 返回构建好的树形结构根节点列表
    pub fn build_available_permission_tree(
        template_map: std::collections::HashMap<uuid::Uuid, crate::domain::business::merchants::entities::system_permission_templates::Model>,
        auth_map: std::collections::HashMap<uuid::Uuid, crate::domain::business::merchants::entities::merchant_authorized_permissions::Model>,
        assigned_permission_ids: Vec<uuid::Uuid>,
    ) -> Vec<Self> {
        let mut node_map: std::collections::HashMap<uuid::Uuid, Self> = std::collections::HashMap::new();

        // 第一遍：创建所有节点
        for (template_id, template) in &template_map {
            if let Some(auth_permission) = auth_map.get(template_id) {
                let is_assigned = assigned_permission_ids.contains(&auth_permission.id);

                let node = Self::builder()
                    .id(template.id)
                    .authorized_permission_id(auth_permission.id)
                    .permission_template_id(template.id)
                    .permission_name(template.permission_name.clone())
                    .permission_code(template.permission_code.clone())
                    .parent_id(template.parent_id.unwrap_or_default())
                    .order_num(template.order_num.unwrap_or(0))
                    .path(template.path.clone().unwrap_or_default())
                    .component(template.component.clone().unwrap_or_default())
                    .permission_type(template.permission_type.unwrap_or(2))
                    .permission_type_desc(get_permission_type_desc(Some(
                        template.permission_type.unwrap_or(2),
                    )))
                    .visible(template.visible.unwrap_or(0))
                    .visible_desc(get_visible_desc(template.visible.unwrap_or(0)))
                    .icon(template.icon.clone().unwrap_or_default())
                    .description(template.description.clone().unwrap_or_default())
                    .is_authorized(true)
                    .is_assigned(is_assigned)
                    .authorized_status(auth_permission.status as i32)
                    .authorized_status_desc(
                        get_authorized_status_desc(Some(auth_permission.status as i32))
                            .unwrap_or_default(),
                    )
                    .authorized_date(crate::utils::datetime::DateTimeUtils::format_datetime(
                        &auth_permission
                            .authorized_date
                            .with_timezone(&chrono::Local),
                    ))
                    .children(Vec::new())
                    .build();

                node_map.insert(template.id, node);
            }
        }

        // 第二遍：构建树形结构
        let mut root_nodes = Vec::new();
        let mut children_map: std::collections::HashMap<uuid::Uuid, Vec<Self>> =
            std::collections::HashMap::new();

        // 分离根节点和子节点
        for (_id, node) in node_map {
            if node.parent_id.is_some() && node.parent_id.unwrap() != uuid::Uuid::nil() {
                children_map
                    .entry(node.parent_id.unwrap())
                    .or_insert_with(Vec::new)
                    .push(node);
            } else {
                root_nodes.push(node);
            }
        }

        // 递归构建树形结构
        fn build_tree_recursive_available(
            nodes: &mut Vec<AvailablePermissionTreeNode>,
            children_map: &mut std::collections::HashMap<uuid::Uuid, Vec<AvailablePermissionTreeNode>>,
        ) {
            for node in nodes {
                if let Some(mut children) = children_map.remove(&node.id) {
                    build_tree_recursive_available(&mut children, children_map);
                    node.children = children;
                }
            }
        }

        build_tree_recursive_available(&mut root_nodes, &mut children_map);

        // 处理可能遗留的孤儿节点（父节点不存在的节点）
        for (_, mut orphan_nodes) in children_map {
            build_tree_recursive_available(
                &mut orphan_nodes,
                &mut std::collections::HashMap::new(),
            );
            root_nodes.extend(orphan_nodes);
        }

        // 对根节点和所有子节点进行排序
        Self::sort_nodes(&mut root_nodes);

        root_nodes
    }

    /// 递归排序可用权限节点
    fn sort_nodes(nodes: &mut Vec<Self>) {
        nodes.sort_by(|a, b| {
            a.order_num
                .cmp(&b.order_num)
                .then_with(|| a.permission_type.cmp(&b.permission_type))
                .then_with(|| a.permission_name.cmp(&b.permission_name))
        });

        for node in nodes.iter_mut() {
            if !node.children.is_empty() {
                Self::sort_nodes(&mut node.children);
            }
        }
    }
} 
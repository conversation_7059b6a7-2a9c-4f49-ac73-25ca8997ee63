use crate::utils::datetime::DateTimeUtils;
use bon::Builder;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;

/// 商户用户角色关联列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "username": "z<PERSON><PERSON>",
    "real_name": "张三",
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "role_type": 1,
    "role_type_desc": "管理员角色",
    "commission_rate": "0.0200",
    "status": 1,
    "status_desc": "启用",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00"
}))]
pub struct MerchantUserRoleListResponse {
    /// 关联记录ID
    pub id: Uuid,

    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: Option<String>,

    /// 用户ID
    pub user_id: Uuid,

    /// 用户名
    pub username: Option<String>,

    /// 真实姓名
    pub real_name: Option<String>,

    /// 角色ID
    pub role_id: Uuid,

    /// 角色名称
    pub role_name: Option<String>,

    /// 角色类型
    pub role_type: Option<i32>,

    /// 角色类型描述
    pub role_type_desc: String,

    /// 状态
    pub status: i32,

    /// 状态描述
    pub status_desc: String,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,
}

/// 商户用户角色关联详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "username": "zhangsan",
    "real_name": "张三",
    "user_phone": "13800138000",
    "user_email": "<EMAIL>",
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "role_code": "MERCHANT_ADMIN",
    "role_type": 1,
    "role_type_desc": "管理员角色",
    "commission_rate": "0.0200",
    "status": 1,
    "status_desc": "启用",
    "created_date": "2023-01-01 10:00:00",
    "updated_date": "2023-01-01 10:00:00",
    "created_by": "550e8400-e29b-41d4-a716-************",
    "updated_by": "550e8400-e29b-41d4-a716-************",
    "created_by_name": "李四",
    "updated_by_name": "王五",
    "remark": "默认管理员角色分配"
}))]
pub struct MerchantUserRoleDetailResponse {
    /// 关联记录ID
    pub id: Uuid,

    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: Option<String>,

    /// 用户ID
    pub user_id: Uuid,

    /// 用户名
    pub username: Option<String>,

    /// 真实姓名
    pub real_name: Option<String>,

    /// 用户手机号
    pub user_phone: Option<String>,

    /// 用户邮箱
    pub user_email: Option<String>,

    /// 角色ID
    pub role_id: Uuid,

    /// 角色名称
    pub role_name: Option<String>,

    /// 角色编码
    pub role_code: Option<String>,

    /// 角色类型
    pub role_type: Option<i32>,

    /// 角色类型描述
    pub role_type_desc: String,

    /// 状态
    pub status: i32,

    /// 状态描述
    pub status_desc: String,

    /// 创建时间
    pub created_date: String,

    /// 更新时间
    pub updated_date: String,

    /// 创建人ID
    pub created_by: Option<Uuid>,

    /// 更新人ID
    pub updated_by: Option<Uuid>,

    /// 创建人姓名
    pub created_by_name: Option<String>,

    /// 更新人姓名
    pub updated_by_name: Option<String>,

    /// 备注
    pub remark: Option<String>,
}

/// 用户角色分配信息响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "user_id": "550e8400-e29b-41d4-a716-************",
    "username": "zhangsan",
    "real_name": "张三",
    "roles": [
        {
            "role_id": "550e8400-e29b-41d4-a716-************",
            "role_name": "商户管理员",
            "role_type": 1,
            "commission_rate": "0.0200",
            "status": 1
        }
    ]
}))]
pub struct UserRoleAssignmentResponse {
    /// 用户ID
    pub user_id: Uuid,

    /// 用户名
    pub username: String,

    /// 真实姓名
    pub real_name: String,

    /// 分配的角色列表
    pub roles: Vec<UserRoleInfo>,
}

/// 用户角色信息
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
pub struct UserRoleInfo {
    /// 角色ID
    pub role_id: Uuid,

    /// 角色名称
    pub role_name: String,

    /// 角色类型
    pub role_type: i32,

    /// 佣金比例
    pub commission_rate: Option<String>,

    /// 状态
    pub status: i32,
}

/// 角色用户分配信息响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "role_type": 1,
    "users": [
        {
            "user_id": "550e8400-e29b-41d4-a716-************",
            "username": "zhangsan",
            "real_name": "张三",
            "commission_rate": "0.0200",
            "status": 1
        }
    ]
}))]
pub struct RoleUserAssignmentResponse {
    /// 角色ID
    pub role_id: Uuid,

    /// 角色名称
    pub role_name: String,

    /// 角色类型
    pub role_type: i32,

    /// 分配的用户列表
    pub users: Vec<RoleUserInfo>,
}

/// 角色用户信息
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
pub struct RoleUserInfo {
    /// 用户ID
    pub user_id: Uuid,

    /// 用户名
    pub username: String,

    /// 真实姓名
    pub real_name: String,

    /// 佣金比例
    pub commission_rate: Option<String>,

    /// 状态
    pub status: i32,
}

/// 商户用户角色统计响应
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
#[schema(example = json!({
    "merchant_id": 1,
    "merchant_name": "张三餐厅",
    "total_assignments": 15,
    "active_assignments": 12,
    "disabled_assignments": 3,
    "by_role_type": [
        {
            "role_type": 1,
            "role_type_desc": "管理员角色",
            "assignment_count": 3
        },
        {
            "role_type": 2,
            "role_type_desc": "自定义角色",
            "assignment_count": 12
        }
    ]
}))]
pub struct MerchantUserRoleStatsResponse {
    /// 商户ID
    pub merchant_id: i64,

    /// 商户名称
    pub merchant_name: String,

    /// 角色分配总数
    pub total_assignments: i32,

    /// 有效分配数
    pub active_assignments: i32,

    /// 禁用分配数
    pub disabled_assignments: i32,

    /// 按角色类型统计
    pub by_role_type: Vec<RoleTypeStats>,
}

/// 角色类型统计
#[derive(Debug, Serialize, Deserialize, ToSchema, Builder)]
pub struct RoleTypeStats {
    /// 角色类型
    pub role_type: i32,

    /// 角色类型描述
    pub role_type_desc: String,

    /// 分配数量
    pub assignment_count: i32,
}

// ============================================================================
// 实体类转 VO - 转换方法
// ============================================================================

use crate::domain::business::merchants::entities::merchant_user_roles::Model as MerchantUserRoleModel;

impl From<MerchantUserRoleModel> for MerchantUserRoleListResponse {
    fn from(model: MerchantUserRoleModel) -> Self {
        Self {
            id: model.id,
            merchant_id: 0,       // 需要通过关联查询获取
            merchant_name: None,  // 需要从关联查询中获取
            user_id: Uuid::nil(), // 需要通过关联查询获取
            username: None,       // 需要从关联查询中获取
            real_name: None,      // 需要从关联查询中获取
            role_id: model.role_id,
            role_name: None,                    // 需要从关联查询中获取
            role_type: None,                    // 需要从关联查询中获取
            role_type_desc: "未知".to_string(), // 需要从关联查询中获取
            status: 1, // 默认启用，实际状态需要通过merchant_user_merchants表查询
            status_desc: "启用".to_string(), // 默认状态描述
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            updated_date: DateTimeUtils::format_datetime(
                &model.updated_date.with_timezone(&chrono::Local),
            ),
        }
    }
}

impl From<MerchantUserRoleModel> for MerchantUserRoleDetailResponse {
    fn from(model: MerchantUserRoleModel) -> Self {
        Self {
            id: model.id,
            merchant_id: 0,       // 需要通过关联查询获取
            merchant_name: None,  // 需要从关联查询中获取
            user_id: Uuid::nil(), // 需要通过关联查询获取
            username: None,       // 需要从关联查询中获取
            real_name: None,      // 需要从关联查询中获取
            user_phone: None,     // 需要从关联查询中获取
            user_email: None,     // 需要从关联查询中获取
            role_id: model.role_id,
            role_name: None,                    // 需要从关联查询中获取
            role_code: None,                    // 需要从关联查询中获取
            role_type: None,                    // 需要从关联查询中获取
            role_type_desc: "未知".to_string(), // 需要从关联查询中获取
            status: 1, // 默认启用，实际状态需要通过merchant_user_merchants表查询
            status_desc: "启用".to_string(), // 默认状态描述
            created_date: DateTimeUtils::format_datetime(
                &model.created_date.with_timezone(&chrono::Local),
            ),
            updated_date: DateTimeUtils::format_datetime(
                &model.updated_date.with_timezone(&chrono::Local),
            ),
            created_by: model.created_by,
            updated_by: model.updated_by,
            created_by_name: None, // 需要从关联查询中获取
            updated_by_name: None, // 需要从关联查询中获取
            remark: model.remark,
        }
    }
}

// ============================================================================
// 便捷构造函数
// ============================================================================

impl MerchantUserRoleListResponse {
    /// 设置商户信息
    pub fn with_merchant_info(mut self, merchant_name: Option<String>) -> Self {
        self.merchant_name = merchant_name;
        self
    }

    /// 设置用户信息
    pub fn with_user_info(mut self, username: Option<String>, real_name: Option<String>) -> Self {
        self.username = username;
        self.real_name = real_name;
        self
    }

    /// 设置角色信息
    pub fn with_role_info(mut self, role_name: Option<String>, role_type: Option<i32>) -> Self {
        self.role_name = role_name;
        self.role_type = role_type;
        self.role_type_desc = get_role_type_desc(role_type);
        self
    }
}

impl MerchantUserRoleDetailResponse {
    /// 设置商户信息
    pub fn with_merchant_info(mut self, merchant_name: Option<String>) -> Self {
        self.merchant_name = merchant_name;
        self
    }

    /// 设置用户信息
    pub fn with_user_info(
        mut self,
        username: Option<String>,
        real_name: Option<String>,
        user_phone: Option<String>,
        user_email: Option<String>,
    ) -> Self {
        self.username = username;
        self.real_name = real_name;
        self.user_phone = user_phone;
        self.user_email = user_email;
        self
    }

    /// 设置角色信息
    pub fn with_role_info(
        mut self,
        role_name: Option<String>,
        role_code: Option<String>,
        role_type: Option<i32>,
    ) -> Self {
        self.role_name = role_name;
        self.role_code = role_code;
        self.role_type = role_type;
        self.role_type_desc = get_role_type_desc(role_type);
        self
    }

    /// 设置操作人信息
    pub fn with_operator_info(
        mut self,
        created_by_name: Option<String>,
        updated_by_name: Option<String>,
    ) -> Self {
        self.created_by_name = created_by_name;
        self.updated_by_name = updated_by_name;
        self
    }
}

// ============================================================================
// 辅助函数
// ============================================================================

/// 获取分配状态描述
fn get_assignment_status_desc(status: i32) -> String {
    match status {
        0 => "禁用".to_string(),
        1 => "启用".to_string(),
        _ => "未知".to_string(),
    }
}

/// 获取角色类型描述
fn get_role_type_desc(role_type: Option<i32>) -> String {
    match role_type {
        Some(1) => "管理员角色".to_string(),
        Some(2) => "自定义角色".to_string(),
        _ => "未知".to_string(),
    }
}

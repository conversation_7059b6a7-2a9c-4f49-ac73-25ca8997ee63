//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.11

use sea_orm::entity::prelude::*;
use sea_orm::QueryOrder;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "system", table_name = "permissions")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    pub menu_name: String,
    pub parent_id: Option<Uuid>,
    pub order_num: Option<i32>,
    pub path: Option<String>,
    pub component: Option<String>,
    pub query: Option<String>,
    pub is_frame: Option<i32>,
    pub is_cache: Option<i32>,
    pub menu_type: Option<i32>,
    pub visible: Option<i32>,
    pub status: Option<i32>,
    pub perms: Option<String>,
    pub icon: Option<String>,
    pub created_date: DateTimeLocal,
    pub updated_date: DateTimeLocal,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::role_permissions::Entity")]
    RolePermissions,
    #[sea_orm(belongs_to = "Entity", from = "Column::ParentId", to = "Column::Id")]
    SelfRef,
}

impl ActiveModelBehavior for ActiveModel {}

impl Related<super::role_permissions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::RolePermissions.def()
    }
}

impl Related<super::roles::Entity> for Entity {
    fn to() -> RelationDef {
        super::role_permissions::Relation::Roles.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::role_permissions::Relation::Permissions.def().rev())
    }
}

/// 权限查询方法封装
impl Entity {
    /// 根据权限代码查询权限
    pub async fn find_by_code(
        db: &DatabaseConnection,
        perms: &str,
    ) -> Result<Option<Model>, DbErr> {
        Self::find().filter(Column::Perms.eq(perms)).one(db).await
    }

    /// 查询权限的所有角色
    pub async fn find_permission_roles(
        db: &DatabaseConnection,
        permission_id: Uuid,
    ) -> Result<Vec<super::roles::Model>, DbErr> {
        Self::find_by_id(permission_id)
            .find_with_related(super::roles::Entity)
            .all(db)
            .await
            .map(|results| results.into_iter().flat_map(|(_, roles)| roles).collect())
    }

    /// 查询拥有该权限的所有用户（通过角色）
    pub async fn find_permission_users(
        db: &DatabaseConnection,
        permission_id: Uuid,
    ) -> Result<Vec<super::users::Model>, DbErr> {
        use sea_orm::*;

        super::users::Entity::find()
            .join(JoinType::InnerJoin, super::users::Relation::UserRoles.def())
            .join(
                JoinType::InnerJoin,
                super::user_roles::Relation::Roles.def(),
            )
            .join(
                JoinType::InnerJoin,
                super::roles::Relation::RolePermissions.def(),
            )
            .filter(super::role_permissions::Column::PermissionId.eq(permission_id))
            .all(db)
            .await
    }

    /// 查询所有根权限（父级权限）
    pub async fn find_root_permissions(db: &DatabaseConnection) -> Result<Vec<Model>, DbErr> {
        Self::find()
            .filter(Column::ParentId.is_null())
            .order_by_asc(Column::OrderNum)
            .all(db)
            .await
    }

    /// 查询子权限
    pub async fn find_children_permissions(
        db: &DatabaseConnection,
        parent_id: Uuid,
    ) -> Result<Vec<Model>, DbErr> {
        Self::find()
            .filter(Column::ParentId.eq(parent_id))
            .order_by_asc(Column::OrderNum)
            .all(db)
            .await
    }

    /// 查询权限树（递归查询所有子权限）
    pub async fn find_permission_tree(
        db: &DatabaseConnection,
        parent_id: Option<Uuid>,
    ) -> Result<Vec<Model>, DbErr> {
        let mut query = Self::find();

        if let Some(parent_id) = parent_id {
            query = query.filter(Column::ParentId.eq(parent_id));
        } else {
            query = query.filter(Column::ParentId.is_null());
        }

        query.order_by_asc(Column::OrderNum).all(db).await
    }

    /// 根据菜单类型查询权限
    pub async fn find_by_menu_type(
        db: &DatabaseConnection,
        menu_type: i32,
    ) -> Result<Vec<Model>, DbErr> {
        Self::find()
            .filter(Column::MenuType.eq(menu_type))
            .order_by_asc(Column::OrderNum)
            .all(db)
            .await
    }

    /// 查询可见的权限
    pub async fn find_visible_permissions(db: &DatabaseConnection) -> Result<Vec<Model>, DbErr> {
        Self::find()
            .filter(Column::Visible.eq(0))
            .filter(Column::Status.eq(0))
            .order_by_asc(Column::OrderNum)
            .all(db)
            .await
    }

    /// 查询用户拥有的权限菜单
    pub async fn find_user_permissions(
        db: &DatabaseConnection,
        user_id: Uuid,
    ) -> Result<Vec<Model>, DbErr> {
        use super::user_roles::Entity as UserRoles;
        use sea_orm::*;

        // 先查询用户ID和角色ID
        let user_roles = UserRoles::find()
            .filter(super::user_roles::Column::UserId.eq(user_id))
            .all(db)
            .await?;

        let role_ids: Vec<Uuid> = user_roles.into_iter().map(|ur| ur.role_id).collect();

        if role_ids.is_empty() {
            return Ok(Vec::new());
        }

        // 查询角色拥有的权限
        let permissions = Self::find()
            .join(
                JoinType::InnerJoin,
                super::role_permissions::Relation::Permissions.def().rev(),
            )
            .filter(super::role_permissions::Column::RoleId.is_in(role_ids))
            .order_by_asc(Column::OrderNum)
            .all(db)
            .await?;

        Ok(permissions)
    }
}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.11

use crate::domain::system::vo::SysRoleSimpleResponse;
use sea_orm::entity::prelude::*;
use sea_orm::{Condition, JoinType, PaginatorTrait, QueryOrder, QuerySelect};
use serde::{Deserialize, Serialize};

#[derive(<PERSON>lone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "system", table_name = "users")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    #[sea_orm(unique)]
    pub username: String,
    pub password: String,
    pub real_name: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub avatar: Option<String>,
    pub gender: Option<i32>,
    pub status: i32,
    pub last_login_ip: Option<String>,
    pub last_login_date: Option<DateTimeLocal>,
    pub created_date: DateTimeLocal,
    pub updated_date: DateTimeLocal,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
    /// 微信OpenID - 微信小程序用户唯一标识，用于微信登录
    #[sea_orm(unique)]
    pub wechat_openid: Option<String>,
    /// 微信昵称 - 微信用户的昵称，用于展示绑定的微信账号信息
    pub wechat_nickname: Option<String>,
    /// 微信头像 - 微信用户的头像URL，用于展示绑定的微信账号头像
    #[sea_orm(column_type = "Text", nullable)]
    pub wechat_avatar: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::user_roles::Entity")]
    UserRoles,
}

impl ActiveModelBehavior for ActiveModel {}

/// 用户状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum UserStatus {
    #[sea_orm(num_value = 1)]
    Active = 1, // 启用
    #[sea_orm(num_value = 2)]
    Disabled = 2, // 禁用
    #[sea_orm(num_value = 3)]
    Locked = 3, // 锁定
}

/// 用户性别枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum Gender {
    #[sea_orm(num_value = 1)]
    Male = 1,
    #[sea_orm(num_value = 2)]
    Female = 2,
    #[sea_orm(num_value = 3)]
    Unknown = 3,
}

/// 用户查询方法封装
impl Entity {
    /// 根据用户名查询用户
    pub async fn find_by_username(
        db: &DatabaseConnection,
        username: &str,
    ) -> Result<Option<Model>, DbErr> {
        Self::find()
            .filter(Column::Username.eq(username))
            .one(db)
            .await
    }

    /// 根据邮箱查询用户
    pub async fn find_by_email(
        db: &DatabaseConnection,
        email: &str,
    ) -> Result<Option<Model>, DbErr> {
        Self::find().filter(Column::Email.eq(email)).one(db).await
    }

    /// 根据手机号查询用户
    pub async fn find_by_phone(
        db: &DatabaseConnection,
        phone: &str,
    ) -> Result<Option<Model>, DbErr> {
        Self::find().filter(Column::Phone.eq(phone)).one(db).await
    }

    /// 根据微信OpenID查询用户
    pub async fn find_by_wechat_openid(
        db: &DatabaseConnection,
        openid: &str,
    ) -> Result<Option<Model>, DbErr> {
        Self::find().filter(Column::WechatOpenid.eq(openid)).one(db).await
    }

    /// 查询所有活跃用户
    pub async fn find_active_users(db: &DatabaseConnection) -> Result<Vec<Model>, DbErr> {
        Self::find()
            .filter(Column::Status.eq(UserStatus::Active as i32))
            .order_by_asc(Column::Username)
            .all(db)
            .await
    }

    /// 根据状态查询用户
    pub async fn find_by_status(
        db: &DatabaseConnection,
        status: UserStatus,
    ) -> Result<Vec<Model>, DbErr> {
        Self::find()
            .filter(Column::Status.eq(status as i32))
            .order_by_desc(Column::CreatedDate)
            .all(db)
            .await
    }

    /// 检查用户名是否存在
    pub async fn username_exists(db: &DatabaseConnection, username: &str) -> Result<bool, DbErr> {
        let count = Self::find()
            .filter(Column::Username.eq(username))
            .count(db)
            .await?;
        Ok(count > 0)
    }

    /// 检查邮箱是否存在
    pub async fn email_exists(db: &DatabaseConnection, email: &str) -> Result<bool, DbErr> {
        let count = Self::find()
            .filter(Column::Email.eq(email))
            .count(db)
            .await?;
        Ok(count > 0)
    }

    /// 用户认证（根据用户名查找用户用于密码验证）
    pub async fn authenticate_user(
        db: &DatabaseConnection,
        username: &str,
    ) -> Result<Option<Model>, DbErr> {
        Self::find()
            .filter(
                Condition::all()
                    .add(Column::Username.eq(username))
                    .add(Column::Status.eq(UserStatus::Active as i32)),
            )
            .one(db)
            .await
    }

    /// 分页查询用户
    pub async fn find_users_paginated(
        db: &DatabaseConnection,
        page: u64,
        page_size: u64,
        status: Option<UserStatus>,
    ) -> Result<Vec<Model>, DbErr> {
        let mut query = Self::find();

        if let Some(status) = status {
            query = query.filter(Column::Status.eq(status as i32));
        }

        query
            .order_by_asc(Column::Username)
            .paginate(db, page_size)
            .fetch_page(page)
            .await
    }

    /// 搜索用户（根据用户名、真实姓名、邮箱模糊搜索）
    pub async fn search_users(
        db: &DatabaseConnection,
        keyword: &str,
        limit: Option<u64>,
    ) -> Result<Vec<Model>, DbErr> {
        let mut query = Self::find()
            .filter(
                Condition::any()
                    .add(Column::Username.contains(keyword))
                    .add(Column::RealName.contains(keyword))
                    .add(Column::Email.contains(keyword)),
            )
            .order_by_asc(Column::Username);

        if let Some(limit) = limit {
            query = query.limit(limit);
        }

        query.all(db).await
    }

    /// 查询最近登录的用户
    pub async fn find_recent_login_users(
        db: &DatabaseConnection,
        limit: u64,
    ) -> Result<Vec<Model>, DbErr> {
        Self::find()
            .filter(
                Condition::all()
                    .add(Column::LastLoginDate.is_not_null())
                    .add(Column::Status.eq(UserStatus::Active as i32)),
            )
            .order_by_desc(Column::LastLoginDate)
            .limit(limit)
            .all(db)
            .await
    }

    /// 查询最近创建的用户
    pub async fn find_recent_created_users(
        db: &DatabaseConnection,
        limit: u64,
    ) -> Result<Vec<Model>, DbErr> {
        Self::find()
            .filter(Column::Status.ne(UserStatus::Locked as i32))
            .order_by_desc(Column::CreatedDate)
            .limit(limit)
            .all(db)
            .await
    }

    /// 统计用户数量（按状态）
    pub async fn count_by_status(
        db: &DatabaseConnection,
        status: UserStatus,
    ) -> Result<u64, DbErr> {
        Self::find()
            .filter(Column::Status.eq(status as i32))
            .count(db)
            .await
    }

    /// 查询有邮箱的用户
    pub async fn find_users_with_email(db: &DatabaseConnection) -> Result<Vec<Model>, DbErr> {
        Self::find()
            .filter(
                Condition::all()
                    .add(Column::Email.is_not_null())
                    .add(Column::Status.eq(UserStatus::Active as i32)),
            )
            .order_by_asc(Column::Username)
            .all(db)
            .await
    }

    /// 查询有手机号的用户
    pub async fn find_users_with_phone(db: &DatabaseConnection) -> Result<Vec<Model>, DbErr> {
        Self::find()
            .filter(Column::Phone.is_not_null())
            .order_by_asc(Column::Username)
            .all(db)
            .await
    }

    // ==================== RBAC 相关查询方法 ====================

    /// 查询用户的所有角色
    pub async fn find_user_roles(
        db: &DatabaseConnection,
        user_id: Uuid,
    ) -> Result<Vec<super::roles::Model>, DbErr> {
        Self::find_by_id(user_id)
            .find_with_related(super::roles::Entity)
            .all(db)
            .await
            .map(|results| results.into_iter().flat_map(|(_, roles)| roles).collect())
    }

    /// 内部辅助函数：根据用户ID获取权限代码和角色ID
    async fn fetch_permissions_and_role_ids(
        db: &DatabaseConnection,
        user_id: Uuid,
    ) -> Result<(Vec<String>, Vec<Uuid>), DbErr> {
        // 查询权限代码集合
        let permission_codes = Self::get_user_permission_codes_by_user_id(db, user_id).await?;

        // 查询角色ID集合
        let role_ids: Vec<Uuid> = super::user_roles::Entity::find()
            .select_only()
            .column(super::user_roles::Column::RoleId)
            .filter(super::user_roles::Column::UserId.eq(user_id))
            .into_tuple::<(Uuid,)>()
            .all(db)
            .await?
            .into_iter()
            .map(|(role_id,)| role_id)
            .collect();

        Ok((permission_codes, role_ids))
    }

    /// 内部辅助函数：根据用户ID获取权限代码和角色信息
    async fn fetch_permissions_and_roles(
        db: &DatabaseConnection,
        user_id: Uuid,
    ) -> Result<(Vec<String>, Vec<SysRoleSimpleResponse>), DbErr> {
        // 查询权限代码集合
        let permission_codes = Self::get_user_permission_codes_by_user_id(db, user_id).await?;

        // 查询角色信息集合（只查询需要的字段，性能更好）
        let roles: Vec<SysRoleSimpleResponse> = super::roles::Entity::find()
            .select_only()
            .columns([
                super::roles::Column::Id,
                super::roles::Column::Name,
                super::roles::Column::RoleCode,
                super::roles::Column::Description,
            ])
            .join(JoinType::InnerJoin, super::roles::Relation::UserRoles.def())
            .filter(super::user_roles::Column::UserId.eq(user_id))
            .into_tuple::<(Uuid, String, Option<String>, Option<String>)>()
            .all(db)
            .await?
            .into_iter()
            .map(|(id, name, role_code, description)| SysRoleSimpleResponse {
                id,
                name,
                role_code,
                description,
            })
            .collect();

        Ok((permission_codes, roles))
    }

    /// 获取用户权限代码列表
    async fn get_user_permission_codes_by_user_id(
        db: &DatabaseConnection,
        user_id: Uuid,
    ) -> Result<Vec<String>, DbErr> {
        let permission_codes: Vec<String> = super::permissions::Entity::find()
            .distinct() // 添加去重，避免重复权限
            .select_only() // 只选择需要的字段，提高性能
            .column(super::permissions::Column::Perms)
            .join(
                JoinType::InnerJoin,
                super::permissions::Relation::RolePermissions.def(),
            )
            .join(
                JoinType::InnerJoin,
                super::role_permissions::Relation::Roles.def(),
            )
            .join(JoinType::InnerJoin, super::roles::Relation::UserRoles.def())
            .filter(super::user_roles::Column::UserId.eq(user_id))
            .filter(super::permissions::Column::Perms.is_not_null())
            .filter(super::permissions::Column::Status.eq(0)) // 只查询有效权限
            .into_tuple::<(Option<String>,)>() // 使用元组优化查询结果
            .all(db)
            .await?
            .into_iter()
            .filter_map(|(perm,)| perm)
            .collect();
        Ok(permission_codes)
    }

    /// 查询用户基本信息（包含权限集合和角色ID集合）
    pub async fn find_user_with_permissions_and_role_ids(
        db: &DatabaseConnection,
        user_id: Uuid,
    ) -> Result<Option<(Model, Vec<String>, Vec<Uuid>)>, DbErr> {
        // 查询用户
        let user = Self::find_by_id(user_id).one(db).await?;

        if let Some(user) = user {
            // 使用辅助函数获取权限和角色ID
            let (permission_codes, role_ids) =
                Self::fetch_permissions_and_role_ids(db, user_id).await?;
            Ok(Some((user, permission_codes, role_ids)))
        } else {
            Ok(None)
        }
    }

    /// 查询用户基本信息（包含权限集合和角色ID集合）
    pub async fn find_user_with_permissions_and_roles(
        db: &DatabaseConnection,
        user_id: Uuid,
    ) -> Result<Option<(Model, Vec<String>, Vec<SysRoleSimpleResponse>)>, DbErr> {
        // 查询用户
        let user = Self::find_by_id(user_id).one(db).await?;

        if let Some(user) = user {
            // 使用辅助函数获取权限和角色ID
            let (permission_codes, roles) = Self::fetch_permissions_and_roles(db, user_id).await?;
            Ok(Some((user, permission_codes, roles)))
        } else {
            Ok(None)
        }
    }

    /// 根据用户姓名查询用户基本信息（包含权限集合和角色ID集合）
    pub async fn find_user_by_username_with_permissions_and_role_ids(
        db: &DatabaseConnection,
        username: &str,
    ) -> Result<Option<(Model, Vec<String>, Vec<Uuid>)>, DbErr> {
        // 查询用户
        let user = Self::find_by_username(db, username).await?;

        if let Some(user) = user {
            // 使用辅助函数获取权限和角色ID
            let (permission_codes, role_ids) =
                Self::fetch_permissions_and_role_ids(db, user.id).await?;
            Ok(Some((user, permission_codes, role_ids)))
        } else {
            Ok(None)
        }
    }

    /// 根据ID查询用户详情并关联查询角色详情
    pub async fn find_user_detail_with_roles(
        db: &DatabaseConnection,
        user_id: Uuid,
    ) -> Result<Option<(Model, Vec<super::roles::Model>)>, DbErr> {
        // 查询用户
        let user = Self::find_by_id(user_id).one(db).await?;

        if let Some(user) = user {
            // 查询用户的角色详情
            let roles = Self::find_user_roles(db, user_id).await?;
            Ok(Some((user, roles)))
        } else {
            Ok(None)
        }
    }
}

/// 用户模型实例方法
impl Model {
    /// 检查用户是否可以登录（只有Active状态才能登录）
    pub fn can_login(&self) -> Result<(), &'static str> {
        match self.get_status() {
            UserStatus::Active => Ok(()),
            UserStatus::Disabled => Err("账号已被禁用"),
            UserStatus::Locked => Err("账号已被锁定"),
        }
    }

    /// 检查用户是否为活跃状态
    pub fn is_active(&self) -> bool {
        self.status == UserStatus::Active as i32
    }

    /// 检查用户是否被禁用
    pub fn is_disabled(&self) -> bool {
        self.status == UserStatus::Disabled as i32
    }

    /// 检查用户是否被锁定
    pub fn is_locked(&self) -> bool {
        self.status == UserStatus::Locked as i32
    }

    /// 获取用户状态
    pub fn get_status(&self) -> UserStatus {
        match self.status {
            1 => UserStatus::Active,
            2 => UserStatus::Disabled,
            3 => UserStatus::Locked,
            _ => UserStatus::Disabled,
        }
    }

    /// 获取用户性别
    pub fn get_gender(&self) -> Option<Gender> {
        match self.gender {
            Some(1) => Some(Gender::Male),
            Some(2) => Some(Gender::Female),
            Some(3) => Some(Gender::Unknown),
            _ => None,
        }
    }

    /// 获取显示名称（优先使用真实姓名，否则使用用户名）
    pub fn display_name(&self) -> &str {
        self.real_name.as_deref().unwrap_or(&self.username)
    }

    /// 检查是否有邮箱
    pub fn has_email(&self) -> bool {
        self.email.is_some() && !self.email.as_ref().unwrap().is_empty()
    }

    /// 检查是否有手机号
    pub fn has_phone(&self) -> bool {
        self.phone.is_some() && !self.phone.as_ref().unwrap().is_empty()
    }

    /// 检查是否有头像
    pub fn has_avatar(&self) -> bool {
        self.avatar.is_some() && !self.avatar.as_ref().unwrap().is_empty()
    }

    /// 检查是否已绑定微信
    pub fn has_wechat(&self) -> bool {
        self.wechat_openid.is_some() && !self.wechat_openid.as_ref().unwrap().is_empty()
    }

    /// 检查是否有微信昵称
    pub fn has_wechat_nickname(&self) -> bool {
        self.wechat_nickname.is_some() && !self.wechat_nickname.as_ref().unwrap().is_empty()
    }

    /// 检查是否有微信头像
    pub fn has_wechat_avatar(&self) -> bool {
        self.wechat_avatar.is_some() && !self.wechat_avatar.as_ref().unwrap().is_empty()
    }

    /// 获取微信显示名称（优先微信昵称，否则显示"已绑定微信"）
    pub fn wechat_display_name(&self) -> String {
        if self.has_wechat() {
            if let Some(nickname) = &self.wechat_nickname {
                if !nickname.is_empty() {
                    return nickname.clone();
                }
            }
            "已绑定微信".to_string()
        } else {
            "未绑定微信".to_string()
        }
    }

    /// 获取性别文本
    pub fn gender_text(&self) -> &str {
        match self.gender {
            Some(1) => "男",
            Some(2) => "女",
            Some(3) => "未知",
            _ => "未设置",
        }
    }

    /// 检查密码（需要配合密码哈希库使用）
    pub fn verify_password(&self, password: &str) -> bool {
        // 这里应该使用实际的密码哈希验证
        // 例如：bcrypt::verify(password, &self.password).unwrap_or(false)
        // 临时实现，实际项目中需要替换
        self.password == password
    }

    /// 格式化最后登录时间
    pub fn last_login_formatted(&self) -> String {
        match &self.last_login_date {
            Some(date) => {
                // 数据库已存储本地时间，直接格式化
                date.format("%Y-%m-%d %H:%M:%S").to_string()
            }
            None => "从未登录".to_string(),
        }
    }

    /// 格式化创建时间
    pub fn created_date_formatted(&self) -> String {
        // 数据库已存储本地时间，直接格式化
        self.created_date.format("%Y-%m-%d %H:%M:%S").to_string()
    }

    /// 格式化更新时间
    pub fn updated_date_formatted(&self) -> String {
        // 数据库已存储本地时间，直接格式化
        self.updated_date.format("%Y-%m-%d %H:%M:%S").to_string()
    }
}

impl Related<super::user_roles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserRoles.def()
    }
}

impl Related<super::roles::Entity> for Entity {
    fn to() -> RelationDef {
        super::user_roles::Relation::Roles.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::user_roles::Relation::Users.def().rev())
    }
}

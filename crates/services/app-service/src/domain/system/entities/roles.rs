//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.11

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "system", table_name = "roles")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    #[sea_orm(unique)]
    pub name: String,
    /// 角色编码 - 角色的唯一标识编码，用于程序逻辑识别
    pub role_code: Option<String>,
    #[sea_orm(column_type = "Text", nullable)]
    pub description: Option<String>,
    pub created_date: DateTimeLocal,
    pub updated_date: DateTimeLocal,
    pub created_by: Option<Uuid>,
    pub updated_by: Option<Uuid>,
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
}

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::user_roles::Entity")]
    UserRoles,
    #[sea_orm(has_many = "super::role_permissions::Entity")]
    RolePermissions,
}

impl ActiveModelBehavior for ActiveModel {}

impl Related<super::user_roles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::UserRoles.def()
    }
}

impl Related<super::role_permissions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::RolePermissions.def()
    }
}

impl Related<super::users::Entity> for Entity {
    fn to() -> RelationDef {
        super::user_roles::Relation::Users.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::user_roles::Relation::Roles.def().rev())
    }
}

impl Related<super::permissions::Entity> for Entity {
    fn to() -> RelationDef {
        super::role_permissions::Relation::Permissions.def()
    }
    fn via() -> Option<RelationDef> {
        Some(super::role_permissions::Relation::Roles.def().rev())
    }
}

/// 角色查询方法封装
impl Entity {
    /// 根据角色名查询角色
    pub async fn find_by_name(db: &DatabaseConnection, name: &str) -> Result<Option<Model>, DbErr> {
        Self::find()
            .filter(Column::Name.eq(name))
            .one(db)
            .await
    }

    /// 根据角色编码查询角色
    pub async fn find_by_code(db: &DatabaseConnection, code: &str) -> Result<Option<Model>, DbErr> {
        Self::find()
            .filter(Column::RoleCode.eq(code))
            .one(db)
            .await
    }

    /// 查询角色的所有权限
    pub async fn find_role_permissions(db: &DatabaseConnection, role_id: Uuid) -> Result<Vec<super::permissions::Model>, DbErr> {
        Self::find_by_id(role_id)
            .find_with_related(super::permissions::Entity)
            .all(db)
            .await
            .map(|results| {
                results.into_iter()
                    .flat_map(|(_, permissions)| permissions)
                    .collect()
            })
    }

    /// 查询角色的所有用户
    pub async fn find_role_users(db: &DatabaseConnection, role_id: Uuid) -> Result<Vec<super::users::Model>, DbErr> {
        Self::find_by_id(role_id)
            .find_with_related(super::users::Entity)
            .all(db)
            .await
            .map(|results| {
                results.into_iter()
                    .flat_map(|(_, users)| users)
                    .collect()
            })
    }

    /// 查询角色是否拥有特定权限
    pub async fn has_permission(db: &DatabaseConnection, role_id: Uuid, permission_code: &str) -> Result<bool, DbErr> {
        use sea_orm::*;
        
        let count = super::permissions::Entity::find()
            .join(JoinType::InnerJoin, super::permissions::Relation::RolePermissions.def())
            .filter(super::role_permissions::Column::RoleId.eq(role_id))
            .filter(super::permissions::Column::Perms.eq(permission_code))
            .count(db)
            .await?;
        
        Ok(count > 0)
    }

    /// 查询拥有特定权限的所有角色
    pub async fn find_roles_with_permission(db: &DatabaseConnection, permission_code: &str) -> Result<Vec<Model>, DbErr> {
        use sea_orm::*;
        
        Self::find()
            .join(JoinType::InnerJoin, Relation::RolePermissions.def())
            .join(JoinType::InnerJoin, super::role_permissions::Relation::Permissions.def())
            .filter(super::permissions::Column::Perms.eq(permission_code))
            .all(db)
            .await
    }
}

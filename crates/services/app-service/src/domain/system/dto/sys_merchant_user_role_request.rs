use bon::Builder;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

/// 商户用户角色关联分页查询请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, IntoParams, Builder)]
#[into_params(parameter_in = Query)]
pub struct SysMerchantUserRolePageRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: Option<i64>,

    /// 用户ID
    #[schema(example = "f47ac10b-58cc-4372-a567-0e02b2c3d479")]
    pub user_id: Option<Uuid>,

    /// 角色ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub role_id: Option<Uuid>,

    /// 关联状态（0禁用 1启用）
    #[schema(example = 1)]
    pub status: Option<i32>,

    /// 当前页码
    #[builder(default = 1)]
    #[schema(example = 1)]
    pub page: u64,

    /// 每页大小
    #[builder(default = 10)]
    #[schema(example = 10)]
    pub page_size: u64,

    /// 创建开始时间
    #[schema(example = "2023-01-01")]
    pub created_start: Option<String>,

    /// 创建结束时间
    #[schema(example = "2023-12-31")]
    pub created_end: Option<String>,
}

/// 商户用户角色关联创建请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUserRoleCreateRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: i64,

    /// 用户ID
    #[schema(example = "f47ac10b-58cc-4372-a567-0e02b2c3d479")]
    pub user_id: Uuid,

    /// 角色ID列表
    #[schema(example = json!(["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]))]
    pub role_ids: Vec<Uuid>,

    /// 佣金比例(字符串格式，如"0.01")
    #[validate(custom(function = "validate_commission_rate"))]
    #[schema(example = "0.01")]
    pub commission_rate: Option<String>,

    /// 关联状态（0禁用 1启用）
    #[builder(default = 1)]
    #[validate(range(min = 0, max = 1, message = "状态值必须是0或1"))]
    #[schema(example = 1)]
    pub status: i32,

    /// 备注
    #[schema(example = "临时角色分配")]
    pub remark: Option<String>,
}

/// 商户用户角色关联更新请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUserRoleUpdateRequest {
    /// 佣金比例(字符串格式，如"0.01")
    #[validate(custom(function = "validate_commission_rate"))]
    #[schema(example = "0.01")]
    pub commission_rate: Option<String>,

    /// 关联状态（0禁用 1启用）
    #[validate(range(min = 0, max = 1, message = "状态值必须是0或1"))]
    #[schema(example = 1)]
    pub status: i32,

    /// 备注
    #[schema(example = "角色权限已更新")]
    pub remark: Option<String>,
}

/// 用户角色批量分配请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUserRoleAssignRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: i64,

    /// 用户ID
    #[schema(example = "f47ac10b-58cc-4372-a567-0e02b2c3d479")]
    pub user_id: Uuid,

    /// 角色ID列表（重新分配，覆盖原有角色）
    #[schema(example = json!(["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]))]
    pub role_ids: Vec<Uuid>,

    /// 佣金比例(字符串格式，如"0.01")
    #[validate(custom(function = "validate_commission_rate"))]
    #[schema(example = "0.01")]
    pub commission_rate: Option<String>,

    /// 备注
    #[schema(example = "批量分配角色")]
    pub remark: Option<String>,
}

/// 商户用户角色关联批量删除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUserRoleBatchDeleteRequest {
    /// 用户角色关联ID列表
    #[schema(example = json!(["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]))]
    pub user_role_ids: Vec<Uuid>,
}

/// 商户用户角色关联状态切换请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUserRoleStatusRequest {
    /// 状态（0禁用 1启用）
    #[validate(range(min = 0, max = 1, message = "状态值必须是0或1"))]
    #[schema(example = 1)]
    pub status: i32,
}

/// 用户角色移除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUserRoleRemoveRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: i64,

    /// 用户ID
    #[schema(example = "f47ac10b-58cc-4372-a567-0e02b2c3d479")]
    pub user_id: Uuid,

    /// 要移除的角色ID列表
    #[schema(example = json!(["550e8400-e29b-41d4-a716-************"]))]
    pub role_ids: Vec<Uuid>,
}

// ============================================================================
// 自定义验证函数
// ============================================================================

use validator::ValidationError;

/// 验证佣金比例
fn validate_commission_rate(rate_str: &str) -> Result<(), ValidationError> {
    crate::utils::commission_utils::CommissionUtils::validate_platform_commission(rate_str)
}

// ============================================================================
// 简化的映射函数
// ============================================================================

use crate::domain::business::merchants::entities::merchant_user_roles::ActiveModel;
use crate::utils::datetime::DateTimeUtils;
use chrono::{DateTime, FixedOffset};
use sea_orm::Set;

impl SysMerchantUserRoleCreateRequest {
    /// 转换为ActiveModel列表
    /// 注意：由于表结构重构，此方法需要先获取user_merchant_id
    /// 建议在service层使用MerchantUserRolesRepository进行角色分配
    pub fn to_active_models_with_user_merchant_id(
        self,
        user_merchant_id: Uuid,
        operator_id: Option<Uuid>,
    ) -> Result<Vec<ActiveModel>, anyhow::Error> {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        let models = self
            .role_ids
            .into_iter()
            .map(|role_id| ActiveModel {
                id: Set(Uuid::now_v7()),
                user_merchant_id: Set(user_merchant_id),
                role_id: Set(role_id),
                created_date: Set(now),
                updated_date: Set(now),
                created_by: Set(operator_id),
                updated_by: Set(operator_id),
                remark: Set(self.remark.clone()),
            })
            .collect();

        Ok(models)
    }
}

impl SysMerchantUserRoleUpdateRequest {
    /// 更新ActiveModel
    pub fn update_active_model(
        self,
        mut existing: ActiveModel,
        operator_id: Option<Uuid>,
    ) -> Result<ActiveModel, anyhow::Error> {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        existing.updated_date = Set(now);
        existing.updated_by = Set(operator_id);
        existing.remark = Set(self.remark);

        Ok(existing)
    }
}

impl SysMerchantUserRoleAssignRequest {
    /// 转换为ActiveModel列表（用于角色重新分配）
    /// 注意：由于表结构重构，此方法需要先获取user_merchant_id
    /// 建议在service层使用MerchantUserRolesRepository进行角色分配
    pub fn to_active_models_with_user_merchant_id(
        self,
        user_merchant_id: Uuid,
        operator_id: Option<Uuid>,
    ) -> Result<Vec<ActiveModel>, anyhow::Error> {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        let models = self
            .role_ids
            .into_iter()
            .map(|role_id| ActiveModel {
                id: Set(Uuid::now_v7()),
                user_merchant_id: Set(user_merchant_id),
                role_id: Set(role_id),
                created_date: Set(now),
                updated_date: Set(now),
                created_by: Set(operator_id),
                updated_by: Set(operator_id),
                remark: Set(self.remark.clone()),
            })
            .collect();

        Ok(models)
    }
}

use bon::Builder;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

/// 商户授权权限分页查询请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, IntoParams, Builder)]
#[into_params(parameter_in = Query)]
pub struct SysMerchantAuthorizedPermissionPageRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: Option<i64>,

    /// 权限模板ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub permission_template_id: Option<Uuid>,

    /// 授权状态（0正常 1停用）
    #[schema(example = 0)]
    pub status: Option<i32>,

    /// 当前页码
    #[validate(range(min = 1, message = "当前页码必须大于0"))]
    #[schema(example = 1)]
    pub page: Option<u64>,

    /// 每页大小
    #[validate(range(min = 1, max = 100, message = "每页大小必须大于0"))]
    #[schema(example = 10)]
    pub page_size: Option<u64>,

    /// 授权时间-开始（授权发生时间）
    #[schema(example = "2023-01-01 00:00:00")]
    pub authorized_start: Option<String>,

    /// 授权时间-结束（授权发生时间）
    #[schema(example = "2023-12-31 23:59:59")]
    pub authorized_end: Option<String>,

    /// 创建时间-开始
    #[schema(example = "2023-01-01 00:00:00")]
    pub created_start: Option<String>,

    /// 创建时间-结束
    #[schema(example = "2023-12-31 23:59:59")]
    pub created_end: Option<String>,
}

/// 商户授权权限创建请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
#[schema(example = json!({
    "merchant_id": 1001,
    "permission_template_ids": ["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"],
    "status": 0,
    "authorized_date": "2023-01-01 10:00:00",
    "remark": "批量授权基础权限"
}))]
pub struct SysMerchantAuthorizedPermissionCreateRequest {
    /// 商户ID
    #[validate(range(min = 1, message = "商户ID必须大于0"))]
    #[schema(example = 1001)]
    pub merchant_id: i64,

    /// 权限模板ID列表
    #[validate(length(min = 1, message = "权限模板列表不能为空"))]
    #[schema(example = json!(["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]))]
    pub permission_template_ids: Vec<Uuid>,

    /// 授权状态（0正常 1停用）
    #[builder(default = 0)]
    #[validate(range(min = 0, max = 1, message = "状态只能是0正常或1停用"))]
    #[schema(example = 0)]
    pub status: i32,

    /// 授权时间（记录何时授权，为空则使用当前时间）
    #[schema(example = "2023-01-01 10:00:00")]
    pub authorized_date: Option<String>,

    /// 备注
    #[validate(length(max = 500, message = "备注长度不能超过500字符"))]
    #[schema(example = "批量授权基础权限")]
    pub remark: Option<String>,
}

/// 商户授权权限更新请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-************",
    "status": 1,
    "authorized_date": "2023-01-01 10:00:00",
    "remark": "暂停权限"
}))]
pub struct SysMerchantAuthorizedPermissionUpdateRequest {
    /// 授权记录ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub id: Uuid,

    /// 授权状态（0正常 1停用）
    #[validate(range(min = 0, max = 1, message = "状态只能是0正常或1停用"))]
    #[schema(example = 1)]
    pub status: i32,

    /// 授权时间（记录何时授权，通常不修改）
    #[schema(example = "2023-01-01 10:00:00")]
    pub authorized_date: Option<String>,

    /// 备注
    #[validate(length(max = 500, message = "备注长度不能超过500字符"))]
    #[schema(example = "暂停权限")]
    pub remark: Option<String>,
}

/// 商户权限批量授权请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantPermissionBatchAuthorizeRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: i64,

    /// 权限模板ID列表（重新授权，覆盖原有权限）
    #[validate(length(min = 1, message = "权限模板列表不能为空"))]
    #[schema(example = json!(["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]))]
    pub permission_template_ids: Vec<Uuid>,

    /// 是否覆盖原有权限
    #[builder(default = false)]
    #[schema(example = false)]
    pub override_existing: bool,

    /// 备注
    #[schema(example = "批量重新授权")]
    pub remark: Option<String>,
}

/// 商户授权权限批量删除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantAuthorizedPermissionBatchDeleteRequest {
    /// 授权权限ID列表
    #[validate(length(min = 1, message = "授权权限ID列表不能为空"))]
    #[schema(example = json!(["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]))]
    pub authorized_permission_ids: Vec<Uuid>,
}

/// 商户授权权限状态切换请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantAuthorizedPermissionStatusRequest {
    /// 状态（0正常 1停用）
    #[validate(range(min = 0, max = 1, message = "状态只能是0正常或1停用"))]
    #[schema(example = 0)]
    pub status: i32,
}

/// 商户权限撤销请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantPermissionRevokeRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: i64,

    /// 要撤销的权限模板ID列表
    #[validate(length(min = 1, message = "权限模板列表不能为空"))]
    #[schema(example = json!(["550e8400-e29b-41d4-a716-************"]))]
    pub permission_template_ids: Vec<Uuid>,

    /// 撤销原因
    #[schema(example = "商户申请撤销")]
    pub revoke_reason: Option<String>,
}

/// 商户权限模板复制请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantPermissionCopyRequest {
    /// 源商户ID
    #[schema(example = 1001)]
    pub source_merchant_id: i64,

    /// 目标商户ID列表
    #[validate(length(min = 1, message = "目标商户列表不能为空"))]
    #[schema(example = json!([1002, 1003, 1004]))]
    pub target_merchant_ids: Vec<i64>,

    /// 是否覆盖目标商户现有权限
    #[builder(default = false)]
    #[schema(example = false)]
    pub override_existing: bool,

    /// 备注
    #[schema(example = "从模板商户复制权限")]
    pub remark: Option<String>,
}

// ============================================================================
// 简化的映射函数
// ============================================================================

use crate::domain::business::merchants::entities::merchant_authorized_permissions::{
    ActiveModel, MerchantAuthorizedPermissionsStatus,
};
use crate::utils::datetime::DateTimeUtils;
use chrono::{DateTime, FixedOffset};
use sea_orm::{ActiveEnum, Set};

impl SysMerchantAuthorizedPermissionCreateRequest {
    /// 转换为ActiveModel列表
    pub fn to_active_models(self, operator_id: Uuid) -> Result<Vec<ActiveModel>, anyhow::Error> {
        let _now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        // 处理授权日期
        let authorized_date = if let Some(date_str) = self.authorized_date {
            DateTimeUtils::parse_datetime(&date_str)?.fixed_offset()
        } else {
            _now
        };

        let models = self
            .permission_template_ids
            .into_iter()
            .map(|template_id| ActiveModel {
                id: Set(Uuid::now_v7()),
                merchant_id: Set(self.merchant_id),
                permission_template_id: Set(template_id),
                status: Set(
                    MerchantAuthorizedPermissionsStatus::try_from_value(&self.status)
                        .unwrap_or(MerchantAuthorizedPermissionsStatus::Enabled),
                ),
                authorized_date: Set(authorized_date),
                created_by: Set(operator_id),
                updated_by: Set(Some(operator_id)),
                remark: Set(self.remark.clone()),
            })
            .collect();

        Ok(models)
    }
}

impl SysMerchantAuthorizedPermissionUpdateRequest {
    /// 更新ActiveModel
    pub fn update_active_model(
        self,
        mut existing: ActiveModel,
        operator_id: Uuid,
    ) -> Result<ActiveModel, anyhow::Error> {
        let _now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        // 处理授权日期
        if let Some(date_str) = self.authorized_date {
            let authorized_date = DateTimeUtils::parse_datetime(&date_str)?.fixed_offset();
            existing.authorized_date = Set(authorized_date);
        }

        existing.status = Set(MerchantAuthorizedPermissionsStatus::try_from_value(&self.status)
            .unwrap_or(MerchantAuthorizedPermissionsStatus::Enabled));
        existing.updated_by = Set(Some(operator_id));
        existing.remark = Set(self.remark);

        Ok(existing)
    }
}

impl SysMerchantPermissionBatchAuthorizeRequest {
    /// 转换为ActiveModel列表（用于批量授权）
    pub fn to_active_models(self, operator_id: Uuid) -> Result<Vec<ActiveModel>, anyhow::Error> {
        let _now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        let models = self
            .permission_template_ids
            .into_iter()
            .map(|template_id| ActiveModel {
                id: Set(Uuid::now_v7()),
                merchant_id: Set(self.merchant_id),
                permission_template_id: Set(template_id),
                status: Set(MerchantAuthorizedPermissionsStatus::Enabled), // 默认正常状态
                authorized_date: Set(_now),
                created_by: Set(operator_id),
                updated_by: Set(Some(operator_id)),
                remark: Set(self.remark.clone()),
            })
            .collect();

        Ok(models)
    }
}

impl SysMerchantPermissionCopyRequest {
    /// 为每个目标商户生成ActiveModel列表
    pub fn to_active_models_for_target(
        &self,
        target_merchant_id: i64,
        permission_template_ids: Vec<Uuid>,
        operator_id: Uuid,
    ) -> Result<Vec<ActiveModel>, anyhow::Error> {
        let _now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        let models = permission_template_ids
            .into_iter()
            .map(|template_id| ActiveModel {
                id: Set(Uuid::now_v7()),
                merchant_id: Set(target_merchant_id),
                permission_template_id: Set(template_id),
                status: Set(MerchantAuthorizedPermissionsStatus::Enabled), // 默认正常状态
                authorized_date: Set(_now),
                created_by: Set(operator_id),
                updated_by: Set(Some(operator_id)),
                remark: Set(self.remark.clone()),
            })
            .collect();

        Ok(models)
    }
}

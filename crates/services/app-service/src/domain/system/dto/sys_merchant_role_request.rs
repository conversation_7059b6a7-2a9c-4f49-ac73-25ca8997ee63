use bon::Builder;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

/// 商户角色分页查询请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, IntoParams, Builder)]
#[into_params(parameter_in = Query)]
pub struct SysMerchantRolePageRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: Option<i64>,

    /// 角色编码
    #[schema(example = "MERCHANT_ADMIN")]
    pub role_code: Option<String>,

    /// 角色名称
    #[schema(example = "商户管理员")]
    pub role_name: Option<String>,

    /// 角色类型（1系统定义角色 2商户自定义角色）
    #[schema(example = 1)]
    pub role_type: Option<i32>,

    /// 是否默认角色
    #[schema(example = true)]
    pub is_default: Option<bool>,

    /// 数据范围（1商户全部数据 2个人数据）
    #[schema(example = 1)]
    pub data_scope: Option<i32>,

    /// 角色状态（0禁用 1启用）
    #[schema(example = 1)]
    pub status: Option<i32>,

    /// 当前页码
    #[validate(range(min = 1, message = "页码必须从1开始"))]
    #[schema(example = 1)]
    pub page: Option<u64>,

    /// 每页大小
    #[validate(range(min = 1, max = 100, message = "每页大小必须在1-100之间"))]
    #[schema(example = 10)]
    pub page_size: Option<u64>,

    /// 创建开始时间
    #[schema(example = "2023-01-01")]
    pub created_start: Option<String>,

    /// 创建结束时间
    #[schema(example = "2023-12-31")]
    pub created_end: Option<String>,
}

/// 商户角色创建请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantRoleCreateRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: i64,

    /// 角色编码
    #[validate(length(max = 50, message = "角色编码长度不能超过50"))]
    #[schema(example = "CUSTOM_ROLE_001")]
    pub role_code: Option<String>,

    /// 角色名称
    #[validate(length(min = 1, max = 100, message = "角色名称长度必须在1-100之间"))]
    #[schema(example = "前厅服务员")]
    pub role_name: String,

    /// 角色类型（1系统定义角色 2商户自定义角色）
    #[builder(default = 2)]
    #[validate(range(min = 1, max = 2, message = "角色类型值必须是1或2"))]
    #[schema(example = 2)]
    pub role_type: i32,

    /// 是否默认角色
    #[builder(default = false)]
    #[schema(example = false)]
    pub is_default: bool,

    /// 数据范围（1商户全部数据 2个人数据）
    #[builder(default = 2)]
    #[validate(range(min = 1, max = 2, message = "数据范围值必须是1或2"))]
    #[schema(example = 2)]
    pub data_scope: i32,

    /// 角色描述
    #[schema(example = "负责前厅接待、点餐、收银等日常工作")]
    pub role_description: Option<String>,

    /// 角色状态（0禁用 1启用）
    #[builder(default = 1)]
    #[validate(range(min = 0, max = 1, message = "状态值必须是0或1"))]
    #[schema(example = 1)]
    pub status: i32,

    /// 备注
    #[schema(example = "餐厅前厅服务人员角色")]
    pub remark: Option<String>,
}

/// 商户角色更新请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantRoleUpdateRequest {
    /// 角色编码
    #[validate(length(max = 50, message = "角色编码长度不能超过50"))]
    #[schema(example = "CUSTOM_ROLE_001")]
    pub role_code: Option<String>,

    /// 角色名称
    #[validate(length(min = 1, max = 100, message = "角色名称长度必须在1-100之间"))]
    #[schema(example = "前厅服务员")]
    pub role_name: String,

    /// 角色类型（1系统定义角色 2商户自定义角色）
    #[validate(range(min = 1, max = 2, message = "角色类型值必须是1或2"))]
    #[schema(example = 2)]
    pub role_type: i32,

    /// 是否默认角色
    #[schema(example = false)]
    pub is_default: bool,

    /// 数据范围（1商户全部数据 2个人数据）
    #[validate(range(min = 1, max = 2, message = "数据范围值必须是1或2"))]
    #[schema(example = 2)]
    pub data_scope: i32,

    /// 角色描述
    #[schema(example = "负责前厅接待、点餐、收银等日常工作")]
    pub role_description: Option<String>,

    /// 角色状态（0禁用 1启用）
    #[validate(range(min = 0, max = 1, message = "状态值必须是0或1"))]
    #[schema(example = 1)]
    pub status: i32,

    /// 备注
    #[schema(example = "餐厅前厅服务人员角色")]
    pub remark: Option<String>,
}

/// 商户角色批量删除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantRoleBatchDeleteRequest {
    /// 角色ID列表
    #[schema(example = json!(["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]))]
    pub role_ids: Vec<Uuid>,
}

/// 商户角色状态切换请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantRoleStatusRequest {
    /// 状态（0禁用 1启用）
    #[validate(range(min = 0, max = 1, message = "状态值必须是0或1"))]
    #[schema(example = 1)]
    pub status: i32,
}

/// 商户角色权限分配请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantRolePermissionRequest {
    /// 授权权限ID列表
    #[schema(example = json!(["fedcba98-7654-3210-fedc-ba9876543201", "fedcba98-7654-3210-fedc-ba9876543202"]))]
    pub authorized_permission_ids: Vec<Uuid>,
}

// ============================================================================
// 简化的映射函数
// ============================================================================

use crate::domain::business::merchants::entities::merchant_roles::{ActiveModel, MerchantRoleDataScope, MerchantRoleStatus, MerchantRoleType};
use crate::utils::datetime::DateTimeUtils;
use chrono::{DateTime, FixedOffset};
use sea_orm::{ActiveEnum, Set};

impl SysMerchantRoleCreateRequest {
    /// 转换为ActiveModel
    pub fn to_active_model(self, operator_id: Option<Uuid>) -> ActiveModel {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        ActiveModel {
            id: Set(Uuid::now_v7()),
            merchant_id: Set(self.merchant_id),
            role_code: Set(self.role_code),
            role_name: Set(self.role_name),
            role_type: Set(MerchantRoleType::try_from_value(&self.role_type).unwrap_or(MerchantRoleType::MerchantCustom)),
            is_default: Set(self.is_default),
            data_scope: Set(MerchantRoleDataScope::try_from_value(&self.data_scope).unwrap_or(MerchantRoleDataScope::PersonalData)),
            role_description: Set(self.role_description),
            status: Set(MerchantRoleStatus::try_from_value(&self.status).unwrap_or(MerchantRoleStatus::Disabled)),
            created_date: Set(now),
            updated_date: Set(now),
            created_by: Set(operator_id),
            updated_by: Set(operator_id),
            remark: Set(self.remark),
        }
    }
}

impl SysMerchantRoleUpdateRequest {
    /// 更新ActiveModel
    pub fn update_active_model(
        self,
        mut existing: ActiveModel,
        operator_id: Option<Uuid>,
    ) -> ActiveModel {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        existing.role_code = Set(self.role_code);
        existing.role_name = Set(self.role_name);
        existing.role_type = Set(MerchantRoleType::try_from_value(&self.role_type).unwrap_or(MerchantRoleType::MerchantCustom));
        existing.is_default = Set(self.is_default);
        existing.data_scope = Set(MerchantRoleDataScope::try_from_value(&self.data_scope).unwrap_or(MerchantRoleDataScope::PersonalData));
        existing.role_description = Set(self.role_description);
        existing.status = Set(MerchantRoleStatus::try_from_value(&self.status).unwrap_or(MerchantRoleStatus::Disabled));
        existing.updated_date = Set(now);
        existing.updated_by = Set(operator_id);
        existing.remark = Set(self.remark);

        existing
    }
}

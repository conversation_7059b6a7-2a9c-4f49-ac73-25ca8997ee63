use bon::Builder;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

/// 商户分类分页查询请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, IntoParams, Builder)]
#[into_params(parameter_in = Query)]
pub struct SysMerchantCategoryPageRequest {
    /// 分类名称
    #[schema(example = "餐饮")]
    pub category_name: Option<String>,

    /// 分类编码
    #[schema(example = "CATERING")]
    pub category_code: Option<String>,

    /// 状态（1启用 2禁用）
    #[schema(example = 1)]
    pub status: Option<i32>,

    /// 当前页码
    #[validate(range(min = 1, message = "页码必须大于1"))]
    #[schema(example = 1)]
    pub page: Option<u64>,

    /// 每页大小
    #[validate(range(min = 1, max = 100, message = "每页大小必须在1-100之间"))]
    #[schema(example = 10)]
    pub page_size: Option<u64>,

    /// 创建开始时间
    #[schema(example = "2023-01-01")]
    pub created_start: Option<String>,

    /// 创建结束时间
    #[schema(example = "2023-12-31")]
    pub created_end: Option<String>,
}

/// 商户分类创建请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantCategoryCreateRequest {
    /// 分类名称
    #[validate(length(min = 1, max = 100, message = "分类名称长度必须在1-100之间"))]
    #[schema(example = "餐饮")]
    pub category_name: String,

    /// 分类编码
    #[validate(length(max = 50, message = "分类编码长度不能超过50"))]
    #[schema(example = "CATERING")]
    pub category_code: Option<String>,

    /// 分类描述
    #[schema(example = "餐饮类商户分类")]
    pub description: Option<String>,

    /// 排序字段
    #[validate(range(min = 0, max = 999, message = "排序字段必须在0-999之间"))]
    #[schema(example = 1)]
    pub sort_order: Option<i32>,

    /// 状态（1启用 2禁用）
    #[builder(default = 1)]
    #[validate(range(min = 1, max = 2, message = "状态值必须是1或2"))]
    #[schema(example = 1)]
    pub status: i32,

    /// 备注
    #[schema(example = "餐饮类商户分类备注")]
    pub remark: Option<String>,
}

/// 商户分类更新请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantCategoryUpdateRequest {
    /// 分类名称
    #[validate(length(min = 1, max = 100, message = "分类名称长度必须在1-100之间"))]
    #[schema(example = "餐饮")]
    pub category_name: String,

    /// 分类编码
    #[validate(length(max = 50, message = "分类编码长度不能超过50"))]
    #[schema(example = "CATERING")]
    pub category_code: Option<String>,

    /// 分类描述
    #[schema(example = "餐饮类商户分类")]
    pub description: Option<String>,

    /// 排序字段
    #[validate(range(min = 0, max = 999, message = "排序字段必须在0-999之间"))]
    #[schema(example = 1)]
    pub sort_order: Option<i32>,

    /// 状态（1启用 2禁用）
    #[builder(default = 1)]
    #[validate(range(min = 1, max = 2, message = "状态值必须是1或2"))]
    #[schema(example = 1)]
    pub status: i32,

    /// 备注
    #[schema(example = "餐饮类商户分类备注")]
    pub remark: Option<String>,
}

/// 商户分类批量删除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantCategoryBatchDeleteRequest {
    /// 分类ID列表
    #[schema(example = json!(["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]))]
    pub category_ids: Vec<Uuid>,
}

/// 商户分类状态切换请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct MerchantCategoryStatusRequest {
    /// 状态（1启用 2禁用）
    #[validate(range(min = 1, max = 2, message = "状态值必须是1或2"))]
    #[schema(example = 1)]
    pub status: i32,
}

// ============================================================================
// 简化的映射函数
// ============================================================================

use crate::domain::business::merchants::entities::merchant_categories::{
    ActiveModel, MerchantCategoryStatus,
};
use crate::utils::datetime::DateTimeUtils;
use chrono::{DateTime, FixedOffset};
use sea_orm::{ActiveEnum, Set};

impl SysMerchantCategoryCreateRequest {
    /// 转换为ActiveModel
    pub fn to_active_model(self, operator_id: Option<Uuid>) -> ActiveModel {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        ActiveModel {
            id: Set(Uuid::now_v7()),
            category_name: Set(self.category_name),
            category_code: Set(self.category_code),
            description: Set(self.description),
            sort_order: Set(self.sort_order),
            status: Set(MerchantCategoryStatus::try_from_value(&self.status)
                .unwrap_or(MerchantCategoryStatus::Disabled)),
            created_date: Set(now),
            updated_date: Set(now),
            created_by: Set(operator_id),
            updated_by: Set(operator_id),
            remark: Set(self.remark),
        }
    }
}

impl SysMerchantCategoryUpdateRequest {
    /// 更新ActiveModel
    pub fn update_active_model(
        self,
        mut existing: ActiveModel,
        operator_id: Option<Uuid>,
    ) -> ActiveModel {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        existing.category_name = Set(self.category_name);
        existing.category_code = Set(self.category_code);
        existing.description = Set(self.description);
        existing.sort_order = Set(self.sort_order);
        existing.status = Set(MerchantCategoryStatus::try_from_value(&self.status)
            .unwrap_or(MerchantCategoryStatus::Disabled));
        existing.updated_date = Set(now);
        existing.updated_by = Set(operator_id);
        existing.remark = Set(self.remark);

        existing
    }
}

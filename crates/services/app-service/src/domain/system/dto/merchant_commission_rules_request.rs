//! 商户门店佣金规则请求DTO
//! Generated by lib-codegen

use crate::domain::business::merchants::entities::merchant_commission_rules::{
    CommissionTier, ComparisonOperator, ConditionType, LogicType, TierCondition, TierRules,
};
use derive_more::{Constructor, From, Into};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::Validate;

// ============================================================================
// 基础请求结构
// ============================================================================

/// 分页查询商户佣金规则请求
#[derive(
    Debug,
    Clone,
    Serialize,
    Deserialize,
    Validate,
    Constructor,
    From,
    Into,
    utoipa::ToSchema,
    utoipa::IntoParams,
)]
pub struct MerchantCommissionRulesPageRequest {
    /// 页码，从1开始
    #[validate(range(min = 1))]
    #[serde(default = "default_page")]
    pub page: u64,

    /// 每页大小
    #[validate(range(min = 1, max = 100))]
    #[serde(default = "default_page_size")]
    pub page_size: u64,

    /// 商户ID
    pub merchant_id: Option<i64>,

    /// 规则名称（模糊搜索）
    pub rule_name: Option<String>,
}

/// 商户佣金规则列表查询请求
#[derive(
    Debug,
    Clone,
    Serialize,
    Deserialize,
    Validate,
    Constructor,
    From,
    Into,
    utoipa::ToSchema,
    utoipa::IntoParams,
)]
pub struct MerchantCommissionRulesListRequest {
    /// 商户ID
    pub merchant_id: Option<i64>,

    /// 规则名称（模糊搜索）
    pub rule_name: Option<String>,
}

/// 创建商户佣金规则请求
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into)]
pub struct MerchantCommissionRulesCreateRequest {
    /// 商户ID
    pub merchant_id: i64,

    /// 规则名称
    pub rule_name: String,

    /// 基础佣金比例（如0.0500表示5%）
    pub base_commission_rate: Decimal,

    /// 阶梯佣金规则
    pub tier_rules: Option<TierRulesRequest>,

    /// 备注信息
    pub remark: Option<String>,
}

impl Validate for MerchantCommissionRulesCreateRequest {
    fn validate(&self) -> Result<(), validator::ValidationErrors> {
        let mut errors = validator::ValidationErrors::new();

        // 验证商户ID
        if self.merchant_id < 1 {
            let error = validator::ValidationError::new("merchant_id_invalid");
            errors.add("merchant_id", error);
        }

        // 验证基础佣金比例
        if self.base_commission_rate < Decimal::ZERO || self.base_commission_rate > Decimal::ONE {
            let error = validator::ValidationError::new("base_commission_rate_range");
            errors.add("base_commission_rate", error);
        }

        // 验证规则名称长度
        if self.rule_name.is_empty() || self.rule_name.len() > 100 {
            let error = validator::ValidationError::new("rule_name_length");
            errors.add("rule_name", error);
        }

        // 验证备注长度
        if let Some(ref remark) = self.remark {
            if remark.len() > 500 {
                let error = validator::ValidationError::new("remark_length");
                errors.add("remark", error);
            }
        }

        // 验证阶梯规则
        if let Some(ref tier_rules) = self.tier_rules {
            if let Err(_tier_errors) = tier_rules.validate() {
                let error = validator::ValidationError::new("tier_rules_invalid");
                errors.add("tier_rules", error);
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 更新商户佣金规则请求
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into)]
pub struct MerchantCommissionRulesUpdateRequest {
    /// 规则名称
    pub rule_name: Option<String>,

    /// 基础佣金比例（如0.0500表示5%）
    pub base_commission_rate: Option<Decimal>,

    /// 阶梯佣金规则
    pub tier_rules: Option<TierRulesRequest>,

    /// 备注信息
    pub remark: Option<String>,
}

impl Validate for MerchantCommissionRulesUpdateRequest {
    fn validate(&self) -> Result<(), validator::ValidationErrors> {
        let mut errors = validator::ValidationErrors::new();

        // 验证基础佣金比例
        if let Some(ref base_rate) = self.base_commission_rate {
            if *base_rate < Decimal::ZERO || *base_rate > Decimal::ONE {
                let error = validator::ValidationError::new("base_commission_rate_range");
                errors.add("base_commission_rate", error);
            }
        }

        // 验证规则名称
        if let Some(ref rule_name) = self.rule_name {
            if rule_name.is_empty() || rule_name.len() > 100 {
                let error = validator::ValidationError::new("rule_name_length");
                errors.add("rule_name", error);
            }
        }

        // 验证备注长度
        if let Some(ref remark) = self.remark {
            if remark.len() > 500 {
                let error = validator::ValidationError::new("remark_length");
                errors.add("remark", error);
            }
        }

        // 验证阶梯规则
        if let Some(ref tier_rules) = self.tier_rules {
            if let Err(_tier_errors) = tier_rules.validate() {
                let error = validator::ValidationError::new("tier_rules_invalid");
                errors.add("tier_rules", error);
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 批量删除商户佣金规则请求
#[derive(
    Debug, Clone, Serialize, Deserialize, Validate, Constructor, From, Into, utoipa::ToSchema,
)]
pub struct MerchantCommissionRulesBatchDeleteRequest {
    /// 佣金规则ID列表
    #[validate(length(min = 1, message = "至少选择一个佣金规则"))]
    pub ids: Vec<Uuid>,
}

// ============================================================================
// 阶梯佣金相关请求结构
// ============================================================================

/// 阶梯佣金规则请求
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into)]
pub struct TierRulesRequest {
    /// 阶梯规则列表
    pub tiers: Vec<CommissionTierRequest>,
}

impl Validate for TierRulesRequest {
    fn validate(&self) -> Result<(), validator::ValidationErrors> {
        let mut errors = validator::ValidationErrors::new();

        if self.tiers.is_empty() {
            let error = validator::ValidationError::new("tiers_empty");
            errors.add("tiers", error);
        }

        // 验证每个阶梯
        for (_i, tier) in self.tiers.iter().enumerate() {
            if let Err(_tier_errors) = tier.validate() {
                let error = validator::ValidationError::new("tier_invalid");
                errors.add("tiers", error);
            }
        }

        // 业务验证：检查阶梯佣金比例是否递增
        for i in 1..self.tiers.len() {
            if self.tiers[i].commission_rate <= self.tiers[i - 1].commission_rate {
                let error = validator::ValidationError::new("tier_rate_not_ascending");
                errors.add("tiers", error);
                break;
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 单个佣金阶梯请求
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into)]
pub struct CommissionTierRequest {
    /// 阶梯名称
    pub tier_name: String,

    /// 逻辑关系类型：OR（或）/ AND（且）
    pub logic_type: LogicType,

    /// 判断条件列表
    pub conditions: Vec<TierConditionRequest>,

    /// 佣金比例（如0.0600表示6%）
    pub commission_rate: Decimal,

    /// 阶梯描述
    pub description: Option<String>,
}

impl Validate for CommissionTierRequest {
    fn validate(&self) -> Result<(), validator::ValidationErrors> {
        let mut errors = validator::ValidationErrors::new();

        // 验证阶梯名称
        if self.tier_name.is_empty() || self.tier_name.len() > 50 {
            let error = validator::ValidationError::new("tier_name_length");
            errors.add("tier_name", error);
        }

        // 验证条件列表
        if self.conditions.is_empty() {
            let error = validator::ValidationError::new("conditions_empty");
            errors.add("conditions", error);
        }

        // 验证佣金比例
        if self.commission_rate < Decimal::ZERO || self.commission_rate > Decimal::ONE {
            let error = validator::ValidationError::new("commission_rate_range");
            errors.add("commission_rate", error);
        }

        // 验证描述长度
        if let Some(ref desc) = self.description {
            if desc.len() > 200 {
                let error = validator::ValidationError::new("description_length");
                errors.add("description", error);
            }
        }

        // 业务验证：AND逻辑至少需要2个条件
        if self.logic_type == LogicType::And && self.conditions.len() < 2 {
            let error = validator::ValidationError::new("and_needs_multiple_conditions");
            errors.add("conditions", error);
        }

        // 验证每个条件
        for (_i, condition) in self.conditions.iter().enumerate() {
            if let Err(_cond_errors) = condition.validate() {
                let error = validator::ValidationError::new("condition_invalid");
                errors.add("conditions", error);
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 阶梯条件请求
#[derive(Debug, Clone, Serialize, Deserialize, Constructor, From, Into)]
pub struct TierConditionRequest {
    /// 条件类型
    pub condition_type: ConditionType,

    /// 阈值
    pub threshold: Decimal,

    /// 操作符
    pub operator: ComparisonOperator,
}

impl Validate for TierConditionRequest {
    fn validate(&self) -> Result<(), validator::ValidationErrors> {
        if self.threshold < Decimal::ZERO {
            let mut errors = validator::ValidationErrors::new();
            let error = validator::ValidationError::new("threshold_negative");
            errors.add("threshold", error);
            return Err(errors);
        }
        Ok(())
    }
}

// ============================================================================
// 转换实现
// ============================================================================

impl From<TierRulesRequest> for TierRules {
    fn from(req: TierRulesRequest) -> Self {
        TierRules {
            tiers: req.tiers.into_iter().map(|tier| tier.into()).collect(),
        }
    }
}

impl From<CommissionTierRequest> for CommissionTier {
    fn from(req: CommissionTierRequest) -> Self {
        CommissionTier::new(
            req.tier_name,
            req.logic_type,
            req.conditions.into_iter().map(|cond| cond.into()).collect(),
            req.commission_rate,
            req.description,
        )
    }
}

impl From<TierConditionRequest> for TierCondition {
    fn from(req: TierConditionRequest) -> Self {
        TierCondition::new(req.condition_type, req.threshold, req.operator)
    }
}

// ============================================================================
// 默认值函数
// ============================================================================

fn default_page() -> u64 {
    1
}

fn default_page_size() -> u64 {
    10
}

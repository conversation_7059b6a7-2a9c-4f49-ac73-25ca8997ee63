use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

/// 权限菜单分页查询请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, IntoParams)]
#[into_params(parameter_in = Query)]
pub struct SysPermissionPageRequest {
    /// 菜单名称
    #[schema(example = "系统管理")]
    pub menu_name: Option<String>,
    
    /// 权限标识
    #[schema(example = "sys:permission:list")]
    pub perms: Option<String>,
    
    /// 菜单类型（1目录 2菜单 3按钮）
    #[schema(example = 0)]
    pub menu_type: Option<i32>,
    
    /// 状态（0正常 1停用）
    #[schema(example = 0)]
    pub status: Option<i32>,
    
    /// 当前页码
    #[schema(example = 1)]
    pub page: Option<u64>,
    
    /// 每页大小
    #[schema(example = 10)]
    pub page_size: Option<u64>,
    
    /// 创建开始时间
    #[schema(example = "2023-01-01")]
    pub created_start: Option<String>,
    
    /// 创建结束时间
    #[schema(example = "2023-12-31")]
    pub created_end: Option<String>,
}

/// 权限菜单创建请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct SysPermissionCreateRequest {
    /// 菜单名称
    #[validate(length(min = 1, max = 50, message = "菜单名称长度必须在1-50之间"))]
    #[schema(example = "系统管理")]
    pub menu_name: String,
    
    /// 父级ID
    #[schema(value_type = Option<String>, example = "null")]
    pub parent_id: Option<Uuid>,
    
    /// 显示顺序
    #[validate(range(min = 0, max = 999, message = "显示顺序必须在0-999之间"))]
    #[schema(example = 1)]
    pub order_num: Option<i32>,
    
    /// 路由地址
    #[validate(length(max = 200, message = "路由地址长度不能超过200"))]
    #[schema(example = "/system")]
    pub path: Option<String>,
    
    /// 组件路径
    #[validate(length(max = 255, message = "组件路径长度不能超过255"))]
    #[schema(example = "Layout")]
    pub component: Option<String>,
    
    /// 路由参数
    #[schema(value_type = Option<String>, example = "null")]
    pub query: Option<String>,
    
    /// 是否为外链（0是 1否）
    #[schema(example = 1)]
    pub is_frame: Option<i32>,
    
    /// 是否缓存（0缓存 1不缓存）
    #[schema(example = 0)]
    pub is_cache: Option<i32>,
    
    /// 菜单类型（1目录 2菜单 3按钮）
    #[validate(range(min = 1, max = 3, message = "菜单类型必须是1、2或3"))]
    #[schema(example = 1)]
    pub menu_type: Option<i32>,
    
    /// 显示状态（0显示 1隐藏）
    #[schema(example = 0)]
    pub visible: Option<i32>,
    
    /// 菜单状态（0正常 1停用）
    #[schema(example = 0)]
    pub status: Option<i32>,
    
    /// 权限标识
    #[validate(length(max = 100, message = "权限标识长度不能超过100"))]
    #[schema(example = "sys:permission:list")]
    pub perms: Option<String>,
    
    /// 菜单图标
    #[schema(example = "system")]
    pub icon: Option<String>,
    
    /// 备注
    #[schema(example = "系统管理菜单")]
    pub remark: Option<String>,
}

/// 权限菜单更新请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct SysPermissionUpdateRequest {
    /// 菜单名称
    #[validate(length(min = 1, max = 50, message = "菜单名称长度必须在1-50之间"))]
    #[schema(example = "系统管理")]
    pub menu_name: String,
    
    /// 父级ID
    #[schema(value_type = Option<String>, example = "null")]
    pub parent_id: Option<Uuid>,
    
    /// 显示顺序
    #[validate(range(min = 0, max = 999, message = "显示顺序必须在0-999之间"))]
    #[schema(example = 1)]
    pub order_num: Option<i32>,
    
    /// 路由地址
    #[validate(length(max = 200, message = "路由地址长度不能超过200"))]
    #[schema(example = "/system")]
    pub path: Option<String>,
    
    /// 组件路径
    #[validate(length(max = 255, message = "组件路径长度不能超过255"))]
    #[schema(example = "Layout")]
    pub component: Option<String>,
    
    /// 路由参数
    #[schema(value_type = Option<String>, example = "null")]
    pub query: Option<String>,
    
    /// 是否为外链（0是 1否）
    #[schema(example = 1)]
    pub is_frame: Option<i32>,
    
    /// 是否缓存（0缓存 1不缓存）
    #[schema(example = 0)]
    pub is_cache: Option<i32>,
    
    /// 菜单类型（1目录 2菜单 3按钮）
    #[validate(range(min = 1, max = 3, message = "菜单类型必须是1、2或3"))]
    #[schema(example = 1)]
    pub menu_type: Option<i32>,
    
    /// 显示状态（0显示 1隐藏）
    #[schema(example = 0)]
    pub visible: Option<i32>,
    
    /// 菜单状态（0正常 1停用）
    #[schema(example = 0)]
    pub status: Option<i32>,
    
    /// 权限标识
    #[validate(length(max = 100, message = "权限标识长度不能超过100"))]
    #[schema(example = "sys:permission:list")]
    pub perms: Option<String>,
    
    /// 菜单图标
    #[schema(example = "system")]
    pub icon: Option<String>,
    
    /// 备注
    #[schema(example = "系统管理菜单")]
    pub remark: Option<String>,
}

/// 权限菜单批量删除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct SysPermissionBatchDeleteRequest {
    /// 权限ID列表
    #[schema(example = json!(["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]))]
    pub permission_ids: Vec<Uuid>,
}

/// 权限菜单状态切换请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema)]
pub struct SysPermissionStatusRequest {
    /// 状态（0正常 1停用）
    #[validate(range(min = 0, max = 1, message = "状态值必须是0或1"))]
    #[schema(example = 0)]
    pub status: i32,
} 
use bon::Builder;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

/// 商户角色权限关联分页查询请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, IntoParams, Builder)]
#[into_params(parameter_in = Query)]
pub struct SysMerchantRolePermissionPageRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: Option<i64>,
    
    /// 角色ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub role_id: Option<Uuid>,
    
    /// 授权权限ID
    #[schema(example = "fedcba98-7654-3210-fedc-ba9876543201")]
    pub authorized_permission_id: Option<Uuid>,
    
    /// 当前页码
    #[builder(default = 1)]
    #[schema(example = 1)]
    pub page: u64,
    
    /// 每页大小
    #[builder(default = 10)]
    #[schema(example = 10)]
    pub page_size: u64,
    
    /// 创建开始时间
    #[schema(example = "2023-01-01")]
    pub created_start: Option<String>,
    
    /// 创建结束时间
    #[schema(example = "2023-12-31")]
    pub created_end: Option<String>,
}

/// 商户角色权限关联创建请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantRolePermissionCreateRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: i64,
    
    /// 角色ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub role_id: Uuid,
    
    /// 授权权限ID列表
    #[schema(example = json!(["fedcba98-7654-3210-fedc-ba9876543201", "fedcba98-7654-3210-fedc-ba9876543202"]))]
    pub authorized_permission_ids: Vec<Uuid>,
    
    /// 备注
    #[schema(example = "批量分配权限")]
    pub remark: Option<String>,
}

/// 角色权限批量分配请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantRolePermissionAssignRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: i64,
    
    /// 角色ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub role_id: Uuid,
    
    /// 授权权限ID列表（重新分配，覆盖原有权限）
    #[schema(example = json!(["fedcba98-7654-3210-fedc-ba9876543201", "fedcba98-7654-3210-fedc-ba9876543202"]))]
    pub authorized_permission_ids: Vec<Uuid>,
    
    /// 备注
    #[schema(example = "权限重新分配")]
    pub remark: Option<String>,
}

/// 商户角色权限关联批量删除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantRolePermissionBatchDeleteRequest {
    /// 角色权限关联ID列表
    #[schema(example = json!(["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]))]
    pub role_permission_ids: Vec<Uuid>,
}

/// 角色权限移除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantRolePermissionRemoveRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: i64,
    
    /// 角色ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub role_id: Uuid,
    
    /// 要移除的授权权限ID列表
    #[schema(example = json!(["fedcba98-7654-3210-fedc-ba9876543201"]))]
    pub authorized_permission_ids: Vec<Uuid>,
}

/// 角色权限复制请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantRolePermissionCopyRequest {
    /// 商户ID
    #[schema(example = 1001)]
    pub merchant_id: i64,
    
    /// 源角色ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub source_role_id: Uuid,
    
    /// 目标角色ID列表
    #[schema(example = json!(["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]))]
    pub target_role_ids: Vec<Uuid>,
    
    /// 是否覆盖目标角色的现有权限
    #[builder(default = false)]
    #[schema(example = false)]
    pub overwrite: bool,
    
    /// 备注
    #[schema(example = "权限复制操作")]
    pub remark: Option<String>,
}

// ============================================================================
// 简化的映射函数
// ============================================================================

use crate::domain::business::merchants::entities::merchant_role_permissions::ActiveModel;
use crate::utils::datetime::DateTimeUtils;
use chrono::{DateTime, FixedOffset};
use sea_orm::Set;

impl SysMerchantRolePermissionCreateRequest {
    /// 转换为ActiveModel列表
    pub fn to_active_models(self, operator_id: Option<Uuid>) -> Vec<ActiveModel> {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();
        
        self.authorized_permission_ids.into_iter().map(|authorized_permission_id| ActiveModel {
            id: Set(Uuid::now_v7()),
            merchant_id: Set(self.merchant_id),
            role_id: Set(self.role_id),
            authorized_permission_id: Set(authorized_permission_id),
            created_date: Set(now),
            created_by: Set(operator_id),
            remark: Set(self.remark.clone()),
        }).collect()
    }
}

impl SysMerchantRolePermissionAssignRequest {
    /// 转换为ActiveModel列表（用于权限重新分配）
    pub fn to_active_models(self, operator_id: Option<Uuid>) -> Vec<ActiveModel> {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();
        
        self.authorized_permission_ids.into_iter().map(|authorized_permission_id| ActiveModel {
            id: Set(Uuid::now_v7()),
            merchant_id: Set(self.merchant_id),
            role_id: Set(self.role_id),
            authorized_permission_id: Set(authorized_permission_id),
            created_date: Set(now),
            created_by: Set(operator_id),
            remark: Set(self.remark.clone()),
        }).collect()
    }
} 
use crate::utils::validation::validate_phone_custom;
use bon::Builder;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

/// 商户用户分页查询请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, IntoParams, Builder)]
#[into_params(parameter_in = Query)]
pub struct SysMerchantUserPageRequest {
    /// 用户名
    #[schema(example = "zhangwei")]
    pub username: Option<String>,

    /// 真实姓名
    #[schema(example = "张伟")]
    pub real_name: Option<String>,

    /// 手机号码
    #[schema(example = "13800138001")]
    pub phone: Option<String>,

    /// 邮箱地址
    #[schema(example = "<EMAIL>")]
    pub email: Option<String>,

    /// 性别（1男 2女 3未知）
    #[schema(example = 1)]
    pub gender: Option<i32>,

    /// 用户状态（1启用 2禁用 3锁定）
    #[schema(example = 1)]
    pub status: Option<i32>,

    /// 当前页码
    #[validate(range(min = 1, message = "页码必须从1开始"))]
    #[schema(example = 1)]
    pub page: Option<u64>,

    /// 每页大小
    #[validate(range(min = 1, max = 100, message = "每页大小必须在1-100之间"))]
    #[schema(example = 10)]
    pub page_size: Option<u64>,

    /// 创建开始时间
    #[schema(example = "2023-01-01")]
    pub created_start: Option<String>,

    /// 创建结束时间
    #[schema(example = "2023-12-31")]
    pub created_end: Option<String>,

    /// 搜索关键词（用户名、真实姓名、手机号）
    #[schema(example = "张伟")]
    pub keyword: Option<String>,
}

/// 商户用户创建请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUserCreateRequest {
    /// 用户名
    #[validate(length(min = 1, max = 50, message = "用户名长度必须在1-50之间"))]
    #[schema(example = "zhangwei")]
    pub username: String,

    /// 密码
    #[validate(length(min = 6, max = 255, message = "密码长度必须在6-255之间"))]
    #[schema(example = "admin123")]
    pub password: String,

    /// 真实姓名
    #[validate(length(min = 1, max = 100, message = "真实姓名长度必须在1-100之间"))]
    #[schema(example = "张伟")]
    pub real_name: String,

    /// 手机号码
    #[validate(length(min = 1, max = 20, message = "手机号码长度必须在1-20之间"))]
    #[validate(custom(function = "validate_phone_custom"))]
    #[schema(example = "13800138001")]
    pub phone: String,

    /// 邮箱地址
    #[validate(email(message = "邮箱格式不正确"))]
    #[validate(length(max = 100, message = "邮箱长度不能超过100"))]
    #[schema(example = "<EMAIL>")]
    pub email: Option<String>,

    /// 头像URL
    #[schema(example = "/uploads/avatars/zhangwei.jpg")]
    pub avatar: Option<String>,

    /// 性别（1男 2女 3未知）
    #[builder(default = 3)]
    #[validate(range(min = 1, max = 3, message = "性别值必须是1、2或3"))]
    #[schema(example = 1)]
    pub gender: i32,

    /// 身份证号码
    #[validate(length(max = 18, message = "身份证号码长度不能超过18"))]
    #[schema(example = "11010119801201001X")]
    pub id_card: Option<String>,

    /// 用户状态（1启用 2禁用 3锁定）
    #[builder(default = 1)]
    #[validate(range(min = 1, max = 3, message = "状态值必须是1、2或3"))]
    #[schema(example = 1)]
    pub status: i32,

    /// 备注
    #[schema(example = "系统管理员")]
    pub remark: Option<String>,
    /// 默认商户ID
    default_merchant_id: Option<i64>
}

/// 商户用户更新请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUserUpdateRequest {
    /// 用户名
    #[validate(length(min = 1, max = 50, message = "用户名长度必须在1-50之间"))]
    #[schema(example = "zhangwei")]
    pub username: String,

    /// 真实姓名
    #[validate(length(min = 1, max = 100, message = "真实姓名长度必须在1-100之间"))]
    #[schema(example = "张伟")]
    pub real_name: String,

    /// 手机号码
    #[validate(length(min = 1, max = 20, message = "手机号码长度必须在1-20之间"))]
    #[validate(custom(function = "validate_phone_custom"))]
    #[schema(example = "13800138001")]
    pub phone: String,

    /// 邮箱地址
    #[validate(email(message = "邮箱格式不正确"))]
    #[validate(length(max = 100, message = "邮箱长度不能超过100"))]
    #[schema(example = "<EMAIL>")]
    pub email: Option<String>,

    /// 头像URL
    #[schema(example = "/uploads/avatars/zhangwei.jpg")]
    pub avatar: Option<String>,

    /// 性别（1男 2女 3未知）
    #[validate(range(min = 1, max = 3, message = "性别值必须是1、2或3"))]
    #[schema(example = 1)]
    pub gender: i32,

    /// 身份证号码
    #[validate(length(max = 18, message = "身份证号码长度不能超过18"))]
    #[schema(example = "11010119801201001X")]
    pub id_card: Option<String>,

    /// 用户状态（1启用 2禁用 3锁定）
    #[validate(range(min = 1, max = 3, message = "状态值必须是1、2或3"))]
    #[schema(example = 1)]
    pub status: i32,

    /// 备注
    #[schema(example = "系统管理员")]
    pub remark: Option<String>,
}

/// 商户用户密码重置请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUserResetPasswordRequest {
    /// 新密码
    #[validate(length(min = 6, max = 255, message = "密码长度必须在6-255之间"))]
    #[schema(example = "newpassword123")]
    pub new_password: String,
}

/// 商户用户批量删除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUserBatchDeleteRequest {
    /// 用户ID列表
    #[schema(example = json!(["f47ac10b-58cc-4372-a567-0e02b2c3d479", "6ba7b810-9dad-11d1-80b4-00c04fd430c8"]))]
    pub user_ids: Vec<Uuid>,
}

/// 商户用户状态切换请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUserStatusRequest {
    /// 状态（1启用 2禁用 3锁定）
    #[validate(range(min = 1, max = 3, message = "状态值必须是1、2或3"))]
    #[schema(example = 1)]
    pub status: i32,
}

// ============================================================================
// 简化的映射函数
// ============================================================================

use crate::domain::business::merchants::entities::merchant_users::{ActiveModel, MerchantUserGender, MerchantUserStatus};
use crate::utils::datetime::DateTimeUtils;
use chrono::{DateTime, FixedOffset};
use sea_orm::{ActiveEnum, NotSet, Set};

impl SysMerchantUserCreateRequest {
    /// 转换为ActiveModel
    pub fn to_active_model(self, operator_id: Option<Uuid>) -> ActiveModel {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        ActiveModel {
            id: Set(Uuid::now_v7()),
            username: Set(self.username),
            password: Set(self.password), // 注意：实际使用时需要先进行密码哈希处理
            real_name: Set(self.real_name),
            phone: Set(self.phone),
            email: Set(self.email),
            avatar: Set(self.avatar),
            gender: Set(Some(MerchantUserGender::try_from_value(&self.gender).unwrap_or(MerchantUserGender::Unknown))),
            id_card: Set(self.id_card),
            status: Set(MerchantUserStatus::try_from_value(&self.status).unwrap_or(MerchantUserStatus::Enabled)),
            default_merchant_id: Set(self.default_merchant_id),
            last_login_date: Set(None),
            last_login_ip: Set(None),
            created_date: Set(now),
            updated_date: Set(now),
            created_by: Set(operator_id),
            updated_by: Set(operator_id),
            remark: Set(self.remark),
            wechat_openid: NotSet,
            wechat_nickname: NotSet,
            wechat_avatar: NotSet,
        }
    }

    /// 转换为ActiveModel（带密码哈希）
    pub fn to_active_model_with_hashed_password(
        self,
        hashed_password: String,
        operator_id: Option<Uuid>,
    ) -> ActiveModel {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        ActiveModel {
            id: Set(Uuid::now_v7()),
            username: Set(self.username),
            password: Set(hashed_password),
            real_name: Set(self.real_name),
            phone: Set(self.phone),
            email: Set(self.email),
            avatar: Set(self.avatar),
            gender: Set(Some(MerchantUserGender::try_from_value(&self.gender).unwrap_or(MerchantUserGender::Unknown))),
            id_card: Set(self.id_card),
            status: Set(MerchantUserStatus::try_from_value(&self.status).unwrap_or(MerchantUserStatus::Enabled)),
            default_merchant_id: Set(self.default_merchant_id),
            last_login_date: Set(None),
            last_login_ip: Set(None),
            created_date: Set(now),
            updated_date: Set(now),
            created_by: Set(operator_id),
            updated_by: Set(operator_id),
            remark: Set(self.remark),
            wechat_openid: NotSet,
            wechat_nickname: NotSet,
            wechat_avatar: NotSet,
        }
    }
}

impl SysMerchantUserUpdateRequest {
    /// 更新ActiveModel
    pub fn update_active_model(
        self,
        mut existing: ActiveModel,
        operator_id: Option<Uuid>,
    ) -> ActiveModel {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        existing.username = Set(self.username);
        existing.real_name = Set(self.real_name);
        existing.phone = Set(self.phone);
        existing.email = Set(self.email);
        existing.avatar = Set(self.avatar);
        existing.gender = Set(Some(MerchantUserGender::try_from_value(&self.gender).unwrap_or(MerchantUserGender::Unknown)));
        existing.id_card = Set(self.id_card);
        existing.status = Set(MerchantUserStatus::try_from_value(&self.status).unwrap_or(MerchantUserStatus::Enabled));
        existing.updated_date = Set(now);
        existing.updated_by = Set(operator_id);
        existing.remark = Set(self.remark);

        existing
    }
}

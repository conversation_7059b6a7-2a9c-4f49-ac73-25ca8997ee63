use serde::Deserialize;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

// ==================== 角色分页查询相关 ====================

/// 角色分页查询请求参数
#[derive(Debug, Deserialize, Validate, IntoParams, ToSchema)]
#[schema(example = json!({
    "page": 1,
    "page_size": 10,
    "name": "系统管理员",
    "description": "管理员",
    "created_start": "2023-01-01",
    "created_end": "2023-12-31"
}))]
pub struct SysRolePageRequest {
    /// 当前页码 (1开始)
    #[validate(range(min = 1, message = "页码必须从1开始"))]
    pub page: Option<u64>,

    /// 每页大小
    #[validate(range(min = 1, max = 100, message = "每页大小必须在1-100之间"))]
    pub page_size: Option<u64>,

    /// 角色名称关键字搜索
    pub name: Option<String>,

    /// 角色描述关键字搜索
    pub description: Option<String>,

    /// 创建时间开始 (YYYY-MM-DD格式)
    pub created_start: Option<String>,

    /// 创建时间结束 (YYYY-MM-DD格式)
    pub created_end: Option<String>,
}

// ==================== 角色创建相关 ====================

/// 角色创建请求DTO
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "name": "部门经理",
    "description": "部门管理权限",
    "permission_ids": ["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"],
    "remark": "部门级别管理角色"
}))]
pub struct SysRoleCreateRequest {
    /// 角色名称
    #[validate(length(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间"))]
    pub name: String,

    /// 角色描述
    #[validate(length(max = 200, message = "角色描述长度不能超过200个字符"))]
    pub description: Option<String>,

    /// 权限ID集合
    pub permission_ids: Option<Vec<Uuid>>,

    /// 备注
    #[validate(length(max = 500, message = "备注长度不能超过500个字符"))]
    pub remark: Option<String>,
}

// ==================== 角色更新相关 ====================

/// 角色更新请求DTO
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct SysRoleUpdateRequest {
    /// 角色名称
    #[validate(length(min = 2, max = 50, message = "角色名称长度必须在2-50个字符之间"))]
    pub name: String,

    /// 角色描述
    #[validate(length(max = 200, message = "角色描述长度不能超过200个字符"))]
    pub description: Option<String>,

    /// 备注
    #[validate(length(max = 500, message = "备注长度不能超过500个字符"))]
    pub remark: Option<String>,
}

// ==================== 角色权限分配相关 ====================

/// 角色权限分配请求DTO
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "permission_ids": ["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]
}))]
pub struct SysRolePermissionRequest {
    /// 权限ID集合
    #[validate(length(min = 0, max = 1000, message = "权限数量不能超过1000个"))]
    pub permission_ids: Vec<Uuid>,
}

// ==================== 角色批量操作相关 ====================

/// 批量删除角色请求DTO
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct SysRoleBatchDeleteRequest {
    /// 角色ID集合
    #[validate(length(min = 1, max = 100, message = "批量删除角色数量必须在1-100之间"))]
    pub role_ids: Vec<Uuid>,
} 
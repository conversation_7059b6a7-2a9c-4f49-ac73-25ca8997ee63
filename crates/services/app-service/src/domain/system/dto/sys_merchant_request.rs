use bon::Builder;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::{Validate, ValidationError};

/// 地理位置信息
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Clone)]
pub struct LocationRequest {
    /// 经度
    #[validate(range(min = -180.0, max = 180.0, message = "经度必须在-180到180之间"))]
    #[schema(example = 116.4074)]
    pub longitude: f64,

    /// 纬度
    #[validate(range(min = -90.0, max = 90.0, message = "纬度必须在-90到90之间"))]
    #[schema(example = 39.9042)]
    pub latitude: f64,
}

/// 商户分页查询请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, IntoParams, Builder)]
#[into_params(parameter_in = Query)]
pub struct SysMerchantPageRequest {
    /// 商户名称
    #[schema(example = "张记私房菜")]
    pub merchant_name: Option<String>,

    /// 商户编码
    #[schema(example = "ZJ_SFC_001")]
    pub merchant_code: Option<String>,

    /// 商户分类ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub category_id: Option<Uuid>,

    /// 联系电话
    #[schema(example = "13800138001")]
    pub phone: Option<String>,

    /// 邮箱地址
    #[schema(example = "<EMAIL>")]
    pub email: Option<String>,

    /// 商户状态（1正常营业 2临时关闭 3永久关闭）
    #[schema(example = 1)]
    pub status: Option<i32>,

    /// 当前页码
    #[validate(range(min = 1, message = "页码必须从1开始"))]
    #[schema(example = 1)]
    pub page: Option<u64>,

    /// 每页大小
    #[validate(range(min = 1, max = 100, message = "每页大小必须在1-100之间"))]
    #[schema(example = 10)]
    pub page_size: Option<u64>,

    /// 创建开始时间
    #[schema(example = "2023-01-01")]
    pub created_start: Option<String>,

    /// 创建结束时间
    #[schema(example = "2023-12-31")]
    pub created_end: Option<String>,
}

/// 商户创建请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantCreateRequest {
    /// 商户名称
    #[validate(length(min = 1, max = 200, message = "商户名称长度必须在1-200之间"))]
    #[schema(example = "张记私房菜")]
    pub merchant_name: String,

    /// 商户编码
    #[validate(length(min = 1, max = 50, message = "商户编码长度必须在1-50之间"))]
    #[schema(example = "ZJ_SFC_001")]
    pub merchant_code: String,

    /// 商户分类ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub category_id: Option<Uuid>,

    /// 联系电话
    #[validate(length(max = 20, message = "联系电话长度不能超过20"))]
    #[schema(example = "13800138001")]
    pub phone: Option<String>,

    /// 邮箱地址
    #[validate(email(message = "邮箱格式不正确"))]
    #[validate(length(max = 100, message = "邮箱长度不能超过100"))]
    #[schema(example = "<EMAIL>")]
    pub email: Option<String>,

    /// 经营地址
    #[schema(example = "北京市朝阳区三里屯街道工体北路8号")]
    pub address: Option<String>,

    /// 地理位置
    #[validate(nested)]
    #[schema(example = json!({"longitude": 116.4074, "latitude": 39.9042}))]
    pub location: Option<LocationRequest>,

    /// 营业执照号
    #[validate(length(max = 100, message = "营业执照号长度不能超过100"))]
    #[schema(example = "110000001234567890")]
    pub business_license: Option<String>,

    /// 营业执照照片URL
    #[schema(example = "/uploads/license/zhangji_license.jpg")]
    pub license_photo: Option<String>,

    /// 商户头像URL
    #[schema(example = "/uploads/avatars/zhangji_logo.jpg")]
    pub avatar: Option<String>,

    /// 商户描述
    #[schema(example = "专注传统川菜30年，口味正宗，食材新鲜")]
    pub description: Option<String>,

    /// 平台抽成比例(字符串格式，支持百分比和小数，如"3%"或"0.03")
    #[validate(custom(function = "validate_platform_commission_rate"))]
    #[schema(example = "3%")]
    pub platform_commission_rate: Option<String>,

    /// 商户状态（1正常营业 2临时关闭 3永久关闭）
    #[builder(default = 1)]
    #[validate(range(min = 1, max = 3, message = "状态值必须是1、2或3"))]
    #[schema(example = 1)]
    pub status: i32,

    /// 账单自动清零日期(YYYY-MM-DD格式)
    #[schema(example = "01-01")]
    pub auto_clear_date: Option<String>,

    /// 排序字段
    #[validate(range(min = 0, max = 999, message = "排序字段必须在0-999之间"))]
    #[schema(example = 1)]
    pub sort_order: Option<i32>,

    /// 备注
    #[schema(example = "知名餐饮连锁品牌")]
    pub remark: Option<String>,
}

/// 商户更新请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantUpdateRequest {
    /// 商户名称
    #[validate(length(min = 1, max = 200, message = "商户名称长度必须在1-200之间"))]
    #[schema(example = "张记私房菜")]
    pub merchant_name: String,

    /// 商户编码
    #[validate(length(min = 1, max = 50, message = "商户编码长度必须在1-50之间"))]
    #[schema(example = "ZJ_SFC_001")]
    pub merchant_code: String,

    /// 商户分类ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub category_id: Option<Uuid>,

    /// 联系电话
    #[validate(length(max = 20, message = "联系电话长度不能超过20"))]
    #[schema(example = "13800138001")]
    pub phone: Option<String>,

    /// 邮箱地址
    #[validate(email(message = "邮箱格式不正确"))]
    #[validate(length(max = 100, message = "邮箱长度不能超过100"))]
    #[schema(example = "<EMAIL>")]
    pub email: Option<String>,

    /// 经营地址
    #[schema(example = "北京市朝阳区三里屯街道工体北路8号")]
    pub address: Option<String>,

    /// 地理位置
    #[validate(nested)]
    #[schema(example = json!({"longitude": 116.4074, "latitude": 39.9042}))]
    pub location: Option<LocationRequest>,

    /// 营业执照号
    #[validate(length(max = 100, message = "营业执照号长度不能超过100"))]
    #[schema(example = "110000001234567890")]
    pub business_license: Option<String>,

    /// 营业执照照片URL
    #[schema(example = "/uploads/license/zhangji_license.jpg")]
    pub license_photo: Option<String>,

    /// 商户头像URL
    #[schema(example = "/uploads/avatars/zhangji_logo.jpg")]
    pub avatar: Option<String>,

    /// 商户描述
    #[schema(example = "专注传统川菜30年，口味正宗，食材新鲜")]
    pub description: Option<String>,

    /// 平台抽成比例(字符串格式，支持百分比和小数，如"3%"或"0.03")
    #[validate(custom(function = "validate_platform_commission_rate"))]
    #[schema(example = "3%")]
    pub platform_commission_rate: Option<String>,

    /// 商户状态（1正常营业 2临时关闭 3永久关闭）
    #[validate(range(min = 1, max = 3, message = "状态值必须是1、2或3"))]
    #[schema(example = 1)]
    pub status: i32,

    /// 账单自动清零日期(MM-DD格式)
    #[schema(example = "01-01")]
    pub auto_clear_date: Option<String>,

    /// 排序字段
    #[validate(range(min = 0, max = 999, message = "排序字段必须在0-999之间"))]
    #[schema(example = 1)]
    pub sort_order: Option<i32>,

    /// 备注
    #[schema(example = "知名餐饮连锁品牌")]
    pub remark: Option<String>,
}

/// 商户批量删除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantBatchDeleteRequest {
    /// 商户ID列表
    #[schema(example = json!([1001, 1002, 1003]))]
    pub merchant_ids: Vec<i64>,
}

/// 商户状态切换请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantStatusRequest {
    /// 状态（1正常营业 2临时关闭 3永久关闭）
    #[validate(range(min = 1, max = 3, message = "状态值必须是1、2或3"))]
    #[schema(example = 1)]
    pub status: i32,
}

/// 商户平台佣金设置请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantPlatformCommissionRequest {
    /// 平台佣金比例（支持百分比和小数，如"5%"或"0.05"）
    #[validate(custom(function = "validate_platform_commission_rate"))]
    #[schema(example = "5%")]
    pub platform_commission_rate: String,
}

/// 商户跟进人佣金设置请求
#[derive(
    Debug, Deserialize, Serialize, Validate, ToSchema, Builder, From, Into, Clone, Constructor,
)]
pub struct SysMerchantFollowerCommissionRequest {
    /// 跟进人ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub follower_id: Uuid,

    /// 跟进人佣金类型（1百分比 2固定金额）
    #[validate(range(min = 1, max = 2, message = "佣金类型必须是1（百分比）或2（固定金额）"))]
    #[schema(example = 1)]
    pub follower_commission_type: i32,

    /// 跟进人佣金值
    /// - 当类型为1（百分比）时，支持百分比和小数格式，如"2.5%"或"0.025"
    /// - 当类型为2（固定金额）时，值应大于等于0
    #[validate(custom(function = "validate_follower_commission_value"))]
    #[schema(example = "2.5%")]
    pub follower_commission_value: String,

    /// 跟进人佣金备注
    #[validate(length(max = 500, message = "备注长度不能超过500"))]
    #[schema(example = "优秀业务员，给予额外佣金激励")]
    pub follower_commission_remark: Option<String>,
}

/// 商户跟进人设置请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantFollowerRequest {
    /// 跟进人ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub follower_id: Option<Uuid>,

    /// 跟进人姓名
    #[validate(length(max = 50, message = "跟进人姓名长度不能超过50"))]
    #[schema(example = "李四")]
    pub follower_name: Option<String>,

    /// 跟进人电话
    #[validate(length(max = 20, message = "跟进人电话长度不能超过20"))]
    #[schema(example = "13900139000")]
    pub follower_phone: Option<String>,

    /// 跟进人佣金比例（支持百分比和小数，如"2%"或"0.02"）
    #[validate(custom(function = "validate_platform_commission_rate"))]
    #[schema(example = "2%")]
    pub follower_commission_rate: Option<String>,
}

/// 获取商户基础信息列表请求参数
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysMerchantListRequest {
    /// 商户名称
    #[schema(example = "辣椒小馆")]
    #[validate(length(min = 1, max = 20, message = "商户名称长度必须在1-20之间"))]
    pub merchant_name: Option<String>,
}

// ============================================================================
// DTO 转 实体类 - 转换方法
// ============================================================================

use crate::domain::business::merchants::entities::merchants::{ActiveModel, MerchantStatus};
use crate::utils::commission_utils::CommissionUtils;
use crate::utils::datetime::DateTimeUtils;
use chrono::{DateTime, FixedOffset};
use derive_more::with_trait::From;
use derive_more::{Constructor, Into};
use sea_orm::{ActiveEnum, Set};

impl SysMerchantCreateRequest {
    /// 转换为 ActiveModel
    pub fn to_active_model(self, operator_id: Option<Uuid>) -> Result<ActiveModel, anyhow::Error> {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        let location_json = if let Some(loc) = self.location {
            Some(serde_json::to_value(
                crate::domain::business::merchants::entities::merchants::Location::new(
                    loc.longitude,
                    loc.latitude,
                ),
            )?)
        } else {
            None
        };

        let auto_clear_date = if let Some(date_str) = self.auto_clear_date {
            Some(chrono::NaiveDate::parse_from_str(
                &format!("2024-{}", date_str),
                "%Y-%m-%d",
            )?)
        } else {
            None
        };

        let platform_commission_rate = if let Some(rate_str) = self.platform_commission_rate {
            Some(
                CommissionUtils::parse_platform_commission(&rate_str)
                    .map_err(|e| anyhow::anyhow!("平台佣金比例转换失败: {}", e))?,
            )
        } else {
            None
        };

        Ok(ActiveModel {
            id: sea_orm::NotSet,
            merchant_name: Set(self.merchant_name),
            merchant_code: Set(self.merchant_code),
            category_id: Set(self.category_id),
            phone: Set(self.phone),
            email: Set(self.email),
            address: Set(self.address),
            location: Set(location_json),
            business_license: Set(self.business_license),
            license_photo: Set(self.license_photo),
            avatar: Set(self.avatar),
            description: Set(self.description),
            platform_commission_rate: Set(platform_commission_rate),
            status: Set(
                MerchantStatus::try_from_value(&self.status).unwrap_or(MerchantStatus::Active)
            ),
            auto_clear_date: Set(auto_clear_date),
            sort_order: Set(self.sort_order),
            created_date: Set(now),
            updated_date: Set(now),
            created_by: Set(operator_id),
            updated_by: Set(operator_id),
            remark: Set(self.remark),
        })
    }
}

impl SysMerchantUpdateRequest {
    /// 更新 ActiveModel
    pub fn update_active_model(
        self,
        mut existing: ActiveModel,
        operator_id: Option<Uuid>,
    ) -> Result<ActiveModel, anyhow::Error> {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        let location_json = if let Some(loc) = self.location {
            Some(serde_json::to_value(
                crate::domain::business::merchants::entities::merchants::Location::new(
                    loc.longitude,
                    loc.latitude,
                ),
            )?)
        } else {
            None
        };

        let auto_clear_date = if let Some(date_str) = self.auto_clear_date {
            Some(chrono::NaiveDate::parse_from_str(
                &format!("2024-{}", date_str),
                "%Y-%m-%d",
            )?)
        } else {
            None
        };

        let platform_commission_rate = if let Some(rate_str) = self.platform_commission_rate {
            Some(
                CommissionUtils::parse_platform_commission(&rate_str)
                    .map_err(|e| anyhow::anyhow!("平台佣金比例转换失败: {}", e))?,
            )
        } else {
            None
        };

        existing.merchant_name = Set(self.merchant_name);
        existing.merchant_code = Set(self.merchant_code);
        existing.category_id = Set(self.category_id);
        existing.phone = Set(self.phone);
        existing.email = Set(self.email);
        existing.address = Set(self.address);
        existing.location = Set(location_json);
        existing.business_license = Set(self.business_license);
        existing.license_photo = Set(self.license_photo);
        existing.avatar = Set(self.avatar);
        existing.description = Set(self.description);
        existing.platform_commission_rate = Set(platform_commission_rate);
        existing.status =
            Set(MerchantStatus::try_from_value(&self.status).unwrap_or(MerchantStatus::Active));
        existing.auto_clear_date = Set(auto_clear_date);
        existing.sort_order = Set(self.sort_order);
        existing.updated_date = Set(now);
        existing.updated_by = Set(operator_id);
        existing.remark = Set(self.remark);

        Ok(existing)
    }
}

// ============================================================================
// 验证函数
// ============================================================================

/// 验证平台佣金比例
pub fn validate_platform_commission_rate(rate_str: &str) -> Result<(), ValidationError> {
    CommissionUtils::validate_platform_commission(rate_str)
}

/// 验证跟进人佣金值
///
/// 由于validator限制，这里只能做基本格式验证
/// 具体的类型验证需要在业务逻辑层进行
pub fn validate_follower_commission_value(value_str: &str) -> Result<(), ValidationError> {
    // 基本格式验证
    if value_str.trim().is_empty() {
        return Err(ValidationError::new("佣金值不能为空"));
    }

    // 支持百分比格式
    if value_str.ends_with('%') {
        return CommissionUtils::validate_platform_commission(value_str);
    }

    // 支持数字格式
    match value_str.parse::<f64>() {
        Ok(value) => {
            if value < 0.0 {
                Err(ValidationError::new("佣金值不能为负数"))
            } else if value > 999999.0 {
                Err(ValidationError::new("佣金值不能超过999999"))
            } else {
                Ok(())
            }
        }
        Err(_) => Err(ValidationError::new("佣金值格式不正确")),
    }
}

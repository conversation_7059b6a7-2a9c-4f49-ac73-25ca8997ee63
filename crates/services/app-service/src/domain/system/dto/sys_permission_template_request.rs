use bon::Builder;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

/// 系统权限模板分页查询请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, IntoParams, Builder)]
#[into_params(parameter_in = Query)]
pub struct SysPermissionTemplatePageRequest {
    /// 权限名称
    #[schema(example = "用户管理")]
    pub permission_name: Option<String>,

    /// 权限编码
    #[schema(example = "system:user:list")]
    pub permission_code: Option<String>,

    /// 父权限ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub parent_id: Option<Uuid>,

    /// 权限类型（1目录 2菜单 3按钮）
    #[schema(example = 2)]
    pub permission_type: Option<i32>,

    /// 菜单状态（0显示 1隐藏）
    #[schema(example = 0)]
    pub visible: Option<i32>,

    /// 当前页码
    #[builder(default = 1)]
    #[schema(example = 1)]
    pub page: u64,

    /// 每页大小
    #[builder(default = 10)]
    #[schema(example = 10)]
    pub page_size: u64,
}

/// 系统权限模板创建请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
#[schema(example = json!({
    "permission_name": "用户管理",
    "permission_code": "system:user:list",
    "parent_id": null,
    "order_num": 1,
    "path": "user",
    "component": "system/user/index",
    "permission_type": 2,
    "visible": 0,
    "icon": "user",
    "description": "系统用户管理功能"
}))]
pub struct SysPermissionTemplateCreateRequest {
    /// 权限名称
    #[validate(length(min = 1, max = 100, message = "权限名称长度必须在1-100之间"))]
    #[schema(example = "用户管理")]
    pub permission_name: String,

    /// 权限编码
    #[validate(length(min = 1, max = 100, message = "权限编码长度必须在1-100之间"))]
    #[schema(example = "system:user:list")]
    pub permission_code: String,

    /// 父权限ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub parent_id: Option<Uuid>,

    /// 显示顺序
    #[builder(default = 0)]
    #[schema(example = 1)]
    pub order_num: i32,

    /// 路由地址
    #[validate(length(max = 200, message = "路由地址长度不能超过200"))]
    #[schema(example = "user")]
    pub path: Option<String>,

    /// 组件路径
    #[validate(length(max = 255, message = "组件路径长度不能超过255"))]
    #[schema(example = "system/user/index")]
    pub component: Option<String>,

    /// 路由参数
    #[validate(length(max = 255, message = "路由参数长度不能超过255"))]
    #[schema(example = "{\"id\":1}")]
    pub query: Option<String>,

    /// 是否为外链（0是 1否）
    #[builder(default = 1)]
    #[validate(range(min = 0, max = 1, message = "是否外链只能是0或1"))]
    #[schema(example = 1)]
    pub is_frame: i32,

    /// 是否缓存（0缓存 1不缓存）
    #[builder(default = 0)]
    #[validate(range(min = 0, max = 1, message = "是否缓存只能是0或1"))]
    #[schema(example = 0)]
    pub is_cache: i32,

    /// 权限类型（1目录 2菜单 3按钮）
    #[builder(default = 2)]
    #[validate(range(min = 1, max = 3, message = "权限类型只能是1、2或3"))]
    #[schema(example = 2)]
    pub permission_type: i32,

    /// 菜单状态（0显示 1隐藏）
    #[builder(default = 0)]
    #[validate(range(min = 0, max = 1, message = "菜单状态只能是0或1"))]
    #[schema(example = 0)]
    pub visible: i32,

    /// 权限图标
    #[builder(default = "#".to_string())]
    #[validate(length(max = 100, message = "图标长度不能超过100"))]
    #[schema(example = "user")]
    pub icon: String,

    /// 权限描述
    #[schema(example = "系统用户管理功能")]
    pub description: Option<String>,

    /// 备注
    #[validate(length(max = 500, message = "备注长度不能超过500字符"))]
    #[schema(example = "基础权限")]
    pub remark: Option<String>,
}

/// 系统权限模板更新请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysPermissionTemplateUpdateRequest {
    /// 权限名称
    #[validate(length(min = 1, max = 100, message = "权限名称长度必须在1-100之间"))]
    #[schema(example = "用户管理")]
    pub permission_name: String,

    /// 权限编码
    #[validate(length(min = 1, max = 100, message = "权限编码长度必须在1-100之间"))]
    #[schema(example = "system:user:list")]
    pub permission_code: String,

    /// 父权限ID
    #[schema(example = "550e8400-e29b-41d4-a716-************")]
    pub parent_id: Option<Uuid>,

    /// 显示顺序
    #[schema(example = 1)]
    pub order_num: i32,

    /// 路由地址
    #[validate(length(max = 200, message = "路由地址长度不能超过200"))]
    #[schema(example = "user")]
    pub path: Option<String>,

    /// 组件路径
    #[validate(length(max = 255, message = "组件路径长度不能超过255"))]
    #[schema(example = "system/user/index")]
    pub component: Option<String>,

    /// 路由参数
    #[validate(length(max = 255, message = "路由参数长度不能超过255"))]
    #[schema(example = "{\"id\":1}")]
    pub query: Option<String>,

    /// 是否为外链（0是 1否）
    #[validate(range(min = 0, max = 1, message = "是否外链只能是0或1"))]
    #[schema(example = 1)]
    pub is_frame: i32,

    /// 是否缓存（0缓存 1不缓存）
    #[validate(range(min = 0, max = 1, message = "是否缓存只能是0或1"))]
    #[schema(example = 0)]
    pub is_cache: i32,

    /// 权限类型（1目录 2菜单 3按钮）
    #[validate(range(min = 1, max = 3, message = "权限类型只能是1、2或3"))]
    #[schema(example = 2)]
    pub permission_type: i32,

    /// 菜单状态（0显示 1隐藏）
    #[validate(range(min = 0, max = 1, message = "菜单状态只能是0或1"))]
    #[schema(example = 0)]
    pub visible: i32,

    /// 权限图标
    #[validate(length(max = 100, message = "图标长度不能超过100"))]
    #[schema(example = "user")]
    pub icon: String,

    /// 权限描述
    #[schema(example = "系统用户管理功能")]
    pub description: Option<String>,

    /// 备注
    #[validate(length(max = 500, message = "备注长度不能超过500字符"))]
    #[schema(example = "基础权限")]
    pub remark: Option<String>,
}

/// 系统权限模板批量删除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysPermissionTemplateBatchDeleteRequest {
    /// 权限模板ID列表
    #[validate(length(min = 1, message = "权限模板ID列表不能为空"))]
    #[schema(example = json!(["550e8400-e29b-41d4-a716-************", "550e8400-e29b-41d4-a716-************"]))]
    pub template_ids: Vec<Uuid>,
}

/// 系统权限模板状态切换请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder)]
pub struct SysPermissionTemplateStatusRequest {
    /// 菜单状态（0显示 1隐藏）
    #[validate(range(min = 0, max = 1, message = "菜单状态只能是0或1"))]
    #[schema(example = 0)]
    pub visible: i32,
}

// ============================================================================
// 简化的映射函数
// ============================================================================

use crate::domain::business::merchants::entities::system_permission_templates::ActiveModel;
use crate::utils::datetime::DateTimeUtils;
use chrono::{DateTime, FixedOffset};
use sea_orm::Set;

impl SysPermissionTemplateCreateRequest {
    /// 转换为ActiveModel
    pub fn to_active_model(self, operator_id: Option<Uuid>) -> ActiveModel {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        ActiveModel {
            id: Set(Uuid::now_v7()),
            permission_name: Set(self.permission_name),
            permission_code: Set(self.permission_code),
            parent_id: Set(self.parent_id),
            order_num: Set(Some(self.order_num)),
            path: Set(self.path),
            component: Set(self.component),
            query: Set(self.query),
            is_frame: Set(Some(self.is_frame)),
            is_cache: Set(Some(self.is_cache)),
            permission_type: Set(Some(self.permission_type)),
            visible: Set(Some(self.visible)),
            icon: Set(Some(self.icon)),
            description: Set(self.description),
            created_date: Set(now),
            updated_date: Set(now),
            created_by: Set(operator_id),
            updated_by: Set(operator_id),
            remark: Set(self.remark),
        }
    }
}

impl SysPermissionTemplateUpdateRequest {
    /// 更新ActiveModel
    pub fn update_active_model(
        self,
        mut existing: ActiveModel,
        operator_id: Option<Uuid>,
    ) -> ActiveModel {
        let now: DateTime<FixedOffset> = DateTimeUtils::now_local().fixed_offset();

        existing.permission_name = Set(self.permission_name);
        existing.permission_code = Set(self.permission_code);
        existing.parent_id = Set(self.parent_id);
        existing.order_num = Set(Some(self.order_num));
        existing.path = Set(self.path);
        existing.component = Set(self.component);
        existing.query = Set(self.query);
        existing.is_frame = Set(Some(self.is_frame));
        existing.is_cache = Set(Some(self.is_cache));
        existing.permission_type = Set(Some(self.permission_type));
        existing.visible = Set(Some(self.visible));
        existing.icon = Set(Some(self.icon));
        existing.description = Set(self.description);
        existing.updated_date = Set(now);
        existing.updated_by = Set(operator_id);
        existing.remark = Set(self.remark);

        existing
    }
}

use serde::Deserialize;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

// ==================== 用户登录相关 ====================

/// 系统用户登录请求接受
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "username": "admin",
    "password": "123456"
}))]
pub struct SysUserLoginRequest {
    #[validate(length(min = 2, max = 50, message = "用户名长度必须在2-50个字符之间"))]
    pub username: String,

    #[validate(length(min = 6, max = 30, message = "密码长度必须在6-30个字符之间"))]
    pub password: String,
    // 是否记住
    // pub remember: bool,
    // /// 验证码
    // pub captcha: String,
    // /// 验证码ID
    // pub captcha_id: String,
}

/// 刷新token请求结构体
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct RefreshTokenRequest {
    #[validate(length(min = 1, message = "refresh_token不能为空"))]
    pub refresh_token: String,
}

/// 修改密码请求结构体
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct ChangePasswordRequest {
    #[validate(length(min = 6, max = 30, message = "旧密码长度必须在6-30个字符之间"))]
    pub old_password: String,

    #[validate(length(min = 6, max = 30, message = "新密码长度必须在6-30个字符之间"))]
    pub new_password: String,
}

/// 修改个人信息请求结构体
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "real_name": "张三",
    "phone": "13812345678",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "gender": 1
}))]
pub struct UpdateProfileRequest {
    /// 真实姓名
    #[validate(length(max = 50, message = "真实姓名长度不能超过50个字符"))]
    pub real_name: Option<String>,

    /// 手机号
    #[validate(length(min = 11, max = 11, message = "手机号长度必须为11位"))]
    pub phone: Option<String>,

    /// 邮箱
    #[validate(email(message = "邮箱格式不正确"))]
    pub email: Option<String>,

    /// 头像地址
    pub avatar: Option<String>,

    /// 性别 1:男 2:女 3:未知
    pub gender: Option<i32>,
}

// ==================== 用户管理相关DTO ====================

/// 用户分页查询请求参数
#[derive(Debug, Deserialize, Validate, ToSchema, IntoParams)]
pub struct SysUserPageRequest {
    /// 当前页码 (1开始)
    #[validate(range(min = 1, message = "页码必须从1开始"))]
    pub page: Option<u64>,

    /// 每页大小
    #[validate(range(min = 1, max = 100, message = "每页大小必须在1-100之间"))]
    pub page_size: Option<u64>,

    /// 用户名关键字搜索
    pub username: Option<String>,

    /// 真实姓名关键字搜索
    pub real_name: Option<String>,
    
    // 角色id
    pub role_id: Option<Uuid>,

    /// 邮箱关键字搜索
    pub email: Option<String>,

    /// 手机号关键字搜索
    pub phone: Option<String>,

    /// 状态过滤 1:启用 2:禁用 3:锁定
    pub status: Option<i32>,

    /// 性别过滤 1:男 2:女 3:未知
    pub gender: Option<i32>,

    /// 创建时间开始 (YYYY-MM-DD格式)
    pub created_start: Option<String>,

    /// 创建时间结束 (YYYY-MM-DD格式)
    pub created_end: Option<String>,
}

/// 用户创建请求DTO
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "username": "newuser",
    "password": "123456",
    "real_name": "新用户",
    "phone": "13812345678",
    "email": "<EMAIL>",
    "gender": 1,
    "status": 1,
    "role_ids": ["a1b2c3d4-e5f6-7890-abcd-ef1234567890"]
}))]
pub struct SysUserCreateRequest {
    /// 用户名
    #[validate(length(min = 2, max = 50, message = "用户名长度必须在2-50个字符之间"))]
    pub username: String,

    /// 密码
    #[validate(length(min = 6, max = 30, message = "密码长度必须在6-30个字符之间"))]
    pub password: String,

    /// 真实姓名
    #[validate(length(max = 50, message = "真实姓名长度不能超过50个字符"))]
    pub real_name: Option<String>,

    /// 手机号
    #[validate(length(min = 11, max = 11, message = "手机号长度必须为11位"))]
    pub phone: Option<String>,

    /// 邮箱
    #[validate(email(message = "邮箱格式不正确"))]
    pub email: Option<String>,

    /// 头像地址
    pub avatar: Option<String>,

    /// 性别 1:男 2:女 3:未知
    pub gender: Option<i32>,

    /// 状态 1:启用 2:禁用 3:锁定 (默认启用)
    pub status: Option<i32>,

    /// 角色ID集合
    pub role_ids: Option<Vec<Uuid>>,

    /// 备注
    #[validate(length(max = 500, message = "备注长度不能超过500个字符"))]
    pub remark: Option<String>,
}

/// 用户更新请求DTO
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct SysUserUpdateRequest {
    /// 真实姓名
    #[validate(length(max = 50, message = "真实姓名长度不能超过50个字符"))]
    pub real_name: Option<String>,

    /// 手机号
    #[validate(length(min = 11, max = 11, message = "手机号长度必须为11位"))]
    pub phone: Option<String>,

    /// 邮箱
    #[validate(email(message = "邮箱格式不正确"))]
    pub email: Option<String>,

    /// 头像地址
    pub avatar: Option<String>,

    /// 性别 1:男 2:女 3:未知
    pub gender: Option<i32>,

    /// 状态 1:启用 2:禁用 3:锁定
    pub status: Option<i32>,

    /// 角色ID集合
    pub role_ids: Option<Vec<Uuid>>,

    /// 备注
    #[validate(length(max = 500, message = "备注长度不能超过500个字符"))]
    pub remark: Option<String>,
}

/// 用户重置密码请求DTO
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct SysUserResetPasswordRequest {
    /// 新密码
    #[validate(length(min = 6, max = 30, message = "密码长度必须在6-30个字符之间"))]
    pub new_password: String,
}

/// 批量删除用户请求DTO
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct SysUserBatchDeleteRequest {
    /// 用户ID集合
    #[validate(length(min = 1, max = 100, message = "批量删除用户数量必须在1-100之间"))]
    pub user_ids: Vec<Uuid>,
}

/// 用户状态切换请求DTO
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct SysUserStatusRequest {
    /// 状态 1:启用 2:禁用 3:锁定
    #[validate(range(min = 1, max = 3, message = "状态值必须在1-3之间"))]
    pub status: i32,
}

// ==================== 微信相关DTO ====================

/// 系统用户微信登录请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "code": "023abc456789def"
}))]
pub struct SysUserWechatLoginRequest {
    /// 微信授权码
    #[validate(length(min = 1, message = "微信授权码不能为空"))]
    pub code: String,
}

/// 绑定微信请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "code": "023abc456789def",
    "nickname": "微信用户",
    "avatar": "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKVUskibD..."
}))]
pub struct BindWechatRequest {
    /// 微信授权码
    #[validate(length(min = 1, message = "微信授权码不能为空"))]
    pub code: String,
    /// 微信昵称（可选，前端通过微信用户信息API获取）
    #[validate(length(max = 50, message = "微信昵称长度不能超过50个字符"))]
    pub nickname: Option<String>,
    /// 微信头像URL（可选，前端通过微信用户信息API获取）
    pub avatar: Option<String>,
}

/// 解绑微信请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "password": "123456"
}))]
pub struct UnbindWechatRequest {
    /// 当前用户密码
    #[validate(length(min = 6, max = 30, message = "密码长度必须在6-30个字符之间"))]
    pub password: String,
}

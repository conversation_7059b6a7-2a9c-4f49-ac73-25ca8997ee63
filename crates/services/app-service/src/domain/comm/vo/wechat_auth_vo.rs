use serde::Serialize;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;

// ==================== 微信登录响应相关 VO ====================

/// 微信登录成功响应
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
}))]
pub struct WechatLoginResponse {
    /// 访问令牌，用于后续API请求的身份验证
    pub token: String,

}

/// 用户基本信息
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "username": "admin",
    "nickname": "张三",
    "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTKxrUx0mxFiafEEHShLQLtJuIZOpTClp2DlMfbdBP0jkHH7TG6aVunZS5ar5GxPDJkMjqrGoFbpRaw/132",
    "user_type": "system"
}))]
pub struct UserInfo {
    /// 用户ID
    pub id: String,

    /// 用户名
    pub username: String,

    /// 昵称（来自微信）
    pub nickname: String,

    /// 头像URL（来自微信）
    pub avatar_url: Option<String>,

    /// 用户类型
    pub user_type: String,
}

/// 微信账号绑定成功响应
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "message": "微信账号绑定成功"
}))]
pub struct WechatBindResponse {
    /// 绑定结果消息
    pub message: String,
}

/// 微信解绑成功响应
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "message": "微信账号解绑成功"
}))]
pub struct WechatUnbindResponse {
    /// 解绑结果消息
    pub message: String,
}

/// 微信用户信息（内部使用）
#[derive(Debug, Clone, Serialize)]
pub struct WechatUserInfo {
    /// 微信OpenID
    pub openid: String,

    /// 微信昵称
    pub nickname: String,

    /// 微信头像URL
    pub avatar_url: String,

    /// 微信UnionID（可选）
    pub unionid: Option<String>,
}

use serde::Deserialize;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use validator::Validate;

// ==================== 微信登录相关 ====================

/// 用户类型枚举
#[derive(Debug, Deserialize, ToSchema, PartialEq, Eq)]
#[serde(rename_all = "lowercase")]
pub enum UserType {
    /// 系统用户
    System,
    /// 商户用户
    Merchant,
}

/// 微信登录请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "code": "061Fwoml0VKPiX1uN5ol0jBqlq3Fwomk",
    "user_type": "merchant",
    "merchant_id": 1001
}))]
pub struct WechatLoginRequest {
    /// 微信授权码，通过微信登录获取
    #[validate(length(min = 1, message = "微信授权码不能为空"))]
    pub code: String,
}

/// 微信账号绑定请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "code": "061Fwoml0VKPiX1uN5ol0jBqlq3Fwomk",
    "username": "admin",
    "password": "123456",
    "user_type": "system",
    "merchant_id": null
}))]
pub struct WechatBindRequest {
    /// 微信授权码
    #[validate(length(min = 1, message = "微信授权码不能为空"))]
    pub code: String,

    /// 用户名
    #[validate(length(min = 1, max = 50, message = "用户名长度必须在1-50个字符之间"))]
    pub username: String,

    /// 密码
    #[validate(length(min = 6, max = 30, message = "密码长度必须在6-30个字符之间"))]
    pub password: String,

    /// 用户类型
    pub user_type: UserType,

    /// 商户ID，当user_type为merchant时必填
    pub merchant_id: Option<i64>,
}

/// 微信解绑请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "password": "123456"
}))]
pub struct WechatUnbindRequest {
    /// 当前密码，用于验证身份
    #[validate(length(min = 6, max = 30, message = "密码长度必须在6-30个字符之间"))]
    pub password: String,
}

use axum::body::Bytes;
use serde::{Deserialize, Serialize};
use utoipa::{IntoParams, ToSchema};
/// 多文件上传结果
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct MultiFileUploadResult {
    /// 成功上传的文件列表
    pub successful_uploads: Vec<UploadResponse>,
    /// 失败上传的文件列表
    pub failed_uploads: Vec<FailedUploadInfo>,
    /// 总文件数
    pub total_files: usize,
    /// 成功上传数量
    pub successful_count: usize,
    /// 失败上传数量
    pub failed_count: usize,
    /// 总文件大小（字节）
    pub total_size: u64,
}

/// 上传失败信息
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct FailedUploadInfo {
    /// 文件名
    pub file_name: String,
    /// 错误信息
    pub error: String,
    /// 文件大小（字节）
    pub file_size: usize,
}

/// 文件上传信息
#[derive(Debug, <PERSON><PERSON>)]
pub struct FileUploadInfo {
    pub file_name: String,
    pub file_data: Bytes,
    pub content_type: Option<String>,
    pub file_size: usize,
}
/// 文件上传响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct UploadResponse {
    /// 文件URL
    pub file_url: String,
    /// 文件路径（存储在OSS中的对象键）
    pub file_path: String,
    /// 文件大小（字节）
    pub file_size: u64,
    /// 文件类型
    pub content_type: String,
}

/// 文件下载请求
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema, IntoParams)]
pub struct FileDownloadRequest {
    /// 文件路径
    pub file_path: String,
}

/// 文件下载响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
pub struct FileDownloadResponse {
    /// 下载链接
    pub download_url: String,
    /// 文件路径
    pub file_path: String,
    /// 文件大小（字节）
    pub file_size: u64,
    /// 文件类型
    pub content_type: String,
}

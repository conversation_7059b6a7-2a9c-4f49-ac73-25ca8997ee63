//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.12

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

/// 商户授权权限表实体
/// 
/// 存储商户被授权的权限信息，关联权限模板，
/// 用于控制商户可以使用的系统功能和权限范围
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(
    schema_name = "business",
    table_name = "merchant_authorized_permissions"
)]
pub struct Model {
    /// 授权权限ID - 主键，UUID类型，非自增
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    /// 商户ID - 关联的商户ID，表示该权限授权给哪个商户
    pub merchant_id: i64,
    /// 权限模板ID - 关联的权限模板ID，指向system_permission_templates表
    pub permission_template_id: Uuid,
    /// 授权状态 - 该权限授权的状态（启用/禁用）
    pub status: MerchantAuthorizedPermissionsStatus,
    /// 授权时间 - 权限被授权的时间戳，带时区信息
    pub authorized_date: DateTimeWithTimeZone,
    /// 创建人ID - 创建该授权记录的用户ID
    pub created_by: Uuid,
    /// 更新人ID - 最后更新该记录的用户ID，可为空
    pub updated_by: Option<Uuid>,
    /// 备注信息 - 额外的备注说明，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::merchants::Entity",
        from = "Column::MerchantId",
        to = "super::merchants::Column::Id"
    )]
    Merchants,
    #[sea_orm(
        belongs_to = "super::system_permission_templates::Entity",
        from = "Column::PermissionTemplateId",
        to = "super::system_permission_templates::Column::Id"
    )]
    SystemPermissionTemplates,
    #[sea_orm(has_many = "super::merchant_role_permissions::Entity")]
    MerchantRolePermissions,
}

/// 商户授权权限状态枚举
/// 
/// 定义商户授权权限的状态，用于控制权限的有效性
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum MerchantAuthorizedPermissionsStatus {
    /// 启用 - 权限正常，商户可以使用该权限
    #[sea_orm(num_value = 0)]
    Enabled = 0,
    /// 禁用 - 权限被禁用，商户无法使用该权限
    #[sea_orm(num_value = 1)]
    Disabled = 1,
}

impl Model {
    /// 获取状态描述
    pub fn get_status_desc(status: MerchantAuthorizedPermissionsStatus) -> String {
        match status {
            MerchantAuthorizedPermissionsStatus::Enabled => "正常".to_string(),
            MerchantAuthorizedPermissionsStatus::Disabled => "停用".to_string(),
        }
    }

    /// 获取权限类型描述
    pub fn get_permission_type_desc(permission_type: Option<i32>) -> String {
        match permission_type {
            Some(1) => "目录".to_string(),
            Some(2) => "菜单".to_string(),
            Some(3) => "按钮".to_string(),
            _ => "未知".to_string(),
        }
    }
}

impl ActiveModelBehavior for ActiveModel {}

impl Related<super::merchants::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Merchants.def()
    }
}

impl Related<super::system_permission_templates::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SystemPermissionTemplates.def()
    }
}

impl Related<super::merchant_role_permissions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantRolePermissions.def()
    }
}

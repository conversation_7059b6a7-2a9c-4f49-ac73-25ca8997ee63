//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.12

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

/// 商户用户角色关联表实体
///
/// 存储用户在不同商户中的角色关联关系，通过用户商户关系表
/// 实现用户在不同商户中拥有不同角色的功能
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "business", table_name = "merchant_user_roles")]
pub struct Model {
    /// 关联ID - 主键，UUID类型，非自增
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    /// 用户商户关系ID - 关联merchant_user_merchants表的关系ID
    pub user_merchant_id: Uuid,
    /// 角色ID - 关联的角色ID，表示用户拥有哪个角色
    pub role_id: Uuid,
    /// 创建时间 - 记录创建的时间戳，带时区信息
    pub created_date: DateTimeWithTimeZone,
    /// 更新时间 - 记录最后更新的时间戳，带时区信息
    pub updated_date: DateTimeWithTimeZone,
    /// 创建人ID - 创建该记录的用户ID，可为空
    pub created_by: Option<Uuid>,
    /// 更新人ID - 最后更新该记录的用户ID，可为空
    pub updated_by: Option<Uuid>,
    /// 备注信息 - 额外的备注说明，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::merchant_user_merchants::Entity",
        from = "Column::UserMerchantId",
        to = "super::merchant_user_merchants::Column::Id"
    )]
    MerchantUserMerchants,
    #[sea_orm(
        belongs_to = "super::merchant_roles::Entity",
        from = "Column::RoleId",
        to = "super::merchant_roles::Column::Id"
    )]
    MerchantRoles,
}

/// 商户用户角色关联状态枚举
///
/// 定义用户角色关联的状态，用于控制用户角色权限的有效性
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum MerchantUserRoleStatus {
    /// 禁用 - 用户角色关联被禁用，用户无法使用该角色权限
    #[sea_orm(num_value = 0)]
    Disabled = 0,
    /// 启用 - 用户角色关联正常，用户可以使用该角色权限
    #[sea_orm(num_value = 1)]
    Enabled = 1,
}

impl ActiveModelBehavior for ActiveModel {}

impl Related<super::merchant_user_merchants::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantUserMerchants.def()
    }
}

impl Related<super::merchant_roles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantRoles.def()
    }
}

// 通过用户商户关系表与用户建立间接关系
impl Related<super::merchant_users::Entity> for Entity {
    fn to() -> RelationDef {
        super::merchant_users::Relation::MerchantUserMerchants.def()
    }

    fn via() -> Option<RelationDef> {
        Some(Relation::MerchantUserMerchants.def().rev())
    }
}

// 通过用户商户关系表与商户建立间接关系
impl Related<super::merchants::Entity> for Entity {
    fn to() -> RelationDef {
        super::merchants::Relation::MerchantUserMerchants.def()
    }

    fn via() -> Option<RelationDef> {
        Some(Relation::MerchantUserMerchants.def().rev())
    }
}

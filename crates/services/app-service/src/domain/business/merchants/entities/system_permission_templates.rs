//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.12

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

/// 系统权限模板表实体
/// 
/// 存储系统权限模板的基本信息，定义了系统中所有可用的权限，
/// 支持树形结构，包括目录、菜单、按钮等不同类型的权限
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "business", table_name = "system_permission_templates")]
pub struct Model {
    /// 权限模板ID - 主键，UUID类型，非自增
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    /// 权限名称 - 权限的显示名称，用于前端展示
    pub permission_name: String,
    /// 权限编码 - 权限的唯一标识编码，用于程序逻辑识别，具有唯一性约束
    #[sea_orm(unique)]
    pub permission_code: String,
    /// 父权限ID - 父级权限的ID，用于构建权限树形结构，可为空表示根权限
    pub parent_id: Option<Uuid>,
    /// 排序序号 - 权限在同级中的显示顺序，数值越小排序越靠前
    pub order_num: Option<i32>,
    /// 路由路径 - 前端路由路径，用于菜单跳转，可为空
    pub path: Option<String>,
    /// 组件路径 - 前端组件路径，用于动态加载组件，可为空
    pub component: Option<String>,
    /// 路由参数 - 路由的查询参数，可为空
    pub query: Option<String>,
    /// 是否外链 - 标识是否为外部链接 (0:否 1:是)，可为空
    pub is_frame: Option<i32>,
    /// 是否缓存 - 标识页面是否缓存 (0:否 1:是)，可为空
    pub is_cache: Option<i32>,
    /// 权限类型 - 权限的类型 (1:目录 2:菜单 3:按钮)，可为空
    pub permission_type: Option<i32>,
    /// 是否可见 - 标识菜单是否显示 (0:隐藏 1:显示)，可为空
    pub visible: Option<i32>,
    /// 菜单图标 - 菜单的图标标识，可为空
    pub icon: Option<String>,
    /// 权限描述 - 权限的详细描述信息，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub description: Option<String>,
    /// 创建时间 - 记录创建的时间戳，带时区信息
    pub created_date: DateTimeWithTimeZone,
    /// 更新时间 - 记录最后更新的时间戳，带时区信息
    pub updated_date: DateTimeWithTimeZone,
    /// 创建人ID - 创建该记录的用户ID，可为空
    pub created_by: Option<Uuid>,
    /// 更新人ID - 最后更新该记录的用户ID，可为空
    pub updated_by: Option<Uuid>,
    /// 备注信息 - 额外的备注说明，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::merchant_authorized_permissions::Entity")]
    MerchantAuthorizedPermissions,
    #[sea_orm(
        belongs_to = "Entity",
        from = "Column::ParentId",
        to = "Column::Id"
    )]
    SelfRef,
}

impl ActiveModelBehavior for ActiveModel {}

impl Related<super::merchant_authorized_permissions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantAuthorizedPermissions.def()
    }
}

impl Related<Entity> for Entity {
    fn to() -> RelationDef {
        Relation::SelfRef.def()
    }
}

//! `SeaORM` Entity - 用户商户关系表

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

/// 用户商户关系表实体
///
/// 管理用户与商户之间的多对多关系，支持用户加入多个商户，
/// 以及用户在不同商户中的状态管理和生命周期管理
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "business", table_name = "merchant_user_merchants")]
pub struct Model {
    /// 关系ID - 主键，UUID类型，非自增
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    /// 用户ID - 关联merchant_users表的用户ID
    pub user_id: Uuid,
    /// 商户ID - 关联merchants表的商户ID
    pub merchant_id: i64,
    /// 用户在该商户的状态 - 表示用户在该商户中的当前状态
    pub status: MerchantUserMerchantStatus,
    /// 加入类型 - 表示用户加入该商户的方式
    pub join_type: MerchantUserJoinType,
    /// 加入时间 - 用户加入该商户的时间戳，带时区信息
    pub join_date: DateTimeWithTimeZone,
    /// 离开时间 - 用户离开该商户的时间戳，可为空
    pub leave_date: Option<DateTimeWithTimeZone>,
    /// 创建时间 - 记录创建的时间戳，带时区信息
    pub created_date: DateTimeWithTimeZone,
    /// 更新时间 - 记录最后更新的时间戳，带时区信息
    pub updated_date: DateTimeWithTimeZone,
    /// 创建人ID - 创建该记录的用户ID，可为空
    pub created_by: Option<Uuid>,
    /// 更新人ID - 最后更新该记录的用户ID，可为空
    pub updated_by: Option<Uuid>,
    /// 备注信息 - 额外的备注说明，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::merchant_users::Entity",
        from = "Column::UserId",
        to = "super::merchant_users::Column::Id"
    )]
    MerchantUsers,
    #[sea_orm(
        belongs_to = "super::merchants::Entity",
        from = "Column::MerchantId",
        to = "super::merchants::Column::Id"
    )]
    Merchants,
    #[sea_orm(has_many = "super::merchant_user_roles::Entity")]
    MerchantUserRoles,
}

/// 用户商户关系状态枚举
///
/// 定义用户在商户中的状态，用于管理用户的生命周期
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum MerchantUserMerchantStatus {
    /// 正常 - 用户在该商户中状态正常，可以正常使用功能
    #[sea_orm(num_value = 1)]
    Active = 1,
    /// 待审核 - 用户加入申请等待商户审核
    #[sea_orm(num_value = 2)]
    Pending = 2,
    /// 已离开 - 用户主动离开该商户
    #[sea_orm(num_value = 3)]
    Left = 3,
    /// 被移除 - 用户被商户管理员移除
    #[sea_orm(num_value = 4)]
    Removed = 4,
}

/// 用户加入商户类型枚举
///
/// 定义用户加入商户的方式，用于区分不同的加入场景
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum MerchantUserJoinType {
    /// 邀请加入 - 商户主动邀请用户加入
    #[sea_orm(num_value = 1)]
    Invited = 1,
    /// 申请加入 - 用户主动申请加入商户
    #[sea_orm(num_value = 2)]
    Applied = 2,
    /// 系统添加 - 系统管理员直接添加用户到商户
    #[sea_orm(num_value = 3)]
    System = 3,
}

impl ActiveModelBehavior for ActiveModel {}

impl Related<super::merchant_users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantUsers.def()
    }
}

impl Related<super::merchants::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Merchants.def()
    }
}

impl Related<super::merchant_user_roles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantUserRoles.def()
    }
}

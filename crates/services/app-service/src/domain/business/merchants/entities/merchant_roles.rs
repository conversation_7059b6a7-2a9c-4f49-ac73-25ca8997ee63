//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.12

use crate::domain::business::merchants::entities::merchant_roles::role_code::SYSTEM_CUSTOM_ADMIN_ROLE;
use crate::utils::DateTimeUtils;
use sea_orm::Set;
use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

/// 商户角色信息表实体
///
/// 存储商户角色的基本信息，包括角色名称、类型、权限范围、
/// 状态等核心角色管理数据
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "business", table_name = "merchant_roles")]
pub struct Model {
    /// 角色ID - 主键，UUID类型，非自增
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    /// 商户ID - 关联的商户ID，表示该角色属于哪个商户
    pub merchant_id: i64,
    /// 角色编码 - 角色的唯一标识编码，用于程序逻辑识别
    pub role_code: Option<String>,
    /// 角色名称 - 角色的显示名称，用于前端展示
    pub role_name: String,
    /// 角色类型 - 角色的类型分类（系统角色/自定义角色等）
    pub role_type: MerchantRoleType,
    /// 是否默认角色 - 标识该角色是否为商户的默认角色
    pub is_default: bool,
    /// 数据权限范围 - 角色的数据访问权限范围
    pub data_scope: MerchantRoleDataScope,
    /// 角色描述 - 角色的详细描述信息，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub role_description: Option<String>,
    /// 角色状态 - 角色的当前状态（启用/禁用）
    pub status: MerchantRoleStatus,
    /// 创建时间 - 记录创建的时间戳，带时区信息
    pub created_date: DateTimeWithTimeZone,
    /// 更新时间 - 记录最后更新的时间戳，带时区信息
    pub updated_date: DateTimeWithTimeZone,
    /// 创建人ID - 创建该记录的用户ID，可为空
    pub created_by: Option<Uuid>,
    /// 更新人ID - 最后更新该记录的用户ID，可为空
    pub updated_by: Option<Uuid>,
    /// 备注信息 - 额外的备注说明，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::merchants::Entity",
        from = "Column::MerchantId",
        to = "super::merchants::Column::Id"
    )]
    Merchants,
    #[sea_orm(has_many = "super::merchant_user_roles::Entity")]
    MerchantUserRoles,
    #[sea_orm(has_many = "super::merchant_role_permissions::Entity")]
    MerchantRolePermissions,
}

impl Related<super::merchants::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Merchants.def()
    }
}

impl Related<super::merchant_user_roles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantUserRoles.def()
    }
}

impl Related<super::merchant_role_permissions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantRolePermissions.def()
    }
}

// 注意：由于表结构重构，merchant_roles 与 merchant_users 之间的关系
// 现在需要经过两个中间表（merchant_user_roles -> merchant_user_merchants -> merchant_users）
// 这种复杂的多级间接关系建议在 Repository 层通过 JOIN 查询实现，而不是定义 Related trait

/// 商户角色编码常量
pub mod role_code {
    /// 系统自定义角色编码
    pub const SYSTEM_CUSTOM_ROLE: &str = "SYSTEM_CUSTOM_MERCHANT";

    /// 系统定义商户管理员角色编码
    pub const SYSTEM_CUSTOM_ADMIN_ROLE: &str = "SYSTEM_CUSTOM_MERCHANT_ADMIN";

    /// 商户自定义角色编码前缀
    pub const CUSTOM_ROLE_PREFIX: &str = "CUSTOM_ROLE_";
}

/// 商户角色类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, DeriveActiveEnum, EnumIter)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum MerchantRoleType {
    /// 系统自定义角色
    SystemCustom = 1,
    /// 商户自定义角色
    MerchantCustom = 2,
}

/// 商户角色类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, DeriveActiveEnum, EnumIter)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum MerchantRoleStatus {
    /// 禁用
    #[sea_orm(num_value = 0)]
    Disabled = 0,
    /// 启用
    #[sea_orm(num_value = 1)]
    Enabled = 1,
}

/// 数据范围枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, DeriveActiveEnum, EnumIter)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum MerchantRoleDataScope {
    /// 商户全部数据
    #[sea_orm(num_value = 1)]
    AllData = 1,
    /// 个人数据
    #[sea_orm(num_value = 2)]
    PersonalData = 2,
}

impl ActiveModelBehavior for ActiveModel {}

impl Entity {
    /// 创建默认管理员角色
    ///
    /// operator_id: 操作人ID
    /// merchant_id: 商户ID
    /// db: 数据库连接
    pub async fn default_admin_role<C>(
        operator_id: Uuid,
        merchant_id: i64,
        db: &C,
    ) -> Result<Uuid, DbErr>
    where
        C: ConnectionTrait,
    {
        let role_id = Uuid::now_v7();
        let new_role = ActiveModel {
            id: Set(role_id),
            merchant_id: Set(merchant_id),
            role_code: Set(Some(SYSTEM_CUSTOM_ADMIN_ROLE.to_string())),
            role_name: Set("管理员".to_string()),
            role_type: Set(MerchantRoleType::SystemCustom), // 管理员类型
            is_default: Set(false),
            data_scope: Set(MerchantRoleDataScope::AllData), // 全部数据权限
            role_description: Set(Some("默认管理员角色".to_string())),
            status: Set(MerchantRoleStatus::Enabled), // 启用
            created_date: Set(DateTimeUtils::now_local().fixed_offset()),
            updated_date: Set(DateTimeUtils::now_local().fixed_offset()),
            created_by: Set(Some(operator_id)),
            updated_by: Set(Some(operator_id)),
            remark: Set(Some("系统自动创建".to_string())),
        };

        Entity::insert(new_role).exec(db).await?;
        Ok(role_id)
    }

    /// 获取状态描述
    pub fn get_status_desc(status: MerchantRoleStatus) -> String {
        match status {
            MerchantRoleStatus::Enabled => "启用".to_string(),
            MerchantRoleStatus::Disabled => "禁用".to_string(),
        }
    }
}

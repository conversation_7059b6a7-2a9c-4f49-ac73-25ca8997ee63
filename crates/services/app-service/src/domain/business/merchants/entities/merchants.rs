//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.12

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;

/// 地理位置数据结构
#[derive(<PERSON>bu<PERSON>, <PERSON>lone, PartialEq, Serialize, Deserialize, ToSchema)]
pub struct Location {
    /// 经度 (-180.0 ~ 180.0)
    pub longitude: f64,
    /// 纬度 (-90.0 ~ 90.0)
    pub latitude: f64,
}

impl Location {
    /// 创建新的位置
    pub fn new(longitude: f64, latitude: f64) -> Self {
        Self {
            longitude,
            latitude,
        }
    }

    /// 从字符串解析位置 (格式: "经度,纬度")
    pub fn from_string(location_str: &str) -> Result<Self, String> {
        if let Some((lng_str, lat_str)) = location_str.split_once(',') {
            let longitude = lng_str
                .trim()
                .parse::<f64>()
                .map_err(|_| "经度格式不正确".to_string())?;
            let latitude = lat_str
                .trim()
                .parse::<f64>()
                .map_err(|_| "纬度格式不正确".to_string())?;

            if longitude < -180.0 || longitude > 180.0 {
                return Err("经度必须在-180到180之间".to_string());
            }
            if latitude < -90.0 || latitude > 90.0 {
                return Err("纬度必须在-90到90之间".to_string());
            }

            Ok(Self::new(longitude, latitude))
        } else {
            Err("位置格式不正确，应为'经度,纬度'".to_string())
        }
    }

    /// 计算与另一个位置的距离（单位：公里）
    /// 使用哈弗塞因公式计算地球表面两点间的距离
    pub fn distance_to(&self, other: &Location) -> f64 {
        let r = 6371.0; // 地球半径（公里）

        let lat1_rad = self.latitude.to_radians();
        let lat2_rad = other.latitude.to_radians();
        let delta_lat = (other.latitude - self.latitude).to_radians();
        let delta_lon = (other.longitude - self.longitude).to_radians();

        let a = (delta_lat / 2.0).sin().powi(2)
            + lat1_rad.cos() * lat2_rad.cos() * (delta_lon / 2.0).sin().powi(2);
        let c = 2.0 * a.sqrt().atan2((1.0 - a).sqrt());

        r * c
    }
}

/// 商户状态枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize, EnumIter, DeriveActiveEnum)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum MerchantStatus {
    /// 正常营业 - 商户可以正常接单和营业
    #[sea_orm(num_value = 1)]
    Active = 1,
    /// 临时关闭 - 商户暂时停止营业，可以重新开启
    #[sea_orm(num_value = 2)]
    Suspended = 2,
    /// 永久关闭 - 商户永久停止营业，无法恢复
    #[sea_orm(num_value = 3)]
    Closed = 3,
}

/// 商户信息表实体
///
/// 存储商户的基本信息，包括商户名称、联系方式、地理位置、
/// 营业状态、佣金费率等核心业务数据
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "business", table_name = "merchants")]
pub struct Model {
    /// 商户ID - 主键，自增长整型
    #[sea_orm(primary_key)]
    pub id: i64,
    /// 商户名称 - 商户的显示名称，用于前端展示
    pub merchant_name: String,
    /// 商户编码 - 商户的唯一标识码，用于业务逻辑识别，具有唯一性约束
    #[sea_orm(unique)]
    pub merchant_code: String,
    /// 商户分类ID - 关联merchant_categories表，可为空
    pub category_id: Option<Uuid>,
    /// 联系电话 - 商户的主要联系电话，可为空
    pub phone: Option<String>,
    /// 电子邮箱 - 商户的联系邮箱，可为空
    pub email: Option<String>,
    /// 详细地址 - 商户的详细地址信息，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub address: Option<String>,
    /// 地理位置 - 经纬度坐标信息，以JSON格式存储Location结构体
    #[sea_orm(column_type = "JsonBinary", nullable)]
    pub location: Option<Json>,
    /// 营业执照号 - 商户的营业执照编号，可为空
    pub business_license: Option<String>,
    /// 营业执照照片 - 营业执照的图片URL或路径，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub license_photo: Option<String>,
    /// 商户头像 - 商户的头像图片URL或路径，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub avatar: Option<String>,
    /// 商户描述 - 商户的详细描述信息，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub description: Option<String>,
    /// 平台佣金费率 - 平台收取的佣金比例，精度为5位小数，4位小数点后
    #[sea_orm(column_type = "Decimal(Some((5, 4)))", nullable)]
    pub platform_commission_rate: Option<Decimal>,
    /// 商户状态 - 商户的营业状态（正常营业/临时关闭/永久关闭）
    pub status: MerchantStatus,
    /// 自动清理日期 - 商户数据的自动清理日期，可为空
    pub auto_clear_date: Option<Date>,
    /// 排序顺序 - 商户在列表中的显示顺序，数值越小排序越靠前
    pub sort_order: Option<i32>,
    /// 创建时间 - 记录创建的时间戳，带时区信息
    pub created_date: DateTimeWithTimeZone,
    /// 更新时间 - 记录最后更新的时间戳，带时区信息
    pub updated_date: DateTimeWithTimeZone,
    /// 创建人ID - 创建该记录的用户ID，可为空
    pub created_by: Option<Uuid>,
    /// 更新人ID - 最后更新该记录的用户ID，可为空
    pub updated_by: Option<Uuid>,
    /// 备注信息 - 额外的备注说明，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::merchant_categories::Entity",
        from = "Column::CategoryId",
        to = "super::merchant_categories::Column::Id"
    )]
    MerchantCategories,
    #[sea_orm(has_many = "super::merchant_user_roles::Entity")]
    MerchantUserRoles,
    #[sea_orm(has_many = "super::merchant_user_merchants::Entity")]
    MerchantUserMerchants,
    #[sea_orm(has_many = "super::merchant_roles::Entity")]
    MerchantRoles,
    #[sea_orm(has_many = "super::merchant_authorized_permissions::Entity")]
    MerchantAuthorizedPermissions,
    #[sea_orm(has_many = "super::merchant_role_permissions::Entity")]
    MerchantRolePermissions,
    #[sea_orm(has_many = "super::merchant_follower_commission::Entity")]
    MerchantFollowerCommission,
}

impl Related<super::merchant_categories::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantCategories.def()
    }
}

impl Related<super::merchant_user_roles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantUserRoles.def()
    }
}

impl Related<super::merchant_user_merchants::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantUserMerchants.def()
    }
}

impl Related<super::merchant_roles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantRoles.def()
    }
}

impl Related<super::merchant_authorized_permissions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantAuthorizedPermissions.def()
    }
}

impl Related<super::merchant_role_permissions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantRolePermissions.def()
    }
}

impl Related<super::merchant_follower_commission::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantFollowerCommission.def()
    }
}

// 通过用户商户关系表与用户建立多对多关系
impl Related<super::merchant_users::Entity> for Entity {
    fn to() -> RelationDef {
        super::merchant_user_merchants::Relation::MerchantUsers.def()
    }

    fn via() -> Option<RelationDef> {
        Some(
            super::merchant_user_merchants::Relation::Merchants
                .def()
                .rev(),
        )
    }
}

impl ActiveModelBehavior for ActiveModel {}
impl Model {
    /// 获取商户状态枚举
    pub fn get_status(&self) -> MerchantStatus {
        self.status
    }

    /// 检查商户是否正常营业
    pub fn is_active(&self) -> bool {
        self.status == MerchantStatus::Active
    }

    /// 检查商户是否已关闭
    pub fn is_closed(&self) -> bool {
        self.status == MerchantStatus::Closed
    }

    /// 获取地理位置信息
    pub fn get_location(&self) -> Result<Option<Location>, serde_json::Error> {
        match &self.location {
            Some(json_value) => {
                let location: Location = serde_json::from_value(json_value.clone())?;
                Ok(Some(location))
            }
            None => Ok(None),
        }
    }

    /// 设置地理位置信息
    pub fn set_location(location: Location) -> Result<Json, serde_json::Error> {
        let json_value = serde_json::to_value(location)?;
        Ok(json_value)
    }

    /// 计算与指定位置的距离（公里）
    pub fn distance_to(&self, target: &Location) -> Result<Option<f64>, serde_json::Error> {
        match self.get_location()? {
            Some(current_location) => Ok(Some(current_location.distance_to(target))),
            None => Ok(None),
        }
    }

    /// 获取状态描述
    pub fn get_status_desc(&self) -> String {
        match &self.status {
            MerchantStatus::Active => "正常营业".to_string(),
            MerchantStatus::Suspended => "临时关闭".to_string(),
            MerchantStatus::Closed => "永久关闭".to_string(),
        }
    }
}

impl Entity {
    /// 获取状态描述
    pub fn get_status_desc(status: MerchantStatus) -> String {
        match status {
            MerchantStatus::Active => "正常营业".to_string(),
            MerchantStatus::Suspended => "临时关闭".to_string(),
            MerchantStatus::Closed => "永久关闭".to_string(),
        }
    }
}

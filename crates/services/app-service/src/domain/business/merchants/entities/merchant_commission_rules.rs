//! 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则实体
//! Generated by lib-codegen
//! 对应表：business.merchant_commission_rules

use crate::utils::DateTimeUtils;
use async_trait::async_trait;
use derive_more::{Constructor, From, Into};
use rust_decimal::Decimal;
use sea_orm::entity::prelude::*;
use sea_orm::{ConnectionTrait, DbErr, FromJsonQueryResult, Set};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则实体
///
/// 存储商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则的相关信息和业务数据
#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Into, From, Constructor,
)]
#[sea_orm(schema_name = "business", table_name = "merchant_commission_rules")]
pub struct Model {
    /// 主键，自增UUID
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    /// 商户ID（关联business.merchants.id）
    pub merchant_id: i64,
    /// 规则名称，方便识别和管理
    pub rule_name: String,
    /// 基础佣金比例，精度为5位数字4位小数（如0.0500表示5%）
    pub base_commission_rate: Decimal,
    /// 阶梯佣金规则JSON配置，存储多组阶梯规则
    pub tier_rules: Option<TierRules>,
    /// 创建时间
    pub created_date: DateTimeWithTimeZone,
    /// 更新时间
    pub updated_date: DateTimeWithTimeZone,
    /// 创建人ID
    pub created_by: Option<Uuid>,
    /// 更新人ID
    pub updated_by: Option<Uuid>,
    /// 备注信息
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::merchants::Entity",
        from = "Column::MerchantId",
        to = "super::merchants::Column::Id"
    )]
    Merchants,
}

#[async_trait]
impl ActiveModelBehavior for ActiveModel {
    /// 插入或更新前自动设置时间戳
    async fn before_save<C>(mut self, _db: &C, insert: bool) -> Result<Self, DbErr>
    where
        C: ConnectionTrait,
    {
        let now = DateTimeUtils::now_china_offset();
        if insert {
            self.created_date = Set(now);
        }
        self.updated_date = Set(now);
        Ok(self)
    }
}

impl Related<super::merchants::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Merchants.def()
    }
}

// ============================================================================
// 阶梯佣金规则JSON结构体
// ============================================================================

/// 阶梯佣金规则配置
#[derive(
    Default,
    Debug,
    Clone,
    Serialize,
    Deserialize,
    PartialEq,
    From,
    Into,
    Constructor,
    FromJsonQueryResult,
    Eq,
)]
pub struct TierRules {
    /// 阶梯规则列表
    pub tiers: Vec<CommissionTier>,
}

/// 单个佣金阶梯配置
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, From, Into, Constructor, Eq)]
pub struct CommissionTier {
    /// 阶梯名称
    pub tier_name: String,
    /// 逻辑关系类型：OR（或）/ AND（且）
    pub logic_type: LogicType,
    /// 判断条件列表
    pub conditions: Vec<TierCondition>,
    /// 佣金比例（如0.0600表示6%）
    pub commission_rate: Decimal,
    /// 阶梯描述
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
}

/// 阶梯条件
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, From, Into, Constructor, Eq)]
pub struct TierCondition {
    /// 条件类型
    #[serde(rename = "type")]
    pub condition_type: ConditionType,
    /// 阈值
    pub threshold: Decimal,
    /// 操作符
    pub operator: ComparisonOperator,
}

/// 逻辑关系类型
#[derive(Default, Debug, Clone, Serialize, Deserialize, PartialEq, Eq, utoipa::ToSchema)]
#[serde(rename_all = "UPPERCASE")]
pub enum LogicType {
    /// 或关系：满足任一条件即可
    #[default]
    Or,
    /// 且关系：必须全部满足
    And,
}

/// 条件类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, utoipa::ToSchema)]
#[serde(rename_all = "snake_case")]
pub enum ConditionType {
    /// 销售额条件
    SalesAmount,
    /// 客户数量条件
    CustomerCount,
}

/// 比较操作符
#[derive(Default, Debug, Clone, Serialize, Deserialize, PartialEq, Eq, utoipa::ToSchema)]
pub enum ComparisonOperator {
    #[serde(rename = ">=")]
    GreaterThanOrEqual,
    #[serde(rename = ">")]
    GreaterThan,
    #[serde(rename = "<=")]
    LessThanOrEqual,
    #[serde(rename = "<")]
    LessThan,
    #[serde(rename = "=")]
    #[default]
    Equal,
}

// ============================================================================
// 便捷构造方法
// ============================================================================

impl TierRules {
    /// 添加阶梯
    pub fn add_tier(mut self, tier: CommissionTier) -> Self {
        self.tiers.push(tier);
        self
    }

    /// 添加多个阶梯
    pub fn add_tiers(mut self, tiers: Vec<CommissionTier>) -> Self {
        self.tiers.extend(tiers);
        self
    }
}

impl CommissionTier {
    /// 创建OR逻辑的阶梯
    pub fn or_tier(name: String, commission_rate: Decimal) -> Self {
        Self {
            tier_name: name,
            logic_type: LogicType::Or,
            conditions: Vec::new(),
            commission_rate,
            description: None,
        }
    }

    /// 创建AND逻辑的阶梯
    pub fn and_tier(name: String, commission_rate: Decimal) -> Self {
        Self {
            tier_name: name,
            logic_type: LogicType::And,
            conditions: Vec::new(),
            commission_rate,
            description: None,
        }
    }

    /// 添加条件
    pub fn add_condition(mut self, condition: TierCondition) -> Self {
        self.conditions.push(condition);
        self
    }

    /// 添加销售额条件
    pub fn add_sales_condition(self, threshold: Decimal, operator: ComparisonOperator) -> Self {
        self.add_condition(TierCondition::new(
            ConditionType::SalesAmount,
            threshold,
            operator,
        ))
    }

    /// 添加客户数条件
    pub fn add_customer_condition(self, threshold: Decimal, operator: ComparisonOperator) -> Self {
        self.add_condition(TierCondition::new(
            ConditionType::CustomerCount,
            threshold,
            operator,
        ))
    }

    /// 设置描述
    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }
}

//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.12

use sea_orm::entity::prelude::*;
use sea_orm::{DeriveActiveEnum, EnumIter};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;

/// 商户用户信息表实体
///
/// 存储商户用户的基本信息，包括登录凭证、个人信息、
/// 账户状态、登录记录等核心用户数据
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "business", table_name = "merchant_users")]
pub struct Model {
    /// 用户ID - 主键，UUID类型，非自增
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    /// 用户名 - 用户的登录用户名，用于身份验证
    pub username: String,
    /// 密码 - 用户的登录密码，已加密存储
    pub password: String,
    /// 真实姓名 - 用户的真实姓名，具有唯一性约束
    #[sea_orm(unique)]
    pub real_name: String,
    /// 手机号码 - 用户的手机号码，用于联系和验证
    pub phone: String,
    /// 电子邮箱 - 用户的电子邮箱地址，可为空
    pub email: Option<String>,
    /// 头像 - 用户头像的图片URL或路径，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub avatar: Option<String>,
    /// 性别 - 用户的性别信息，可为空
    pub gender: Option<MerchantUserGender>,
    /// 身份证号 - 用户的身份证号码，可为空
    pub id_card: Option<String>,
    /// 账户状态 - 用户账户的当前状态（启用/禁用/锁定）
    pub status: MerchantUserStatus,
    /// 商户用户登录默认选择商户
    pub default_merchant_id: Option<i64>,
    /// 最后登录时间 - 用户最后一次登录的时间戳，带时区信息
    pub last_login_date: Option<DateTimeWithTimeZone>,
    /// 最后登录IP - 用户最后一次登录的IP地址
    pub last_login_ip: Option<String>,
    /// 创建时间 - 记录创建的时间戳，带时区信息
    pub created_date: DateTimeWithTimeZone,
    /// 更新时间 - 记录最后更新的时间戳，带时区信息
    pub updated_date: DateTimeWithTimeZone,
    /// 创建人ID - 创建该记录的用户ID，可为空
    pub created_by: Option<Uuid>,
    /// 更新人ID - 最后更新该记录的用户ID，可为空
    pub updated_by: Option<Uuid>,
    /// 备注信息 - 额外的备注说明，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
    /// 微信OpenID - 微信小程序用户唯一标识，用于微信登录
    #[sea_orm(unique)]
    pub wechat_openid: Option<String>,
    /// 微信昵称 - 微信用户的昵称，用于展示绑定的微信账号信息
    pub wechat_nickname: Option<String>,
    /// 微信头像 - 微信用户的头像URL，用于展示绑定的微信账号头像
    #[sea_orm(column_type = "Text", nullable)]
    pub wechat_avatar: Option<String>,
}

/// 用户性别枚举
///
/// 定义用户的性别类型，包括男性、女性和未知
#[derive(
    Debug, Clone, Copy, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize, ToSchema,
)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum MerchantUserGender {
    /// 男性
    #[sea_orm(num_value = 1)]
    Male = 1,
    /// 女性
    #[sea_orm(num_value = 2)]
    Female = 2,
    /// 未知 - 用户未提供性别信息或选择不透露
    #[sea_orm(num_value = 3)]
    Unknown = 3,
}

impl MerchantUserGender {
    /// 从整数值解析性别枚举
    /// 
    /// # Arguments
    /// * `value` - 整数值 (1=男性, 2=女性, 3=未知)
    /// 
    /// # Returns
    /// * `Ok(MerchantUserGender)` - 成功解析的性别枚举
    /// * `Err(String)` - 解析失败的错误信息
    pub fn from_i32(value: i32) -> Result<Self, String> {
        match value {
            1 => Ok(MerchantUserGender::Male),
            2 => Ok(MerchantUserGender::Female),
            3 => Ok(MerchantUserGender::Unknown),
            _ => Err(format!("无效的性别值: {}，有效值为 1(男性)、2(女性)、3(未知)", value)),
        }
    }
}

/// 用户状态枚举
///
/// 定义用户账户的状态，用于控制用户的访问权限
#[derive(
    Debug, Clone, Copy, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize, ToSchema,
)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum MerchantUserStatus {
    /// 启用 - 用户可以正常登录和使用系统
    #[sea_orm(num_value = 1)]
    Enabled = 1,
    /// 禁用 - 用户被管理员禁用，无法登录系统
    #[sea_orm(num_value = 2)]
    Disabled = 2,
    /// 锁定 - 用户账户被锁定，通常因为多次登录失败或安全原因
    #[sea_orm(num_value = 3)]
    Locked = 3,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::merchant_user_roles::Entity")]
    MerchantUserRoles,
    #[sea_orm(has_many = "super::merchant_user_merchants::Entity")]
    MerchantUserMerchants,
    #[sea_orm(
        belongs_to = "super::merchants::Entity",
        from = "Column::DefaultMerchantId",
        to = "super::merchants::Column::Id"
    )]
    DefaultMerchant,
}

impl ActiveModelBehavior for ActiveModel {}

impl Related<super::merchant_user_roles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantUserRoles.def()
    }
}

impl Related<super::merchant_user_merchants::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantUserMerchants.def()
    }
}

// 通过用户商户关系表与商户建立多对多关系
impl Related<super::merchants::Entity> for Entity {
    fn to() -> RelationDef {
        super::merchant_user_merchants::Relation::Merchants.def()
    }

    fn via() -> Option<RelationDef> {
        Some(
            super::merchant_user_merchants::Relation::MerchantUsers
                .def()
                .rev(),
        )
    }
}

// 通过用户商户关系表和用户角色关系表与角色建立间接关系
impl Related<super::merchant_roles::Entity> for Entity {
    fn to() -> RelationDef {
        super::merchant_roles::Relation::MerchantUserRoles.def()
    }

    fn via() -> Option<RelationDef> {
        Some(
            super::merchant_user_roles::Relation::MerchantUserMerchants
                .def()
                .rev(),
        )
    }
}

/// 完整的用户登录信息
#[derive(Debug, Clone)]
pub struct LoginInfo {
    /// 用户基本信息
    pub user: Model,
    /// 用户关联的商户ID列表
    pub merchant_ids: Vec<i64>,
    /// 默认商户ID（优先选择有管理员角色的商户）
    pub default_merchant_id: Option<i64>,
}

impl Model {
    /// 检查用户是否可以登录
    pub fn can_login(&self) -> bool {
        self.status == MerchantUserStatus::Enabled // 1-启用
    }

    /// 检查用户是否被禁用
    pub fn is_disabled(&self) -> bool {
        self.status == MerchantUserStatus::Disabled // 2-禁用
    }

    /// 检查用户是否被锁定
    pub fn is_locked(&self) -> bool {
        self.status == MerchantUserStatus::Locked // 3-锁定
    }

    /// 获取用户状态描述
    pub fn get_status_desc(&self) -> &'static str {
        match self.status {
            MerchantUserStatus::Enabled => "启用",
            MerchantUserStatus::Disabled => "禁用",
            MerchantUserStatus::Locked => "锁定",
        }
    }

    /// 获取性别描述
    pub fn get_gender_desc(&self) -> &'static str {
        match self.gender {
            Some(MerchantUserGender::Male) => "男",
            Some(MerchantUserGender::Female) => "女",
            Some(MerchantUserGender::Unknown) | None => "未知",
        }
    }

    /// 检查是否已绑定微信
    pub fn has_wechat(&self) -> bool {
        self.wechat_openid.is_some() && !self.wechat_openid.as_ref().unwrap().is_empty()
    }

    /// 检查是否有微信昵称
    pub fn has_wechat_nickname(&self) -> bool {
        self.wechat_nickname.is_some() && !self.wechat_nickname.as_ref().unwrap().is_empty()
    }

    /// 检查是否有微信头像
    pub fn has_wechat_avatar(&self) -> bool {
        self.wechat_avatar.is_some() && !self.wechat_avatar.as_ref().unwrap().is_empty()
    }

    /// 获取微信显示名称（优先微信昵称，否则显示"已绑定微信"）
    pub fn wechat_display_name(&self) -> String {
        if self.has_wechat() {
            if let Some(nickname) = &self.wechat_nickname {
                if !nickname.is_empty() {
                    return nickname.clone();
                }
            }
            "已绑定微信".to_string()
        } else {
            "未绑定微信".to_string()
        }
    }
}

impl Entity {}

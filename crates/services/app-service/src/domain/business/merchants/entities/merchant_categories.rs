//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.12

use sea_orm::entity::prelude::*;
use serde::{Deserialize, Serialize};

/// 商户分类表实体
/// 
/// 存储商户分类的基本信息，用于对商户进行分类管理，
/// 包括分类名称、编码、描述、排序等信息
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "business", table_name = "merchant_categories")]
pub struct Model {
    /// 分类ID - 主键，UUID类型，非自增
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    /// 分类名称 - 商户分类的显示名称，具有唯一性约束
    #[sea_orm(unique)]
    pub category_name: String,
    /// 分类编码 - 商户分类的唯一标识编码，用于程序逻辑识别
    pub category_code: Option<String>,
    /// 分类描述 - 商户分类的详细描述信息，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub description: Option<String>,
    /// 排序顺序 - 分类在列表中的显示顺序，数值越小排序越靠前
    pub sort_order: Option<i32>,
    /// 分类状态 - 商户分类的当前状态（启用/禁用）
    pub status: MerchantCategoryStatus,
    /// 创建时间 - 记录创建的时间戳，带时区信息
    pub created_date: DateTimeWithTimeZone,
    /// 更新时间 - 记录最后更新的时间戳，带时区信息
    pub updated_date: DateTimeWithTimeZone,
    /// 创建人ID - 创建该记录的用户ID，可为空
    pub created_by: Option<Uuid>,
    /// 更新人ID - 最后更新该记录的用户ID，可为空
    pub updated_by: Option<Uuid>,
    /// 备注信息 - 额外的备注说明，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::merchants::Entity")]
    Merchants,
}

/// 商户分类状态枚举
/// 
/// 定义商户分类的状态，用于控制分类的有效性
#[derive(Debug, Clone, Copy, PartialEq, Eq, EnumIter, DeriveActiveEnum,Serialize, Deserialize)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum MerchantCategoryStatus {
    /// 启用 - 分类正常使用，可以关联商户
    #[sea_orm(num_value = 1)]
    Enabled = 1,
    /// 禁用 - 分类被禁用，不能关联新的商户
    #[sea_orm(num_value = 2)]
    Disabled = 2,
}

impl ActiveModelBehavior for ActiveModel {}

impl Related<super::merchants::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Merchants.def()
    }
}

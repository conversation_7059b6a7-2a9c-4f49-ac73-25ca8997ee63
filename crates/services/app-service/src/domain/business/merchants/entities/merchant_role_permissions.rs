//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.12

use crate::utils::DateTimeUtils;
use sea_orm::entity::prelude::*;
use sea_orm::Set;
use serde::{Deserialize, Serialize};

/// 商户角色权限关联表实体
/// 
/// 存储商户角色与权限的多对多关联关系，实现角色权限管理，
/// 通过授权权限ID关联到具体的权限模板
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize)]
#[sea_orm(schema_name = "business", table_name = "merchant_role_permissions")]
pub struct Model {
    /// 关联ID - 主键，UUID类型，非自增
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    /// 商户ID - 关联的商户ID，表示该权限关联属于哪个商户
    pub merchant_id: i64,
    /// 角色ID - 关联的角色ID，表示哪个角色拥有该权限
    pub role_id: Uuid,
    /// 授权权限ID - 关联的授权权限ID，指向merchant_authorized_permissions表
    pub authorized_permission_id: Uuid,
    /// 创建时间 - 记录创建的时间戳，带时区信息
    pub created_date: DateTimeWithTimeZone,
    /// 创建人ID - 创建该记录的用户ID，可为空
    pub created_by: Option<Uuid>,
    /// 备注信息 - 额外的备注说明，存储为文本类型
    #[sea_orm(column_type = "Text", nullable)]
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(
        belongs_to = "super::merchants::Entity",
        from = "Column::MerchantId",
        to = "super::merchants::Column::Id"
    )]
    Merchants,
    #[sea_orm(
        belongs_to = "super::merchant_roles::Entity",
        from = "Column::RoleId",
        to = "super::merchant_roles::Column::Id"
    )]
    MerchantRoles,
    #[sea_orm(
        belongs_to = "super::merchant_authorized_permissions::Entity",
        from = "Column::AuthorizedPermissionId",
        to = "super::merchant_authorized_permissions::Column::Id"
    )]
    MerchantAuthorizedPermissions,
}

impl ActiveModelBehavior for ActiveModel {}

impl Related<super::merchants::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Merchants.def()
    }
}

impl Related<super::merchant_roles::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantRoles.def()
    }
}

impl Related<super::merchant_authorized_permissions::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::MerchantAuthorizedPermissions.def()
    }
}

impl Entity {
    /// 从role_id和权限模版ids(Vec)集合
    ///
    /// 创建一个Vec实体并插入数据库
    pub async fn create_from_role_id_and_authorized_permissions_ids<C>(
        role_id: Uuid,
        merchant_id: i64,
        authorized_permissions_ids: Vec<Uuid>,
        operator_id: Uuid,
        db: &C,
    ) -> Result<(), DbErr>
    where
        C: ConnectionTrait,
    {
        let mut authorized_permissions: Vec<ActiveModel> = vec![];
        authorized_permissions_ids
            .iter()
            .for_each(|authorized_permissions_id| {
                let p = ActiveModel {
                    id: Set(Uuid::now_v7()),
                    merchant_id: Set(merchant_id),
                    role_id: Set(role_id),
                    authorized_permission_id: Set(*authorized_permissions_id),
                    created_date: Set(DateTimeUtils::now_china_offset()),
                    created_by: Set(Some(operator_id)),
                    remark: Set(Some("系统自动创建".to_string())),
                };
                authorized_permissions.push(p);
            });

        Self::insert_many(authorized_permissions).exec(db).await?;

        Ok(())
    }
}

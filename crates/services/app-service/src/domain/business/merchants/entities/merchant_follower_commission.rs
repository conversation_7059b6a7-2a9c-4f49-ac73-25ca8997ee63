//! 商户跟进人佣金表实体
//! 对应表：business.merchant_follower_commission

use crate::utils::DateTimeUtils;
use async_trait::async_trait;
use derive_more::{From, Into};
use rust_decimal::Decimal;
use sea_orm::entity::prelude::*;
use sea_orm::{ConnectionTrait, DbErr, Set};
use sea_orm::{DeriveActiveEnum, EnumIter};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use uuid::Uuid;

/// 佣金类型枚举
///
/// 定义佣金的计算方式类型
#[derive(
    Debug, Clone, Copy, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize, ToSchema,
)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum CommissionType {
    /// 比例佣金 - 按照百分比计算佣金
    #[sea_orm(num_value = 1)]
    Percentage = 1,
    /// 固定金额佣金 - 按照固定金额计算佣金
    #[sea_orm(num_value = 2)]
    Fixed = 2,
}

/// 商户跟进人佣金表实体
///
/// 存储商户跟进人的佣金配置信息，包括佣金类型、佣金值等，
/// 用于计算跟进人在商户业务中的佣金收益
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Into, From)]
#[sea_orm(schema_name = "business", table_name = "merchant_follower_commission")]
pub struct Model {
    /// 佣金记录ID - 主键，UUID类型，非自增
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    /// 商户ID - 关联的商户ID，表示该佣金配置属于哪个商户
    pub merchant_id: i64,
    /// 跟进人ID - 关联的用户ID，表示该佣金配置属于哪个跟进人
    pub user_id: Uuid,
    /// 佣金类型 - 佣金的计算方式（比例/固定金额）
    pub commission_type: CommissionType,
    /// 佣金值 - 佣金的具体数值，根据佣金类型解释（比例时为百分比，固定时为金额）
    pub commission_value: Decimal,
    /// 创建时间 - 记录创建的时间戳，带时区信息（中国时区）
    pub created_date: DateTimeWithTimeZone,
    /// 更新时间 - 记录最后更新的时间戳，带时区信息（中国时区）
    pub updated_date: DateTimeWithTimeZone,
    /// 备注信息 - 额外的备注说明，可为空
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    /// 逻辑关联商户表（business.merchants）
    #[sea_orm(
        belongs_to = "super::merchants::Entity",
        from = "Column::MerchantId",
        to = "super::merchants::Column::Id"
    )]
    Merchant,
    /// 逻辑关联商户用户表（business.merchant_users）
    #[sea_orm(
        belongs_to = "super::merchant_users::Entity",
        from = "Column::UserId",
        to = "super::merchant_users::Column::Id"
    )]
    User,
}

#[async_trait]
impl ActiveModelBehavior for ActiveModel {
    /// 插入或更新前自动设置时间戳
    async fn before_save<C>(mut self, _db: &C, insert: bool) -> Result<Self, DbErr>
    where
        C: ConnectionTrait,
    {
        let now = DateTimeUtils::now_china_offset();
        if insert {
            self.created_date = Set(now);
        }
        self.updated_date = Set(now);
        Ok(self)
    }
}

impl Related<super::merchants::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Merchant.def()
    }
}

impl Related<super::merchant_users::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::User.def()
    }
}

/// 从请求数据和相关参数创建 ActiveModel
///
/// 参数说明：
/// - req: SysMerchantFollowerCommissionRequest 请求体引用
/// - merchant_id: 商户ID
/// - commission_type: 佣金类型枚举
/// - commission_value: 解析后的佣金值
/// - now: 当前时间
impl
    From<(
        &crate::domain::system::dto::sys_merchant_request::SysMerchantFollowerCommissionRequest,
        i64,
        CommissionType,
        Decimal,
        DateTimeWithTimeZone,
    )> for ActiveModel
{
    fn from(
        (req, merchant_id, commission_type, commission_value, now): (
            &crate::domain::system::dto::sys_merchant_request::SysMerchantFollowerCommissionRequest,
            i64,
            CommissionType,
            Decimal,
            DateTimeWithTimeZone,
        ),
    ) -> Self {
        Self {
            id: Set(Uuid::now_v7()),
            merchant_id: Set(merchant_id),
            user_id: Set(req.follower_id),
            commission_type: Set(commission_type),
            commission_value: Set(commission_value),
            created_date: Set(now),
            updated_date: Set(now),
            remark: Set(req.follower_commission_remark.clone()),
        }
    }
}

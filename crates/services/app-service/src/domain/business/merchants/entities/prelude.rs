//! `SeaORM` Entity, @generated by sea-orm-codegen 1.1.12

pub use super::merchant_authorized_permissions::Entity as MerchantAuthorizedPermissions;
pub use super::merchant_categories::Entity as MerchantCategories;
pub use super::merchant_follower_commission::Entity as MerchantFollowerCommission;
pub use super::merchant_role_permissions::Entity as MerchantRolePermissions;
pub use super::merchant_roles::Entity as MerchantRoles;
pub use super::merchant_user_roles::Entity as MerchantUserRoles;
pub use super::merchant_users::Entity as MerchantUsers;
pub use super::merchants::Entity as Merchants;
pub use super::system_permission_templates::Entity as SystemPermissionTemplates;

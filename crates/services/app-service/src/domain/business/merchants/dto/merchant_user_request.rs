use serde::Deserialize;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use validator::Validate;

// ==================== 商户用户登录相关 ====================

/// 商户用户登录请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "phone": "13800138000", 
    "password": "123456"
}))]
pub struct MerchantUserLoginRequest {
    /// 手机号
    #[validate(length(min = 11, max = 11, message = "手机号必须为11位"))]
    #[validate(custom(
        function = "crate::utils::validation::validate_phone_custom",
        message = "手机号格式不正确"
    ))]
    pub phone: String,

    /// 密码
    #[validate(length(min = 6, max = 30, message = "密码长度必须在6-30个字符之间"))]
    pub password: String,
}

/// 商户用户微信登录请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "code": "061Fwoml0VKPiX1uN5ol0jBqlq3Fwomk"
}))]
pub struct MerchantUserWechatLoginRequest {
    /// 微信授权码，通过微信登录获取
    #[validate(length(min = 1, message = "微信授权码不能为空"))]
    pub code: String,
}

// ==================== 商户列表相关 ====================

/// 获取用户商户列表请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "include_disabled": false
}))]
pub struct UserMerchantListRequest {
    /// 是否包含已禁用的商户，默认为false（仅显示启用的商户）
    #[serde(default)]
    pub include_disabled: bool,
}

// ==================== 切换商户相关 ====================

/// 切换当前商户请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "merchant_id": 123456
}))]
pub struct SwitchMerchantRequest {
    /// 要切换到的商户ID
    #[validate(range(min = 1, message = "商户ID必须大于0"))]
    pub merchant_id: i64,
}

// ==================== 修改密码相关 ====================

/// 修改密码请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "old_password": "123456",
    "new_password": "newpassword123"
}))]
pub struct ChangePasswordRequest {
    /// 旧密码
    #[validate(length(min = 6, max = 30, message = "旧密码长度必须在6-30个字符之间"))]
    pub old_password: String,

    /// 新密码
    #[validate(length(min = 6, max = 30, message = "新密码长度必须在6-30个字符之间"))]
    pub new_password: String,
}


// ==================== 修改个人信息相关 ====================

/// 修改个人信息请求
/// 注意：密码、身份证号、真实姓名、状态等字段不能通过此接口修改
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "username": "newusername",
    "phone": "13800138001", 
    "email": "<EMAIL>",
    "avatar": "https://example.com/new-avatar.jpg",
    "gender": 1,
    "remark": "更新个人信息",
    "default_merchant_id": 123456
}))]
pub struct UpdateProfileRequest {
    /// 用户名（可选）
    #[validate(length(min = 2, max = 50, message = "用户名长度必须在2-50个字符之间"))]
    pub username: Option<String>,

    /// 手机号（可选）
    #[validate(length(min = 11, max = 11, message = "手机号必须为11位"))]
    #[validate(custom(
        function = "crate::utils::validation::validate_phone_custom",
        message = "手机号格式不正确"
    ))]
    pub phone: Option<String>,

    /// 邮箱（可选）
    #[validate(email(message = "邮箱格式不正确"))]
    #[validate(length(max = 100, message = "邮箱长度不能超过100个字符"))]
    pub email: Option<String>,

    /// 头像URL（可选）
    #[validate(url(message = "头像URL格式不正确"))]
    pub avatar: Option<String>,

    /// 性别（可选）
    /// 1: 男性, 2: 女性, 3: 未知
    #[validate(range(min = 1, max = 3, message = "性别值必须为1(男性)、2(女性)或3(未知)"))]
    pub gender: Option<i32>,

    /// 备注信息（可选）
    #[validate(length(max = 500, message = "备注信息长度不能超过500个字符"))]
    pub remark: Option<String>,

    /// 默认商户ID（可选）
    #[validate(range(min = 1, message = "默认商户ID必须大于0"))]
    pub default_merchant_id: Option<i64>,
}

// ==================== 微信绑定相关 ====================

/// 绑定微信请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "code": "061Fwoml0VKPiX1uN5ol0jBqlq3Fwomk"
}))]
pub struct BindWechatRequest {
    /// 微信授权码，通过微信登录获取
    #[validate(length(min = 1, message = "微信授权码不能为空"))]
    pub code: String,
}

/// 解绑微信请求
#[derive(Debug, Deserialize, Validate, ToSchema)]
#[schema(example = json!({
    "password": "123456"
}))]
pub struct UnbindWechatRequest {
    /// 当前密码，用于安全验证
    #[validate(length(min = 6, max = 30, message = "密码长度必须在6-30个字符之间"))]
    pub password: String,
}

use serde::Serialize;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;

// ==================== 商户角色响应相关 VO ====================

/// 商户用户角色信息 - 用于当前用户信息中显示角色
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "id": "f1e6b32e-4471-4192-a709-36d76b92050a",
    "name": "超级管理员",
    "role_code": "ADMIN",
    "description": "系统超级管理员，拥有所有权限"
}))]
pub struct MerchantUserRoleInfo {
    /// 角色ID
    pub id: String,
    /// 角色名称
    pub name: String,
    /// 角色编码
    pub role_code: String,
    /// 角色描述
    pub description: Option<String>,
}

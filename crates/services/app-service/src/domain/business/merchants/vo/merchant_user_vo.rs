use crate::domain::business::merchants::entities::merchant_follower_commission::CommissionType;
use crate::domain::business::merchants::entities::merchant_users::{
    MerchantUserGender, MerchantUserStatus,
};
use bon::Builder;
use derive_more::Constructor;
use rust_decimal::Decimal;
use sea_orm::prelude::DateTimeWithTimeZone;
use sea_orm::FromQueryResult;
use serde::Serialize;
#[allow(unused_imports)]
use serde_json::json;
use utoipa::ToSchema;
use uuid::Uuid;
// ==================== 商户用户响应相关 VO ====================

/// 商户用户登录成功返回信息
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
    })
)]
pub struct MerchantUserLoginResponse {
    /// 访问令牌，用于后续API请求的身份验证
    pub token: String,
}

/// 商户跟进人信息 - 专门用于数据库查询
#[derive(Debug, FromQueryResult)]
pub struct FollowerUserInfo {
    /// 用户ID - 数据库查询时需要，API响应时可选
    pub id: Option<Uuid>,
    /// 用户名
    pub username: String,
    /// 真实姓名
    pub real_name: String,
    /// 手机号码
    pub phone: String,
    /// 头像
    pub avatar: Option<String>,
    /// 性别 - 原始数据库类型：1=男，2=女，3=未知
    pub gender: Option<i32>,
    /// 账户状态 - 原始数据库类型：1=启用，2=禁用，3=锁定
    pub status: i32,
    /// 最后登录时间 - 原始数据库类型
    pub last_login_date: Option<DateTimeWithTimeZone>,
    /// 最后登录IP
    pub last_login_ip: Option<String>,
    /// 备注信息 - 用户备注
    pub user_remark: Option<String>,
    /// 佣金类型
    pub commission_type: CommissionType,
    /// 佣金值 - 原始数据库类型
    pub commission_value: Decimal,
    /// 佣金备注
    pub commission_remark: Option<String>,
}

impl FollowerUserInfo {
    /// 转换为API响应格式
    pub fn to_response(self) -> FollowerUserResponse {
        // 转换性别枚举
        let gender = match self.gender {
            Some(1) => Some(MerchantUserGender::Male),
            Some(2) => Some(MerchantUserGender::Female),
            _ => Some(MerchantUserGender::Unknown),
        };

        // 转换状态枚举
        let status = match self.status {
            1 => MerchantUserStatus::Enabled,
            2 => MerchantUserStatus::Disabled,
            _ => MerchantUserStatus::Locked,
        };

        // 格式化登录时间
        let last_login_date = self
            .last_login_date
            .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string());

        // 合并备注信息（优先使用佣金备注，如果没有则使用用户备注）
        let remark = self.commission_remark.or(self.user_remark);

        FollowerUserResponse {
            username: self.username,
            real_name: self.real_name,
            phone: self.phone,
            avatar: self.avatar,
            gender,
            status,
            last_login_date,
            last_login_ip: self.last_login_ip,
            remark,
            commission_type: self.commission_type,
            commission_value: self.commission_value.to_string(),
        }
    }
}

/// 商户跟进人响应信息 - 专门用于API响应
#[derive(Debug, Serialize, ToSchema)]
#[schema(
    example = json!({
        "username": "zhangsan",
        "real_name": "张三",
        "phone": "13800138000",
        "avatar": "https://example.com/avatar.png",
        "gender": 1,
        "status": 1,
        "last_login_date": "2024-07-18 10:00:00",
        "last_login_ip": "*************",
        "remark": "核心跟进人",
        "commission_type": 1,
        "commission_value": "0.05"
    })
)]
pub struct FollowerUserResponse {
    /// 用户名
    pub username: String,
    /// 真实姓名
    pub real_name: String,
    /// 手机号码
    pub phone: String,
    /// 头像
    pub avatar: Option<String>,
    /// 性别
    pub gender: Option<MerchantUserGender>,
    /// 账户状态 - 用户账户的当前状态（启用/禁用/锁定）
    pub status: MerchantUserStatus,
    /// 最后登录时间
    pub last_login_date: Option<String>,
    /// 最后登录IP
    pub last_login_ip: Option<String>,
    /// 备注信息
    pub remark: Option<String>,
    /// 佣金类型：1=比例，2=固定金额
    pub commission_type: CommissionType,
    /// 佣金值：比例（如0.05）或金额（如100.00）
    /// 佣金值 - 佣金的具体数值，根据佣金类型解释（比例时为百分比，固定时为金额）
    pub commission_value: String,
}

// ==================== 商户列表响应相关 VO ====================

/// 商户角色信息 - 用于商户列表中显示用户在商户下的角色
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "role_id": "550e8400-e29b-41d4-a716-************",
    "role_name": "商户管理员",
    "role_code": "merchant_admin"
}))]
pub struct MerchantRoleInfo {
    /// 角色ID
    pub role_id: Uuid,
    /// 角色名称
    pub role_name: String,
    /// 角色编码
    pub role_code: Option<String>,
}

/// 商户分类信息 - 用于商户列表中显示商户分类
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "category_id": "550e8400-e29b-41d4-a716-************",
    "category_name": "餐饮服务"
}))]
pub struct MerchantCategoryInfo {
    /// 分类ID
    pub category_id: Uuid,
    /// 分类名称
    pub category_name: String,
}

/// 用户商户列表项 - 用户关联的商户详细信息
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "merchant_id": 123456,
    "merchant_name": "张三餐厅",
    "merchant_code": "REST001",
    "merchant_category": {
        "category_id": "550e8400-e29b-41d4-a716-************",
        "category_name": "餐饮服务"
    },
    "merchant_avatar": "https://example.com/avatar.jpg",
    "user_roles": [
        {
            "role_id": "550e8400-e29b-41d4-a716-************",
            "role_name": "商户管理员",
            "role_code": "merchant_admin"
        }
    ]
}))]
pub struct UserMerchantListItemResponse {
    /// 商户ID
    pub merchant_id: i64,
    /// 商户名称
    pub merchant_name: String,
    /// 商户编码
    pub merchant_code: String,
    /// 商户分类信息（可能为空）
    pub merchant_category: Option<MerchantCategoryInfo>,
    /// 商户头像logo（可能为空）
    pub merchant_avatar: Option<String>,
    /// 用户在该商户下的角色列表
    pub user_roles: Vec<MerchantRoleInfo>,
}

/// 用户商户列表响应 - 获取用户关联的所有商户列表
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "merchants": [
        {
            "merchant_id": 123456,
            "merchant_name": "张三餐厅",
            "merchant_code": "REST001",
            "merchant_category": {
                "category_id": "550e8400-e29b-41d4-a716-************",
                "category_name": "餐饮服务"
            },
            "merchant_avatar": "https://example.com/avatar.jpg",
            "user_roles": [
                {
                    "role_id": "550e8400-e29b-41d4-a716-************",
                    "role_name": "商户管理员",
                    "role_code": "merchant_admin"
                }
            ]
        }
    ],
    "total_count": 1
}))]
pub struct UserMerchantListResponse {
    /// 商户列表
    pub merchants: Vec<UserMerchantListItemResponse>,
    /// 商户总数
    pub total_count: usize,
}

// ==================== 当前用户信息响应相关 VO ====================

/// 当前用户信息响应 - 获取商户用户当前登录人的详细信息
#[derive(Debug, Serialize, ToSchema, Builder, Constructor)]
#[schema(example = json!({
    "id": "2d317d33-e2ab-4ce4-b694-c69c1c112824",
    "username": "admin",
    "real_name": "张三",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "avatar": "https://static.oywm.top/avatars/users/55/9b/fe7a8f81-0825-4c9f-accd-622103ecaf36.png",
    "gender": 1,
    "status": 1,
    "last_login_date": "2025-07-28 11:15:32",
    "last_login_ip": "*************",
    "current_merchant": {
        "merchant_id": 123456,
        "merchant_name": "张三餐厅",
        "merchant_code": "REST001",
        "merchant_category": {
            "category_id": "550e8400-e29b-41d4-a716-************",
            "category_name": "餐饮服务"
        },
        "merchant_avatar": "https://example.com/avatar.jpg",
        "user_roles": [
            {
                "role_id": "550e8400-e29b-41d4-a716-************",
                "role_name": "商户管理员",
                "role_code": "merchant_admin"
            }
        ]
    },
    "permissions": [
        "sys:permission:detail",
        "permission_template:delete",
        "sys:role:update"
    ]
}))]
pub struct MerchantUserProfileResponse {
    /// 用户ID
    pub id: String,
    /// 用户名
    pub username: String,
    /// 真实姓名
    pub real_name: Option<String>,
    /// 手机号
    pub phone: Option<String>,
    /// 邮箱
    pub email: Option<String>,
    /// 头像
    pub avatar: Option<String>,
    /// 性别
    pub gender: Option<MerchantUserGender>,
    /// 账户状态
    pub status: i32,
    /// 最后登录时间
    pub last_login_date: Option<String>,
    /// 最后登录IP
    pub last_login_ip: Option<String>,
    /// 当前商户信息
    pub current_merchant: UserMerchantListItemResponse,
    /// 权限列表
    pub permissions: Vec<String>,
    /// 是否绑定微信
    pub wechat_bound: bool,
}

// ==================== 切换商户响应相关 VO ====================

/// 切换商户响应 - 切换当前商户后的响应信息
#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "merchant_name": "张三餐厅"
}))]
pub struct SwitchMerchantResponse {
    /// 切换后的商户名称
    pub merchant_name: String,
}

// ==================== From trait 实现 ====================

/// 从商户角色实体转换为角色信息VO
impl From<crate::domain::business::merchants::entities::merchant_roles::Model>
    for MerchantRoleInfo
{
    fn from(role: crate::domain::business::merchants::entities::merchant_roles::Model) -> Self {
        Self {
            role_id: role.id,
            role_name: role.role_name,
            role_code: role.role_code,
        }
    }
}

/// 从商户分类实体转换为分类信息VO
impl From<crate::domain::business::merchants::entities::merchant_categories::Model>
    for MerchantCategoryInfo
{
    fn from(
        category: crate::domain::business::merchants::entities::merchant_categories::Model,
    ) -> Self {
        Self {
            category_id: category.id,
            category_name: category.category_name,
        }
    }
}

/// 从数据库查询结果转换为用户商户列表项VO
impl
    From<(
        crate::domain::business::merchants::entities::merchants::Model,
        Option<crate::domain::business::merchants::entities::merchant_categories::Model>,
        Vec<crate::domain::business::merchants::entities::merchant_roles::Model>,
    )> for UserMerchantListItemResponse
{
    fn from(
        (merchant, category, roles): (
            crate::domain::business::merchants::entities::merchants::Model,
            Option<crate::domain::business::merchants::entities::merchant_categories::Model>,
            Vec<crate::domain::business::merchants::entities::merchant_roles::Model>,
        ),
    ) -> Self {
        // 转换分类信息
        let merchant_category = category.map(MerchantCategoryInfo::from);

        // 转换角色信息
        let user_roles = roles.into_iter().map(MerchantRoleInfo::from).collect();

        Self {
            merchant_id: merchant.id,
            merchant_name: merchant.merchant_name,
            merchant_code: merchant.merchant_code,
            merchant_category,
            merchant_avatar: merchant.avatar,
            user_roles,
        }
    }
}

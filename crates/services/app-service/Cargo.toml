[package]
name = "app-service"
version.workspace = true
edition.workspace = true

[dependencies]
lib-auth = { path = "../../libs/lib-auth" }
lib-core = { path = "../../libs/lib-core" }
lib-web = { path = "../../libs/lib-web" }
lib-data = { path = "../../libs/lib-data" }
lib-cache = { path = "../../libs/lib-cache" }
lib-macros = { path = "../../libs/lib-macros" }
lib-job = { path = "../../libs/lib-job" }
lib-store = { path = "../../libs/lib-store" }
lib-sharding = { path = "../../libs/lib-sharding" }
lib-wechat = { path = "../../libs/lib-wechat" }

tokio = { workspace = true, features = ["full", "fs"] }
tracing = { workspace = true }
sea-orm = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
serde_yaml = { workspace = true }
axum = { workspace = true, features = ["multipart", "macros"] }
tower-http = { workspace = true, features = ["trace"] }
uuid = { workspace = true, features = ["v7"] }
argon2 = { workspace = true }
password-hash = { workspace = true }
validator = { workspace = true, features = ["derive"] }
chrono = { workspace = true, features = ["serde"] }
anyhow = { workspace = true }
futures = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "fmt"] }
async-trait = { workspace = true }
sea-query = { workspace = true }
thiserror = { workspace = true }
# API文档生成相关依赖
utoipa = { workspace = true, features = [
    "axum_extras",
    "chrono",
    "uuid",
    "preserve_order",
    "macros",
    "decimal",
    "time"

] }
form_urlencoded = { workspace = true }
serde_urlencoded = { workspace = true }
serde_path_to_error = { workspace = true }
# 对象构建和映射
bon = { workspace = true }
derive_more = { version = "2.0.1", features = [
    "from",
    "into",
    "try_from",
    "try_into",
    "constructor",
] }
http = { version = "0.2.11" }
rust_decimal = { workspace = true }
lazy_static = { workspace = true }
regex = { workspace = true }

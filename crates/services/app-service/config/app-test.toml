# 应用程序基本配置
[app]
server_name = "invoice-book-app-server"
port = 9002
host = "0.0.0.0"

# Redis缓存配置
[redis]
host = "localhost"
port = 6379
username = ""
password = ""
database = 0
max_connections = 8        # 最大连接数
min_idle_connections = 0   # 最小空闲连接数
idle_timeout = 300         # 空闲超时时间(秒)
connect_timeout = 1500     # 连接超时时间(毫秒)
timeout = 2000             # 操作超时时间(毫秒)
tls = false
read_timeout = 2000
write_timeout = 2000
pool_size = 8
retry_on_timeout = true
connection_name = "invoice-app"

#"postgresql://admin:123456@localhost/postgres"
# 数据库配置
[database]
host = "localhost"
port = 5432
username = "admin"
password = "123456"
db = "invoice_book"
max_connections = 32       # 最大连接数
min_idle_connections = 4   # 最小空闲连接数
idle_timeout = 300         # 空闲超时时间(秒)
connect_timeout = 1500     # 连接超时时间(毫秒)
timeout = 60               # 操作超时时间(秒)
pool_len = "32"            # 连接池大小
acquire_timeout = 8         # 获取连接超时时间(秒)
max_lifetime = 600          # 连接最大生命周期(秒)
sqlx_logging = true         # SQL 日志开关
sqlx_logging_level = "debug" # SQL 日志级别
schema = "public"           # 默认schema
# 日志配置
[log]
dir = "logs/app-service/"
console_level = "debug"    # 控制台日志级别
file_level = "debug"        # 文件日志级别
max_size = 10485760        # 单个日志文件最大大小(字节)，10MB
max_files = 30             # 保留的日志文件数量
colored = true             # 控制台日志是否使用彩色
console = true             # 是否输出到控制台

# 存储配置
[storage]
type = "local"             # 存储类型: "local"本地存储

# 安全配置
[security]
login_fail_retry = 3                  # 登录失败重试次数限制
login_fail_retry_wait_sec = 30        # 登录失败后等待时间(秒)
trash_recycle_days = 90               # 回收站数据保留天数

# JWT配置
jwt_secret = "hfauisdfjh894.3/45.34;6$%6esrj8g9s9;l234,(ds.f;ds'"
jwt_access_token_exp_sec = 310 # 60 minutes
jwt_refresh_token_exp_sec = 604800 # 7 days
jwt_issuer = "test-service"
jwt_audience = "test-client"
# JWT自动刷新配置
jwt_auto_refresh_enabled = true  # 启用JWT自动刷新
jwt_refresh_threshold_sec = 300  # 在token过期前5分钟开始自动续期
# ========================================
# 日志配置 (lib-core) - 融合新旧日志系统
# ========================================
[logger]
enable = true                      # 是否启用日志
pretty_backtrace = true            # 是否启用美观的堆栈回溯（开发环境推荐开启）
level = "debug"                    # 日志级别: off, trace, debug, info, warn, error
format = "compact"                 # 日志格式: compact, pretty, json
time_style = "local"               # 时间格式: system, uptime, local, utc, none（使用本地时间避免时区问题）
time_pattern = "%Y-%m-%d %H:%M:%S" # 时间格式模式（使用本地时间格式）

# 日志字段配置
with_fields = [
    "file",        # 包含文件名
    "line_number", # 包含行号
    "thread_name", # 包含线程名
    "thread_id",   # 线程id
    "target",      # 事件目标
]

# 自定义过滤器（可选）- 用于查看内部库的日志
# override_filter = "debug,sqlx=info,sea_orm=debug"

# 文件日志配置
[logger.file]
enable = true                   # 是否启用文件日志
non_blocking = true             # 是否使用非阻塞写入
format = "compact"              # 文件日志格式: compact, pretty, json
rotation = "daily"              # 日志轮转: minutely, hourly, daily, never
dir = "./logs/app-service"      # 日志文件目录
filename_prefix = "app-service" # 日志文件名前缀
filename_suffix = "log"         # 日志文件名后缀
max_log_files = 30              # 最大保留日志文件数量

# ========================================
# Web服务器配置 (lib-web)
# ========================================
[web]
binding = "0.0.0.0" # 服务器绑定地址
#binding = "127.0.0.1"        # 服务器绑定地址
port = 9002         # 服务器端口
connect_info = true # 是否启用连接信息
graceful = true     # 是否启用优雅关闭

# Web中间件配置
[web.middlewares]
# 响应压缩中间件
[web.middlewares.compression]
enable = true

# 请求负载大小限制中间件
[web.middlewares.limit_payload]
enable = true
body_limit = "10mb" # 请求体大小限制

# 跟踪日志中间件
[web.middlewares.logger]
enable = true
level = "info" # 日志级别: trace, debug, info, warn, error

# 恐慌捕获中间件
[web.middlewares.catch_panic]
enable = true

# 请求超时中间件
[web.middlewares.timeout_request]
enable = true
timeout = 30000 # 请求超时时间（毫秒）

# CORS中间件
[web.middlewares.cors]
enable = true
allow_origins = ["*"]                                       # 允许的源
allow_headers = ["*"]                                       # 允许的头部
allow_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"] # 允许的方法
max_age = 3600                                              # 预检请求缓存时间（秒）

# 静态资源中间件
[web.middlewares.static]
enable = true
must_exist = false      # 资源是否必须存在于磁盘
fallback = "index.html" # 404回退页面（SPA应用）
precompressed = false   # 是否启用预压缩gzip
uri = "/static"         # 静态资源URI路径
path = "static"         # 静态资源文件系统路径

# ========================================
# Redis缓存配置 (lib-cache)
# ========================================
[redis]
uri = "redis://localhost:6379/0" # Redis连接URI
host = "localhost"               # Redis服务器地址
port = 6379                      # Redis端口
database = 0                     # Redis数据库索引（0-15）
username = ""                    # Redis用户名（Redis 6.0+）
password = ""                    # Redis密码
max_connections = 30              # 连接池最大连接数
min_idle_connections = 0         # 连接池最小空闲连接数
idle_timeout = 300               # 连接空闲超时时间(秒)
connect_timeout = 1500           # 建立连接超时时间(毫秒)
timeout = 2000                   # Redis操作超时时间(毫秒)
tls = false                      # 是否启用TLS加密
read_timeout = 2000              # 读取超时时间(毫秒)
write_timeout = 2000             # 写入超时时间(毫秒)
pool_size = 16                    # 连接池大小
retry_on_timeout = true          # 超时时是否重试
connection_name = "invoice-app"  # Redis连接名称

# ========================================
# 数据库配置 (lib-data)
# ========================================
[database]
uri = "postgres://admin:123456@localhost:5432/invoice_book" # 数据库连接URI
enable_logging = true                                       # 是否启用SQLx日志记录
sqlx_logging_level = "debug"                                # SQL日志级别
max_connections = 50                                        # 连接池最大连接数
min_connections = 10                                         # 连接池最小连接数
connect_timeout = 8000                                      # 连接超时时间（毫秒）
idle_timeout = 600000                                       # 空闲超时时间（毫秒）
acquire_timeout = 5000                                     # 获取连接超时时间（毫秒）
max_lifetime = 1800000                                      # 连接最大生命周期（毫秒）
schema = "public"                                           # PostgreSQL默认schema

# 数据库Web分页配置
[database-web]
# one_indexed = false      # 是否使用基于1的页码索引（默认false）
# max_page_size = 2000     # 最大页面大小限制（默认2000）
default_page_size = 20 # 默认页面大小

# 安全配置
[security.system]
login_fail_retry = 3           # 登录失败重试次数限制
login_fail_retry_wait_sec = 30 # 登录失败后等待时间(秒)
trash_recycle_days = 90        # 回收站数据保留天数

# JWT配置 (系统用户)
jwt_secret = "hfauisdfjh894.3/45.34;6$%6esrj8g9s9;l234,(ds.f;ds'"
jwt_access_token_exp_sec = 1500                                   # 25 minutes
jwt_refresh_token_exp_sec = 604800 # 7 days
jwt_issuer = "app-service"
jwt_audience = "app-client"
# JWT自动刷新配置
jwt_auto_refresh_enabled = true # 启用JWT自动刷新
jwt_refresh_threshold_sec = 300 # 在token过期前5分钟开始自动续期

# 商户安全配置
[security.merchant]
jwt_secret = "merchant-dev-secret-key-change-in-production"
jwt_access_token_exp_sec = 28800    # 8 hours for merchant users
jwt_refresh_token_exp_sec = 2592000 # 30 days for merchant users
jwt_auto_refresh_enabled = true
jwt_refresh_threshold_sec = 1800    # 30 minutes threshold
login_fail_retry = 5
login_fail_retry_wait_sec = 180     # 3 minutes

# 存储配置
[storage]
# 阿里云 OSS 特定配置
oss_access_key_id = "LTAI5t8fsa7y77kqgEc1Zf6G"           # 阿里云 AccessKey ID
oss_access_key_secret = "******************************" # 阿里云 AccessKey Secret
oss_endpoint = "oss-cn-shenzhen.aliyuncs.com"            # 阿里云 OSS Endpoint (例如：华东1杭州)
oss_bucket_name = "invoicebookoss"                       # 您在OSS上创建的 Bucket 名称
# region 是 aws-sdk-s3 可能需要的，虽然对于阿里云OSS，endpoint 更为关键，
oss_region = "cn-shenzhen" # Bucket 所在的区域 (例如：cn-hangzhou)

oss_custom_domain = "static.oywm.top" # 如果您的 Bucket 绑定了自定义域名，可以在这里配置

# ========================================
# 分表系统配置 (lib-sharding) - 简化配置
# ========================================
[sharding]
# 基本配置
enable = true
auto_create_tables = true
schema_name = "business"

# 商户分表策略配置
merchant_strategy_enable = true
merchant_table_prefix = "merchant"
merchant_min_id = 1
merchant_max_id = 9223372036854775807 # i64::MAX
merchant_allow_negative = false

# 表管理配置
create_timeout_ms = 10000       # 创建表超时时间（毫秒）
create_retries = 2              # 表创建重试次数
cache_table_existence = true    # 是否缓存表存在性检查
table_existence_cache_ttl = 180 # 表存在性检查缓存TTL（秒）

# DDL配置
log_ddl = true                   # 是否启用DDL日志
ddl_execution_timeout_ms = 30000 # DDL执行超时时间（毫秒）
validate_schema = true           # 是否在创建表前检查schema

# ========================================
# 微信小程序配置 (lib-wechat)
# ========================================
[wechat]
app_id = "wx1c28b113b3d1f63a"                    # 微信小程序 AppID
app_secret = "d2d3ba8fb18ae3b963fc0b567d9f8842"  # 微信小程序 AppSecret
request_timeout_ms = 30000                       # 请求超时时间（毫秒），默认30秒
retry_count = 3                                  # 重试次数，默认3次

[package]
name = "lib-web"
version.workspace = true
edition.workspace = true

[dependencies]
lib-core = { path = "../lib-core" }
lib-macros = { path = "../lib-macros" }

axum = { workspace = true, features = ["json", "multipart", "ws", "http2", "tracing", "tokio", "form"] }
tokio = { workspace = true, features = ["full"] }
anyhow = { workspace = true }
tracing = { workspace = true }
serde = { workspace = true }
thiserror = { workspace = true }
tower-http = { workspace = true, features = ["full"] }
byte-unit = { workspace = true, features = ["serde"] }
schemars = { workspace = true }
async-trait = { workspace = true }
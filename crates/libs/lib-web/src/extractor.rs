pub use axum::extract::*;

use crate::error::{Result, WebError};
use crate::AppState;
use anyhow::Context;
use axum::http::request::Parts;
use lib_core::app::plugin::ComponentRegistry;
use lib_core::config::{ConfigRegistry, Configurable};
use std::ops::{Deref, DerefMut};
use std::result::Result as StdResult;

/// 扩展RequestParts的功能
pub trait RequestPartsExt {
    /// 获取AppState
    fn get_app_state(&self) -> &AppState;

    /// 获取组件
    fn get_component<T: Clone + Send + Sync + 'static>(&self) -> Result<T>;

    /// 获取配置
    fn get_config<T: serde::de::DeserializeOwned + Configurable>(&self) -> Result<T>;
}

impl RequestPartsExt for Parts {
    fn get_app_state(&self) -> &AppState {
        self.extensions
            .get::<AppState>()
            .expect("从扩展中提取应用状态失败")
    }

    fn get_component<T: Clone + Send + Sync + 'static>(&self) -> Result<T> {
        Ok(self
            .get_app_state()
            .app
            .try_get_component()
            .context("获取组件失败")?)
    }

    fn get_config<T: serde::de::DeserializeOwned + Configurable>(&self) -> Result<T> {
        self.get_app_state()
            .app
            .get_config::<T>()
            .map_err(|e| WebError::ConfigDeserializeErr(std::any::type_name::<T>(), Box::new(e)))
    }
}

/// 从AppState中提取插件注册的组件
pub struct Component<T: Clone>(pub T);

impl<T, S> FromRequestParts<S> for Component<T>
where
    T: Clone + Send + Sync + 'static,
    S: Sync,
{
    type Rejection = WebError;

    async fn from_request_parts(parts: &mut Parts, _s: &S) -> StdResult<Self, Self::Rejection> {
        parts.get_component::<T>().map(|c| Component(c))
    }
}

impl<T: Clone> Deref for Component<T> {
    type Target = T;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl<T: Clone> DerefMut for Component<T> {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}

pub struct Config<T>(pub T)
where
    T: serde::de::DeserializeOwned + Configurable;

impl<T, S> FromRequestParts<S> for Config<T>
where
    T: serde::de::DeserializeOwned + Configurable,
    S: Sync,
{
    type Rejection = WebError;

    async fn from_request_parts(parts: &mut Parts, _s: &S) -> StdResult<Self, Self::Rejection> {
        parts.get_config().map(|c| Config(c))
    }
}

impl<T> Deref for Config<T>
where
    T: serde::de::DeserializeOwned + Configurable,
{
    type Target = T;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl<T> DerefMut for Config<T>
where
    T: serde::de::DeserializeOwned + Configurable,
{
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}

mod config;
pub mod error;
pub mod extractor;

mod middleware;

/// 重导出axum以便外部使用
pub use axum;


/// axum::routing::MethodFilter 重导出
pub use axum::routing::MethodFilter;
/// 带有AppState的MethodRouter
pub use axum::routing::MethodRouter;
/// 带有AppState的Router
pub use axum::Router;


use anyhow::Context;
use async_trait::async_trait;
use axum::Extension;
use config::ServerConfig;
use config::WebConfig;
use lib_core::app::app::{App, AppBuilder};
use lib_core::app::plugin::{ComponentRegistry, MutableComponentRegistry, Plugin};
use lib_core::config::ConfigRegistry;
use lib_core::error::Result;
use std::{net::SocketAddr, sync::Arc};

/// Web配置器trait，用于向应用添加路由
pub trait WebConfigurator {
    /// 向应用添加路由
    fn add_router(&mut self, router: Router) -> &mut Self;
}

impl WebConfigurator for AppBuilder {
    fn add_router(&mut self, router: Router) -> &mut Self {
        // 简单地将路由器作为组件添加到应用中
        self.add_component(router)
    }
}

/// 应用状态结构，包含应用注册表的引用
#[derive(Clone)]
pub struct AppState {
    /// 应用注册表引用
    pub app: Arc<App>,
}

/// Web插件定义，负责启动Web服务器
pub struct WebPlugin;

#[async_trait]
impl Plugin for WebPlugin {
    async fn build(&self, app: &mut AppBuilder) {
        // 1. 加载Web配置
        let config = app
            .get_config::<WebConfig>()
            .expect("web plugin config load failed");

        // 2. 获取主路由器（如果存在）
        let router: Router = app.get_component().unwrap_or_else(|| Router::new());

        // 3. 应用中间件配置
        let router = if let Some(middlewares) = config.middlewares {
            crate::middleware::apply_middleware(router, middlewares)
        } else {
            router
        };

        let server_conf = config.server;

        // 4. 添加Web服务器调度任务
        app.add_scheduler(move |app: Arc<App>| Box::new(Self::schedule(router, app, server_conf)));
    }
}

impl WebPlugin {
    /// Web服务器调度方法，负责启动和运行Axum服务器
    async fn schedule(router: Router, app: Arc<App>, config: ServerConfig) -> Result<String> {
        // 1. 绑定TCP监听器
        let addr = SocketAddr::from((config.binding, config.port));
        let listener = tokio::net::TcpListener::bind(addr)
            .await
            .with_context(|| format!("bind tcp listener failed:{}", addr))?;
        tracing::info!("bind tcp listener: {}", addr);

        // 2. 配置Axum服务器，添加应用状态扩展
        let router = router.layer(Extension(AppState { app }));

        tracing::info!("axum server started");
        
        // 3. 根据配置决定是否启用连接信息和优雅关闭
        if config.connect_info {
            // 启用客户端连接信息
            let service = router.into_make_service_with_connect_info::<SocketAddr>();
            let server = axum::serve(listener, service);
            if config.graceful {
                // 启用优雅关闭
                server.with_graceful_shutdown(shutdown_signal()).await
            } else {
                server.await
            }
        } else {
            // 不启用连接信息
            let service = router.into_make_service();
            let server = axum::serve(listener, service);
            if config.graceful {
                // 启用优雅关闭
                server.with_graceful_shutdown(shutdown_signal()).await
            } else {
                server.await
            }
        }
        .context("start axum server failed")?;
        Ok("axum schedule finished".to_string())
    }
}

/// 优雅关闭信号处理
/// 监听Ctrl+C和SIGTERM信号，用于优雅关闭服务器
async fn shutdown_signal() {
    // 监听Ctrl+C信号
    let ctrl_c = async {
        tokio::signal::ctrl_c()
            .await
            .expect("failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    // Unix系统下监听SIGTERM信号
    let terminate = async {
        tokio::signal::unix::signal(tokio::signal::unix::SignalKind::terminate())
            .expect("failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    // 非Unix系统下的占位符
    let terminate = std::future::pending::<()>();

    // 等待任一信号触发
    tokio::select! {
        _ = ctrl_c => {
            tracing::info!("Received Ctrl+C signal, waiting for web server shutdown")
        },
        _ = terminate => {
            tracing::info!("Received kill signal, waiting for web server shutdown")
        },
    }
}

use crate::config::CorsMiddleware;
use crate::config::{
    EnableMiddleware, LimitPayloadMiddleware, Middlewares, StaticAssetsMiddleware,
    TimeoutRequestMiddleware, TraceLoggerMiddleware,
};
use anyhow::Context;
use axum::{body::Body, response::Response, Router};
use lib_core::{error::Result, response::ResponseBuilder};
use std::path::PathBuf;
use std::str::FromStr;
use std::time::Duration;
use axum::response::IntoResponse;
use tower_http::trace::DefaultMakeSpan;
use tower_http::trace::DefaultOnRequest;
use tower_http::trace::DefaultOnResponse;
use tower_http::{
    catch_panic::CatchPanicLayer,
    compression::CompressionLayer,
    cors::CorsLayer,
    limit::RequestBodyLimitLayer,
    services::{ServeDir, ServeFile},
    timeout::TimeoutLayer,
    trace::TraceLayer,
};
use trace::DefaultOnEos;

/// 重导出tower_http所有内容
pub use tower_http::*;

/// 应用中间件到路由器
/// 根据配置依次应用各种中间件层
pub(crate) fn apply_middleware(mut router: Router, middleware: Middlewares) -> Router {
    // 1. 恐慌捕获中间件 - 捕获处理器中的panic并返回友好的500错误
    if Some(EnableMiddleware { enable: true }) == middleware.catch_panic {
        router = router.layer(CatchPanicLayer::custom(handle_panic));
    }

    // 2. 响应压缩中间件 - 自动压缩响应内容
    if Some(EnableMiddleware { enable: true }) == middleware.compression {
        router = router.layer(CompressionLayer::new());
    }

    // 3. 请求跟踪中间件 - 记录HTTP请求的详细信息
    if let Some(TraceLoggerMiddleware { enable, level }) = middleware.logger {
        if enable {
            let level = level.into();
            router = router.layer(
                TraceLayer::new_for_http()
                    .make_span_with(DefaultMakeSpan::default().level(level))
                    .on_request(DefaultOnRequest::default().level(level))
                    .on_response(DefaultOnResponse::default().level(level))
                    .on_eos(DefaultOnEos::default().level(level)),
            );
        }
    }

    // 4. 请求超时中间件 - 为请求设置超时限制
    if let Some(TimeoutRequestMiddleware { enable, timeout }) = middleware.timeout_request {
        if enable {
            router = router.layer(TimeoutLayer::new(Duration::from_millis(timeout)));
        }
    }

    // 5. 请求体大小限制中间件 - 限制请求体的最大大小
    if let Some(LimitPayloadMiddleware { enable, body_limit }) = middleware.limit_payload {
        if enable {
            // 解析大小字符串（如"5mb", "1gb"等）
            let limit = byte_unit::Byte::from_str(&body_limit)
                .unwrap_or_else(|_| panic!("parse limit payload str failed: {}", &body_limit));

            let limit_payload = RequestBodyLimitLayer::new(limit.as_u64() as usize);
            router = router.layer(limit_payload);
        }
    }

    // 6. CORS中间件 - 处理跨域资源共享
    if let Some(cors) = middleware.cors {
        if cors.enable {
            let cors = build_cors_middleware(&cors).expect("cors middleware build failed");
            router = router.layer(cors);
        }
    }

    // 7. 静态资源服务中间件 - 提供静态文件服务
    if let Some(static_assets) = middleware.static_assets {
        if static_assets.enable {
            router = apply_static_dir(router, static_assets);
        }
    }
    router
}

/// 自定义 panic 处理器
/// 将 panic 转换为 ApiResult 格式的友好错误响应
fn handle_panic(err: Box<dyn std::any::Any + Send + 'static>) -> Response<Body> {
    // 尝试提取 panic 消息
    let panic_message = if let Some(s) = err.downcast_ref::<String>() {
        s.clone()
    } else if let Some(s) = err.downcast_ref::<&str>() {
        s.to_string()
    } else {
        "未知的系统错误".to_string()
    };

    // 记录 panic 日志
    tracing::error!("应用程序发生 panic: {}", panic_message);

    // 在开发环境显示详细错误，生产环境显示通用错误
    let user_message = if cfg!(debug_assertions) {
        format!("服务器内部错误: {}", panic_message)
    } else {
        "服务器暂时无法处理您的请求，请稍后重试".to_string()
    };

    // 使用 ResponseBuilder 创建统一格式的错误响应
    ResponseBuilder::internal_error(&user_message).into_response()
}

/// 应用静态目录服务
/// 配置静态文件服务，支持回退页面和预压缩
fn apply_static_dir(router: Router, static_assets: StaticAssetsMiddleware) -> Router {
    // 检查静态资源路径是否存在
    if static_assets.must_exist
        && (!PathBuf::from(&static_assets.path).exists()
            || !PathBuf::from(&static_assets.fallback).exists())
    {
        panic!(
            "one of the static path are not found, Folder `{}` fallback: `{}`",
            static_assets.path, static_assets.fallback
        );
    }

    // 配置回退文件（通常用于SPA应用的index.html）
    let fallback = ServeFile::new(format!("{}/{}", static_assets.path, static_assets.fallback));
    let serve_dir = ServeDir::new(static_assets.path).not_found_service(fallback);

    // 根据配置决定是否启用预压缩
    let service = if static_assets.precompressed {
        tracing::info!("[Middleware] Enable precompressed static assets");
        serve_dir.precompressed_gzip()
    } else {
        serve_dir
    };

    // 根据URI路径决定使用fallback_service还是nest_service
    if static_assets.uri == "/" {
        // 根路径使用fallback服务
        router.fallback_service(service)
    } else {
        // 特定路径使用嵌套服务
        router.nest_service(&static_assets.uri, service)
    }
}

/// 构建CORS中间件
/// 根据配置构建CORS层，处理跨域请求的各种设置
fn build_cors_middleware(cors: &CorsMiddleware) -> Result<CorsLayer> {
    let mut layer = CorsLayer::new();

    // 配置允许的源（Origins）
    if let Some(allow_origins) = &cors.allow_origins {
        if allow_origins.iter().any(|item| item == "*") {
            // 允许所有源
            layer = layer.allow_origin(cors::Any);
        } else {
            // 允许特定源列表
            let mut origins = Vec::with_capacity(allow_origins.len());
            for origin in allow_origins {
                let origin = origin
                    .parse()
                    .with_context(|| format!("cors origin parse failed:{}", origin))?;
                origins.push(origin);
            }
            layer = layer.allow_origin(origins);
        }
    }

    // 配置允许的请求头
    if let Some(allow_headers) = &cors.allow_headers {
        if allow_headers.iter().any(|item| item == "*") {
            // 允许所有请求头
            layer = layer.allow_headers(cors::Any);
        } else {
            // 允许特定请求头列表
            let mut headers = Vec::with_capacity(allow_headers.len());
            for header in allow_headers {
                let header = header
                    .parse()
                    .with_context(|| format!("http header parse failed:{}", header))?;
                headers.push(header);
            }
            layer = layer.allow_headers(headers);
        }
    }

    // 配置允许的HTTP方法
    if let Some(allow_methods) = &cors.allow_methods {
        if allow_methods.iter().any(|item| item == "*") {
            // 允许所有HTTP方法
            layer = layer.allow_methods(cors::Any);
        } else {
            // 允许特定HTTP方法列表
            let mut methods = Vec::with_capacity(allow_methods.len());
            for method in allow_methods {
                let method = method
                    .parse()
                    .with_context(|| format!("http method parse failed:{}", method))?;
                methods.push(method);
            }
            layer = layer.allow_methods(methods);
        }
    }

    // 配置预检请求的缓存时间
    if let Some(max_age) = cors.max_age {
        layer = layer.max_age(Duration::from_secs(max_age));
    }

    Ok(layer)
}

#![allow(missing_docs)]
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
};
use lib_core::AppError;
use thiserror::Error;

pub type Result<T> = std::result::Result<T, WebError>;

/// HTTP状态码错误 <https://tools.ietf.org/html/rfc7231>
#[derive(Error, Debug)]
#[error("请求错误，状态码是 {status_code}: {msg}")]
pub struct KnownWebError {
    status_code: StatusCode,
    msg: String,
}

macro_rules! impl_known_status_error {
    (
        $(
            $(#[$docs:meta])*
            $lower_case:ident, $upper_case:ident,
        )+
    ) => {
        impl KnownWebError {
            pub fn new<S:Into<String>>(status_code: StatusCode, msg: S) -> Self {
                Self {
                    status_code,
                    msg: msg.into(),
                }
            }
        $(
            $(#[$docs])*
            pub fn $lower_case<S:Into<String>>(msg: S) -> Self {
                Self::new(StatusCode::$upper_case, msg)
            }
        )+
        }
    };
}

impl_known_status_error! (
    /// 200 成功
    /// [[RFC7231, Section 6.3.1](https://tools.ietf.org/html/rfc7231#section-6.3.1)]
    ok, OK,
    /// 201 已创建
    /// [[RFC7231, Section 6.3.2](https://tools.ietf.org/html/rfc7231#section-6.3.2)]
    created, CREATED,
    /// 202 已接受
    /// [[RFC7231, Section 6.3.3](https://tools.ietf.org/html/rfc7231#section-6.3.3)]
    accepted, ACCEPTED,
    /// 203 非权威信息
    /// [[RFC7231, Section 6.3.4](https://tools.ietf.org/html/rfc7231#section-6.3.4)]
    non_authoritative_information, NON_AUTHORITATIVE_INFORMATION,
    /// 204 无内容
    /// [[RFC7231, Section 6.3.5](https://tools.ietf.org/html/rfc7231#section-6.3.5)]
    no_content, NO_CONTENT,
    /// 205 重置内容
    /// [[RFC7231, Section 6.3.6](https://tools.ietf.org/html/rfc7231#section-6.3.6)]
    reset_content, RESET_CONTENT,
    /// 206 部分内容
    /// [[RFC7233, Section 4.1](https://tools.ietf.org/html/rfc7233#section-4.1)]
    partial_content, PARTIAL_CONTENT,
    /// 207 多状态
    /// [[RFC4918](https://tools.ietf.org/html/rfc4918)]
    multi_status, MULTI_STATUS,
    /// 208 已报告
    /// [[RFC5842](https://tools.ietf.org/html/rfc5842)]
    already_reported, ALREADY_REPORTED,


    /// 226 IM已使用
    /// [[RFC3229](https://tools.ietf.org/html/rfc3229)]
    im_used, IM_USED,


    /// 300 多重选择
    /// [[RFC7231, Section 6.4.1](https://tools.ietf.org/html/rfc7231#section-6.4.1)]
    multiple_choices, MULTIPLE_CHOICES,
    /// 301 永久移动
    /// [[RFC7231, Section 6.4.2](https://tools.ietf.org/html/rfc7231#section-6.4.2)]
    moved_permanently, MOVED_PERMANENTLY,
    /// 302 找到
    /// [[RFC7231, Section 6.4.3](https://tools.ietf.org/html/rfc7231#section-6.4.3)]
    found, FOUND,
    /// 303 查看其他
    /// [[RFC7231, Section 6.4.4](https://tools.ietf.org/html/rfc7231#section-6.4.4)]
    see_other, SEE_OTHER,
    /// 304 未修改
    /// [[RFC7232, Section 4.1](https://tools.ietf.org/html/rfc7232#section-4.1)]
    not_modified, NOT_MODIFIED,
    /// 305 使用代理
    /// [[RFC7231, Section 6.4.5](https://tools.ietf.org/html/rfc7231#section-6.4.5)]
    use_proxy, USE_PROXY,
    /// 307 临时重定向
    /// [[RFC7231, Section 6.4.7](https://tools.ietf.org/html/rfc7231#section-6.4.7)]
    temporary_redirect, TEMPORARY_REDIRECT,
    /// 308 永久重定向
    /// [[RFC7238](https://tools.ietf.org/html/rfc7238)]
    permanent_redirect, PERMANENT_REDIRECT,


    /// 400 错误请求
    /// [[RFC7231, Section 6.5.1](https://tools.ietf.org/html/rfc7231#section-6.5.1)]
    bad_request, BAD_REQUEST,
    /// 401 未授权
    /// [[RFC7235, Section 3.1](https://tools.ietf.org/html/rfc7235#section-3.1)]
    unauthorized, UNAUTHORIZED,
    /// 402 需要付款
    /// [[RFC7231, Section 6.5.2](https://tools.ietf.org/html/rfc7231#section-6.5.2)]
    payment_required, PAYMENT_REQUIRED,
    /// 403 禁止访问
    /// [[RFC7231, Section 6.5.3](https://tools.ietf.org/html/rfc7231#section-6.5.3)]
    forbidden, FORBIDDEN,
    /// 404 未找到
    /// [[RFC7231, Section 6.5.4](https://tools.ietf.org/html/rfc7231#section-6.5.4)]
    not_found, NOT_FOUND,
    /// 405 方法不允许
    /// [[RFC7231, Section 6.5.5](https://tools.ietf.org/html/rfc7231#section-6.5.5)]
    method_not_allowed, METHOD_NOT_ALLOWED,
    /// 406 不可接受
    /// [[RFC7231, Section 6.5.6](https://tools.ietf.org/html/rfc7231#section-6.5.6)]
    not_acceptable, NOT_ACCEPTABLE,
    /// 407 需要代理认证
    /// [[RFC7235, Section 3.2](https://tools.ietf.org/html/rfc7235#section-3.2)]
    proxy_authentication_required, PROXY_AUTHENTICATION_REQUIRED,
    /// 408 请求超时
    /// [[RFC7231, Section 6.5.7](https://tools.ietf.org/html/rfc7231#section-6.5.7)]
    request_timeout, REQUEST_TIMEOUT,
    /// 409 冲突
    /// [[RFC7231, Section 6.5.8](https://tools.ietf.org/html/rfc7231#section-6.5.8)]
    conflict, CONFLICT,
    /// 410 已删除
    /// [[RFC7231, Section 6.5.9](https://tools.ietf.org/html/rfc7231#section-6.5.9)]
    gone, GONE,
    /// 411 需要长度
    /// [[RFC7231, Section 6.5.10](https://tools.ietf.org/html/rfc7231#section-6.5.10)]
    length_required, LENGTH_REQUIRED,
    /// 412 先决条件失败
    /// [[RFC7232, Section 4.2](https://tools.ietf.org/html/rfc7232#section-4.2)]
    precondition_failed, PRECONDITION_FAILED,
    /// 413 负载过大
    /// [[RFC7231, Section 6.5.11](https://tools.ietf.org/html/rfc7231#section-6.5.11)]
    payload_too_large, PAYLOAD_TOO_LARGE,
    /// 414 URI过长
    /// [[RFC7231, Section 6.5.12](https://tools.ietf.org/html/rfc7231#section-6.5.12)]
    uri_too_long, URI_TOO_LONG,
    /// 415 不支持的媒体类型
    /// [[RFC7231, Section 6.5.13](https://tools.ietf.org/html/rfc7231#section-6.5.13)]
    unsupported_media_type, UNSUPPORTED_MEDIA_TYPE,
    /// 416 范围不满足
    /// [[RFC7233, Section 4.4](https://tools.ietf.org/html/rfc7233#section-4.4)]
    range_not_satisfiable, RANGE_NOT_SATISFIABLE,
    /// 417 期望失败
    /// [[RFC7231, Section 6.5.14](https://tools.ietf.org/html/rfc7231#section-6.5.14)]
    expectation_failed, EXPECTATION_FAILED,
    /// 418 我是茶壶
    /// [curiously not registered by IANA but [RFC2324](https://tools.ietf.org/html/rfc2324)]
    im_a_teapot, IM_A_TEAPOT,


    /// 421 请求错误导向
    /// [RFC7540, Section 9.1.2](https://tools.ietf.org/html/rfc7540#section-9.1.2)
    misdirected_request, MISDIRECTED_REQUEST,
    /// 422 无法处理的实体
    /// [[RFC4918](https://tools.ietf.org/html/rfc4918)]
    unprocessable_entity, UNPROCESSABLE_ENTITY,
    /// 423 已锁定
    /// [[RFC4918](https://tools.ietf.org/html/rfc4918)]
    locked, LOCKED,
    /// 424 依赖失败
    /// [[RFC4918](https://tools.ietf.org/html/rfc4918)]
    failed_dependency, FAILED_DEPENDENCY,


    /// 426 需要升级
    /// [[RFC7231, Section 6.5.15](https://tools.ietf.org/html/rfc7231#section-6.5.15)]
    upgrade_required, UPGRADE_REQUIRED,


    /// 428 需要先决条件
    /// [[RFC6585](https://tools.ietf.org/html/rfc6585)]
    precondition_required, PRECONDITION_REQUIRED,
    /// 429 请求过多
    /// [[RFC6585](https://tools.ietf.org/html/rfc6585)]
    too_many_requests, TOO_MANY_REQUESTS,


    /// 431 请求头字段过大
    /// [[RFC6585](https://tools.ietf.org/html/rfc6585)]
    request_header_fields_too_large, REQUEST_HEADER_FIELDS_TOO_LARGE,


    /// 451 因法律原因不可用
    /// [[RFC7725](https://tools.ietf.org/html/rfc7725)]
    unavailable_for_legal_reasons, UNAVAILABLE_FOR_LEGAL_REASONS,


    /// 500 内部服务器错误
    /// [[RFC7231, Section 6.6.1](https://tools.ietf.org/html/rfc7231#section-6.6.1)]
    internal_server_error, INTERNAL_SERVER_ERROR,
    /// 501 未实现
    /// [[RFC7231, Section 6.6.2](https://tools.ietf.org/html/rfc7231#section-6.6.2)]
    not_implemented, NOT_IMPLEMENTED,
    /// 502 错误网关
    /// [[RFC7231, Section 6.6.3](https://tools.ietf.org/html/rfc7231#section-6.6.3)]
    bad_gateway, BAD_GATEWAY,
    /// 503 服务不可用
    /// [[RFC7231, Section 6.6.4](https://tools.ietf.org/html/rfc7231#section-6.6.4)]
    service_unavailable, SERVICE_UNAVAILABLE,
    /// 504 网关超时
    /// [[RFC7231, Section 6.6.5](https://tools.ietf.org/html/rfc7231#section-6.6.5)]
    gateway_timeout, GATEWAY_TIMEOUT,
    /// 505 HTTP版本不支持
    /// [[RFC7231, Section 6.6.6](https://tools.ietf.org/html/rfc7231#section-6.6.6)]
    http_version_not_supported, HTTP_VERSION_NOT_SUPPORTED,
    /// 506 变体也在协商
    /// [[RFC2295](https://tools.ietf.org/html/rfc2295)]
    variant_also_negotiates, VARIANT_ALSO_NEGOTIATES,
    /// 507 存储空间不足
    /// [[RFC4918](https://tools.ietf.org/html/rfc4918)]
    insufficient_storage, INSUFFICIENT_STORAGE,
    /// 508 检测到循环
    /// [[RFC5842](https://tools.ietf.org/html/rfc5842)]
    loop_detected, LOOP_DETECTED,


    /// 510 未扩展
    /// [[RFC2774](https://tools.ietf.org/html/rfc2774)]
    not_extended, NOT_EXTENDED,
    /// 511 需要网络认证
    /// [[RFC6585](https://tools.ietf.org/html/rfc6585)]
    network_authentication_required, NETWORK_AUTHENTICATION_REQUIRED,

);

#[derive(Error, Debug)]
pub enum WebError {
    #[error(transparent)]
    ResponseStatusError(#[from] KnownWebError),

    #[error("获取服务器配置失败，类型 {0}，错误: {1}")]
    ConfigDeserializeErr(&'static str, Box<AppError>),

    #[error(transparent)]
    ServerError(#[from] anyhow::Error),
}

// 告诉axum如何将 `AppError` 转换为响应
impl IntoResponse for WebError {
    fn into_response(self) -> Response {
        match self {
            Self::ResponseStatusError(e) => {
                tracing::warn!("处理器错误: {:?}", e);
                (e.status_code, e.msg)
            }
            _other => {
                tracing::error!("内部服务器错误: {:?}", _other);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    format!("出现了一些问题: {}", _other),
                )
            }
        }
        .into_response()
    }
}

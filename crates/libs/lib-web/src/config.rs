use lib_macros::Configurable;
use schemars::JsonSchema;
use serde::Deserialize;
use std::net::{IpAddr, Ipv4Addr};
use tracing::Level;

/// Web服务配置
#[derive(Debug, Configurable, JsonSchema, Deserialize)]
#[config_prefix = "web"]
pub struct WebConfig {
    #[serde(flatten)]
    pub(crate) server: ServerConfig,
    pub(crate) middlewares: Option<Middlewares>,
}

#[derive(Debug, Clone, JsonSchema, Deserialize)]
pub struct ServerConfig {
    #[serde(default = "default_binding")]
    pub(crate) binding: IpAddr,
    #[serde(default = "default_port")]
    pub(crate) port: u16,
    #[serde(default)]
    pub(crate) connect_info: bool,
    #[serde(default)]
    pub(crate) graceful: bool,
}

fn default_binding() -> IpAddr {
    IpAddr::V4(Ipv4Addr::new(0, 0, 0, 0))
}

fn default_port() -> u16 {
    8080
}

/// 服务器中间件配置结构
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, JsonSchema, Deserialize)]
pub struct Middlewares {
    /// 启用响应压缩的中间件
    pub compression: Option<EnableMiddleware>,
    /// 限制请求负载大小的中间件
    pub limit_payload: Option<LimitPayloadMiddleware>,
    /// 改进跟踪日志记录并为每个请求添加跟踪ID的中间件
    pub logger: Option<TraceLoggerMiddleware>,
    /// 捕获任何代码恐慌并记录错误
    pub catch_panic: Option<EnableMiddleware>,
    /// 为请求设置全局超时
    pub timeout_request: Option<TimeoutRequestMiddleware>,
    /// 设置CORS配置
    pub cors: Option<CorsMiddleware>,
    /// 提供静态资源服务
    #[serde(rename = "static")]
    pub static_assets: Option<StaticAssetsMiddleware>,
}

/// 静态资源中间件配置
#[derive(Debug, Clone, JsonSchema, Deserialize)]
pub struct StaticAssetsMiddleware {
    /// 切换启用状态
    pub enable: bool,
    /// 检查资源是否必须存在于磁盘上
    #[serde(default = "bool::default")]
    pub must_exist: bool,
    /// 当没有资源存在时的回退页面（404）。对于SPA（单页应用）很有用，其中路由是虚拟的
    #[serde(default = "default_fallback")]
    pub fallback: String,
    /// 启用预压缩gzip
    #[serde(default = "bool::default")]
    pub precompressed: bool,
    /// 资源的URI路径
    #[serde(default = "default_assets_uri")]
    pub uri: String,
    /// 资源的文件系统路径
    #[serde(default = "default_assets_path")]
    pub path: String,
}

/// 跟踪日志中间件配置
#[derive(Debug, Clone, JsonSchema, Deserialize)]
pub struct TraceLoggerMiddleware {
    /// 切换启用状态
    pub enable: bool,
    pub level: LogLevel,
}

#[derive(Debug, Default, Clone, JsonSchema, Deserialize)]
pub enum LogLevel {
    /// "trace" 级别
    #[serde(rename = "trace")]
    Trace,
    /// "debug" 级别
    #[serde(rename = "debug")]
    Debug,
    /// "info" 级别
    #[serde(rename = "info")]
    #[default]
    Info,
    /// "warn" 级别
    #[serde(rename = "warn")]
    Warn,
    /// "error" 级别
    #[serde(rename = "error")]
    Error,
}

#[allow(clippy::from_over_into)]
impl Into<Level> for LogLevel {
    fn into(self) -> Level {
        match self {
            Self::Trace => Level::TRACE,
            Self::Debug => Level::DEBUG,
            Self::Info => Level::INFO,
            Self::Warn => Level::WARN,
            Self::Error => Level::ERROR,
        }
    }
}

/// CORS中间件配置
#[derive(Debug, Clone, JsonSchema, Deserialize)]
pub struct CorsMiddleware {
    /// 切换启用状态
    pub enable: bool,
    /// 允许的源
    pub allow_origins: Option<Vec<String>>,
    /// 允许的头部
    pub allow_headers: Option<Vec<String>>,
    /// 允许的方法
    pub allow_methods: Option<Vec<String>>,
    /// 最大存活时间
    pub max_age: Option<u64>,
}

/// 超时中间件配置
#[derive(Debug, Clone, JsonSchema, Deserialize)]
pub struct TimeoutRequestMiddleware {
    /// 切换启用状态
    pub enable: bool,
    /// 请求超时时间（毫秒）
    pub timeout: u64,
}

/// 限制负载大小中间件配置
#[derive(Debug, Clone, JsonSchema, Deserialize)]
pub struct LimitPayloadMiddleware {
    /// 切换启用状态
    pub enable: bool,
    /// 主体限制，例如：5mb
    pub body_limit: String,
}

/// 可以启用或禁用的通用中间件配置
#[derive(Debug, PartialEq, Clone, JsonSchema, Deserialize)]
pub struct EnableMiddleware {
    /// 切换启用状态
    pub enable: bool,
}

fn default_assets_path() -> String {
    "static".to_string()
}

fn default_assets_uri() -> String {
    "/static".to_string()
}

fn default_fallback() -> String {
    "index.html".to_string()
}

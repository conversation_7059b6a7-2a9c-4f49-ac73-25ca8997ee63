pub mod config;

pub use redis;

use anyhow::Context;
use async_trait::async_trait;
use config::RedisConfig;
use lib_core::app::app::AppBuilder;
use lib_core::app::plugin::{MutableComponentRegistry, Plugin};
use lib_core::config::ConfigRegistry;
use lib_core::error::Result;
use redis::{aio::ConnectionManagerConfig, Client};
use std::time::Duration;

pub type Redis = redis::aio::ConnectionManager;

/// Redis缓存插件
/// 负责Redis连接的初始化、配置和生命周期管理
pub struct RedisPlugin;

#[async_trait]
impl Plugin for RedisPlugin {
    async fn build(&self, app: &mut AppBuilder) {
        // 1. 加载Redis配置
        let config = app
            .get_config::<RedisConfig>()
            .expect("redis plugin config load failed");

        // 2. 建立Redis连接
        let connect: Redis = Self::connect(config).await.expect("redis connect failed");
        
        // 3. 将Redis连接注册为组件
        app.add_component(connect);
    }
}

impl RedisPlugin {
    /// 连接Redis服务器
    /// 根据配置创建Redis连接，支持URI方式和独立参数方式
    async fn connect(config: RedisConfig) -> Result<Redis> {
        // 构建连接字符串
        let connection_url = Self::build_connection_url(&config)?;
        
        // 创建Redis客户端
        let client = Client::open(connection_url.clone())
            .with_context(|| format!("redis client create failed: {}", connection_url))?;

        // 配置连接管理器
        let conn_config = Self::build_connection_config(&config);

        // 建立连接管理器
        Ok(client
            .get_connection_manager_with_config(conn_config)
            .await
            .with_context(|| format!("redis connection manager create failed: {}", connection_url))?)
    }

    /// 构建Redis连接URL
    /// 支持直接使用URI或通过独立参数构建连接URL
    fn build_connection_url(config: &RedisConfig) -> Result<String> {
        // 如果提供了完整的URI，直接使用
        if !config.uri.is_empty() && config.uri.starts_with("redis") {
            return Ok(config.uri.clone());
        }

        // 使用独立参数构建连接URL
        let mut url = String::new();
        
        // 协议部分
        if config.tls {
            url.push_str("rediss://");
        } else {
            url.push_str("redis://");
        }
        
        // 认证部分
        if let (Some(username), Some(password)) = (&config.username, &config.password) {
            url.push_str(&format!("{}:{}@", username, password));
        } else if let Some(password) = &config.password {
            // 只有密码，没有用户名（Redis 6.0之前的认证方式）
            url.push_str(&format!(":{}@", password));
        }
        
        // 主机和端口
        url.push_str(&format!("{}:{}", config.host, config.port));
        
        // 数据库索引
        if config.database != 0 {
            url.push_str(&format!("/{}", config.database));
        }

        Ok(url)
    }

    /// 构建连接管理器配置
    /// 根据配置设置重试策略、超时等参数
    fn build_connection_config(config: &RedisConfig) -> ConnectionManagerConfig {
        let mut conn_config = ConnectionManagerConfig::new();

        // 设置重试相关参数
        if let Some(exponent_base) = config.exponent_base {
            conn_config = conn_config.set_exponent_base(exponent_base);
        }
        
        if let Some(factor) = config.factor {
            conn_config = conn_config.set_factor(factor);
        }
        
        if let Some(number_of_retries) = config.number_of_retries {
            conn_config = conn_config.set_number_of_retries(number_of_retries);
        }
        
        if let Some(max_delay) = config.max_delay {
            conn_config = conn_config.set_max_delay(max_delay);
        }

        // 设置超时参数
        if let Some(response_timeout) = config.response_timeout {
            conn_config = conn_config.set_response_timeout(Duration::from_millis(response_timeout));
        }
        
        conn_config = conn_config.set_connection_timeout(Duration::from_millis(config.connect_timeout));

        conn_config
    }
}

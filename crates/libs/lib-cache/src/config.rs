use lib_macros::Configurable;
use schemars::JsonSchema;
use serde::Deserialize;

/// Redis缓存配置
#[derive(Debug, Configurable, Clone, JsonSchema, Deserialize)]
#[config_prefix = "redis"]
pub struct RedisConfig {
    /// Redis服务器连接URI，例如：
    /// * `redis://127.0.0.1/`
    /// * `redis://username:password@127.0.0.1:6379/0`
    /// 如果提供了URI，会优先使用URI，忽略其他单独的连接参数
    pub uri: String,

    /// Redis服务器主机地址
    #[serde(default = "default_host")]
    pub host: String,

    /// Redis服务器端口
    #[serde(default = "default_port")]
    pub port: u16,

    /// Redis认证用户名（Redis 6.0+支持）
    pub username: Option<String>,

    /// Redis认证密码
    pub password: Option<String>,

    /// Redis数据库索引（0-15）
    #[serde(default = "default_database")]
    pub database: i32,

    /// 连接池最大连接数
    #[serde(default = "default_max_connections")]
    pub max_connections: u32,

    /// 连接池最小空闲连接数
    #[serde(default = "default_min_idle_connections")]
    pub min_idle_connections: u32,

    /// 连接空闲超时时间（毫秒）
    /// 空闲时间超过此值的连接将被关闭
    #[serde(default = "default_idle_timeout")]
    pub idle_timeout: u64,

    /// 建立连接的超时时间（毫秒）
    #[serde(default = "default_connect_timeout")]
    pub connect_timeout: u64,

    /// Redis操作响应超时时间（毫秒）
    #[serde(default = "default_response_timeout")]
    pub response_timeout: Option<u64>,

    /// 是否启用TLS加密连接
    #[serde(default)]
    pub tls: bool,

    /// 重试次数，采用指数递增延迟策略
    #[serde(default = "default_number_of_retries")]
    pub number_of_retries: Option<usize>,

    /// 指数基数，用于计算重试延迟
    /// 结果持续时间通过将基数提升到第n次幂来计算，其中n表示过去尝试的次数
    #[serde(default = "default_exponent_base")]
    pub exponent_base: Option<u64>,

    /// 应用于重试延迟的乘法因子
    ///
    /// 例如，使用因子1000将使每个延迟以秒为单位
    #[serde(default = "default_factor")]
    pub factor: Option<u64>,

    /// 连接尝试之间的最大延迟时间（毫秒）
    /// 尝试之间的延迟不会超过max_delay毫秒
    #[serde(default = "default_max_delay")]
    pub max_delay: Option<u64>,
}

fn default_host() -> String {
    "127.0.0.1".to_string()
}

fn default_port() -> u16 {
    6379
}

fn default_database() -> i32 {
    0
}

fn default_max_connections() -> u32 {
    10
}

fn default_min_idle_connections() -> u32 {
    1
}

fn default_idle_timeout() -> u64 {
    600000 // 10分钟
}

fn default_connect_timeout() -> u64 {
    5000 // 5秒
}

fn default_response_timeout() -> Option<u64> {
    Some(3000) // 3秒
}

fn default_number_of_retries() -> Option<usize> {
    Some(3)
}

fn default_exponent_base() -> Option<u64> {
    Some(2)
}

fn default_factor() -> Option<u64> {
    Some(1000)
}

fn default_max_delay() -> Option<u64> {
    Some(60000) // 60秒
}

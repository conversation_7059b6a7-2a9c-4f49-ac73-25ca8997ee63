[package]
name = "lib-cache"
version.workspace = true
edition.workspace = true

[dependencies]
lib-core = { path = "../lib-core" }
lib-macros = { path = "../lib-macros" }
serde = { workspace = true }
anyhow = { workspace = true }
tracing = { workspace = true, features = ["log"] }
redis = { workspace = true, features = ["connection-manager", "tokio-comp", "json"] }
schemars = { workspace = true }
async-trait = {workspace = true}
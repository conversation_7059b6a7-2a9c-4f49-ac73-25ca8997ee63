//! 分表连接代理
use crate::{
    config::ShardingConfig, context::get_current_merchant_id, rewriter::ShardingSqlRewriter,
};
use sea_orm::{
    AccessMode, ConnectionTrait, DatabaseBackend, DatabaseTransaction, DbErr, ExecResult,
    IsolationLevel, QueryResult, Statement, StreamTrait, TransactionTrait,
};
use std::future::Future;
use std::pin::Pin;

use tracing::{debug, instrument};

/// 分表连接代理
///
/// 实现 `ConnectionTrait`，拦截所有 SQL 执行并自动重写表名
/// 用户可以像使用普通连接一样使用，但会自动处理分表逻辑
#[derive(Clone)]
pub struct ShardedConnection<C>
where
    C: ConnectionTrait,
{
    /// 原始数据库连接
    inner: C,
    /// SQL重写器 - 复用实例以提升性能（包含配置信息）
    rewriter: ShardingSqlRewriter,
}

impl<C> ShardedConnection<C>
where
    C: ConnectionTrait,
{
    /// 创建新的分表连接代理
    ///
    /// # 参数
    /// - `inner`: 原始数据库连接
    /// - `config`: 分表配置
    pub fn new(inner: C, config: ShardingConfig) -> Self {
        let rewriter = ShardingSqlRewriter::new(config);
        Self {
            inner,
            rewriter,
        }
    }

    /// 重写 SQL 语句以支持分表
    ///
    /// 这是分表逻辑的核心：
    /// 1. 获取当前租户上下文
    /// 2. 使用复用的 SQL 重写器重写表名
    /// 3. 返回新的 Statement
    #[instrument(skip(self, stmt))]
    fn rewrite_sql(&self, stmt: Statement) -> std::result::Result<Statement, DbErr> {
        // 1. 获取当前商户ID（租户上下文）
        let merchant_id = get_current_merchant_id()
            .map_err(|e| DbErr::Custom(format!("获取租户上下文失败: {}", e)))?;

        debug!(
            "重写SQL - 商户ID: {}, 原SQL: {}",
            merchant_id,
            &stmt.sql[..std::cmp::min(100, stmt.sql.len())]
        );

        // 2. 使用复用的 SQL 重写器进行重写（性能优化）
        let (rewritten_sql, rewritten_values) = self.rewriter
            .rewrite_statements(&stmt)
            .map_err(|e| DbErr::Custom(format!("SQL重写失败: {}", e)))?;

        debug!(
            "SQL重写完成 - 新SQL: {}",
            &rewritten_sql[..std::cmp::min(100, rewritten_sql.len())]
        );

        // 3. 构造新的 Statement
        Ok(Statement::from_sql_and_values(
            stmt.db_backend,
            rewritten_sql,
            rewritten_values,
        ))
    }
}

#[async_trait::async_trait]
impl<C> ConnectionTrait for ShardedConnection<C>
where
    C: ConnectionTrait,
{
    /// 获取数据库后端类型
    fn get_database_backend(&self) -> DatabaseBackend {
        self.inner.get_database_backend()
    }

    /// 执行 SQL 语句 - 核心拦截点
    ///
    /// 在执行前自动重写 SQL 以支持分表
    #[instrument(skip(self, stmt))]
    async fn execute(&self, stmt: Statement) -> std::result::Result<ExecResult, DbErr> {
        let rewritten_stmt = self.rewrite_sql(stmt)?;
        self.inner.execute(rewritten_stmt).await
    }

    async fn execute_unprepared(&self, sql: &str) -> Result<ExecResult, DbErr> {
        self.inner.execute_unprepared(sql).await
    }

    /// 执行查询并返回单个结果 - 核心拦截点
    ///
    /// 在执行前自动重写 SQL 以支持分表
    #[instrument(skip(self, stmt))]
    async fn query_one(&self, stmt: Statement) -> std::result::Result<Option<QueryResult>, DbErr> {
        let rewritten_stmt = self.rewrite_sql(stmt)?;
        self.inner.query_one(rewritten_stmt).await
    }

    /// 执行查询并返回所有结果 - 核心拦截点
    ///
    /// 在执行前自动重写 SQL 以支持分表
    #[instrument(skip(self, stmt))]
    async fn query_all(&self, stmt: Statement) -> std::result::Result<Vec<QueryResult>, DbErr> {
        let rewritten_stmt = self.rewrite_sql(stmt)?;
        self.inner.query_all(rewritten_stmt).await
    }

    /// 检查是否支持 RETURNING 子句
    fn support_returning(&self) -> bool {
        self.inner.support_returning()
    }
}

#[async_trait::async_trait]
impl<C> TransactionTrait for ShardedConnection<C>
where
    C: TransactionTrait + ConnectionTrait,
{
    /// 开始事务
    async fn begin(&self) -> std::result::Result<DatabaseTransaction, DbErr> {
        self.inner.begin().await
    }

    /// 开始带选项的事务
    async fn begin_with_config(
        &self,
        isolation_level: Option<IsolationLevel>,
        access_mode: Option<AccessMode>,
    ) -> std::result::Result<DatabaseTransaction, DbErr> {
        self.inner
            .begin_with_config(isolation_level, access_mode)
            .await
    }

    /// 执行事务回调
    async fn transaction<F, T, E>(&self, callback: F) -> Result<T, sea_orm::TransactionError<E>>
    where
        F: for<'c> FnOnce(
                &'c DatabaseTransaction,
            ) -> Pin<Box<dyn Future<Output = Result<T, E>> + Send + 'c>>
            + Send,
        T: Send,
        E: std::error::Error + Send,
    {
        self.inner.transaction(callback).await
    }

    /// 执行带配置的事务回调
    async fn transaction_with_config<F, T, E>(
        &self,
        callback: F,
        isolation_level: Option<IsolationLevel>,
        access_mode: Option<AccessMode>,
    ) -> Result<T, sea_orm::TransactionError<E>>
    where
        F: for<'c> FnOnce(
                &'c DatabaseTransaction,
            ) -> Pin<Box<dyn Future<Output = Result<T, E>> + Send + 'c>>
            + Send,
        T: Send,
        E: std::error::Error + Send,
    {
        self.inner
            .transaction_with_config(callback, isolation_level, access_mode)
            .await
    }
}

impl<C> StreamTrait for ShardedConnection<C>
where
    C: StreamTrait + ConnectionTrait,
{
    type Stream<'a>
        = C::Stream<'a>
    where
        Self: 'a;

    /// 执行流式查询 - 核心拦截点
    fn stream<'a>(
        &'a self,
        stmt: Statement,
    ) -> Pin<Box<dyn Future<Output = std::result::Result<Self::Stream<'a>, DbErr>> + Send + 'a>>
    {
        Box::pin(async move {
            let rewritten_stmt = self.rewrite_sql(stmt)?;
            self.inner.stream(rewritten_stmt).await
        })
    }
}

/// 便捷函数：创建分表连接
///
/// # 参数
/// - `connection`: 原始数据库连接
/// - `config`: 分表配置
///
/// # 性能说明
/// 创建时会初始化一个复用的 SQL 重写器，避免每次 SQL 执行时重复创建
pub fn create_sharded_connection<C>(connection: C, config: ShardingConfig) -> ShardedConnection<C>
where
    C: ConnectionTrait,
{
    ShardedConnection::new(connection, config)
}

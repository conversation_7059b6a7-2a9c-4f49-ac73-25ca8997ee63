//! 表名重写策略模块
//!
//! 提供可扩展的表名重写策略实现，支持不同的分表命名规则。

use crate::config::NamingStrategy;
use crate::error::{Result, ShardingError};
use std::collections::hash_map::DefaultHasher;
use std::hash::{Hash, Hasher};
use tracing::{debug, instrument};

/// 表名重写策略抽象接口
///
/// 定义了表名重写的核心功能，支持不同命名策略的扩展实现。
pub trait TableNamingStrategy: Send + Sync {
    /// 生成分表名称
    ///
    /// # 参数
    /// - `base_table_name`: 原始表名
    /// - `merchant_id`: 商户ID
    /// - `schema_name`: 模式名称（可选）
    ///
    /// # 返回
    /// - `Ok(String)`: 生成的分表名称
    /// - `Err(ShardingError)`: 生成失败错误
    fn generate_shard_name(
        &self,
        base_table_name: &str,
        merchant_id: i64,
        schema_name: Option<&str>,
    ) -> Result<String>;

    /// 生成商户专用Schema名称
    ///
    /// # 参数
    /// - `merchant_id`: 商户ID
    ///
    /// # 返回
    /// - `Ok(String)`: 生成的Schema名称
    /// - `Err(ShardingError)`: 生成失败错误
    ///
    /// # 默认实现
    /// 默认返回错误，只有支持Schema策略的实现才会重写此方法
    fn generate_merchant_schema(&self, _merchant_id: i64) -> Result<String> {
        Err(ShardingError::table_rewrite(
            "",
            format!("策略 '{}' 不支持独立Schema生成", self.strategy_name()),
        ))
    }

    /// 检查策略是否支持独立Schema
    ///
    /// # 返回
    /// - `true`: 支持独立Schema
    /// - `false`: 不支持独立Schema（使用表名重写）
    fn supports_independent_schema(&self) -> bool {
        false
    }

    /// 验证表名是否有效
    ///
    /// # 参数
    /// - `table_name`: 要验证的表名
    ///
    /// # 返回
    /// - `Ok(())`: 表名有效
    /// - `Err(ShardingError)`: 表名无效
    fn validate_table_name(&self, table_name: &str) -> Result<()>;

    /// 获取策略名称
    fn strategy_name(&self) -> &'static str;

    /// 获取最大表名长度限制
    fn max_table_name_length(&self) -> usize {
        63 // PostgreSQL 默认限制
    }

    /// 获取最大Schema名称长度限制
    fn max_schema_name_length(&self) -> usize {
        63 // PostgreSQL 默认限制
    }
}

/// 商户后缀策略实现
///
/// 格式：`{table_name}_merchant_{merchant_id}`
/// 示例：`orders` -> `orders_merchant_123`
#[derive(Debug, Clone)]
pub struct MerchantSuffixStrategy {
    /// 后缀分隔符
    separator: String,
    /// 是否启用表名验证
    validate_names: bool,
}

impl Default for MerchantSuffixStrategy {
    fn default() -> Self {
        Self::new()
    }
}

impl MerchantSuffixStrategy {
    /// 创建新的商户后缀策略
    pub fn new() -> Self {
        Self {
            separator: "_merchant_".to_string(),
            validate_names: true,
        }
    }

    /// 创建自定义分隔符的商户后缀策略
    pub fn with_separator(separator: String) -> Self {
        Self {
            separator,
            validate_names: true,
        }
    }

    /// 设置是否启用表名验证
    pub fn with_validation(mut self, validate: bool) -> Self {
        self.validate_names = validate;
        self
    }
}

impl TableNamingStrategy for MerchantSuffixStrategy {
    #[instrument(skip(self), fields(strategy = "merchant_suffix"))]
    fn generate_shard_name(
        &self,
        base_table_name: &str,
        merchant_id: i64,
        schema_name: Option<&str>,
    ) -> Result<String> {
        if self.validate_names {
            self.validate_table_name(base_table_name)?;
        }

        let shard_name = format!("{}{}{}", base_table_name, self.separator, merchant_id);

        // 检查长度限制
        if shard_name.len() > self.max_table_name_length() {
            return Err(ShardingError::table_rewrite(
                base_table_name,
                format!(
                    "生成的分表名长度 {} 超过限制 {}",
                    shard_name.len(),
                    self.max_table_name_length()
                ),
            ));
        }

        let result = if let Some(schema) = schema_name {
            format!("{}.{}", schema, shard_name)
        } else {
            shard_name
        };

        debug!("生成商户后缀分表名: {} -> {}", base_table_name, result);

        Ok(result)
    }

    fn validate_table_name(&self, table_name: &str) -> Result<()> {
        if table_name.is_empty() {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名不能为空".to_string(),
            ));
        }

        if !table_name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名只能包含字母、数字和下划线".to_string(),
            ));
        }

        if table_name.starts_with(char::is_numeric) {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名不能以数字开头".to_string(),
            ));
        }

        Ok(())
    }

    fn strategy_name(&self) -> &'static str {
        "merchant_suffix"
    }
}

/// 商户前缀策略实现
///
/// 格式：`merchant_{merchant_id}_{table_name}`
/// 示例：`orders` -> `merchant_123_orders`
#[derive(Debug, Clone)]
pub struct MerchantPrefixStrategy {
    /// 前缀分隔符
    separator: String,
    /// 是否启用表名验证
    validate_names: bool,
}

impl Default for MerchantPrefixStrategy {
    fn default() -> Self {
        Self::new()
    }
}

impl MerchantPrefixStrategy {
    /// 创建新的商户前缀策略
    pub fn new() -> Self {
        Self {
            separator: "_".to_string(),
            validate_names: true,
        }
    }

    /// 创建自定义分隔符的商户前缀策略
    pub fn with_separator(separator: String) -> Self {
        Self {
            separator,
            validate_names: true,
        }
    }

    /// 设置是否启用表名验证
    pub fn with_validation(mut self, validate: bool) -> Self {
        self.validate_names = validate;
        self
    }
}

impl TableNamingStrategy for MerchantPrefixStrategy {
    #[instrument(skip(self), fields(strategy = "merchant_prefix"))]
    fn generate_shard_name(
        &self,
        base_table_name: &str,
        merchant_id: i64,
        schema_name: Option<&str>,
    ) -> Result<String> {
        if self.validate_names {
            self.validate_table_name(base_table_name)?;
        }

        let shard_name = format!(
            "merchant{}{}{}{}",
            self.separator, merchant_id, self.separator, base_table_name
        );

        // 检查长度限制
        if shard_name.len() > self.max_table_name_length() {
            return Err(ShardingError::table_rewrite(
                base_table_name,
                format!(
                    "生成的分表名长度 {} 超过限制 {}",
                    shard_name.len(),
                    self.max_table_name_length()
                ),
            ));
        }

        let result = if let Some(schema) = schema_name {
            format!("{}.{}", schema, shard_name)
        } else {
            shard_name
        };

        debug!("生成商户前缀分表名: {} -> {}", base_table_name, result);

        Ok(result)
    }

    fn validate_table_name(&self, table_name: &str) -> Result<()> {
        if table_name.is_empty() {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名不能为空".to_string(),
            ));
        }

        if !table_name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名只能包含字母、数字和下划线".to_string(),
            ));
        }

        if table_name.starts_with(char::is_numeric) {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名不能以数字开头".to_string(),
            ));
        }

        Ok(())
    }

    fn strategy_name(&self) -> &'static str {
        "merchant_prefix"
    }
}

/// 哈希后缀策略实现
///
/// 格式：`{table_name}_{hash_suffix}`
/// 示例：`orders` -> `orders_001` (基于merchant_id哈希)
#[derive(Debug, Clone)]
pub struct HashSuffixStrategy {
    /// 哈希位数
    hash_digits: usize,
    /// 是否启用表名验证
    validate_names: bool,
}

impl Default for HashSuffixStrategy {
    fn default() -> Self {
        Self::new()
    }
}

impl HashSuffixStrategy {
    /// 创建新的哈希后缀策略（默认3位）
    pub fn new() -> Self {
        Self {
            hash_digits: 3,
            validate_names: true,
        }
    }

    /// 创建指定哈希位数的策略
    pub fn with_digits(digits: usize) -> Self {
        Self {
            hash_digits: digits.clamp(1, 8), // 限制在 1-8 位
            validate_names: true,
        }
    }

    /// 设置是否启用表名验证
    pub fn with_validation(mut self, validate: bool) -> Self {
        self.validate_names = validate;
        self
    }

    /// 计算商户ID的哈希值
    fn calculate_hash(&self, merchant_id: i64) -> String {
        let mut hasher = DefaultHasher::new();
        merchant_id.hash(&mut hasher);
        let hash = hasher.finish();

        // 生成指定位数的哈希后缀
        let modulo = 10_u64.pow(self.hash_digits as u32);
        format!("{:0width$}", hash % modulo, width = self.hash_digits)
    }
}

impl TableNamingStrategy for HashSuffixStrategy {
    #[instrument(skip(self), fields(strategy = "hash_suffix"))]
    fn generate_shard_name(
        &self,
        base_table_name: &str,
        merchant_id: i64,
        schema_name: Option<&str>,
    ) -> Result<String> {
        if self.validate_names {
            self.validate_table_name(base_table_name)?;
        }

        let hash_suffix = self.calculate_hash(merchant_id);
        let shard_name = format!("{}_{}", base_table_name, hash_suffix);

        // 检查长度限制
        if shard_name.len() > self.max_table_name_length() {
            return Err(ShardingError::table_rewrite(
                base_table_name,
                format!(
                    "生成的分表名长度 {} 超过限制 {}",
                    shard_name.len(),
                    self.max_table_name_length()
                ),
            ));
        }

        let result = if let Some(schema) = schema_name {
            format!("{}.{}", schema, shard_name)
        } else {
            shard_name
        };

        debug!(
            "生成哈希后缀分表名: {} -> {} (merchant_id: {}, hash: {})",
            base_table_name, result, merchant_id, hash_suffix
        );

        Ok(result)
    }

    fn validate_table_name(&self, table_name: &str) -> Result<()> {
        if table_name.is_empty() {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名不能为空".to_string(),
            ));
        }

        if !table_name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名只能包含字母、数字和下划线".to_string(),
            ));
        }

        if table_name.starts_with(char::is_numeric) {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名不能以数字开头".to_string(),
            ));
        }

        Ok(())
    }

    fn strategy_name(&self) -> &'static str {
        "hash_suffix"
    }
}

/// 独立Schema策略实现
///
/// 格式：`merchant_{merchant_id}.{table_name}`
/// 示例：`orders` -> `merchant_123.orders`
///
/// 这种策略为每个商户创建独立的数据库Schema，提供更强的数据隔离。
#[derive(Debug, Clone)]
pub struct MerchantSchemaStrategy {
    /// Schema名称前缀
    schema_prefix: String,
    /// 是否启用表名验证
    validate_names: bool,
    /// 是否启用Schema名称验证
    validate_schema_names: bool,
}

impl Default for MerchantSchemaStrategy {
    fn default() -> Self {
        Self::new()
    }
}

impl MerchantSchemaStrategy {
    /// 创建新的独立Schema策略
    pub fn new() -> Self {
        Self {
            schema_prefix: "merchant_".to_string(),
            validate_names: true,
            validate_schema_names: true,
        }
    }

    /// 创建自定义前缀的独立Schema策略
    pub fn with_prefix(prefix: String) -> Self {
        Self {
            schema_prefix: prefix,
            validate_names: true,
            validate_schema_names: true,
        }
    }

    /// 设置是否启用表名验证
    pub fn with_table_validation(mut self, validate: bool) -> Self {
        self.validate_names = validate;
        self
    }

    /// 设置是否启用Schema名称验证
    pub fn with_schema_validation(mut self, validate: bool) -> Self {
        self.validate_schema_names = validate;
        self
    }

    /// 验证Schema名称是否有效
    fn validate_schema_name(&self, schema_name: &str) -> Result<()> {
        if !self.validate_schema_names {
            return Ok(());
        }

        if schema_name.is_empty() {
            return Err(ShardingError::table_rewrite(
                schema_name,
                "Schema名称不能为空".to_string(),
            ));
        }

        if !schema_name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(ShardingError::table_rewrite(
                schema_name,
                "Schema名称只能包含字母、数字和下划线".to_string(),
            ));
        }

        if schema_name.starts_with(char::is_numeric) {
            return Err(ShardingError::table_rewrite(
                schema_name,
                "Schema名称不能以数字开头".to_string(),
            ));
        }

        if schema_name.len() > self.max_schema_name_length() {
            return Err(ShardingError::table_rewrite(
                schema_name,
                format!(
                    "Schema名称长度 {} 超过限制 {}",
                    schema_name.len(),
                    self.max_schema_name_length()
                ),
            ));
        }

        Ok(())
    }
}

impl TableNamingStrategy for MerchantSchemaStrategy {
    #[instrument(skip(self), fields(strategy = "merchant_schema"))]
    fn generate_shard_name(
        &self,
        base_table_name: &str,
        merchant_id: i64,
        _schema_name: Option<&str>, // 忽略传入的schema_name，使用生成的merchant schema
    ) -> Result<String> {
        if self.validate_names {
            self.validate_table_name(base_table_name)?;
        }

        // 生成商户专用Schema名称
        let merchant_schema = self.generate_merchant_schema(merchant_id)?;

        // 返回完整的表引用：merchant_123.orders
        let result = format!("{}.{}", merchant_schema, base_table_name);

        debug!(
            "生成独立Schema分表名: {} -> {} (merchant_id: {}, schema: {})",
            base_table_name, result, merchant_id, merchant_schema
        );

        Ok(result)
    }

    fn generate_merchant_schema(&self, merchant_id: i64) -> Result<String> {
        let schema_name = format!("{}{}", self.schema_prefix, merchant_id);

        // 验证生成的Schema名称
        self.validate_schema_name(&schema_name)?;

        debug!(
            "生成商户Schema: {} (merchant_id: {})",
            schema_name, merchant_id
        );

        Ok(schema_name)
    }

    fn supports_independent_schema(&self) -> bool {
        true
    }

    fn validate_table_name(&self, table_name: &str) -> Result<()> {
        if !self.validate_names {
            return Ok(());
        }

        if table_name.is_empty() {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名不能为空".to_string(),
            ));
        }

        if !table_name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名只能包含字母、数字和下划线".to_string(),
            ));
        }

        if table_name.starts_with(char::is_numeric) {
            return Err(ShardingError::table_rewrite(
                table_name,
                "表名不能以数字开头".to_string(),
            ));
        }

        Ok(())
    }

    fn strategy_name(&self) -> &'static str {
        "merchant_schema"
    }
}

/// 表名策略工厂
///
/// 提供创建不同表名策略实例的工厂方法。
pub struct TableNamingStrategyFactory;

impl TableNamingStrategyFactory {
    /// 根据命名策略枚举创建策略实例
    pub fn create_strategy(naming_strategy: &NamingStrategy) -> Box<dyn TableNamingStrategy> {
        match naming_strategy {
            NamingStrategy::MerchantSuffix => Box::new(MerchantSuffixStrategy::new()),
            NamingStrategy::MerchantPrefix => Box::new(MerchantPrefixStrategy::new()),
            NamingStrategy::HashSuffix => Box::new(HashSuffixStrategy::new()),
            NamingStrategy::MerchantSchema => Box::new(MerchantSchemaStrategy::new()),
        }
    }

    /// 创建商户后缀策略
    pub fn create_merchant_suffix() -> Box<dyn TableNamingStrategy> {
        Box::new(MerchantSuffixStrategy::new())
    }

    /// 创建商户前缀策略
    pub fn create_merchant_prefix() -> Box<dyn TableNamingStrategy> {
        Box::new(MerchantPrefixStrategy::new())
    }

    /// 创建哈希后缀策略
    pub fn create_hash_suffix(digits: Option<usize>) -> Box<dyn TableNamingStrategy> {
        if let Some(d) = digits {
            Box::new(HashSuffixStrategy::with_digits(d))
        } else {
            Box::new(HashSuffixStrategy::new())
        }
    }

    /// 创建独立Schema策略
    pub fn create_merchant_schema(prefix: Option<String>) -> Box<dyn TableNamingStrategy> {
        if let Some(p) = prefix {
            Box::new(MerchantSchemaStrategy::with_prefix(p))
        } else {
            Box::new(MerchantSchemaStrategy::new())
        }
    }
}

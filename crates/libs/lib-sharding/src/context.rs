//! 租户上下文管理模块
//!
//! 提供线程安全的租户上下文管理，用于在分表操作中获取当前商户ID

use std::cell::RefCell;
use thiserror::Error;
use tokio::task_local;
use lib_core::BusinessError;

/// 租户上下文错误
#[derive(Error, Debug)]
pub enum TenantContextError {
    #[error("租户上下文未设置")]
    NotSet,
    #[error("无效的商户ID: {0}")]
    InvalidMerchantId(i64),
}
/// 为 BusinessError 实现 From<TenantContextError> trait
impl From<TenantContextError> for BusinessError {
    fn from(error: TenantContextError) -> Self {
        BusinessError::new(500, format!("租户上下文错误: {}", error))
    }
}
/// 租户上下文信息
#[derive(Debug, Clone)]
pub struct TenantContext {
    /// 商户ID
    pub merchant_id: i64,
}

impl TenantContext {
    /// 创建新的租户上下文
    pub fn new(merchant_id: i64) -> Result<Self, TenantContextError> {
        if merchant_id <= 0 {
            return Err(TenantContextError::InvalidMerchantId(merchant_id));
        }
        
        Ok(Self { merchant_id })
    }

    /// 获取商户ID
    pub fn merchant_id(&self) -> i64 {
        self.merchant_id
    }
}

// 使用 tokio task_local 存储租户上下文
task_local! {
    static TENANT_CONTEXT: RefCell<Option<TenantContext>>;
}

/// 设置当前租户上下文
pub fn set_tenant_context(context: TenantContext) {
    TENANT_CONTEXT.with(|ctx| {
        *ctx.borrow_mut() = Some(context);
    });
}

/// 获取当前租户上下文
pub fn get_tenant_context() -> Result<TenantContext, TenantContextError> {
    TENANT_CONTEXT.with(|ctx| {
        ctx.borrow()
            .as_ref()
            .cloned()
            .ok_or(TenantContextError::NotSet)
    })
}

/// 获取当前商户ID
pub fn get_current_merchant_id() -> Result<i64, TenantContextError> {
    get_tenant_context().map(|ctx| ctx.merchant_id)
}

/// 清除租户上下文
pub fn clear_tenant_context() {
    TENANT_CONTEXT.with(|ctx| {
        *ctx.borrow_mut() = None;
    });
}

/// 在指定租户上下文中执行异步操作
pub async fn with_tenant_context<F, R>(
    merchant_id: i64,
    future: F,
) -> Result<R, TenantContextError>
where
    F: std::future::Future<Output = R>,
{
    let context = TenantContext::new(merchant_id)?;
    let result = TENANT_CONTEXT
        .scope(RefCell::new(Some(context)), future)
        .await;
    Ok(result)
}


/// 获取当前商户ID（返回 ShardingError）
pub fn get_current_merchant_id_for_sharding() -> crate::error::Result<i64> {
    get_current_merchant_id()
        .map_err(|e| crate::error::ShardingError::internal(format!("获取租户上下文失败: {}", e)))
}

//! 分表管理器模块
//!
//! 提供自动分表创建、删除和管理功能，支持 DDL 模板系统和事务处理。

use crate::config::{NamingStrategy, ShardingConfig};
use crate::error::{Result, ShardingError};
use crate::rewriter::ShardingSqlRewriter;
use crate::template::TemplateManager;
use sea_orm::{ConnectionTrait, DatabaseBackend, Statement};
use serde::{Deserialize, Serialize};
use tracing::{debug, instrument, warn};

/// 分表删除选项配置
///
/// 提供灵活的删除配置，支持安全检查、级联删除、数据备份等选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DropTableOptions {
    /// 是否强制删除（跳过所有安全检查）
    pub force: bool,
    /// 是否检查表中的数据
    pub check_data: bool,
    /// 是否检查外键依赖关系
    pub check_dependencies: bool,
    /// 是否使用级联删除（CASCADE）
    pub cascade: bool,
    /// 是否在删除前备份数据
    pub backup_data: bool,
}

impl Default for DropTableOptions {
    fn default() -> Self {
        Self {
            force: false,
            check_data: true,
            check_dependencies: true,
            cascade: false,
            backup_data: false,
        }
    }
}

impl DropTableOptions {
    /// 创建安全删除选项（所有安全检查开启）
    pub fn safe() -> Self {
        Self {
            force: false,
            check_data: true,
            check_dependencies: true,
            cascade: false,
            backup_data: true,
        }
    }

    /// 创建强制删除选项（跳过所有检查）
    pub fn force() -> Self {
        Self {
            force: true,
            check_data: false,
            check_dependencies: false,
            cascade: true,
            backup_data: false,
        }
    }

    /// 创建级联删除选项（处理外键依赖）
    pub fn cascade() -> Self {
        Self {
            force: false,
            check_data: true,
            check_dependencies: true,
            cascade: true,
            backup_data: false,
        }
    }
}

/// 分表删除结果统计
///
/// 记录删除操作的详细结果和统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShardTableDeleteResult {
    /// 商户ID
    pub merchant_id: i64,
    /// 删除开始时间
    pub start_time: chrono::DateTime<chrono::Utc>,
    /// 删除结束时间
    pub end_time: chrono::DateTime<chrono::Utc>,
    /// 总耗时（毫秒）
    pub duration_ms: u128,
    /// 待删除的表数量
    pub tables_to_delete: usize,
    /// 成功删除的表
    pub deleted_tables: Vec<String>,
    /// 跳过删除的表（不存在）
    pub skipped_tables: Vec<String>,
    /// 删除失败的表
    pub failed_tables: Vec<(String, String)>, // (表名, 错误信息)
    /// 备份的表
    pub backed_up_tables: Vec<String>,
    /// 使用的删除选项
    pub options: DropTableOptions,
}

impl ShardTableDeleteResult {
    /// 创建新的删除结果
    pub fn new(merchant_id: i64, tables_to_delete: usize, options: DropTableOptions) -> Self {
        let now = chrono::Utc::now();
        Self {
            merchant_id,
            start_time: now,
            end_time: now,
            duration_ms: 0,
            tables_to_delete,
            deleted_tables: Vec::new(),
            skipped_tables: Vec::new(),
            failed_tables: Vec::new(),
            backed_up_tables: Vec::new(),
            options,
        }
    }

    /// 完成删除操作
    pub fn complete(&mut self) {
        self.end_time = chrono::Utc::now();
        self.duration_ms = (self.end_time - self.start_time).num_milliseconds() as u128;
    }

    /// 是否全部成功
    pub fn is_success(&self) -> bool {
        self.failed_tables.is_empty()
    }

    /// 实际处理的表数量
    pub fn processed_count(&self) -> usize {
        self.deleted_tables.len() + self.skipped_tables.len() + self.failed_tables.len()
    }

    /// 获取总结信息
    pub fn summary(&self) -> String {
        format!(
            "商户 {} 分表删除完成 - 总耗时: {}ms, 待删除: {}, 成功: {}, 跳过: {}, 失败: {}, 备份: {}",
            self.merchant_id,
            self.duration_ms,
            self.tables_to_delete,
            self.deleted_tables.len(),
            self.skipped_tables.len(),
            self.failed_tables.len(),
            self.backed_up_tables.len()
        )
    }
}

/// 分表信息
///
/// 包含分表的详细信息，用于查询和管理分表状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub struct ShardTableInfo {
    /// 基础表名
    pub base_table_name: String,
    /// 分表名称
    pub shard_table_name: String,
    /// 商户ID
    pub merchant_id: i64,
    /// 分表是否存在
    pub exists: bool,
    /// 模式名称
    pub schema_name: String,
    /// 命名策略
    pub naming_strategy: NamingStrategy,
}

impl ShardTableInfo {
    /// 获取完整的分表名称（包含模式）
    pub fn full_table_name(&self) -> String {
        format!("{}.{}", self.schema_name, self.shard_table_name)
    }

    /// 检查分表是否可用
    pub fn is_available(&self) -> bool {
        self.exists
    }
}

/// 分表管理器
///
/// 负责分表的创建、删除和管理，支持：
/// - DDL 模板系统
/// - 批量分表操作
/// - 事务支持
/// - 表存在性检查
///
/// 🆕 重构说明：
/// - 改为无状态设计，所有依赖通过方法参数传递
/// - 支持依赖注入模式，从 ShardingService 获取依赖
/// - 提供静态方法版本，性能更优
#[derive(Clone)]
pub struct ShardTableManager<C>
where
    C: ConnectionTrait,
{
    /// 数据库连接
    db: C,
}

impl<C: ConnectionTrait> ShardTableManager<C> {
    /// 🆕 创建新的分表管理器（推荐通过 ShardingService 创建）
    ///
    /// # 参数
    /// - `db`: 数据库连接
    ///
    /// # 使用说明
    /// 推荐通过 ShardingService 的方法来使用分表功能，而不是直接创建 ShardTableManager
    ///
    /// # 示例
    pub fn new(db: C) -> Self {
        Self { db }
    }

    /// 获取数据库连接引用
    pub fn db(&self) -> &C {
        &self.db
    }

    /// 🆕 为商户创建所有分表（需要传递依赖）
    ///
    /// # 参数
    /// - `config`: 分表配置
    /// - `sql_rewriter`: SQL重写器
    /// - `merchant_id`: 商户ID
    ///
    /// # 使用说明
    /// 推荐使用 ShardingService.create_merchant_tables_fast() 方法
    #[instrument(skip(self, config, sql_rewriter), fields(merchant_id = merchant_id))]
    pub async fn create_merchant_tables_with_deps(
        &self,
        config: &ShardingConfig,
        sql_rewriter: &ShardingSqlRewriter,
        merchant_id: i64,
    ) -> Result<()> {
        let start_time = std::time::Instant::now();

        debug!(
            "开始为商户 {} 创建分表 - 待创建表数量: {}",
            merchant_id,
            config.shardable_tables.len()
        );

        // 按需创建模板管理器
        let mut template_manager = TemplateManager::new();

        // 为每个可分表创建分表
        let mut created_tables = Vec::new();
        let mut skipped_tables = Vec::new();

        for base_table_name in &config.shardable_tables {
            match self
                .create_single_shard_table_with_deps(
                    config,
                    sql_rewriter,
                    &mut template_manager,
                    base_table_name,
                    merchant_id,
                )
                .await
            {
                Ok(()) => {
                    let shard_table_name =
                        sql_rewriter.generate_shard_table_name(base_table_name, merchant_id)?;
                    if Self::table_exists_static(&self.db, config, &shard_table_name).await {
                        created_tables.push(shard_table_name);
                    } else {
                        skipped_tables.push(base_table_name.clone());
                    }
                }
                Err(e) => {
                    let error_msg = format!("创建分表 '{}' 失败: {}", base_table_name, e);
                    warn!("{}", error_msg);
                    return Err(ShardingError::table_management(
                        "create_merchant_tables_with_deps",
                        merchant_id,
                        error_msg,
                    ));
                }
            }
        }

        let duration = start_time.elapsed();
        debug!(
            "商户 {} 分表创建完成 - 总耗时: {:?}, 创建表数量: {}, 跳过表数量: {}",
            merchant_id,
            duration,
            created_tables.len(),
            skipped_tables.len()
        );

        if !skipped_tables.is_empty() {
            debug!("商户 {} 跳过的表: {:?}", merchant_id, skipped_tables);
        }

        Ok(())
    }

    /// 🆕 创建单个分表（需要传递依赖）
    ///
    /// # 参数
    /// - `config`: 分表配置
    /// - `sql_rewriter`: SQL重写器
    /// - `template_manager`: DDL模板管理器
    /// - `base_table_name`: 基础表名
    /// - `merchant_id`: 商户ID
    #[instrument(skip(self, config, sql_rewriter, template_manager), fields(base_table = base_table_name, merchant_id = merchant_id))]
    pub async fn create_single_shard_table_with_deps(
        &self,
        config: &ShardingConfig,
        sql_rewriter: &ShardingSqlRewriter,
        template_manager: &mut TemplateManager,
        base_table_name: &str,
        merchant_id: i64,
    ) -> Result<()> {
        debug!(
            "开始创建单个分表 - 基础表: {}, 商户ID: {}",
            base_table_name, merchant_id
        );

        // 验证基础表是否为可分表
        if !Self::is_shardable_table_static(config, base_table_name) {
            return Err(ShardingError::table_management(
                "create_single_shard_table_with_deps",
                merchant_id,
                format!("表 '{}' 不在可分表列表中", base_table_name),
            ));
        }

        // 生成分表名称
        let shard_table_name =
            sql_rewriter.generate_shard_table_name(base_table_name, merchant_id)?;
        debug!(
            "生成分表名称 - 基础表: {} -> 分表: {}",
            base_table_name, shard_table_name
        );

        // 检查分表是否已存在
        if Self::table_exists_static(&self.db, config, &shard_table_name).await {
            debug!("分表 {} 已存在，跳过创建", shard_table_name);
            return Ok(());
        }

        // 生成 DDL 语句 - 根据策略选择正确的schema
        let create_sql = if config.naming_strategy == crate::config::NamingStrategy::MerchantSchema {
            // Schema策略：使用商户专用schema，表名保持不变
            let merchant_schema = sql_rewriter.generate_merchant_schema(Some(base_table_name), merchant_id)?;
            template_manager.generate_ddl_for_schema(
                base_table_name,
                base_table_name,  // Schema策略下表名不变
                merchant_id,
                &merchant_schema,
            )
        } else {
            // 传统策略：使用配置的schema，表名已重写
            template_manager.generate_ddl(
                base_table_name,
                &shard_table_name,
                merchant_id,
                &config.schema_name,
            )
        };
        debug!("生成创建分表 SQL: {}", create_sql);

        // 执行 DDL 语句
        let statement = Statement::from_string(DatabaseBackend::Postgres, create_sql);
        match self.db.execute(statement).await {
            Ok(result) => {
                debug!(
                    "分表 {} 创建成功 - 受影响行数: {:?}",
                    shard_table_name,
                    result.rows_affected()
                );
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("分表 {} 创建失败: {}", shard_table_name, e);
                warn!("{}", error_msg);
                Err(ShardingError::table_management(
                    "create_single_shard_table_with_deps",
                    merchant_id,
                    error_msg,
                ))
            }
        }
    }

    /// 🆕 删除商户的所有分表（带依赖）
    ///
    /// # 参数
    /// - `config`: 分表配置
    /// - `sql_rewriter`: SQL重写器
    /// - `merchant_id`: 商户ID
    /// - `options`: 删除选项
    #[instrument(skip(self, config, sql_rewriter, options), fields(merchant_id = merchant_id))]
    pub async fn drop_merchant_tables_with_deps(
        &self,
        config: &ShardingConfig,
        sql_rewriter: &ShardingSqlRewriter,
        merchant_id: i64,
        options: &DropTableOptions,
    ) -> Result<ShardTableDeleteResult> {
        let mut result = ShardTableDeleteResult::new(
            merchant_id,
            config.shardable_tables.len(),
            options.clone(),
        );

        warn!(
            "开始删除商户 {} 的所有分表 - 选项: {:?}, 待删除表数量: {}",
            merchant_id, options, result.tables_to_delete
        );

        // 全局安全检查
        if !options.force {
            debug!("执行全局安全检查 - 商户ID: {}", merchant_id);
            self.perform_global_safety_checks_with_deps(config, sql_rewriter, merchant_id, options)
                .await?;
        }

        let mut has_error = false;
        let mut first_error_msg: Option<String> = None;

        // 删除每个分表
        for base_table_name in &config.shardable_tables {
            match self
                .drop_single_shard_table_with_deps(
                    config,
                    sql_rewriter,
                    base_table_name,
                    merchant_id,
                    options,
                )
                .await
            {
                Ok(()) => {
                    let shard_table_name =
                        sql_rewriter.generate_shard_table_name(base_table_name, merchant_id)?;
                    if !Self::table_exists_static(&self.db, config, &shard_table_name).await {
                        result.deleted_tables.push(shard_table_name.clone());
                    } else {
                        result.skipped_tables.push(base_table_name.clone());
                    }

                    // 记录备份信息
                    if options.backup_data
                        && Self::table_exists_static(
                            &self.db,
                            config,
                            &format!("{}_backup_*", shard_table_name),
                        )
                        .await
                    {
                        result.backed_up_tables.push(shard_table_name);
                    }
                }
                Err(e) => {
                    has_error = true;
                    if first_error_msg.is_none() {
                        first_error_msg = Some(e.to_string());
                    }
                    result
                        .failed_tables
                        .push((base_table_name.clone(), e.to_string()));
                    warn!("删除分表 '{}' 失败: {}", base_table_name, e);
                }
            }
        }

        if has_error && !options.force {
            result.complete();
            let error_msg = format!(
                "删除分表过程中出现错误: {}",
                first_error_msg.unwrap_or_else(|| "未知错误".to_string())
            );
            warn!("{}", error_msg);

            return Err(ShardingError::table_management(
                "drop_merchant_tables_with_deps",
                merchant_id,
                error_msg,
            ));
        }

        result.complete();
        warn!("{}", result.summary());

        Ok(result)
    }

    /// 🆕 删除单个分表（带依赖）
    #[instrument(skip(self, config, sql_rewriter, options), fields(base_table = base_table_name, merchant_id = merchant_id))]
    pub async fn drop_single_shard_table_with_deps(
        &self,
        config: &ShardingConfig,
        sql_rewriter: &ShardingSqlRewriter,
        base_table_name: &str,
        merchant_id: i64,
        options: &DropTableOptions,
    ) -> Result<()> {
        debug!(
            "开始删除单个分表 - 基础表: {}, 商户ID: {}, 选项: {:?}",
            base_table_name, merchant_id, options
        );

        // 验证基础表是否为可分表
        if !Self::is_shardable_table_static(config, base_table_name) {
            return Err(ShardingError::table_management(
                "drop_single_shard_table_with_deps",
                merchant_id,
                format!("表 '{}' 不在可分表列表中", base_table_name),
            ));
        }

        // 生成分表名称
        let shard_table_name =
            sql_rewriter.generate_shard_table_name(base_table_name, merchant_id)?;

        // 检查分表是否存在
        if !Self::table_exists_static(&self.db, config, &shard_table_name).await {
            debug!("分表 {} 不存在，跳过删除", shard_table_name);
            return Ok(());
        }

        // 执行安全检查
        if !options.force {
            self.perform_safety_checks_with_deps(config, &shard_table_name, merchant_id, options)
                .await?;
        }

        // 如果需要备份数据
        if options.backup_data {
            self.backup_table_data_with_deps(config, &shard_table_name, merchant_id)
                .await?;
        }

        // 生成删除 SQL
        let drop_sql = if options.cascade {
            format!(
                "DROP TABLE IF EXISTS {}.{} CASCADE",
                config.schema_name, shard_table_name
            )
        } else {
            format!(
                "DROP TABLE IF EXISTS {}.{}",
                config.schema_name, shard_table_name
            )
        };

        debug!("生成删除分表 SQL: {}", drop_sql);

        // 执行删除语句
        let statement = Statement::from_string(DatabaseBackend::Postgres, drop_sql);
        match self.db.execute(statement).await {
            Ok(result) => {
                debug!(
                    "分表 {} 删除成功 - 受影响行数: {:?}",
                    shard_table_name,
                    result.rows_affected()
                );
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("分表 {} 删除失败: {}", shard_table_name, e);
                warn!("{}", error_msg);
                Err(ShardingError::table_management(
                    "drop_single_shard_table_with_deps",
                    merchant_id,
                    error_msg,
                ))
            }
        }
    }

    /// 🆕 获取商户的所有分表信息（带依赖）
    #[instrument(skip(self, config, sql_rewriter))]
    pub async fn get_merchant_tables_info_with_deps(
        &self,
        config: &ShardingConfig,
        sql_rewriter: &ShardingSqlRewriter,
        merchant_id: i64,
    ) -> Result<Vec<ShardTableInfo>> {
        debug!("获取商户 {} 的分表信息", merchant_id);

        let mut tables_info = Vec::new();

        for base_table_name in &config.shardable_tables {
            match self
                .get_shard_table_info_with_deps(config, sql_rewriter, base_table_name, merchant_id)
                .await
            {
                Ok(info) => tables_info.push(info),
                Err(e) => {
                    warn!(
                        "获取分表信息失败 - 基础表: {}, 商户ID: {}, 错误: {}",
                        base_table_name, merchant_id, e
                    );
                    // 继续处理其他表，不中断整个流程
                }
            }
        }

        debug!(
            "商户 {} 分表信息获取完成 - 分表数量: {}",
            merchant_id,
            tables_info.len()
        );

        Ok(tables_info)
    }

    /// 🆕 获取分表信息（带依赖）
    #[instrument(skip(self, config, sql_rewriter))]
    pub async fn get_shard_table_info_with_deps(
        &self,
        config: &ShardingConfig,
        sql_rewriter: &ShardingSqlRewriter,
        base_table_name: &str,
        merchant_id: i64,
    ) -> Result<ShardTableInfo> {
        let shard_table_name =
            sql_rewriter.generate_shard_table_name(base_table_name, merchant_id)?;
        let exists = Self::table_exists_static(&self.db, config, &shard_table_name).await;

        Ok(ShardTableInfo {
            base_table_name: base_table_name.to_string(),
            shard_table_name: shard_table_name.clone(),
            merchant_id,
            exists,
            schema_name: config.schema_name.clone(),
            naming_strategy: config.naming_strategy.clone(),
        })
    }

    /// 🆕 检查商户分表的完整性（带依赖）
    #[instrument(skip(self, config, sql_rewriter))]
    pub async fn check_merchant_tables_integrity_with_deps(
        &self,
        config: &ShardingConfig,
        sql_rewriter: &ShardingSqlRewriter,
        merchant_id: i64,
    ) -> Result<bool> {
        debug!("检查商户 {} 的分表完整性", merchant_id);

        let tables_info = self
            .get_merchant_tables_info_with_deps(config, sql_rewriter, merchant_id)
            .await?;

        let total_tables = config.shardable_tables.len();
        let existing_tables = tables_info.iter().filter(|info| info.exists).count();

        let is_complete = existing_tables == total_tables;

        debug!(
            "商户 {} 分表完整性检查结果 - 应有: {}, 实有: {}, 完整: {}",
            merchant_id, total_tables, existing_tables, is_complete
        );

        Ok(is_complete)
    }

    // ========================================
    // 🆕 私有辅助方法（带依赖）
    // ========================================

    /// 执行全局安全检查（带依赖）
    #[instrument(skip(self, config, sql_rewriter, options))]
    async fn perform_global_safety_checks_with_deps(
        &self,
        config: &ShardingConfig,
        sql_rewriter: &ShardingSqlRewriter,
        merchant_id: i64,
        options: &DropTableOptions,
    ) -> Result<()> {
        debug!("执行全局安全检查 - 商户ID: {}", merchant_id);

        // 检查商户是否存在活跃业务
        if options.check_data {
            let tables_info = self
                .get_merchant_tables_info_with_deps(config, sql_rewriter, merchant_id)
                .await?;
            let active_tables: Vec<_> = tables_info.iter().filter(|info| info.exists).collect();

            if active_tables.is_empty() {
                debug!("商户 {} 无活跃分表，跳过数据检查", merchant_id);
            } else {
                debug!(
                    "商户 {} 有 {} 个活跃分表，将进行详细数据检查",
                    merchant_id,
                    active_tables.len()
                );
            }
        }

        debug!("全局安全检查通过 - 商户ID: {}", merchant_id);
        Ok(())
    }

    /// 执行分表删除安全检查（带依赖）
    #[instrument(skip(self, config))]
    async fn perform_safety_checks_with_deps(
        &self,
        config: &ShardingConfig,
        shard_table_name: &str,
        merchant_id: i64,
        options: &DropTableOptions,
    ) -> Result<()> {
        debug!("执行分表删除安全检查 - 表: {}", shard_table_name);

        // 检查表中是否还有数据
        if options.check_data {
            let data_count = self
                .check_table_data_count_with_deps(config, shard_table_name)
                .await?;
            if data_count > 0 {
                warn!(
                    "分表 {} 包含 {} 条数据，删除前请确认",
                    shard_table_name, data_count
                );
            }
        }

        debug!("分表删除安全检查通过 - 表: {}", shard_table_name);
        Ok(())
    }

    /// 检查表中的数据行数（带依赖）
    #[instrument(skip(self, config))]
    async fn check_table_data_count_with_deps(
        &self,
        config: &ShardingConfig,
        table_name: &str,
    ) -> Result<i64> {
        let count_sql = format!(
            "SELECT COUNT(*) as count FROM {}.{}",
            config.schema_name, table_name
        );

        let statement = Statement::from_string(DatabaseBackend::Postgres, count_sql);

        match self.db.query_one(statement).await {
            Ok(Some(row)) => {
                let count: i64 = row.try_get("", "count").unwrap_or(0);
                debug!("表 {} 数据行数: {}", table_name, count);
                Ok(count)
            }
            Ok(None) => Ok(0),
            Err(e) => {
                warn!("查询表 {} 数据行数失败: {}", table_name, e);
                Ok(0) // 查询失败时返回0，允许删除继续
            }
        }
    }

    /// 备份表数据（带依赖）
    #[instrument(skip(self, config))]
    async fn backup_table_data_with_deps(
        &self,
        config: &ShardingConfig,
        table_name: &str,
        merchant_id: i64,
    ) -> Result<()> {
        let backup_table_name = format!("{}_backup_{}", table_name, chrono::Utc::now().timestamp());

        let backup_sql = format!(
            "CREATE TABLE {}.{} AS SELECT * FROM {}.{}",
            config.schema_name, backup_table_name, config.schema_name, table_name
        );

        debug!("创建备份表 SQL: {}", backup_sql);

        let statement = Statement::from_string(DatabaseBackend::Postgres, backup_sql);
        match self.db.execute(statement).await {
            Ok(_) => {
                debug!("表 {} 数据备份成功 -> {}", table_name, backup_table_name);
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("备份表 {} 失败: {}", table_name, e);
                warn!("{}", error_msg);
                Err(ShardingError::table_management(
                    "backup_table_data_with_deps",
                    merchant_id,
                    error_msg,
                ))
            }
        }
    }

    // ========================================
    // 🆕 静态方法版本 - 更高性能，无需实例化
    // ========================================

    /// 🆕 检查表是否为可分表（静态方法）
    pub fn is_shardable_table_static(config: &ShardingConfig, table_name: &str) -> bool {
        config.shardable_tables.contains(&table_name.to_string())
    }

    /// 🆕 验证分表配置（静态方法）
    pub fn validate_config_static(config: &ShardingConfig) -> Result<()> {
        if config.shardable_tables.is_empty() {
            return Err(ShardingError::config("可分表列表不能为空"));
        }

        if config.schema_name.trim().is_empty() {
            return Err(ShardingError::config("模式名称不能为空"));
        }

        debug!(
            "分表配置验证通过 - 可分表数量: {}, 模式: {}",
            config.shardable_tables.len(),
            config.schema_name
        );

        Ok(())
    }

    /// 🆕 生成分表名称（静态方法）
    pub fn generate_shard_table_name_static(
        config: &ShardingConfig,
        base_table_name: &str,
        merchant_id: i64,
    ) -> Result<String> {
        let rewriter = ShardingSqlRewriter::new(config.clone());
        rewriter.generate_shard_table_name(base_table_name, merchant_id)
    }

    /// 🆕 创建单个分表（静态方法 - 推荐使用）
    ///
    /// # 参数
    /// - `conn`: 数据库连接
    /// - `config`: 分表配置
    /// - `base_table_name`: 基础表名
    /// - `merchant_id`: 商户ID
    ///
    /// # 优势
    /// - 无需实例化 ShardTableManager
    /// - 按需创建内部组件，性能更佳
    /// - 无状态设计，更适合并发场景
    #[instrument(skip(conn, config), fields(base_table = base_table_name, merchant_id = merchant_id))]
    pub async fn create_single_shard_table_static<Conn>(
        conn: &Conn,
        config: &ShardingConfig,
        base_table_name: &str,
        merchant_id: i64,
    ) -> Result<()>
    where
        Conn: ConnectionTrait,
    {
        debug!(
            "开始创建单个分表（静态方法）- 基础表: {}, 商户ID: {}",
            base_table_name, merchant_id
        );

        // 验证基础表是否为可分表
        if !Self::is_shardable_table_static(config, base_table_name) {
            return Err(ShardingError::table_management(
                "create_single_shard_table_static",
                merchant_id,
                format!("表 '{}' 不在可分表列表中", base_table_name),
            ));
        }

        // 按需创建组件
        let mut template_manager = TemplateManager::new();
        let rewriter = ShardingSqlRewriter::new(config.clone());

        // 生成分表名称
        let shard_table_name = rewriter.generate_shard_table_name(base_table_name, merchant_id)?;
        debug!(
            "生成分表名称 - 基础表: {} -> 分表: {}",
            base_table_name, shard_table_name
        );

        // 检查分表是否已存在
        if Self::table_exists_static(conn, config, &shard_table_name).await {
            debug!("分表 {} 已存在，跳过创建", shard_table_name);
            return Ok(());
        }

        // 生成 DDL 语句 - 根据策略选择正确的schema
        let create_sql = if config.naming_strategy == crate::config::NamingStrategy::MerchantSchema {
            // Schema策略：使用商户专用schema，表名保持不变
            let merchant_schema = rewriter.generate_merchant_schema(Some(base_table_name), merchant_id)?;
            template_manager.generate_ddl_for_schema(
                base_table_name,
                base_table_name,  // Schema策略下表名不变
                merchant_id,
                &merchant_schema,
            )
        } else {
            // 传统策略：使用配置的schema，表名已重写
            template_manager.generate_ddl(
                base_table_name,
                &shard_table_name,
                merchant_id,
                &config.schema_name,
            )
        };
        debug!("生成创建分表 SQL: {}", create_sql);

        // 执行 DDL 语句
        let statement = Statement::from_string(DatabaseBackend::Postgres, create_sql);
        match conn.execute(statement).await {
            Ok(result) => {
                debug!(
                    "分表 {} 创建成功 - 受影响行数: {:?}",
                    shard_table_name,
                    result.rows_affected()
                );
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("分表 {} 创建失败: {}", shard_table_name, e);
                warn!("{}", error_msg);
                Err(ShardingError::table_management(
                    "create_single_shard_table_static",
                    merchant_id,
                    error_msg,
                ))
            }
        }
    }

    /// 🆕 检查分表是否存在（静态方法）
    #[instrument(skip(conn, config))]
    pub async fn table_exists_static<Conn>(
        conn: &Conn,
        config: &ShardingConfig,
        shard_table_name: &str,
    ) -> bool
    where
        Conn: ConnectionTrait,
    {
        // 🆕 检查是否使用Schema策略
        let check_sql = if config.naming_strategy == crate::config::NamingStrategy::MerchantSchema {
            // Schema策略：shard_table_name格式为 "merchant_123.orders"
            if let Some(dot_pos) = shard_table_name.find('.') {
                let schema_name = &shard_table_name[..dot_pos];
                let table_name = &shard_table_name[dot_pos + 1..];
                format!(
                    "SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables
                        WHERE table_name = '{}'
                        AND table_schema = '{}'
                    )",
                    table_name, schema_name
                )
            } else {
                // 没有点号，当作表名处理，在配置的schema中查找
                format!(
                    "SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables
                        WHERE table_name = '{}'
                        AND table_schema = '{}'
                    )",
                    shard_table_name, config.schema_name
                )
            }
        } else {
            // 传统策略：在配置的schema中查找表名
            match config.schema_name.as_str() {
                "public" | "" => {
                    format!(
                        "SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables
                            WHERE table_name = '{}'
                            AND table_schema = 'public'
                        )",
                        shard_table_name
                    )
                }
                schema => {
                    format!(
                        "SELECT EXISTS (
                            SELECT 1 FROM information_schema.tables
                            WHERE table_name = '{}'
                            AND table_schema = '{}'
                        )",
                        shard_table_name, schema
                    )
                }
            }
        };

        debug!("检查表存在性（静态方法）- SQL: {}", check_sql);

        let statement = Statement::from_string(DatabaseBackend::Postgres, check_sql);

        match conn.query_one(statement).await {
            Ok(Some(row)) => {
                let exists: bool = row.try_get("", "exists").unwrap_or(false);
                debug!("表 {} 存在性检查结果: {}", shard_table_name, exists);
                exists
            }
            Ok(None) => {
                debug!("表 {} 存在性查询无结果", shard_table_name);
                false
            }
            Err(e) => {
                warn!("表 {} 存在性检查失败: {}", shard_table_name, e);
                false
            }
        }
    }

    /// 🆕 为商户创建所有分表（静态方法 - 推荐使用）
    ///
    /// # 参数
    /// - `conn`: 数据库连接
    /// - `config`: 分表配置
    /// - `merchant_id`: 商户ID
    ///
    /// # 优势
    /// - 无需实例化，性能更佳
    /// - 完全无状态，并发安全
    /// - 按需创建组件，内存占用更少
    #[instrument(skip(conn, config), fields(merchant_id = merchant_id))]
    pub async fn create_merchant_tables_static<Conn>(
        conn: &Conn,
        config: &ShardingConfig,
        merchant_id: i64,
    ) -> Result<()>
    where
        Conn: ConnectionTrait,
    {
        let start_time = std::time::Instant::now();

        debug!(
            "开始为商户 {} 创建分表（静态方法）- 待创建表数量: {}",
            merchant_id,
            config.shardable_tables.len()
        );

        // 为每个可分表创建分表
        let mut created_tables = Vec::new();
        let mut skipped_tables = Vec::new();

        for base_table_name in &config.shardable_tables {
            match Self::create_single_shard_table_static(conn, config, base_table_name, merchant_id)
                .await
            {
                Ok(()) => {
                    let shard_table_name = Self::generate_shard_table_name_static(
                        config,
                        base_table_name,
                        merchant_id,
                    )?;
                    if Self::table_exists_static(conn, config, &shard_table_name).await {
                        created_tables.push(shard_table_name);
                    } else {
                        skipped_tables.push(base_table_name.clone());
                    }
                }
                Err(e) => {
                    let error_msg = format!("创建分表 '{}' 失败: {}", base_table_name, e);
                    warn!("{}", error_msg);
                    return Err(ShardingError::table_management(
                        "create_merchant_tables_static",
                        merchant_id,
                        error_msg,
                    ));
                }
            }
        }

        let duration = start_time.elapsed();
        debug!(
            "商户 {} 分表创建完成（静态方法）- 总耗时: {:?}, 创建表数量: {}, 跳过表数量: {}",
            merchant_id,
            duration,
            created_tables.len(),
            skipped_tables.len()
        );

        if !skipped_tables.is_empty() {
            debug!("商户 {} 跳过的表: {:?}", merchant_id, skipped_tables);
        }

        Ok(())
    }
}

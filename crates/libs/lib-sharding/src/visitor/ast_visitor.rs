//! AST 访问者抽象模块
//!
//! 提供基于访问者模式的 AST 遍历和修改功能，支持可扩展的 SQL 重写操作。

use crate::error::{Result, ShardingError};
use crate::strategy::naming_strategy::TableNamingStrategy;
use sqlparser::ast::{
    Expr, FunctionArg, FunctionArgExpr, FunctionArgumentList, FunctionArguments, Ident, ObjectName,
    Query, Select, SetExpr, Statement, TableFactor, TableWithJoins,
};
use std::collections::HashSet;
use std::sync::Arc;
use tracing::{debug, instrument, warn};

/// AST 访问者抽象接口
///
/// 定义了遍历和修改 SQL AST 的核心功能，支持不同类型的访问者实现。
pub trait AstVisitor {
    /// 访问语句
    ///
    /// # 参数
    /// - `statement`: 要访问的语句
    ///
    /// # 返回
    /// - `Ok(())`: 访问成功
    /// - `Err(ShardingError)`: 访问失败
    fn visit_statement(&mut self, statement: &mut Statement) -> Result<()>;

    /// 访问查询
    fn visit_query(&mut self, query: &mut Query) -> Result<()>;

    /// 访问选择语句
    fn visit_select(&mut self, select: &mut Select) -> Result<()>;

    /// 访问表达式
    fn visit_expr(&mut self, expr: &mut Expr) -> Result<()>;

    /// 访问表因子
    fn visit_table_factor(&mut self, table: &mut TableFactor) -> Result<()>;

    /// 获取访问者类型名称
    fn visitor_type(&self) -> &'static str;

    /// 获取访问统计信息
    fn get_stats(&self) -> VisitorStats;

    /// 重置访问者状态
    fn reset(&mut self);
}

/// 访问者统计信息
#[derive(Debug, Clone, Default)]
pub struct VisitorStats {
    /// 访问的语句数量
    pub statements_visited: usize,
    /// 访问的表数量
    pub tables_visited: usize,
    /// 修改的项目数量
    pub items_modified: usize,
    /// 遇到的错误数量
    pub errors_encountered: usize,
}

impl VisitorStats {
    /// 创建新的统计信息
    pub fn new() -> Self {
        Self::default()
    }

    /// 增加语句访问计数
    pub fn increment_statements(&mut self) {
        self.statements_visited += 1;
    }

    /// 增加表访问计数
    pub fn increment_tables(&mut self) {
        self.tables_visited += 1;
    }

    /// 增加修改计数
    pub fn increment_modifications(&mut self) {
        self.items_modified += 1;
    }

    /// 增加错误计数
    pub fn increment_errors(&mut self) {
        self.errors_encountered += 1;
    }
}

/// 表名重写访问者
///
/// 专门用于重写 SQL AST 中的表名的访问者实现。
pub struct TableNameVisitor {
    /// 表名重写策略
    naming_strategy: Box<dyn TableNamingStrategy>,
    /// 商户ID
    merchant_id: i64,
    /// 可分表的表名集合
    shardable_tables: Arc<HashSet<String>>,
    /// 模式名称
    schema_name: Option<String>,
    /// 访问统计
    stats: VisitorStats,
    /// 是否启用严格模式
    strict_mode: bool,
}

impl TableNameVisitor {
    /// 创建新的表名访问者
    ///
    /// # 参数
    /// - `naming_strategy`: 表名重写策略
    /// - `merchant_id`: 商户ID
    /// - `shardable_tables`: 可分表的表名集合
    /// - `schema_name`: 模式名称
    ///
    /// # 示例
    ///
    /// ```rust
    /// use lib_sharding::visitor::TableNameVisitor;
    /// use lib_sharding::strategy::TableNamingStrategyFactory;
    /// use lib_sharding::config::NamingStrategy;
    /// use std::collections::HashSet;
    /// use std::sync::Arc;
    ///
    /// let strategy = TableNamingStrategyFactory::create_strategy(&NamingStrategy::MerchantSuffix);
    /// let shardable_tables = Arc::new(["orders", "customers"].iter().map(|s| s.to_string()).collect());
    /// let visitor = TableNameVisitor::new(
    ///     strategy,
    ///     123,
    ///     shardable_tables,
    ///     Some("business".to_string())
    /// );
    /// ```
    pub fn new(
        naming_strategy: Box<dyn TableNamingStrategy>,
        merchant_id: i64,
        shardable_tables: Arc<HashSet<String>>,
        schema_name: Option<String>,
    ) -> Self {
        debug!(
            "创建表名访问者 - 商户ID: {}, 策略: {}, 可分表数量: {}",
            merchant_id,
            naming_strategy.strategy_name(),
            shardable_tables.len()
        );

        Self {
            naming_strategy,
            merchant_id,
            shardable_tables,
            schema_name,
            stats: VisitorStats::new(),
            strict_mode: false,
        }
    }

    /// 设置严格模式
    pub fn with_strict_mode(mut self, strict: bool) -> Self {
        self.strict_mode = strict;
        self
    }

    /// 检查表是否需要分表
    fn is_shardable_table(&self, table_name: &str) -> bool {
        self.shardable_tables.contains(table_name)
    }

    /// 重写对象名称（表名）
    #[instrument(skip(self))]
    fn rewrite_object_name(&mut self, object_name: &mut ObjectName) -> Result<bool> {
        if object_name.0.is_empty() {
            return Ok(false);
        }

        // 检查是否有原始模式名
        let (original_schema, table_name) = if object_name.0.len() > 1 {
            // 有模式名：取最后一个作为表名，倒数第二个作为模式名
            let schema = Some(object_name.0[object_name.0.len() - 2].value.clone());
            let table = object_name.0[object_name.0.len() - 1].value.clone();
            (schema, table)
        } else {
            // 没有模式名：只有表名
            let table = object_name.0[0].value.clone();
            (None, table)
        };

        self.stats.increment_tables();

        if !self.is_shardable_table(&table_name) {
            debug!("表 '{}' 不在分表列表中，跳过重写", table_name);
            return Ok(false);
        }

        // 🆕 检查策略是否支持独立Schema
        if self.naming_strategy.supports_independent_schema() {
            // 使用Schema重写策略：只重写Schema，表名保持不变
            return self.rewrite_schema_only(&table_name, object_name);
        } else {
            // 使用传统表名重写策略 (保留原有逻辑)
            return self.rewrite_table_name_traditional(original_schema, &table_name, object_name);
        }
    }

    /// 🆕 Schema策略重写：只重写Schema部分，表名保持不变
    #[instrument(skip(self, object_name))]
    fn rewrite_schema_only(&mut self, table_name: &str, object_name: &mut ObjectName) -> Result<bool> {
        // 生成商户专用的Schema名称
        let merchant_schema = self.naming_strategy.generate_merchant_schema(self.merchant_id)?;
        
        // 🔥 重建ObjectName：完全替换原有schema为商户schema
        // 不管原始SQL中有没有schema，都统一替换为商户专用schema
        let new_idents = vec![
            Ident::new(&merchant_schema),  // 商户专用schema
            Ident::new(table_name),        // 📝 表名保持不变
        ];
        
        *object_name = ObjectName(new_idents);
        self.stats.increment_modifications();

        debug!(
            "Schema重写成功: 任意schema.{} -> {}.{} (商户ID: {})", 
            table_name, 
            merchant_schema, 
            table_name, 
            self.merchant_id
        );

        Ok(true)
    }

    /// 🆕 传统表名重写策略 (保留原有逻辑)
    #[instrument(skip(self, object_name))]
    fn rewrite_table_name_traditional(
        &mut self,
        original_schema: Option<String>,
        table_name: &str,
        object_name: &mut ObjectName,
    ) -> Result<bool> {
        // 决定使用哪个模式名
        let schema_to_use = if let Some(ref orig_schema) = original_schema {
            // 如果原始SQL有模式名，保持原始模式名
            Some(orig_schema.as_str())
        } else {
            // 如果原始SQL没有模式名，使用配置的默认模式名
            self.schema_name.as_deref()
        };

        // 生成新的表名（只传递表名部分，不传递模式名）
        let new_table_name = self.naming_strategy.generate_shard_name(
            table_name,
            self.merchant_id,
            None, // 不让策略自动添加模式名
        )?;

        // 重建 ObjectName
        let mut new_idents = Vec::new();

        if let Some(schema) = schema_to_use {
            new_idents.push(Ident::new(schema));
        }
        new_idents.push(Ident::new(&new_table_name));

        *object_name = ObjectName(new_idents);
        self.stats.increment_modifications();

        let original_full_name = if let Some(ref orig_schema) = original_schema {
            format!("{}.{}", orig_schema, table_name)
        } else {
            table_name.to_string()
        };

        let final_name = if let Some(schema) = schema_to_use {
            format!("{}.{}", schema, new_table_name)
        } else {
            new_table_name.clone()
        };

        debug!("表名重写成功: {} -> {}", original_full_name, final_name);

        Ok(true)
    }
}

impl AstVisitor for TableNameVisitor {
    #[instrument(skip(self, statement))]
    fn visit_statement(&mut self, statement: &mut Statement) -> Result<()> {
        use sqlparser::ast::Statement::*;

        self.stats.increment_statements();

        match statement {
            Query(query) => {
                debug!("访问查询语句");
                self.visit_query(query)?;
            }
            Insert(insert) => {
                debug!("访问插入语句");
                match &mut insert.table {
                    sqlparser::ast::TableObject::TableName(table_name) => {
                        self.rewrite_object_name(table_name)?;
                    }
                    sqlparser::ast::TableObject::TableFunction(_) => {
                        debug!("跳过表函数重写");
                    }
                }
                if let Some(query) = &mut insert.source {
                    self.visit_query(query)?;
                }
            }
            Update {
                table, selection, ..
            } => {
                debug!("访问更新语句");
                self.visit_table_with_joins(table)?;
                if let Some(expr) = selection {
                    self.visit_expr(expr)?;
                }
            }
            Delete(delete) => {
                debug!("访问删除语句");
                for table in &mut delete.tables {
                    self.rewrite_object_name(table)?;
                }
                match &mut delete.from {
                    sqlparser::ast::FromTable::WithFromKeyword(from) => {
                        debug!("处理 FROM 子句");
                        for table_with_joins in from {
                            self.visit_table_with_joins(table_with_joins)?;
                        }
                    }
                    sqlparser::ast::FromTable::WithoutKeyword(_) => {
                        debug!("跳过无 FROM 关键字的删除语句");
                    }
                }
                if let Some(expr) = &mut delete.selection {
                    self.visit_expr(expr)?;
                }
            }
            CreateTable(create_table) => {
                debug!("访问建表语句");
                self.rewrite_object_name(&mut create_table.name)?;
            }
            Drop { names, .. } => {
                debug!("访问删除表语句");
                for name in names {
                    self.rewrite_object_name(name)?;
                }
            }
            _ => {
                debug!("访问其他类型语句: {:?}", statement);
                if self.strict_mode {
                    warn!("严格模式下不支持的语句类型");
                    self.stats.increment_errors();
                    return Err(ShardingError::unsupported(format!(
                        "不支持的语句类型: {:?}",
                        statement
                    )));
                }
            }
        }

        Ok(())
    }

    #[instrument(skip(self, query))]
    fn visit_query(&mut self, query: &mut Query) -> Result<()> {
        // 访问 WITH 子句
        if let Some(with) = &mut query.with {
            for cte in &mut with.cte_tables {
                self.visit_query(&mut cte.query)?;
            }
        }

        // 访问主体查询
        self.visit_set_expr(&mut query.body)?;

        // 访问 ORDER BY 子句
        #[allow(for_loops_over_fallibles)]
        for order_by in &mut query.order_by {
            for expr in &mut order_by.exprs {
                self.visit_expr(&mut expr.expr)?;
            }
        }

        Ok(())
    }

    #[instrument(skip(self, select))]
    fn visit_select(&mut self, select: &mut Select) -> Result<()> {
        // 访问 FROM 子句
        for table_with_joins in &mut select.from {
            self.visit_table_with_joins(table_with_joins)?;
        }

        // 访问 WHERE 子句
        if let Some(selection) = &mut select.selection {
            self.visit_expr(selection)?;
        }

        // 访问 GROUP BY 子句
        match &mut select.group_by {
            sqlparser::ast::GroupByExpr::Expressions(exprs, _) => {
                for expr in exprs {
                    self.visit_expr(expr)?;
                }
            }
            _ => {}
        }

        // 访问 HAVING 子句
        if let Some(having) = &mut select.having {
            self.visit_expr(having)?;
        }

        // 访问选择列表中的表达式
        for item in &mut select.projection {
            if let sqlparser::ast::SelectItem::UnnamedExpr(expr) = item {
                self.visit_expr(expr)?;
            } else if let sqlparser::ast::SelectItem::ExprWithAlias { expr, .. } = item {
                self.visit_expr(expr)?;
            }
        }

        Ok(())
    }

    #[instrument(skip(self, expr))]
    fn visit_expr(&mut self, expr: &mut Expr) -> Result<()> {
        use sqlparser::ast::Expr::*;

        match expr {
            CompoundIdentifier(idents) => {
                // 处理形如 "table_name"."column_name" 的复合标识符
                if idents.len() >= 2 {
                    let table_name = &idents[0].value;
                    if self.shardable_tables.contains(table_name) {
                        let new_table_name = self.naming_strategy.generate_shard_name(
                            table_name,
                            self.merchant_id,
                            None, // 不传递 schema，只生成表名部分
                        )?;
                        idents[0] = Ident::new(&new_table_name);
                        self.stats.increment_modifications();
                    }
                }
            }
            Subquery(query) => {
                self.visit_query(query)?;
            }
            Function(func) => match &mut func.args {
                FunctionArguments::List(FunctionArgumentList { args, .. }) => {
                    for arg in args {
                        if let FunctionArg::Unnamed(FunctionArgExpr::Expr(expr)) = arg {
                            self.visit_expr(expr)?;
                        }
                    }
                }
                _ => {}
            },
            BinaryOp { left, right, .. } => {
                self.visit_expr(left)?;
                self.visit_expr(right)?;
            }
            UnaryOp { expr, .. } => {
                self.visit_expr(expr)?;
            }
            Cast { expr, .. } => {
                self.visit_expr(expr)?;
            }
            Nested(expr) => {
                self.visit_expr(expr)?;
            }
            InSubquery { expr, subquery, .. } => {
                self.visit_expr(expr)?;
                self.visit_query(subquery)?;
            }
            Exists { subquery, .. } => {
                self.visit_query(subquery)?;
            }
            Case {
                operand,
                conditions,
                else_result,
                results,
                ..
            } => {
                // 处理 CASE 表达式
                if let Some(operand) = operand {
                    self.visit_expr(operand)?;
                }
                // 访问所有条件表达式
                for condition in conditions {
                    self.visit_expr(condition)?;
                }
                // 访问所有结果表达式
                for result in results {
                    self.visit_expr(result)?;
                }
                if let Some(else_result) = else_result {
                    self.visit_expr(else_result)?;
                }
            }
            Between {
                expr, low, high, ..
            } => {
                // 处理 BETWEEN 表达式
                self.visit_expr(expr)?;
                self.visit_expr(low)?;
                self.visit_expr(high)?;
            }
            InList { expr, list, .. } => {
                // 处理 IN 列表表达式
                self.visit_expr(expr)?;
                for item in list {
                    self.visit_expr(item)?;
                }
            }
            IsNull(expr) | IsNotNull(expr) => {
                self.visit_expr(expr)?;
            }
            Like { expr, pattern, .. } | ILike { expr, pattern, .. } => {
                self.visit_expr(expr)?;
                self.visit_expr(pattern)?;
            }
            SimilarTo { expr, pattern, .. } => {
                self.visit_expr(expr)?;
                self.visit_expr(pattern)?;
            }
            AnyOp { left, right, .. } | AllOp { left, right, .. } => {
                self.visit_expr(left)?;
                self.visit_expr(right)?;
            }
            _ => {
                // 其他表达式类型不需要特殊处理
            }
        }

        Ok(())
    }

    #[instrument(skip(self, table))]
    fn visit_table_factor(&mut self, table: &mut TableFactor) -> Result<()> {
        use sqlparser::ast::TableFactor::*;

        match table {
            Table { name, .. } => {
                self.rewrite_object_name(name)?;
            }
            Derived { subquery, .. } => {
                self.visit_query(subquery)?;
            }
            TableFunction { expr, .. } => {
                self.visit_expr(expr)?;
            }
            _ => {
                debug!("访问其他类型的表因子");
            }
        }

        Ok(())
    }

    fn visitor_type(&self) -> &'static str {
        "table_name_visitor"
    }

    fn get_stats(&self) -> VisitorStats {
        self.stats.clone()
    }

    fn reset(&mut self) {
        self.stats = VisitorStats::new();
        debug!("表名访问者状态已重置");
    }
}

impl TableNameVisitor {
    /// 访问表连接
    #[instrument(skip(self, table_with_joins))]
    fn visit_table_with_joins(&mut self, table_with_joins: &mut TableWithJoins) -> Result<()> {
        // 访问主表
        self.visit_table_factor(&mut table_with_joins.relation)?;

        // 访问连接表
        for join in &mut table_with_joins.joins {
            self.visit_table_factor(&mut join.relation)?;
            match &mut join.join_operator {
                sqlparser::ast::JoinOperator::Inner(constraint)
                | sqlparser::ast::JoinOperator::LeftOuter(constraint)
                | sqlparser::ast::JoinOperator::RightOuter(constraint)
                | sqlparser::ast::JoinOperator::FullOuter(constraint) => {
                    if let sqlparser::ast::JoinConstraint::On(expr) = constraint {
                        self.visit_expr(expr)?;
                    }
                }
                _ => {}
            }
        }

        Ok(())
    }

    /// 访问集合表达式
    #[instrument(skip(self, set_expr))]
    fn visit_set_expr(&mut self, set_expr: &mut SetExpr) -> Result<()> {
        use sqlparser::ast::SetExpr::*;

        match set_expr {
            Select(select) => {
                self.visit_select(select)?;
            }
            Query(query) => {
                self.visit_query(query)?;
            }
            SetOperation { left, right, .. } => {
                self.visit_set_expr(left)?;
                self.visit_set_expr(right)?;
            }
            Values(_) => {
                // VALUES 子句不包含表名
            }
            _ => {
                debug!("访问其他类型的集合表达式");
            }
        }

        Ok(())
    }
}

/// AST 访问者工厂
///
/// 提供创建不同类型访问者的工厂方法。
pub struct AstVisitorFactory;

impl AstVisitorFactory {
    /// 创建表名重写访问者
    pub fn create_table_name_visitor(
        naming_strategy: Box<dyn TableNamingStrategy>,
        merchant_id: i64,
        shardable_tables: Arc<HashSet<String>>,
        schema_name: Option<String>,
    ) -> Box<dyn AstVisitor> {
        Box::new(TableNameVisitor::new(
            naming_strategy,
            merchant_id,
            shardable_tables,
            schema_name,
        ))
    }
}

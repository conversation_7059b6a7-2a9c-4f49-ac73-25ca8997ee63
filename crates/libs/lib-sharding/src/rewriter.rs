//! SQL 重写器模块
//!
//! 提供基于 AST 的 SQL 重写功能，支持表名替换和分表路由。

use crate::config::ShardingConfig;
use crate::context::get_current_merchant_id;
use crate::error::{Result, ShardingError};
use crate::strategy::naming_strategy::TableNamingStrategyFactory;
use crate::visitor::AstVisitorFactory;
use lib_core::app::plugin::service::Service as ServiceTrait;
use lib_core::app::plugin::Service;
use sea_orm::{Statement, Values};
use sqlparser::dialect::PostgreSqlDialect;
use sqlparser::parser::Parser;
use std::collections::HashSet;
use std::sync::Arc;
use std::time::Instant;
use tracing::{debug, info, instrument, warn};

/// 分表 SQL 重写器
///
/// 负责将原始 SQL 语句重写为分表 SQL，支持表名替换和复杂查询处理。
/// 商户ID从租户上下文中获取，不再存储在重写器中。
#[derive(Debug, Clone, Service)]
pub struct ShardingSqlRewriter {
    /// 分表配置
    #[inject(config)]
    config: ShardingConfig,
}

impl ShardingSqlRewriter {
    /// 创建新的 SQL 重写器
    pub fn new(config: ShardingConfig) -> Self {
        debug!(
            "创建 SQL 重写器 - 可分表数量: {}",
            config.custom_ddl_templates.len()
        );

        Self { config }
    }

    /// 获取当前商户ID
    fn get_merchant_id(&self) -> Result<i64> {
        get_current_merchant_id()
            .map_err(|e| ShardingError::internal(format!("获取租户上下文失败: {}", e)))
    }

    /// 重写 SQL 语句
    #[instrument(skip(self, values))]
    pub fn rewrite_sql(
        &self,
        sql: &str,
        values: Option<Values>,
    ) -> Result<(String, Option<Values>)> {
        let start_time = Instant::now();
        let merchant_id = self.get_merchant_id()?;

        // 验证输入参数
        if sql.trim().is_empty() {
            return Err(ShardingError::internal("SQL 语句不能为空"));
        }

        // 解析 SQL 语句
        let parsed_statements = self.parse_sql_statements(sql)?;
        self.validate_parsed_statements(&parsed_statements)?;

        // 执行 AST 表名替换
        let rewritten_sql =
            self.rewrite_statements_sql_statement(parsed_statements, merchant_id)?;

        let duration = start_time.elapsed();
        if self.config.log_sql_rewrite {
            info!(
                "SQL 重写完成 - 商户ID: {}, 耗时: {:?}",
                merchant_id, duration
            );
        }

        Ok((rewritten_sql, values))
    }

    /// 解析 SQL 语句
    fn parse_sql_statements(&self, sql: &str) -> Result<Vec<sqlparser::ast::Statement>> {
        let dialect = PostgreSqlDialect {};
        Parser::parse_sql(&dialect, sql).map_err(|e| ShardingError::SqlParse { source: e })
    }

    /// 验证解析后的语句
    fn validate_parsed_statements(&self, statements: &[sqlparser::ast::Statement]) -> Result<()> {
        if statements.is_empty() {
            return Err(ShardingError::internal("未解析到有效的 SQL 语句"));
        }

        for (i, statement) in statements.iter().enumerate() {
            debug!("验证语句 {}: {:?}", i + 1, statement);
        }

        Ok(())
    }

    // 重写语句列表
    pub fn rewrite_statements_sql_statement(
        &self,
        mut statements: Vec<sqlparser::ast::Statement>,
        merchant_id: i64,
    ) -> Result<String> {
        let shardable_tables: Arc<HashSet<String>> =
            Arc::new(self.config.shardable_tables.iter().cloned().collect());
        // 创建表名访问者
        let naming_strategy =
            TableNamingStrategyFactory::create_strategy(&self.config.naming_strategy);
        let mut visitor = AstVisitorFactory::create_table_name_visitor(
            naming_strategy,
            merchant_id,
            shardable_tables,
            Some(self.config.schema_name.clone()),
        );

        // 重写每个语句
        for statement in &mut statements {
            visitor.visit_statement(statement)?;
        }

        // 获取统计信息
        let stats = visitor.get_stats();
        debug!(
            "重写统计 - 语句数: {}, 表访问数: {}, 修改项目数: {}",
            stats.statements_visited, stats.tables_visited, stats.items_modified
        );

        // 重新生成 SQL
        let rewritten_sql = statements
            .iter()
            .map(|stmt| stmt.to_string())
            .collect::<Vec<_>>()
            .join("; ");

        Ok(rewritten_sql)
    }

    /// 生成分表名
    pub fn generate_shard_table_name(
        &self,
        base_table_name: &str,
        merchant_id: i64,
    ) -> Result<String> {
        let naming_strategy =
            TableNamingStrategyFactory::create_strategy(&self.config.naming_strategy);
        naming_strategy.generate_shard_name(base_table_name, merchant_id, None)
    }

    /// 生成商户Schema名称（仅支持Schema策略）
    pub fn generate_merchant_schema(
        &self,
        base_table_name: Option<&str>,
        merchant_id: i64,
    ) -> Result<String> {
        let naming_strategy =
            TableNamingStrategyFactory::create_strategy(&self.config.naming_strategy);

        if !naming_strategy.supports_independent_schema() {
            return Err(ShardingError::config(format!(
                "当前命名策略 '{}' 不支持独立Schema生成",
                naming_strategy.strategy_name()
            )));
        }

        let schema_name = naming_strategy.generate_merchant_schema(merchant_id)?;

        if let Some(table) = base_table_name {
            debug!(
                "为表 '{}' 生成商户Schema: {} (商户ID: {})",
                table, schema_name, merchant_id
            );
        } else {
            debug!("生成商户Schema: {} (商户ID: {})", schema_name, merchant_id);
        }

        Ok(schema_name)
    }

    /// 重写 Sea-ORM Statement
    #[instrument(skip(self, statement))]
    pub fn rewrite_statements(&self, statement: &Statement) -> Result<(String, Values)> {
        let start_time = Instant::now();
        let merchant_id = self.get_merchant_id()?;

        // 从 Statement 中提取 SQL 和参数
        let sql = statement.sql.as_str();
        let values = statement.values.clone().unwrap();

        // 验证输入参数
        if sql.trim().is_empty() {
            return Err(ShardingError::internal("SQL 语句不能为空"));
        }

        // 解析 SQL 语句
        let parsed_statements = self.parse_sql_statements(sql)?;
        self.validate_parsed_statements(&parsed_statements)?;

        // 执行 AST 表名替换
        let rewritten_sql = self.rewrite_statements_ast(parsed_statements, merchant_id)?;

        let duration = start_time.elapsed();
        if self.config.log_sql_rewrite {
            info!(
                "Statement 重写完成 - 商户ID: {}, 耗时: {:?}",
                merchant_id, duration
            );
        }

        Ok((rewritten_sql, values))
    }
    /// 重写语句列表 (AST)
    fn rewrite_statements_ast(
        &self,
        mut statements: Vec<sqlparser::ast::Statement>,
        merchant_id: i64,
    ) -> Result<String> {
        // 创建表名访问者
        let naming_strategy =
            TableNamingStrategyFactory::create_strategy(&self.config.naming_strategy);
        let mut visitor = AstVisitorFactory::create_table_name_visitor(
            naming_strategy,
            merchant_id,
            Arc::new(self.config.shardable_tables.iter().cloned().collect()),
            Some(self.config.schema_name.clone()),
        );

        // 重写每个语句
        for statement in &mut statements {
            visitor.visit_statement(statement)?;
        }

        // 获取统计信息
        let stats = visitor.get_stats();
        debug!(
            "重写统计 - 语句数: {}, 表访问数: {}, 修改项目数: {}",
            stats.statements_visited, stats.tables_visited, stats.items_modified
        );

        // 重新生成 SQL
        let rewritten_sql = statements
            .iter()
            .map(|stmt| stmt.to_string())
            .collect::<Vec<_>>()
            .join("; ");

        Ok(rewritten_sql)
    }
    /// 🆕 检查当前配置是否使用Schema策略
    pub fn is_using_schema_strategy(&self) -> bool {
        let naming_strategy =
            TableNamingStrategyFactory::create_strategy(&self.config.naming_strategy);
        naming_strategy.supports_independent_schema()
    }
}

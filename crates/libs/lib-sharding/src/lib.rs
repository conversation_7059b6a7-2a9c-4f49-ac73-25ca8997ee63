//! 分表库
//! 
//! 提供基于 Sea-ORM 的透明分表功能

pub mod config;
pub mod context;
pub mod error;
pub mod manager;
pub mod rewriter;
pub mod service;
pub mod sharded_connection;
pub mod strategy;
pub mod template;
pub mod visitor;

use async_trait::async_trait;
use sea_orm::DatabaseConnection;
use lib_core::app::app::AppBuilder;
use lib_core::app::plugin::{ComponentRegistry, MutableComponentRegistry, Plugin};
use lib_core::config::ConfigRegistry;

// 重新导出主要类型
pub use config::{ShardingConfig, NamingStrategy};
pub use context::{TenantContext, with_tenant_context, set_tenant_context, get_current_merchant_id};
pub use error::{ShardingError, Result as ShardingResult};
pub use manager::{ShardTableManager, ShardTableInfo, DropTableOptions, ShardTableDeleteResult};
pub use service::ShardingService;
pub use sharded_connection::{ShardedConnection};


use crate::sharded_connection::create_sharded_connection;

// 常量
pub const DEFAULT_SCHEMA_NAME: &str = "business";

/// 分表插件
/// 负责分表连接代理的初始化和注册
pub struct ShardingPlugin;

#[async_trait]
impl Plugin for ShardingPlugin {
    async fn build(&self, app: &mut AppBuilder) {
        // 1. 加载分表配置
        let config = app
            .get_config::<ShardingConfig>()
            .expect("sharding plugin config load failed");

        // 2. 获取数据库连接组件
        // let db_conn = app
        let db_conn = app.try_get_component::<DatabaseConnection>()
            // .get_component::<DatabaseConnection>()
            .expect("database connection not found, make sure DataPlugin is loaded first")
            .clone();

        // 3. 创建分表连接代理
        let sharded_conn = Self::create_sharded_connection(db_conn, config);

        // 4. 注册分表连接代理组件
        app.add_component(sharded_conn);
    }
}

impl ShardingPlugin {
    /// 创建分表连接代理
    /// 为指定的数据库连接创建分表代理，支持透明的SQL重写
    fn create_sharded_connection(db_conn: DatabaseConnection, config: ShardingConfig) -> ShardedConnection<DatabaseConnection> {
        tracing::info!("Creating sharded database connection proxy...");
        
        let sharded_conn = create_sharded_connection(db_conn, config);
        
        tracing::info!("Sharded database connection proxy created successfully");
        sharded_conn
    }
}

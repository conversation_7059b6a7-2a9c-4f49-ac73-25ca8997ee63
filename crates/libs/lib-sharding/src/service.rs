//! 分表服务模块
//!
//! 提供对外访问的分表服务接口，支持依赖注入和业务集成。

use crate::config::ShardingConfig;
use crate::error::Result;
use crate::manager::{DropTableOptions, ShardTableDeleteResult, ShardTableInfo, ShardTableManager};
use crate::rewriter::ShardingSqlRewriter;
use lib_core::app::plugin::service::Service as ServiceTrait;
use sea_orm::{ConnectionTrait, Statement};
use tracing::{debug, info, instrument, warn};

use lib_macros::Service;
use crate::{set_tenant_context, ShardingError, TenantContext};

/// 分表服务主接口
///
/// 这是lib-sharding库的主要对外服务接口，提供：
/// - 透明的分表CRUD操作
/// - 分表管理功能（创建、删除、查询）
/// - 租户上下文管理
/// - SQL重写服务
/// - 依赖注入支持
#[derive(Clone, Service)]
pub struct ShardingService {
    /// 分表配置
    #[inject(config)]
    config: ShardingConfig,
    /// SQL重写器（可以作为组件注入）
    #[inject(component)]
    sql_rewriter: ShardingSqlRewriter,
}

impl ShardingService {
    /// 创建新的分表服务实例
    ///
    /// # 参数
    /// - `config`: 分表配置
    pub fn new(config: ShardingConfig) -> Self {
        let sql_rewriter = ShardingSqlRewriter::new(config.clone());
        Self {
            config,
            sql_rewriter,
        }
    }

    /// 获取分表配置引用
    pub fn config(&self) -> &ShardingConfig {
        &self.config
    }

    /// 获取分表配置副本
    ///
    /// 用于创建 ShardedConnection 等需要拥有配置的场景
    pub fn get_config(&self) -> ShardingConfig {
        self.config.clone()
    }

    /// 获取SQL重写器引用
    pub fn sql_rewriter(&self) -> &ShardingSqlRewriter {
        &self.sql_rewriter
    }

    /// 创建分表管理器（按需创建）
    ///
    /// 注意：推荐使用静态方法版本以获得更好的性能
    fn create_table_manager<C>(&self, db: C) -> ShardTableManager<C>
    where
        C: ConnectionTrait,
    {
        ShardTableManager::new(db)
    }

    // ========================================
    // 🚀 高性能静态方法版本（推荐使用）
    // ========================================

    /// 🆕 为商户创建所有分表（静态方法版本 - 推荐）
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `merchant_id`: 商户ID
    ///
    /// # 优势
    /// - 无需创建manager实例，性能更佳
    /// - 按需创建组件，内存占用更少
    /// - 完全无状态，更适合并发场景
    #[instrument(skip(self, db))]
    pub async fn create_merchant_tables_fast<C>(&self, db: &C, merchant_id: i64) -> Result<()>
    where
        C: ConnectionTrait,
    {
        info!("开始为商户 {} 创建分表（静态方法）", merchant_id);
        ShardTableManager::<C>::create_merchant_tables_static(db, &self.config, merchant_id).await
    }

    /// 🆕 创建单个分表（静态方法版本 - 推荐）
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `base_table_name`: 基础表名
    /// - `merchant_id`: 商户ID
    ///
    /// # 优势
    /// - 无需创建manager实例，性能更佳
    /// - 按需创建组件，内存占用更少
    #[instrument(skip(self, db))]
    pub async fn create_single_shard_table_fast<C>(
        &self,
        db: &C,
        base_table_name: &str,
        merchant_id: i64,
    ) -> Result<()>
    where
        C: ConnectionTrait,
    {
        debug!(
            "创建单个分表（静态方法）- 表: {}, 商户: {}",
            base_table_name, merchant_id
        );
        ShardTableManager::<C>::create_single_shard_table_static(
            db,
            &self.config,
            base_table_name,
            merchant_id,
        )
        .await
    }

    // ========================================
    // 📦 传统方法（兼容性保留）
    // ========================================

    /// 为商户创建所有分表
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `merchant_id`: 商户ID
    #[instrument(skip(self, db))]
    pub async fn create_merchant_tables<C>(&self, db: C, merchant_id: i64) -> Result<()>
    where
        C: ConnectionTrait,
    {
        info!("开始为商户 {} 创建分表", merchant_id);
        let table_manager = self.create_table_manager(db);
        table_manager
            .create_merchant_tables_with_deps(&self.config, &self.sql_rewriter, merchant_id)
            .await
    }

    /// 删除商户的所有分表
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `merchant_id`: 商户ID
    /// - `force`: 是否强制删除
    #[instrument(skip(self, db))]
    pub async fn drop_merchant_tables<C>(&self, db: C, merchant_id: i64, force: bool) -> Result<()>
    where
        C: ConnectionTrait,
    {
        warn!(
            "开始删除商户 {} 的所有分表 - 强制删除: {}",
            merchant_id, force
        );
        let table_manager = self.create_table_manager(db);
        let options = if force {
            crate::manager::DropTableOptions::force()
        } else {
            crate::manager::DropTableOptions::default()
        };
        table_manager
            .drop_merchant_tables_with_deps(&self.config, &self.sql_rewriter, merchant_id, &options)
            .await?;
        Ok(())
    }

    /// 删除商户的所有分表（带选项）
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `merchant_id`: 商户ID
    /// - `options`: 删除选项
    #[instrument(skip(self, db))]
    pub async fn drop_merchant_tables_with_options<C>(
        &self,
        db: C,
        merchant_id: i64,
        options: &DropTableOptions,
    ) -> Result<ShardTableDeleteResult>
    where
        C: ConnectionTrait,
    {
        warn!(
            "开始删除商户 {} 的所有分表 - 选项: {:?}",
            merchant_id, options
        );
        let table_manager = self.create_table_manager(db);
        table_manager
            .drop_merchant_tables_with_deps(&self.config, &self.sql_rewriter, merchant_id, options)
            .await
    }

    /// 创建单个分表
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `base_table_name`: 基础表名
    /// - `merchant_id`: 商户ID
    #[instrument(skip(self, db))]
    pub async fn create_single_shard_table<C>(
        &self,
        db: C,
        base_table_name: &str,
        merchant_id: i64,
    ) -> Result<()>
    where
        C: ConnectionTrait,
    {
        debug!(
            "创建单个分表 - 表: {}, 商户: {}",
            base_table_name, merchant_id
        );
        let table_manager = self.create_table_manager(db);
        let mut template_manager = crate::template::TemplateManager::new();
        table_manager
            .create_single_shard_table_with_deps(
                &self.config,
                &self.sql_rewriter,
                &mut template_manager,
                base_table_name,
                merchant_id,
            )
            .await
    }

    /// 删除单个分表
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `base_table_name`: 基础表名
    /// - `merchant_id`: 商户ID
    /// - `force`: 是否强制删除
    #[instrument(skip(self, db))]
    pub async fn drop_single_shard_table<C>(
        &self,
        db: C,
        base_table_name: &str,
        merchant_id: i64,
        force: bool,
    ) -> Result<()>
    where
        C: ConnectionTrait,
    {
        debug!(
            "删除单个分表 - 表: {}, 商户: {}, 强制: {}",
            base_table_name, merchant_id, force
        );
        let table_manager = self.create_table_manager(db);
        let options = if force {
            crate::manager::DropTableOptions::force()
        } else {
            crate::manager::DropTableOptions::default()
        };
        table_manager
            .drop_single_shard_table_with_deps(
                &self.config,
                &self.sql_rewriter,
                base_table_name,
                merchant_id,
                &options,
            )
            .await
    }

    /// 获取商户分表信息
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `merchant_id`: 商户ID
    #[instrument(skip(self, db))]
    pub async fn get_merchant_tables_info<C>(
        &self,
        db: C,
        merchant_id: i64,
    ) -> Result<Vec<ShardTableInfo>>
    where
        C: ConnectionTrait,
    {
        debug!("获取商户 {} 的分表信息", merchant_id);
        let table_manager = self.create_table_manager(db);
        table_manager
            .get_merchant_tables_info_with_deps(&self.config, &self.sql_rewriter, merchant_id)
            .await
    }

    /// 检查商户分表完整性
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `merchant_id`: 商户ID
    #[instrument(skip(self, db))]
    pub async fn check_merchant_tables_integrity<C>(&self, db: C, merchant_id: i64) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        debug!("检查商户 {} 的分表完整性", merchant_id);
        let table_manager = self.create_table_manager(db);
        table_manager
            .check_merchant_tables_integrity_with_deps(
                &self.config,
                &self.sql_rewriter,
                merchant_id,
            )
            .await
    }

    /// 获取单个分表信息
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `base_table_name`: 基础表名
    /// - `merchant_id`: 商户ID
    #[instrument(skip(self, db))]
    pub async fn get_shard_table_info<C>(
        &self,
        db: C,
        base_table_name: &str,
        merchant_id: i64,
    ) -> Result<ShardTableInfo>
    where
        C: ConnectionTrait,
    {
        debug!(
            "获取分表信息 - 表: {}, 商户: {}",
            base_table_name, merchant_id
        );
        let table_manager = self.create_table_manager(db);
        table_manager
            .get_shard_table_info_with_deps(
                &self.config,
                &self.sql_rewriter,
                base_table_name,
                merchant_id,
            )
            .await
    }

    // ========================================
    // 🔍 SQL重写相关方法
    // ========================================

    /// 重写SQL语句
    ///
    /// # 参数
    /// - `sql`: 原始SQL语句
    /// - `merchant_id`: 商户ID
    #[instrument(skip(self, sql))]
    pub async fn rewrite_sql<C>(&self, sql: &str, merchant_id: i64) -> Result<String>
    where
        C: ConnectionTrait + std::fmt::Debug,
    {
        debug!("重写SQL - 商户: {}, SQL: {}", merchant_id, sql);
        let (rewritten_sql, _) = self.sql_rewriter.rewrite_sql(sql, None)?;
        Ok(rewritten_sql)
    }

    /// 重写Statement
    ///
    /// # 参数
    /// - `stmt`: Statement对象
    /// - `merchant_id`: 商户ID
    #[instrument(skip(self, sql))]
    pub async fn rewrite_statement<C>(&self, sql: Statement, merchant_id: i64) -> Result<Statement>
    where
        C: ConnectionTrait + std::fmt::Debug,
    {
        debug!("重写Statement - 商户: {}", merchant_id);
        let (rewritten_sql, values) = self.sql_rewriter.rewrite_statements(&sql)?;
        Ok(Statement::from_sql_and_values(
            sql.db_backend,
            rewritten_sql,
            values,
        ))
    }

    // ========================================
    // 🚧 Schema策略相关方法（暂时移除）
    //
    // 注意：Schema策略相关的方法在新的架构中暂时移除
    // 如需使用Schema策略，请直接使用manager的静态方法
    // ========================================
    /// 生成分表名称
    ///
    /// # 参数
    /// - `base_table_name`: 基础表名
    /// - `merchant_id`: 商户ID
    pub fn generate_shard_table_name(
        &self,
        base_table_name: &str,
        merchant_id: i64,
    ) -> Result<String> {
        self.sql_rewriter
            .generate_shard_table_name(base_table_name, merchant_id)
    }

    /// 生成商户Schema名称（仅支持Schema策略）
    ///
    /// # 参数
    /// - `merchant_id`: 商户ID
    pub fn generate_merchant_schema_name(&self, merchant_id: i64) -> Result<String> {
        self.sql_rewriter
            .generate_merchant_schema(None, merchant_id)
    }

    /// 检查是否使用Schema策略
    pub fn is_using_schema_strategy(&self) -> bool {
        self.sql_rewriter.is_using_schema_strategy()
    }

    /// 为商户创建Schema和所有表（Schema策略专用）
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `merchant_id`: 商户ID
    /// 
    /// # 功能说明
    /// 此方法会：
    /// 1. 先检查并创建商户专用的Schema（如果不存在）
    /// 2. 然后在该Schema中创建所有分表
    #[instrument(skip(self, db))]
    pub async fn create_merchant_schema_and_tables<C>(&self, db: C, merchant_id: i64) -> Result<()>
    where
        C: ConnectionTrait + Clone,
    {
        if !self.is_using_schema_strategy() {
            return Err(crate::error::ShardingError::config(
                "当前配置不使用Schema策略，请使用 create_merchant_tables 方法",
            ));
        }
        info!("开始为商户 {} 创建Schema和表", merchant_id);
        
        // 步骤1：先创建商户Schema（如果不存在）
        let schema_exists = self.merchant_schema_exists(db.clone(), merchant_id).await?;
        
        if !schema_exists {
            info!("商户 {} 的Schema不存在，开始创建", merchant_id);
            self.create_merchant_schema(db.clone(), merchant_id).await?;
        } else {
            debug!("商户 {} 的Schema已存在，跳过创建", merchant_id);
        }
        
        // 步骤2：在Schema中创建所有分表
        info!("开始为商户 {} 创建分表", merchant_id);
        self.create_merchant_tables(db, merchant_id).await?;
        
        info!("商户 {} 的Schema和表创建完成", merchant_id);
        Ok(())
    }

    /// 创建商户Schema
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `merchant_id`: 商户ID
    #[instrument(skip(self, db))]
    pub async fn create_merchant_schema<C>(&self, db: C, merchant_id: i64) -> Result<()>
    where
        C: ConnectionTrait,
    {
        debug!("创建商户 {} 的Schema", merchant_id);
        
        if !self.is_using_schema_strategy() {
            return Err(crate::error::ShardingError::config(
                "当前配置不使用Schema策略",
            ));
        }

        // 生成商户Schema名称
        let schema_name = self.generate_merchant_schema_name(merchant_id)?;
        
        // 创建Schema的SQL
        let create_schema_sql = format!("CREATE SCHEMA IF NOT EXISTS {}", schema_name);
        let statement = sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres, 
            create_schema_sql
        );
        
        let table_manager = self.create_table_manager(db);
        table_manager.db().execute(statement).await
            .map_err(|e| crate::error::ShardingError::internal(format!("创建Schema失败: {}", e)))?;
        
        debug!("商户 {} 的Schema {} 创建成功", merchant_id, schema_name);
        Ok(())
    }

    /// 删除商户Schema
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `merchant_id`: 商户ID
    /// - `cascade`: 是否级联删除
    #[instrument(skip(self, db))]
    pub async fn drop_merchant_schema<C>(&self, db: C, merchant_id: i64, cascade: bool) -> Result<()>
    where
        C: ConnectionTrait,
    {
        warn!("删除商户 {} 的Schema - 级联: {}", merchant_id, cascade);
        
        if !self.is_using_schema_strategy() {
            return Err(crate::error::ShardingError::config(
                "当前配置不使用Schema策略",
            ));
        }

        // 生成商户Schema名称
        let schema_name = self.generate_merchant_schema_name(merchant_id)?;
        
        // 删除Schema的SQL
        let drop_schema_sql = if cascade {
            format!("DROP SCHEMA IF EXISTS {} CASCADE", schema_name)
        } else {
            format!("DROP SCHEMA IF EXISTS {} RESTRICT", schema_name)
        };
        
        let statement = sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres, 
            drop_schema_sql
        );
        
        let table_manager = self.create_table_manager(db);
        table_manager.db().execute(statement).await
            .map_err(|e| crate::error::ShardingError::internal(format!("删除Schema失败: {}", e)))?;
        
        debug!("商户 {} 的Schema {} 删除成功", merchant_id, schema_name);
        Ok(())
    }

    /// 检查商户Schema是否存在
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `merchant_id`: 商户ID
    #[instrument(skip(self, db))]
    pub async fn merchant_schema_exists<C>(&self, db: C, merchant_id: i64) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        let table_manager = self.create_table_manager(db);
        self.merchant_schema_exists_internal(&table_manager, merchant_id).await
    }

    /// 内部方法：检查商户Schema是否存在
    async fn merchant_schema_exists_internal<C>(&self, table_manager: &ShardTableManager<C>, merchant_id: i64) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        debug!("检查商户 {} 的Schema是否存在", merchant_id);
        
        if !self.is_using_schema_strategy() {
            return Ok(false);
        }

        // 生成商户Schema名称
        let schema_name = self.generate_merchant_schema_name(merchant_id)?;
        
        // 检查Schema是否存在的SQL
        let check_sql = format!(
            "SELECT EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = '{}')",
            schema_name
        );
        
        let statement = sea_orm::Statement::from_string(
            sea_orm::DatabaseBackend::Postgres, 
            check_sql
        );
        
        match table_manager.db().query_one(statement).await {
            Ok(Some(row)) => {
                let exists: bool = row.try_get("", "exists").unwrap_or(false);
                debug!("商户 {} 的Schema {} 存在性: {}", merchant_id, schema_name, exists);
                Ok(exists)
            }
            Ok(None) => Ok(false),
            Err(e) => {
                warn!("检查Schema存在性失败: {}", e);
                Ok(false)
            }
        }
    }


    // ========================================
    // 🛠️ 工具方法
    // ========================================

    /// 检查表是否可分表
    ///
    /// # 参数
    /// - `table_name`: 表名
    pub fn is_shardable_table(&self, table_name: &str) -> bool {
        // 不需要数据库连接，只需要配置
        ShardTableManager::<sea_orm::DatabaseConnection>::is_shardable_table_static(
            &self.config,
            table_name,
        )
    }

    /// 验证分表配置
    pub fn validate_config(&self) -> Result<()> {
        // 不需要数据库连接，只需要配置
        ShardTableManager::<sea_orm::DatabaseConnection>::validate_config_static(&self.config)
    }

    pub fn set_current_tenant(&self, merchant_id: i64) -> Result<()> {
        let context = TenantContext::new(merchant_id)
            .map_err(|e| ShardingError::internal(format!("创建租户上下文失败: {}", e)))?;
        set_tenant_context(context);
        debug!("设置租户上下文 - 商户ID: {}", merchant_id);
        Ok(())
    }

}
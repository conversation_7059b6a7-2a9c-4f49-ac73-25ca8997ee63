//! 分表库错误处理模块

use std::fmt;

/// 分表库统一错误类型
#[derive(Debug, thiserror::Error)]
pub enum ShardingError {
    /// 配置错误
    #[error("配置错误: {message}")]
    Config { message: String },

    /// SQL 解析错误
    #[error("SQL 解析失败: {source}")]
    SqlParse {
        #[source]
        source: sqlparser::parser::ParserError,
    },

    /// 数据库操作错误
    #[error("数据库操作失败: {0}")]
    Database(#[from] sea_orm::DbErr),

    /// 表名重写错误
    #[error("表名重写失败: {table_name}, 原因: {reason}")]
    TableRewrite { table_name: String, reason: String },

    /// 分表管理错误
    #[error("分表管理操作失败: {operation}, 商户ID: {merchant_id}, 原因: {reason}")]
    TableManagement {
        operation: String,
        merchant_id: i64,
        reason: String,
    },

    /// 不支持的操作
    #[error("不支持的操作: {operation}")]
    UnsupportedOperation { operation: String },

    /// 内部错误
    #[error("内部错误: {message}")]
    Internal { message: String },

    /// DDL 验证错误
    #[error("DDL 验证错误: {message}")]
    Validation { message: String },

    /// 无效的商户ID
    #[error("无效的商户ID: {merchant_id}，商户ID必须大于0")]
    InvalidMerchantId { merchant_id: i64 },

    /// 租户上下文错误
    #[error("租户上下文错误: {0}")]
    TenantContext(#[from] crate::context::TenantContextError),
}

impl ShardingError {
    /// 创建配置错误
    pub fn config<S: Into<String>>(message: S) -> Self {
        Self::Config {
            message: message.into(),
        }
    }

    /// 创建表名重写错误
    pub fn table_rewrite<S1: Into<String>, S2: Into<String>>(table_name: S1, reason: S2) -> Self {
        Self::TableRewrite {
            table_name: table_name.into(),
            reason: reason.into(),
        }
    }

    /// 创建分表管理错误
    pub fn table_management<S1: Into<String>, S2: Into<String>>(
        operation: S1,
        merchant_id: i64,
        reason: S2,
    ) -> Self {
        Self::TableManagement {
            operation: operation.into(),
            merchant_id,
            reason: reason.into(),
        }
    }

    /// 创建不支持的操作错误
    pub fn unsupported<S: Into<String>>(operation: S) -> Self {
        Self::UnsupportedOperation {
            operation: operation.into(),
        }
    }

    /// 创建内部错误
    pub fn internal<S: Into<String>>(message: S) -> Self {
        Self::Internal {
            message: message.into(),
        }
    }

    /// 创建验证错误
    pub fn validation<S: Into<String>>(message: S) -> Self {
        Self::Validation {
            message: message.into(),
        }
    }

    /// 创建无效商户ID错误
    pub fn invalid_merchant_id(merchant_id: i64) -> Self {
        Self::InvalidMerchantId { merchant_id }
    }
}

/// 分表库统一Result类型
pub type Result<T> = std::result::Result<T, ShardingError>;

/// 配置验证错误
#[derive(Debug, Clone)]
pub struct ValidationError {
    pub field: String,
    pub message: String,
}

impl ValidationError {
    pub fn new<S1: Into<String>, S2: Into<String>>(field: S1, message: S2) -> Self {
        Self {
            field: field.into(),
            message: message.into(),
        }
    }
}

impl fmt::Display for ValidationError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "字段 '{}' 验证失败: {}", self.field, self.message)
    }
}

impl std::error::Error for ValidationError {}

/// 配置验证结果
pub type ValidationResult = std::result::Result<(), Vec<ValidationError>>;

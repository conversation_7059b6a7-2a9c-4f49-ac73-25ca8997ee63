//! 分表配置系统
//!
//! 提供分表相关的配置结构体和验证器，支持从配置文件自动加载。

use crate::error::{ValidationError, ValidationResult};
use crate::DEFAULT_SCHEMA_NAME;
use lib_macros::Configurable;
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};

/// 分表命名策略
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize, JsonSchema, Default)]
#[serde(rename_all = "snake_case")]
pub enum NamingStrategy {
    /// 商户ID后缀：orders -> orders_merchant_123
    #[default]
    MerchantSuffix,
    /// 商户ID前缀：orders -> merchant_123_orders
    MerchantPrefix,
    /// 哈希后缀：orders -> orders_001 (基于merchant_id哈希)
    HashSuffix,
    /// 独立Schema：orders -> merchant_123.orders
    MerchantSchema,
}


impl std::fmt::Display for NamingStrategy {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::MerchantSuffix => write!(f, "merchant_suffix"),
            Self::MerchantPrefix => write!(f, "merchant_prefix"),
            Self::HashSuffix => write!(f, "hash_suffix"),
            Self::MerchantSchema => write!(f, "merchant_schema"),
        }
    }
}

impl std::str::FromStr for NamingStrategy {
    type Err = String;

    fn from_str(s: &str) -> std::result::Result<Self, Self::Err> {
        match s {
            "merchant_suffix" => Ok(Self::MerchantSuffix),
            "merchant_prefix" => Ok(Self::MerchantPrefix),
            "hash_suffix" => Ok(Self::HashSuffix),
            "merchant_schema" => Ok(Self::MerchantSchema),
            _ => Err(format!(
                "不支持的命名策略: '{}'，支持的策略: merchant_suffix, merchant_prefix, hash_suffix, merchant_schema",
                s
            )),
        }
    }
}

/// 默认分表命名策略
pub const DEFAULT_NAMING_STRATEGY: NamingStrategy = NamingStrategy::MerchantSuffix;

/// 分表配置
///
/// 定义分表相关的所有配置参数，支持从配置文件自动加载。
///
/// # 配置示例
///
/// ```toml
/// [sharding]
/// enabled = true
/// naming_strategy = "merchant_suffix"
/// schema_name = "business"
/// shardable_tables = ["orders", "customers", "products"]
/// cache_enabled = true
/// cache_size = 10000
/// cache_ttl_seconds = 3600
/// ```
#[derive(Debug, Configurable, Clone, JsonSchema, Deserialize, Serialize, PartialEq)]
#[config_prefix = "sharding"]
pub struct ShardingConfig {
    /// 需要分表的表名列表
    /// 只有在此列表中的表才会被自动分表
    #[serde(default = "default_shardable_tables")]
    pub shardable_tables: Vec<String>,

    /// 分表命名策略
    /// 支持的策略：
    /// - MerchantSuffix: orders -> orders_merchant_123
    /// - MerchantPrefix: orders -> merchant_123_orders
    /// - HashSuffix: orders -> orders_001 (基于商户ID哈希)
    #[serde(default = "default_naming_strategy")]
    pub naming_strategy: NamingStrategy,

    /// 数据库模式名称
    /// 分表将在此模式下创建
    #[serde(default = "default_schema_name")]
    pub schema_name: String,

    /// 是否启用缓存
    /// 启用后会缓存SQL重写结果，提高性能
    #[serde(default = "default_cache_enabled")]
    pub cache_enabled: bool,

    /// 缓存大小限制
    /// 最大缓存条目数量
    #[serde(default = "default_cache_size")]
    pub cache_size: usize,

    /// 缓存过期时间（秒）
    /// 缓存条目的生存时间
    #[serde(default = "default_cache_ttl_seconds")]
    pub cache_ttl_seconds: u64,

    /// 是否启用SQL重写日志
    /// 启用后会记录SQL重写的详细过程
    #[serde(default = "default_log_sql_rewrite")]
    pub log_sql_rewrite: bool,

    /// SQL重写超时时间（毫秒）
    /// 超过此时间的重写操作将被中断
    #[serde(default = "default_rewrite_timeout_ms")]
    pub rewrite_timeout_ms: u64,

    /// 自定义DDL模板映射
    /// 表名 -> DDL模板，用于自定义分表的DDL语句
    #[serde(default = "default_custom_ddl_templates")]
    pub custom_ddl_templates: HashMap<String, String>,
}

impl Default for ShardingConfig {
    fn default() -> Self {
        Self {
            shardable_tables: default_shardable_tables(),
            naming_strategy: default_naming_strategy(),
            schema_name: default_schema_name(),
            cache_enabled: default_cache_enabled(),
            cache_size: default_cache_size(),
            cache_ttl_seconds: default_cache_ttl_seconds(),
            log_sql_rewrite: default_log_sql_rewrite(),
            rewrite_timeout_ms: default_rewrite_timeout_ms(),
            custom_ddl_templates: default_custom_ddl_templates(),
        }
    }
}

impl ShardingConfig {
    /// 创建新的配置实例
    pub fn new() -> Self {
        Self::default()
    }

    /// 创建用于测试的配置
    pub fn for_test() -> Self {
        Self {
            shardable_tables: vec!["merchant_categories".to_string(), "test_orders".to_string()],
            naming_strategy: NamingStrategy::MerchantSchema,
            schema_name: "public".to_string(),
            cache_enabled: false,
            cache_size: 100,
            cache_ttl_seconds: 60,
            log_sql_rewrite: true,
            rewrite_timeout_ms: 1000,
            custom_ddl_templates: HashMap::new(),
        }
    }

    /// 检查表是否可分表
    pub fn is_shardable_table(&self, table_name: &str) -> bool {
        self.shardable_tables.contains(&table_name.to_string())
    }
}

/// 配置验证器
///
/// 负责验证分表配置的正确性和一致性。
#[derive(Debug, Clone)]
pub struct ConfigValidator {
    supported_naming_strategies: HashSet<NamingStrategy>,
}

impl ConfigValidator {
    /// 创建新的配置验证器
    pub fn new() -> Self {
        let mut supported_strategies = HashSet::new();
        supported_strategies.insert(NamingStrategy::MerchantSuffix);
        supported_strategies.insert(NamingStrategy::MerchantPrefix);
        supported_strategies.insert(NamingStrategy::HashSuffix);
        supported_strategies.insert(NamingStrategy::MerchantSchema);

        Self {
            supported_naming_strategies: supported_strategies,
        }
    }

    /// 验证分表配置
    pub fn validate(&self, config: &ShardingConfig) -> ValidationResult {
        let mut errors = Vec::new();

        // 验证分表表名配置
        if let Err(mut table_errors) = self.validate_shardable_tables(&config.shardable_tables) {
            errors.append(&mut table_errors);
        }

        // 验证命名策略
        if let Err(strategy_error) = self.validate_naming_strategy(&config.naming_strategy) {
            errors.push(strategy_error);
        }

        // 验证模式名称
        if let Err(schema_error) = self.validate_schema_name(&config.schema_name) {
            errors.push(schema_error);
        }

        // 验证缓存配置
        if let Err(mut cache_errors) = self.validate_cache_config(config) {
            errors.append(&mut cache_errors);
        }

        // 验证超时配置
        if let Err(timeout_error) = self.validate_timeout_config(config.rewrite_timeout_ms) {
            errors.push(timeout_error);
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }

    /// 验证分表表名列表
    fn validate_shardable_tables(&self, tables: &[String]) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if tables.is_empty() {
            errors.push(ValidationError::new(
                "shardable_tables",
                "分表表名列表不能为空",
            ));
            return Err(errors);
        }

        // 检查表名格式
        for table in tables {
            if table.trim().is_empty() {
                errors.push(ValidationError::new(
                    "shardable_tables",
                    "表名不能为空或只包含空白字符",
                ));
                continue;
            }

            if !is_valid_table_name(table) {
                errors.push(ValidationError::new(
                    "shardable_tables",
                    format!("无效的表名格式: '{}'", table),
                ));
            }
        }

        // 检查重复表名
        let mut unique_tables = HashSet::new();
        for table in tables {
            if !unique_tables.insert(table) {
                errors.push(ValidationError::new(
                    "shardable_tables",
                    format!("重复的表名: '{}'", table),
                ));
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }

    /// 验证命名策略
    fn validate_naming_strategy(&self, strategy: &NamingStrategy) -> Result<(), ValidationError> {
        if !self.supported_naming_strategies.contains(strategy) {
            return Err(ValidationError::new(
                "naming_strategy",
                format!(
                    "不支持的命名策略: '{}'，支持的策略: {:?}",
                    strategy,
                    self.supported_naming_strategies.iter().collect::<Vec<_>>()
                ),
            ));
        }

        Ok(())
    }

    /// 验证模式名称
    fn validate_schema_name(&self, schema: &str) -> Result<(), ValidationError> {
        if schema.trim().is_empty() {
            return Err(ValidationError::new("schema_name", "模式名称不能为空"));
        }

        if !is_valid_schema_name(schema) {
            return Err(ValidationError::new(
                "schema_name",
                format!("无效的模式名称格式: '{}'", schema),
            ));
        }

        Ok(())
    }

    /// 验证缓存配置
    fn validate_cache_config(&self, config: &ShardingConfig) -> Result<(), Vec<ValidationError>> {
        let mut errors = Vec::new();

        if config.cache_enabled {
            if config.cache_size == 0 {
                errors.push(ValidationError::new(
                    "cache_size",
                    "启用缓存时，缓存大小必须大于0",
                ));
            }

            if config.cache_size > 1_000_000 {
                errors.push(ValidationError::new(
                    "cache_size",
                    "缓存大小不能超过1,000,000条",
                ));
            }

            if config.cache_ttl_seconds == 0 {
                errors.push(ValidationError::new(
                    "cache_ttl_seconds",
                    "启用缓存时，缓存过期时间必须大于0",
                ));
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }

    /// 验证超时配置
    fn validate_timeout_config(&self, timeout_ms: u64) -> Result<(), ValidationError> {
        if timeout_ms == 0 {
            return Err(ValidationError::new(
                "rewrite_timeout_ms",
                "SQL重写超时时间必须大于0",
            ));
        }

        if timeout_ms > 60_000 {
            return Err(ValidationError::new(
                "rewrite_timeout_ms",
                "SQL重写超时时间不能超过60秒",
            ));
        }

        Ok(())
    }

}

impl Default for ConfigValidator {
    fn default() -> Self {
        Self::new()
    }
}

/// 检查表名是否有效
fn is_valid_table_name(name: &str) -> bool {
    if name.is_empty() || name.len() > 63 {
        return false;
    }

    // 表名必须以字母或下划线开头
    let first_char = name.chars().next().unwrap();
    if !first_char.is_ascii_alphabetic() && first_char != '_' {
        return false;
    }

    // 表名只能包含字母、数字和下划线
    name.chars().all(|c| c.is_ascii_alphanumeric() || c == '_')
}

/// 检查模式名称是否有效
fn is_valid_schema_name(name: &str) -> bool {
    is_valid_table_name(name)
}

// 默认值函数
fn default_shardable_tables() -> Vec<String> {
    vec![
        "orders".to_string(),
        "customers".to_string(),
        "products".to_string(),
        "invoices".to_string(),
        "invoice_items".to_string(),
        "payments".to_string(),
    ]
}

fn default_naming_strategy() -> NamingStrategy {
    DEFAULT_NAMING_STRATEGY
}

fn default_schema_name() -> String {
    DEFAULT_SCHEMA_NAME.to_string()
}

fn default_cache_enabled() -> bool {
    true
}

fn default_cache_size() -> usize {
    10_000
}

fn default_cache_ttl_seconds() -> u64 {
    3600 // 1小时
}

fn default_log_sql_rewrite() -> bool {
    false
}

fn default_rewrite_timeout_ms() -> u64 {
    5000 // 5秒
}

fn default_custom_ddl_templates() -> HashMap<String, String> {
    HashMap::new()
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = ShardingConfig::default();
        assert!(!config.shardable_tables.is_empty());
        assert_eq!(config.naming_strategy, DEFAULT_NAMING_STRATEGY);
        assert_eq!(config.schema_name, DEFAULT_SCHEMA_NAME);
        assert!(config.cache_enabled);
    }

    #[test]
    fn test_for_test_config() {
        let config = ShardingConfig::for_test();
        assert_eq!(config.shardable_tables.len(), 2);
        assert_eq!(config.schema_name, "public");
        assert_eq!(config.naming_strategy, NamingStrategy::MerchantSchema);
        assert!(!config.cache_enabled);
        assert!(config.log_sql_rewrite);
    }

    #[test]
    fn test_is_shardable_table() {
        let config = ShardingConfig::default();
        assert!(config.is_shardable_table("orders"));
        assert!(config.is_shardable_table("customers"));
        assert!(!config.is_shardable_table("non_sharded_table"));
    }



    #[test]
    fn test_validator_shardable_tables() {
        let validator = ConfigValidator::new();

        // 测试空列表
        let result = validator.validate_shardable_tables(&[]);
        assert!(result.is_err());

        // 测试有效表名
        let result =
            validator.validate_shardable_tables(&["orders".to_string(), "customers".to_string()]);
        assert!(result.is_ok());

        // 测试无效表名
        let result = validator.validate_shardable_tables(&["123invalid".to_string()]);
        assert!(result.is_err());

        // 测试重复表名
        let result =
            validator.validate_shardable_tables(&["orders".to_string(), "orders".to_string()]);
        assert!(result.is_err());
    }

    #[test]
    fn test_validator_naming_strategy() {
        let validator = ConfigValidator::new();

        // 测试有效策略
        assert!(
            validator
                .validate_naming_strategy(&NamingStrategy::MerchantSuffix)
                .is_ok()
        );
        assert!(
            validator
                .validate_naming_strategy(&NamingStrategy::MerchantPrefix)
                .is_ok()
        );
        assert!(
            validator
                .validate_naming_strategy(&NamingStrategy::HashSuffix)
                .is_ok()
        );
    }

    #[test]
    fn test_validator_schema_name() {
        let validator = ConfigValidator::new();

        // 测试有效模式名
        assert!(validator.validate_schema_name("business").is_ok());
        assert!(validator.validate_schema_name("test_schema").is_ok());

        // 测试无效模式名
        assert!(validator.validate_schema_name("").is_err());
        assert!(validator.validate_schema_name("123invalid").is_err());
    }

    #[test]
    fn test_validator_cache_config() {
        let validator = ConfigValidator::new();

        // 测试有效缓存配置
        let config = ShardingConfig {
            cache_enabled: true,
            cache_size: 1000,
            cache_ttl_seconds: 3600,
            ..Default::default()
        };
        assert!(validator.validate_cache_config(&config).is_ok());

        // 测试禁用缓存的配置
        let config = ShardingConfig {
            cache_enabled: false,
            cache_size: 0,
            cache_ttl_seconds: 0,
            ..Default::default()
        };
        assert!(validator.validate_cache_config(&config).is_ok());

        // 测试无效缓存配置
        let config = ShardingConfig {
            cache_enabled: true,
            cache_size: 0, // 启用缓存但大小为0
            cache_ttl_seconds: 3600,
            ..Default::default()
        };
        assert!(validator.validate_cache_config(&config).is_err());
    }

    #[test]
    fn test_validator_timeout_config() {
        let validator = ConfigValidator::new();

        // 测试有效超时配置
        assert!(validator.validate_timeout_config(5000).is_ok());
        assert!(validator.validate_timeout_config(1000).is_ok());

        // 测试无效超时配置
        assert!(validator.validate_timeout_config(0).is_err());
        assert!(validator.validate_timeout_config(70_000).is_err());
    }

    #[test]
    fn test_table_name_validation() {
        // 测试有效表名
        assert!(is_valid_table_name("orders"));
        assert!(is_valid_table_name("customer_orders"));
        assert!(is_valid_table_name("_private_table"));

        // 测试无效表名
        assert!(!is_valid_table_name(""));
        assert!(!is_valid_table_name("123orders"));
        assert!(!is_valid_table_name("orders-invalid"));
        assert!(!is_valid_table_name("orders table"));
    }

    #[test]
    fn test_schema_name_validation() {
        // 测试有效模式名
        assert!(is_valid_schema_name("business"));
        assert!(is_valid_schema_name("test_schema"));

        // 测试无效模式名
        assert!(!is_valid_schema_name(""));
        assert!(!is_valid_schema_name("123schema"));
        assert!(!is_valid_schema_name("schema-invalid"));
    }

    #[test]
    fn test_config_serialization() {
        let config = ShardingConfig::for_test();

        // 测试序列化
        let json = serde_json::to_string(&config).unwrap();
        assert!(!json.is_empty());

        // 测试反序列化
        let deserialized: ShardingConfig = serde_json::from_str(&json).unwrap();
        assert_eq!(config, deserialized);
    }

    #[test]
    fn test_naming_strategy_default() {
        let default_strategy = NamingStrategy::default();
        assert_eq!(default_strategy, NamingStrategy::MerchantSuffix);
        assert_eq!(DEFAULT_NAMING_STRATEGY, NamingStrategy::MerchantSuffix);
    }

    #[test]
    fn test_naming_strategy_display() {
        assert_eq!(
            NamingStrategy::MerchantSuffix.to_string(),
            "merchant_suffix"
        );
        assert_eq!(
            NamingStrategy::MerchantPrefix.to_string(),
            "merchant_prefix"
        );
        assert_eq!(NamingStrategy::HashSuffix.to_string(), "hash_suffix");
    }

    #[test]
    fn test_naming_strategy_from_str() {
        use std::str::FromStr;

        // 测试有效的字符串解析
        assert_eq!(
            NamingStrategy::from_str("merchant_suffix").unwrap(),
            NamingStrategy::MerchantSuffix
        );
        assert_eq!(
            NamingStrategy::from_str("merchant_prefix").unwrap(),
            NamingStrategy::MerchantPrefix
        );
        assert_eq!(
            NamingStrategy::from_str("hash_suffix").unwrap(),
            NamingStrategy::HashSuffix
        );

        // 测试无效的字符串解析
        assert!(NamingStrategy::from_str("invalid_strategy").is_err());
        assert!(NamingStrategy::from_str("").is_err());
        assert!(NamingStrategy::from_str("MERCHANT_SUFFIX").is_err());
    }

    #[test]
    fn test_naming_strategy_serialization() {
        // 测试序列化
        let strategy = NamingStrategy::MerchantSuffix;
        let json = serde_json::to_string(&strategy).unwrap();
        assert_eq!(json, "\"merchant_suffix\"");

        let strategy = NamingStrategy::MerchantPrefix;
        let json = serde_json::to_string(&strategy).unwrap();
        assert_eq!(json, "\"merchant_prefix\"");

        let strategy = NamingStrategy::HashSuffix;
        let json = serde_json::to_string(&strategy).unwrap();
        assert_eq!(json, "\"hash_suffix\"");

        // 测试反序列化
        let strategy: NamingStrategy = serde_json::from_str("\"merchant_suffix\"").unwrap();
        assert_eq!(strategy, NamingStrategy::MerchantSuffix);

        let strategy: NamingStrategy = serde_json::from_str("\"merchant_prefix\"").unwrap();
        assert_eq!(strategy, NamingStrategy::MerchantPrefix);

        let strategy: NamingStrategy = serde_json::from_str("\"hash_suffix\"").unwrap();
        assert_eq!(strategy, NamingStrategy::HashSuffix);

        // 测试无效的反序列化
        assert!(serde_json::from_str::<NamingStrategy>("\"invalid\"").is_err());
    }

    #[test]
    fn test_naming_strategy_equality_and_hash() {
        let strategy1 = NamingStrategy::MerchantSuffix;
        let strategy2 = NamingStrategy::MerchantSuffix;
        let strategy3 = NamingStrategy::MerchantPrefix;

        // 测试相等性
        assert_eq!(strategy1, strategy2);
        assert_ne!(strategy1, strategy3);

        // 测试可以用于HashSet
        use std::collections::HashSet;
        let mut set = HashSet::new();
        set.insert(strategy1);
        set.insert(strategy2); // 应该不会增加元素，因为相等
        set.insert(strategy3);

        assert_eq!(set.len(), 2);
        assert!(set.contains(&NamingStrategy::MerchantSuffix));
        assert!(set.contains(&NamingStrategy::MerchantPrefix));
        assert!(!set.contains(&NamingStrategy::HashSuffix));
    }
}

//! DDL 模板系统模块
//!
//! 提供灵活的 DDL 模板管理功能，支持变量替换和内置表模板。

use crate::error::{Result, ShardingError};
use rust_embed::RustEmbed;
use serde::{Deserialize, Serialize};
use sqlparser::dialect::PostgreSqlDialect;
use sqlparser::parser::Parser;
use std::collections::HashMap;
use tracing::{debug, warn};

/// 嵌入所有 SQL 模板文件
///
/// 自动发现并嵌入 templates/ 目录下的所有 .sql 文件
#[derive(RustEmbed)]
#[folder = "templates/"]
#[include = "*.sql"]
struct SqlTemplates;

/// DDL SQL 语法校验器
///
/// 用于验证生成的 DDL 语句是否语法正确
#[derive(Debug)]
pub struct DdlValidator {
    /// PostgreSQL 方言解析器
    dialect: PostgreSqlDialect,
    /// 是否启用严格模式
    strict_mode: bool,
}

impl DdlValidator {
    /// 创建新的 DDL 校验器
    pub fn new() -> Self {
        Self {
            dialect: PostgreSqlDialect {},
            strict_mode: true,
        }
    }

    /// 创建非严格模式的校验器
    pub fn new_lenient() -> Self {
        Self {
            dialect: PostgreSqlDialect {},
            strict_mode: false,
        }
    }

    /// 验证 DDL 语句语法
    ///
    /// # 参数
    /// - `ddl`: DDL 语句
    ///
    /// # 返回
    /// 验证成功返回 `Ok(())`，失败返回错误信息
    pub fn validate_ddl(&self, ddl: &str) -> Result<()> {
        if ddl.trim().is_empty() {
            return Err(ShardingError::validation("DDL 语句不能为空"));
        }

        // 尝试解析 DDL 语句
        match Parser::parse_sql(&self.dialect, ddl) {
            Ok(statements) => {
                if statements.is_empty() {
                    return Err(ShardingError::validation("未解析到有效的 DDL 语句"));
                }

                // 验证每个语句
                for (i, statement) in statements.iter().enumerate() {
                    self.validate_statement(statement, i)?;
                }

                debug!("DDL 语法验证通过 - 语句数量: {}", statements.len());
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("DDL 语法错误: {}", e);
                warn!("{}", error_msg);
                Err(ShardingError::validation(error_msg))
            }
        }
    }

    /// 验证单个语句
    fn validate_statement(
        &self,
        statement: &sqlparser::ast::Statement,
        index: usize,
    ) -> Result<()> {
        use sqlparser::ast::Statement;

        match statement {
            Statement::CreateTable { .. } => {
                debug!("验证 CREATE TABLE 语句 - 索引: {}", index);
                self.validate_create_table_statement(statement)
            }
            Statement::CreateIndex { .. } => {
                debug!("验证 CREATE INDEX 语句 - 索引: {}", index);
                Ok(())
            }
            Statement::AlterTable { .. } => {
                debug!("验证 ALTER TABLE 语句 - 索引: {}", index);
                Ok(())
            }
            _ => {
                if self.strict_mode {
                    let error_msg = format!("不支持的语句类型: {:?}", statement);
                    Err(ShardingError::validation(error_msg))
                } else {
                    debug!("跳过验证非 DDL 语句 - 索引: {}", index);
                    Ok(())
                }
            }
        }
    }

    /// 验证 CREATE TABLE 语句
    fn validate_create_table_statement(&self, statement: &sqlparser::ast::Statement) -> Result<()> {
        use sqlparser::ast::Statement;

        if let Statement::CreateTable(create_table) = statement {
            // 验证表名
            if create_table.name.0.is_empty() {
                return Err(ShardingError::validation("表名不能为空"));
            }

            // 验证列定义
            if create_table.columns.is_empty() {
                return Err(ShardingError::validation("表必须至少有一列"));
            }

            // 验证列名不重复
            let mut column_names = std::collections::HashSet::new();
            for column in &create_table.columns {
                let column_name = &column.name.value;
                if column_names.contains(column_name) {
                    return Err(ShardingError::validation(format!(
                        "重复的列名: {}",
                        column_name
                    )));
                }
                column_names.insert(column_name.clone());
            }

            debug!(
                "CREATE TABLE 验证通过 - 表名: {:?}, 列数: {}",
                create_table.name,
                create_table.columns.len()
            );
        }

        Ok(())
    }

    /// 验证并提供详细的错误信息
    ///
    /// # 参数
    /// - `ddl`: DDL 语句
    ///
    /// # 返回
    /// 返回验证结果和详细信息
    pub fn validate_with_details(&self, ddl: &str) -> (Result<()>, ValidationDetails) {
        let start_time = std::time::Instant::now();
        let result = self.validate_ddl(ddl);
        let duration = start_time.elapsed();

        let details = ValidationDetails {
            ddl_length: ddl.len(),
            validation_time: duration,
            statement_count: self.count_statements(ddl),
            is_valid: result.is_ok(),
            error_message: result.as_ref().err().map(|e| e.to_string()),
        };

        (result, details)
    }

    /// 计算语句数量
    fn count_statements(&self, ddl: &str) -> usize {
        match Parser::parse_sql(&self.dialect, ddl) {
            Ok(statements) => statements.len(),
            Err(_) => 0,
        }
    }
}

impl Default for DdlValidator {
    fn default() -> Self {
        Self::new()
    }
}

impl Clone for DdlValidator {
    fn clone(&self) -> Self {
        DdlValidator {
            dialect: PostgreSqlDialect {},
            strict_mode: self.strict_mode,
        }
    }
}

/// DDL 验证详细信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationDetails {
    /// DDL 语句长度
    pub ddl_length: usize,
    /// 验证耗时
    pub validation_time: std::time::Duration,
    /// 语句数量
    pub statement_count: usize,
    /// 是否有效
    pub is_valid: bool,
    /// 错误信息
    pub error_message: Option<String>,
}

/// 🆕 模板验证汇总信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationSummary {
    /// 有效的模板列表
    pub valid_templates: Vec<String>,
    /// 无效的模板错误列表
    pub invalid_templates: Vec<TemplateError>,
}

/// 🆕 模板错误信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateError {
    /// 模板名称
    pub name: String,
    /// 文件路径
    pub path: String,
    /// 错误信息
    pub error: String,
}

/// DDL 模板变量替换器
///
/// 支持以下模板变量：
/// - `{table_name}`: 分表名称
/// - `{merchant_id}`: 商户ID
/// - `{schema}`: 模式名称
/// - `{base_table}`: 基础表名
/// - `{timestamp}`: 当前时间戳
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateRenderer {
    variables: HashMap<String, String>,
}

impl TemplateRenderer {
    /// 创建新的模板渲染器
    pub fn new() -> Self {
        Self {
            variables: HashMap::new(),
        }
    }

    /// 设置变量值
    pub fn set_variable(&mut self, key: &str, value: &str) -> &mut Self {
        self.variables.insert(key.to_string(), value.to_string());
        self
    }

    /// 批量设置变量
    pub fn set_variables(&mut self, vars: HashMap<String, String>) -> &mut Self {
        for (key, value) in vars {
            self.variables.insert(key, value);
        }
        self
    }

    /// 渲染模板字符串
    ///
    /// # 参数
    /// - `template`: 模板字符串
    ///
    /// # 返回
    /// 渲染后的字符串
    pub fn render(&self, template: &str) -> String {
        let mut result = template.to_string();

        for (key, value) in &self.variables {
            let placeholder = format!("{{{}}}", key);
            result = result.replace(&placeholder, value);
        }

        debug!("模板渲染完成 - 变量数量: {}", self.variables.len());
        result
    }

    /// 为分表创建设置标准变量
    ///
    /// # 参数
    /// - `table_name`: 分表名称
    /// - `merchant_id`: 商户ID  
    /// - `schema`: 模式名称
    /// - `base_table`: 基础表名
    pub fn set_shard_variables(
        &mut self,
        table_name: &str,
        merchant_id: i64,
        schema: &str,
        base_table: &str,
    ) -> &mut Self {
        // 对于约束名称，需要将点号替换为下划线
        let constraint_safe_table_name = table_name.replace('.', "_");
        
        self.set_variable("table_name", &constraint_safe_table_name)
            .set_variable("merchant_id", &merchant_id.to_string())
            .set_variable("schema", schema)
            .set_variable("base_table", base_table)
            .set_variable("timestamp", &chrono::Utc::now().timestamp().to_string())
    }
}

impl Default for TemplateRenderer {
    fn default() -> Self {
        Self::new()
    }
}

/// DDL 模板管理器
///
/// 负责管理和组织各种表的 DDL 模板，支持语法验证
#[derive(Debug)]
pub struct TemplateManager {
    /// 自定义模板映射
    custom_templates: HashMap<String, String>,
    /// 内置模板映射
    builtin_templates: HashMap<String, String>,
    /// 模板渲染器
    renderer: TemplateRenderer,
    /// DDL 语法校验器
    validator: DdlValidator,
}

impl TemplateManager {
    /// 创建新的模板管理器
    pub fn new() -> Self {
        let mut manager = Self {
            custom_templates: HashMap::new(),
            builtin_templates: HashMap::new(),
            renderer: TemplateRenderer::new(),
            validator: DdlValidator::new(),
        };

        // 初始化内置模板
        manager.load_builtin_templates();
        manager
    }

    /// 使用自定义模板创建管理器
    pub fn with_custom_templates(custom_templates: HashMap<String, String>) -> Self {
        let mut manager = Self {
            custom_templates,
            builtin_templates: HashMap::new(),
            renderer: TemplateRenderer::new(),
            validator: DdlValidator::new(),
        };

        // 初始化内置模板
        manager.load_builtin_templates();
        manager
    }

    /// 使用指定的校验器创建管理器
    pub fn with_validator(validator: DdlValidator) -> Self {
        let mut manager = Self {
            custom_templates: HashMap::new(),
            builtin_templates: HashMap::new(),
            renderer: TemplateRenderer::new(),
            validator,
        };

        // 初始化内置模板
        manager.load_builtin_templates();
        manager
    }

    /// 加载内置表模板
    /// 
    /// 使用 rust-embed 自动发现并加载所有 templates/ 目录下的 .sql 文件
    fn load_builtin_templates(&mut self) {
        debug!("开始自动加载内置 DDL 模板");

        let mut loaded_count = 0;

        // 自动遍历所有嵌入的 SQL 文件
        for file_path in SqlTemplates::iter() {
            if let Some(content) = SqlTemplates::get(&file_path) {
                // 提取模板名称
                let template_name = self.extract_template_name(&file_path);
                
                // 转换为字符串
                match std::str::from_utf8(&content.data) {
                    Ok(sql_content) => {
                        self.builtin_templates.insert(
                            template_name.clone(),
                            sql_content.to_string(),
                        );
                        loaded_count += 1;
                        debug!("自动加载模板: {} <- {}", template_name, file_path);
                    }
                    Err(e) => {
                        warn!("模板文件编码错误: {} - {}", file_path, e);
                    }
                }
            }
        }

        debug!(
            "内置 DDL 模板自动加载完成 - 模板数量: {}",
            loaded_count
        );
    }

    /// 从文件路径提取模板名称
    /// 
    /// # 示例
    /// - `business/orders.sql` -> `orders`
    /// - `test/test_orders.sql` -> `test_orders`
    /// - `demo/demo_transactions.sql` -> `demo_transactions`
    fn extract_template_name(&self, file_path: &str) -> String {
        file_path
            .strip_suffix(".sql")
            .unwrap_or(file_path)
            .split('/')
            .last()
            .unwrap_or("unknown")
            .to_string()
    }

    /// 添加自定义模板
    pub fn add_template(&mut self, table_name: String, template: String) {
        self.custom_templates.insert(table_name.clone(), template);
        debug!("添加自定义模板 - 表名: {}", table_name);
    }

    /// 移除自定义模板
    pub fn remove_template(&mut self, table_name: &str) -> Option<String> {
        let removed = self.custom_templates.remove(table_name);
        if removed.is_some() {
            debug!("移除自定义模板 - 表名: {}", table_name);
        }
        removed
    }

    /// 获取模板（优先使用自定义模板）
    pub fn get_template(&self, table_name: &str) -> Option<&String> {
        self.custom_templates
            .get(table_name)
            .or_else(|| self.builtin_templates.get(table_name))
    }

    /// 检查是否有指定表的模板
    pub fn has_template(&self, table_name: &str) -> bool {
        self.custom_templates.contains_key(table_name)
            || self.builtin_templates.contains_key(table_name)
    }

    /// 获取所有可用模板名称
    pub fn get_available_templates(&self) -> Vec<String> {
        let mut templates = Vec::new();

        // 添加自定义模板
        templates.extend(self.custom_templates.keys().cloned());

        // 添加内置模板（如果不在自定义模板中）
        for key in self.builtin_templates.keys() {
            if !self.custom_templates.contains_key(key) {
                templates.push(key.clone());
            }
        }

        templates.sort();
        templates
    }

    /// 生成 DDL 语句
    ///
    /// # 参数
    /// - `base_table_name`: 基础表名
    /// - `shard_table_name`: 分表名称
    /// - `merchant_id`: 商户ID
    /// - `schema`: 模式名称
    ///
    /// # 返回
    /// 生成的 DDL 语句
    pub fn generate_ddl(
        &mut self,
        base_table_name: &str,
        shard_table_name: &str,
        merchant_id: i64,
        schema: &str,
    ) -> String {
        if let Some(template) = self.get_template(base_table_name).cloned() {
            // 使用模板渲染
            self.renderer.set_shard_variables(
                shard_table_name,
                merchant_id,
                schema,
                base_table_name,
            );

            let rendered = self.renderer.render(&template);
            debug!(
                "使用模板生成 DDL - 表: {}, 商户: {}",
                base_table_name, merchant_id
            );
            rendered
        } else {
            // 使用默认的 LIKE 语句
            let default_ddl = format!(
                "CREATE TABLE {}.{} (LIKE {}.{} INCLUDING ALL)",
                schema, shard_table_name, schema, base_table_name
            );
            debug!(
                "使用默认 LIKE 语句生成 DDL - 表: {}, 商户: {}",
                base_table_name, merchant_id
            );
            default_ddl
        }
    }

    /// 生成并验证 DDL 语句
    ///
    /// # 参数
    /// - `base_table_name`: 基础表名
    /// - `shard_table_name`: 分表名称
    /// - `merchant_id`: 商户ID
    /// - `schema`: 模式名称
    ///
    /// # 返回
    /// 返回验证过的 DDL 语句，如果语法错误则返回错误
    pub fn generate_validated_ddl(
        &mut self,
        base_table_name: &str,
        shard_table_name: &str,
        merchant_id: i64,
        schema: &str,
    ) -> Result<String> {
        let ddl = self.generate_ddl(base_table_name, shard_table_name, merchant_id, schema);

        // 验证生成的 DDL 语法
        self.validator.validate_ddl(&ddl)?;

        debug!(
            "DDL 语法验证通过 - 表: {}, 商户: {}, DDL 长度: {}",
            base_table_name, merchant_id, ddl.len()
        );

        Ok(ddl)
    }

    /// 生成 DDL 语句并返回详细的验证信息
    ///
    /// # 参数
    /// - `base_table_name`: 基础表名
    /// - `shard_table_name`: 分表名称
    /// - `merchant_id`: 商户ID
    /// - `schema`: 模式名称
    ///
    /// # 返回
    /// 返回 DDL 语句和验证详情
    pub fn generate_ddl_with_validation_details(
        &mut self,
        base_table_name: &str,
        shard_table_name: &str,
        merchant_id: i64,
        schema: &str,
    ) -> (String, ValidationDetails) {
        let ddl = self.generate_ddl(base_table_name, shard_table_name, merchant_id, schema);

        // 获取详细的验证信息
        let (validation_result, details) = self.validator.validate_with_details(&ddl);

        if let Err(e) = validation_result {
            warn!(
                "DDL 语法验证失败 - 表: {}, 商户: {}, 错误: {}",
                base_table_name, merchant_id, e
            );
        }

        (ddl, details)
    }

    /// 验证指定的 DDL 模板
    ///
    /// # 参数
    /// - `table_name`: 表名
    ///
    /// # 返回
    /// 验证结果
    pub fn validate_template(&mut self, table_name: &str) -> Result<()> {
        if let Some(template) = self.get_template(table_name).cloned() {
            // 使用示例参数渲染模板进行验证
            self.renderer.set_shard_variables(
                "test_table_merchant_999",
                999,
                "test_schema",
                table_name,
            );

            let rendered_ddl = self.renderer.render(&template);
            self.validator.validate_ddl(&rendered_ddl)?;

            debug!("模板验证通过 - 表名: {}", table_name);
            Ok(())
        } else {
            Err(ShardingError::validation(format!(
                "未找到表 '{}' 的模板",
                table_name
            )))
        }
    }

    /// 验证所有内置模板
    ///
    /// # 返回
    /// 验证结果，包含失败的模板列表
    pub fn validate_all_builtin_templates(&mut self) -> (Vec<String>, Vec<String>) {
        let mut valid_templates = Vec::new();
        let mut invalid_templates = Vec::new();

        for table_name in self.builtin_templates.keys().cloned().collect::<Vec<_>>() {
            match self.validate_template(&table_name) {
                Ok(_) => {
                    valid_templates.push(table_name);
                }
                Err(e) => {
                    warn!("内置模板验证失败 - 表名: {}, 错误: {}", table_name, e);
                    invalid_templates.push(table_name);
                }
            }
        }

        debug!(
            "内置模板验证完成 - 有效: {}, 无效: {}",
            valid_templates.len(),
            invalid_templates.len()
        );

        (valid_templates, invalid_templates)
    }

    /// 🆕 验证所有嵌入的模板文件
    /// 
    /// 直接从 rust-embed 嵌入的文件中验证，提供更详细的错误信息
    /// 
    /// # 返回
    /// 返回验证汇总信息
    pub fn validate_all_embedded_templates(&mut self) -> ValidationSummary {
        let mut summary = ValidationSummary {
            valid_templates: Vec::new(),
            invalid_templates: Vec::new(),
        };
        
        debug!("开始验证所有嵌入的模板文件");
        
        for file_path in SqlTemplates::iter() {
            if let Some(content) = SqlTemplates::get(&file_path) {
                if let Ok(sql_content) = std::str::from_utf8(&content.data) {
                    let template_name = self.extract_template_name(&file_path);
                    
                    // 设置测试变量进行验证
                    self.renderer.set_shard_variables(
                        "test_table_merchant_999",
                        999,
                        "test_schema",
                        &template_name,
                    );
                    
                    let rendered = self.renderer.render(sql_content);
                    match self.validator.validate_ddl(&rendered) {
                        Ok(_) => {
                            summary.valid_templates.push(template_name);
                            debug!("模板验证通过: {}", file_path);
                        }
                        Err(e) => {
                            let error = TemplateError {
                                name: template_name,
                                path: file_path.to_string(),
                                error: e.to_string(),
                            };
                            warn!("模板验证失败: {:?}", error);
                            summary.invalid_templates.push(error);
                        }
                    }
                }
            }
        }
        
        debug!(
            "嵌入模板验证完成 - 有效: {}, 无效: {}",
            summary.valid_templates.len(),
            summary.invalid_templates.len()
        );
        
        summary
    }

    /// 获取自定义模板数量
    pub fn custom_template_count(&self) -> usize {
        self.custom_templates.len()
    }

    /// 获取内置模板数量
    pub fn builtin_template_count(&self) -> usize {
        self.builtin_templates.len()
    }

    /// 获取总模板数量
    pub fn total_template_count(&self) -> usize {
        let mut total_keys = std::collections::HashSet::new();
        total_keys.extend(self.custom_templates.keys());
        total_keys.extend(self.builtin_templates.keys());
        total_keys.len()
    }

    /// 🆕 获取可用的模板分类
    /// 
    /// # 返回
    /// 返回所有模板分类的列表（business, test, demo 等）
    pub fn get_template_categories(&self) -> Vec<String> {
        let mut categories = std::collections::HashSet::new();
        
        // 从嵌入的文件中提取分类
        for file_path in SqlTemplates::iter() {
            if let Some(category) = file_path.split('/').next() {
                categories.insert(category.to_string());
            }
        }
        
        let mut result: Vec<String> = categories.into_iter().collect();
        result.sort();
        debug!("发现模板分类: {:?}", result);
        result
    }
    
    /// 🆕 按分类获取模板列表
    /// 
    /// # 参数
    /// - `category`: 分类名称（如 "business", "test", "demo"）
    /// 
    /// # 返回
    /// 该分类下的所有模板名称列表
    pub fn get_templates_by_category(&self, category: &str) -> Vec<String> {
        let prefix = format!("{}/", category);
        
        let templates: Vec<String> = SqlTemplates::iter()
            .filter(|path| path.starts_with(&prefix))
            .map(|path| self.extract_template_name(&path))
            .collect();
            
        debug!("分类 '{}' 下的模板: {:?}", category, templates);
        templates
    }
    
    /// 🆕 检查模板文件是否存在于嵌入资源中
    /// 
    /// # 参数
    /// - `file_path`: 文件路径（如 "business/orders.sql"）
    pub fn template_file_exists(&self, file_path: &str) -> bool {
        SqlTemplates::get(file_path).is_some()
    }
    
    /// 🆕 智能模板查找
    /// 
    /// 支持模糊匹配，优先精确匹配，然后尝试模糊匹配
    /// 
    /// # 参数
    /// - `partial_name`: 部分模板名称
    /// 
    pub fn find_template_smart(&self, partial_name: &str) -> Option<String> {
        // 首先尝试精确匹配
        if let Some(template) = self.get_template(partial_name) {
            return Some(template.clone());
        }
        
        // 然后尝试模糊匹配
        for file_path in SqlTemplates::iter() {
            let template_name = self.extract_template_name(&file_path);
            if template_name.contains(partial_name) {
                if let Some(content) = SqlTemplates::get(&file_path) {
                    if let Ok(sql_content) = std::str::from_utf8(&content.data) {
                        debug!("智能匹配找到模板: {} -> {}", partial_name, template_name);
                        return Some(sql_content.to_string());
                    }
                }
            }
        }
        
        debug!("智能匹配未找到模板: {}", partial_name);
        None
    }

    /// 🆕 为Schema策略生成DDL语句
    ///
    /// 与 `generate_ddl()` 的区别在于：
    /// - Schema策略：表名保持不变，使用独立的商户Schema
    /// - 表名策略：表名会被重写，使用共享Schema
    ///
    /// # 参数
    /// - `base_table_name`: 基础表名
    /// - `table_name`: 实际表名（Schema策略下与base_table_name相同）
    /// - `merchant_id`: 商户ID
    /// - `schema_name`: 商户专用Schema名称
    ///
    /// # 返回
    /// 生成的 DDL 语句
    pub fn generate_ddl_for_schema(
        &mut self,
        base_table_name: &str,
        table_name: &str,
        merchant_id: i64,
        schema_name: &str,
    ) -> String {
        if let Some(template) = self.get_template(base_table_name).cloned() {
            // 使用模板渲染 - Schema策略专用变量设置
            self.renderer.set_shard_variables(
                table_name,    // 表名保持不变
                merchant_id,
                schema_name,   // 使用商户专用Schema
                base_table_name,
            );

            let rendered = self.renderer.render(&template);
            debug!(
                "使用模板为Schema生成 DDL - 表: {}, 商户: {}, Schema: {}",
                base_table_name, merchant_id, schema_name
            );
            rendered
        } else {
            // 使用默认的 LIKE 语句 - Schema策略版本
            let default_ddl = format!(
                "CREATE TABLE {}.{} (LIKE public.{} INCLUDING ALL)",
                schema_name, table_name, base_table_name
            );
            debug!(
                "使用默认 LIKE 语句为Schema生成 DDL - 表: {}, 商户: {}, Schema: {}",
                base_table_name, merchant_id, schema_name
            );
            default_ddl
        }
    }
}

impl Default for TemplateManager {
    fn default() -> Self {
        Self::new()
    }
}

impl Clone for TemplateManager {
    fn clone(&self) -> Self {
        TemplateManager {
            custom_templates: self.custom_templates.clone(),
            builtin_templates: self.builtin_templates.clone(),
            renderer: self.renderer.clone(),
            validator: self.validator.clone(),
        }
    }
}

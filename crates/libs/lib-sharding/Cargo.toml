[package]
name = "lib-sharding"
version.workspace = true
edition.workspace = true
description = "A flexible database sharding library for multi-tenant applications"
authors = ["Invoice-Book Team"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/your-org/invoice-book"
keywords = ["database", "sharding", "multi-tenant", "sea-orm", "postgres"]
categories = ["database"]

[dependencies]
# Core framework
lib-core = { path = "../lib-core" }
lib-macros = { path = "../lib-macros" }
lib-auth = { path = "../lib-auth" }
rust_decimal = "1.37.1"
# SQL Parser
sqlparser = { version = "0.54.0", features = ["visitor", "sqlparser_derive"] }
#sqlparser = { version = "0.56.0", features = ["visitor", "sqlparser_derive"] }

# Database ORM
sea-orm = { workspace = true, features = [
    "sqlx-postgres",
    "runtime-tokio-rustls",
    "macros",
    "with-chrono",

] }

axum = { workspace = true }

# Async runtime and traits
tokio = { workspace = true, features = ["full"] }
async-trait = { workspace = true }

# Derive macros
derive_more = { version = "1.0", features = ["constructor", "from", "into"] }

# Regular expressions
regex = { version = "1.10" }
# Serialization and schema
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
schemars = { workspace = true }

# Template embedding
rust-embed = { version = "8.5", features = ["include-exclude"] }

# Error handling
anyhow = { workspace = true }
thiserror = { workspace = true }

# Logging
tracing = { workspace = true }

# Time handling
chrono = { workspace = true, features = ["serde"] }

# UUID support
uuid = { workspace = true, features = ["v4", "serde"] }

# Async utilities
futures = { version = "0.3" }

[dev-dependencies]
tokio-test = "0.4"
num_cpus = "1.16"
futures = "0.3"
criterion = { version = "0.5", features = ["html_reports"] }

[features]
default = ["postgres"]

# Database backends
postgres = []
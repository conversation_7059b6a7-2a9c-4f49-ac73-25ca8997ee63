CREATE TABLE {schema}.{table_name} (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    transaction_no VARCHAR(50) NOT NULL UNIQUE,
    transaction_type VARCHAR(20) NOT NULL,
    reference_id BIGINT,
    reference_type VARCHAR(50),
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'CNY',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    payment_method VARCHAR(50),
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT chk_{table_name}_amount CHECK (amount <> 0),
    CONSTRAINT chk_{table_name}_transaction_type CHECK (transaction_type IN ('payment', 'refund', 'adjustment', 'fee', 'commission')),
    CONSTRAINT chk_{table_name}_status CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled'))
) 
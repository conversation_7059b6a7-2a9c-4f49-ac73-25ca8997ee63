CREATE TABLE {schema}.{table_name} (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    name VARCHAR(200) NOT NULL,
    sku VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    cost_price DECIMAL(15,2) DEFAULT 0.00,
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT uk_{table_name}_merchant_sku UNIQUE (merchant_id, sku),
    CONSTRAINT chk_{table_name}_price CHECK (price >= 0),
    CONSTRAINT chk_{table_name}_cost_price CHECK (cost_price >= 0),
    CONSTRAINT chk_{table_name}_stock_quantity CHECK (stock_quantity >= 0),
    CONSTRAINT chk_{table_name}_status CHECK (status IN ('active', 'inactive', 'discontinued'))
) 
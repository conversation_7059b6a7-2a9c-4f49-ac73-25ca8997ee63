CREATE TABLE {schema}.{table_name} (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    invoice_no VARCHAR(50) NOT NULL UNIQUE,
    order_id BIGINT,
    customer_id BIGINT NOT NULL,
    customer_name VARCHAR(100) NOT NULL,
    invoice_date DATE NOT NULL DEFAULT CURRENT_DATE,
    due_date DATE,
    subtotal DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    paid_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    status VARCHAR(20) NOT NULL DEFAULT 'draft',
    payment_status VARCHAR(20) NOT NULL DEFAULT 'unpaid',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_{table_name}_subtotal CHECK (subtotal >= 0),
    CONSTRAINT chk_{table_name}_tax_amount CHECK (tax_amount >= 0),
    CONSTRAINT chk_{table_name}_total_amount CHECK (total_amount >= 0),
    CONSTRAINT chk_{table_name}_paid_amount CHECK (paid_amount >= 0),
    CONSTRAINT chk_{table_name}_status CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')),
    CONSTRAINT chk_{table_name}_payment_status CHECK (payment_status IN ('unpaid', 'partial', 'paid', 'overdue'))
) 
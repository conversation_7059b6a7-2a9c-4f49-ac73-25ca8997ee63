CREATE TABLE {schema}.{table_name} (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    order_no VARCHAR(50) NOT NULL,
    customer_id BIGINT,
    customer_name VARCHAR(100) NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    payment_status VARCHAR(20) NOT NULL DEFAULT 'unpaid',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    
    CONSTRAINT chk_{table_name}_total_amount CHECK (total_amount >= 0),
    CONSTRAINT chk_{table_name}_status CHECK (status IN ('pending', 'confirmed', 'shipped', 'completed', 'cancelled'))
) 
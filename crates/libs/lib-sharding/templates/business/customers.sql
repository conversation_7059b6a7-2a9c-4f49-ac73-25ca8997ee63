CREATE TABLE {schema}.{table_name} (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    customer_no VARCHAR(50) UNIQUE,
    name VARCHAR(100) NOT NULL,
    display_name VA<PERSON>HA<PERSON>(100),
    email VARCHAR(150),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    gender VARCHAR(10) CHECK (gender IN ('male', 'female', 'other', 'unknown')),
    birthday DATE,
    avatar_url VARCHAR(500),
    
    -- 地址信息
    address TEXT,
    city VARCHAR(50),
    province VARCHAR(50),
    postal_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'CN',
    
    -- 业务字段
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    customer_type VARCHAR(20) NOT NULL DEFAULT 'individual',
    level VARCHAR(20) DEFAULT 'normal',
    source VARCHAR(50) DEFAULT 'manual',
    tags TEXT[],
    
    -- 信用和财务
    credit_limit DECIMAL(15,2) DEFAULT 0.00,
    current_debt DECIMAL(15,2) DEFAULT 0.00,
    
    -- 统计字段
    total_orders INTEGER DEFAULT 0,
    total_spent DECIMAL(15,2) DEFAULT 0.00,
    avg_order_value DECIMAL(15,2) DEFAULT 0.00,
    last_order_date TIMESTAMP WITH TIME ZONE,
    last_contact_date TIMESTAMP WITH TIME ZONE,
    
    -- 偏好设置
    preferred_language VARCHAR(10) DEFAULT 'zh-CN',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
    marketing_consent BOOLEAN DEFAULT FALSE,
    
    -- 元数据
    custom_fields JSONB,
    metadata JSONB,
    notes TEXT,
    
    -- 审计字段
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    version INTEGER NOT NULL DEFAULT 1,
    
    -- 约束条件
    CONSTRAINT chk_{table_name}_status CHECK (status IN ('active', 'inactive', 'blocked', 'deleted')),
    CONSTRAINT chk_{table_name}_customer_type CHECK (customer_type IN ('individual', 'business', 'vip', 'wholesale')),
    CONSTRAINT chk_{table_name}_credit_limit CHECK (credit_limit >= 0),
    CONSTRAINT chk_{table_name}_total_orders CHECK (total_orders >= 0),
    CONSTRAINT chk_{table_name}_total_spent CHECK (total_spent >= 0)
) 
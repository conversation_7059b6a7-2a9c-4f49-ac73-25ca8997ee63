CREATE TABLE {schema}.{table_name} (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    product_no VARCHAR(50) UNIQUE,
    name VARCHAR(200) NOT NULL,
    display_name <PERSON><PERSON><PERSON><PERSON>(200),
    sku VARCHAR(100) NOT NULL,
    barcode VARCHAR(100),
    qr_code VARCHAR(200),
    
    -- 基本信息
    description TEXT,
    short_description VARCHAR(500),
    category_id BIGINT,
    category_path VARCHAR(500),
    brand VARCHAR(100),
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    
    -- 价格信息
    price DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    cost_price DECIMAL(15,2) DEFAULT 0.00,
    market_price DECIMAL(15,2) DEFAULT 0.00,
    wholesale_price DECIMAL(15,2) DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'CNY',
    
    -- 库存信息
    stock_quantity INTEGER NOT NULL DEFAULT 0,
    available_quantity INTEGER DEFAULT 0,
    reserved_quantity INTEGER DEFAULT 0,
    min_stock_level INTEGER DEFAULT 0,
    max_stock_level INTEGER,
    reorder_point INTEGER DEFAULT 0,
    reorder_quantity INTEGER DEFAULT 0,
    
    -- 物理属性
    unit VARCHAR(20) DEFAULT 'pcs',
    weight DECIMAL(10,3),
    length DECIMAL(10,2),
    width DECIMAL(10,2),
    height DECIMAL(10,2),
    volume DECIMAL(10,3),
    
    -- 状态和类型
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    product_type VARCHAR(50) DEFAULT 'physical',
    is_digital BOOLEAN NOT NULL DEFAULT FALSE,
    is_virtual BOOLEAN NOT NULL DEFAULT FALSE,
    is_downloadable BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- 销售设置
    is_taxable BOOLEAN DEFAULT TRUE,
    tax_rate DECIMAL(5,4) DEFAULT 0.0000,
    is_shipping_required BOOLEAN DEFAULT TRUE,
    shipping_class VARCHAR(50),
    
    -- 展示和营销
    featured BOOLEAN DEFAULT FALSE,
    tags TEXT[],
    search_keywords TEXT[],
    seo_title VARCHAR(200),
    seo_description TEXT,
    seo_keywords TEXT[],
    
    -- 多媒体
    images JSONB,
    videos JSONB,
    documents JSONB,
    
    -- 变体和选项
    has_variants BOOLEAN DEFAULT FALSE,
    variant_attributes JSONB,
    custom_attributes JSONB,
    specifications JSONB,
    
    -- 供应商信息
    supplier_id BIGINT,
    supplier_sku VARCHAR(100),
    supplier_price DECIMAL(15,2),
    
    -- 元数据
    metadata JSONB,
    custom_fields JSONB,
    notes TEXT,
    internal_notes TEXT,
    
    -- 审计字段
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    published_at TIMESTAMP WITH TIME ZONE,
    discontinued_at TIMESTAMP WITH TIME ZONE,
    created_by BIGINT,
    updated_by BIGINT,
    version INTEGER NOT NULL DEFAULT 1,
    
    -- 约束条件
    CONSTRAINT uk_{table_name}_merchant_sku UNIQUE (merchant_id, sku),
    CONSTRAINT chk_{table_name}_price CHECK (price >= 0),
    CONSTRAINT chk_{table_name}_cost_price CHECK (cost_price >= 0),
    CONSTRAINT chk_{table_name}_stock_quantity CHECK (stock_quantity >= 0),
    CONSTRAINT chk_{table_name}_status CHECK (status IN ('active', 'inactive', 'discontinued', 'draft', 'archived')),
    CONSTRAINT chk_{table_name}_product_type CHECK (product_type IN ('physical', 'digital', 'service', 'bundle', 'gift_card'))
) 
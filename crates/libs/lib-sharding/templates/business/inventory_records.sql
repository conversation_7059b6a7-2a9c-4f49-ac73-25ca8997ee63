CREATE TABLE {schema}.{table_name} (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    product_id BIGINT NOT NULL,
    warehouse_id BIGINT,
    location_id BIGINT,
    
    -- 变更信息
    change_type VARCHAR(20) NOT NULL,
    change_quantity INTEGER NOT NULL,
    before_quantity INTEGER NOT NULL DEFAULT 0,
    after_quantity INTEGER NOT NULL DEFAULT 0,
    unit_cost DECIMAL(15,2) DEFAULT 0.00,
    total_cost DECIMAL(15,2) DEFAULT 0.00,
    
    -- 关联信息
    reference_type VARCHAR(50),
    reference_id BIGINT,
    reference_no VARCHAR(50),
    batch_no VARCHAR(50),
    lot_no VARCHAR(50),
    serial_no VARCHAR(100),
    
    -- 时间信息
    expiry_date DATE,
    production_date DATE,
    
    -- 审计信息
    reason VARCHAR(200),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL,
    
    -- 约束条件
    CONSTRAINT chk_{table_name}_change_type CHECK (change_type IN ('in', 'out', 'adjustment', 'transfer', 'return', 'damage', 'expired'))
) 
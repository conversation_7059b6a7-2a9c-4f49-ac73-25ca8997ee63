CREATE TABLE {schema}.{table_name} (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    order_no VARCHAR(50) NOT NULL UNIQUE,
    customer_id BIGINT,
    customer_name VARCHAR(100) NOT NULL,
    customer_phone VARCHAR(20),
    customer_email VARCHAR(150),
    total_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    shipping_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    final_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) NOT NULL DEFAULT 'CNY',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    payment_status VARCHAR(20) NOT NULL DEFAULT 'unpaid',
    payment_method VARCHAR(50),
    payment_time TIMESTAMP WITH TIME ZONE,
    shipping_address JSONB,
    billing_address JSONB,
    items JSONB,
    notes TEXT,
    internal_notes TEXT,
    tags TEXT[],
    metadata JSONB,
    source VARCHAR(50) DEFAULT 'manual',
    channel VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    created_by BIGINT,
    updated_by BIGINT,
    version INTEGER NOT NULL DEFAULT 1,
    
    -- 约束条件
    CONSTRAINT chk_{table_name}_total_amount CHECK (total_amount >= 0),
    CONSTRAINT chk_{table_name}_discount_amount CHECK (discount_amount >= 0),
    CONSTRAINT chk_{table_name}_final_amount CHECK (final_amount >= 0),
    CONSTRAINT chk_{table_name}_status CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'completed', 'cancelled', 'refunded')),
    CONSTRAINT chk_{table_name}_payment_status CHECK (payment_status IN ('unpaid', 'partial', 'paid', 'refunded', 'failed'))
) 
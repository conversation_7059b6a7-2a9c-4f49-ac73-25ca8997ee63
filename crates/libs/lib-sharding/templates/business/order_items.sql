CREATE TABLE {schema}.{table_name} (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    order_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    variant_id BIGINT,
    
    -- 产品信息快照
    product_name VARCHAR(200) NOT NULL,
    product_sku VARCHAR(100) NOT NULL,
    variant_name VARCHAR(200),
    variant_sku VARCHAR(100),
    
    -- 数量和单位
    quantity INTEGER NOT NULL DEFAULT 1,
    unit VARCHAR(20) DEFAULT 'pcs',
    
    -- 价格信息
    unit_price DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    total_price DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    discount_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    tax_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    final_amount DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    
    -- 成本信息（用于利润计算）
    unit_cost DECIMAL(15,2) DEFAULT 0.00,
    total_cost DECIMAL(15,2) DEFAULT 0.00,
    
    -- 状态
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    fulfillment_status VARCHAR(20) NOT NULL DEFAULT 'unfulfilled',
    
    -- 库存处理
    inventory_reserved BOOLEAN DEFAULT FALSE,
    inventory_allocated BOOLEAN DEFAULT FALSE,
    
    -- 自定义信息
    custom_fields JSONB,
    notes TEXT,
    
    -- 审计字段
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束条件
    CONSTRAINT chk_{table_name}_quantity CHECK (quantity > 0),
    CONSTRAINT chk_{table_name}_unit_price CHECK (unit_price >= 0),
    CONSTRAINT chk_{table_name}_status CHECK (status IN ('pending', 'confirmed', 'shipped', 'delivered', 'cancelled', 'refunded')),
    CONSTRAINT chk_{table_name}_fulfillment_status CHECK (fulfillment_status IN ('unfulfilled', 'partial', 'fulfilled'))
) 
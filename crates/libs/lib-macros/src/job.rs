//! 任务调度宏实现
//! 
//! 这个模块实现了任务调度相关的过程宏，包括 `#[cron]`、`#[fix_delay]`、
//! `#[fix_rate]` 和 `#[one_shot]` 等任务调度宏。这些宏可以将普通的异步函数
//! 转换为可调度的任务，并自动注册到任务调度系统中。
//! 
//! # 功能特性
//! 
//! - **多种调度策略**: 支持一次性、固定延迟、固定频率和 Cron 表达式调度
//! - **自动注册**: 通过宏标记的任务会自动注册到任务系统
//! - **类型安全**: 保持原函数的签名和属性
//! - **依赖注入**: 支持从应用容器中注入依赖
//! - **代码生成**: 自动生成任务注册和管理代码
//! 
//! # 支持的任务类型
//! 
//! ## 一次性任务
//! ```rust
//! #[one_shot(60)]  // 60秒后执行一次
//! async fn cleanup_temp_files() {
//!     // 清理临时文件
//! }
//! ```
//! 
//! ## 固定延迟任务
//! ```rust
//! #[fix_delay(30)]  // 每次执行完后等待30秒
//! async fn process_queue() {
//!     // 处理队列
//! }
//! ```
//! 
//! ## 固定频率任务
//! ```rust
//! #[fix_rate(60)]  // 每60秒执行一次
//! async fn health_check() {
//!     // 健康检查
//! }
//! ```
//! 
//! ## Cron 表达式任务
//! ```rust
//! #[cron("0 0 2 * * *")]  // 每天凌晨2点执行
//! async fn daily_backup() {
//!     // 每日备份
//! }
//! ```

use crate::input_and_compile_error;
use proc_macro::TokenStream;
use proc_macro2::{Span, TokenStream as TokenStream2};
use quote::{quote, ToTokens};
use syn::{ItemFn, LitInt, LitStr, Token};

/// 任务参数解析宏
/// 
/// 这个宏用于生成不同类型任务的参数解析代码，
/// 减少重复代码并确保一致的解析逻辑
macro_rules! job_args_parse {
    (
        $($name:ident, $trigger_type:ident, $lower:ident, $trigger_runtime_type:ty,)+
    ) => {
        /// 任务类型枚举
        /// 
        /// 定义了所有支持的任务调度类型
        pub(crate) enum JobType {
            $(
                /// 任务类型变体
                $name,
            )+
        }

        impl JobType {
            /// 解析任务参数
            /// 
            /// 根据任务类型解析对应的参数
            /// 
            /// 参数:
            /// - `args`: 宏参数的 TokenStream
            /// 
            /// 返回: 解析后的任务参数
            /// 
            /// 错误: 当参数格式不正确时返回解析错误
            fn parse_args(self, args: TokenStream) -> syn::Result<JobArgs> {
                match self {
                    $(
                        Self::$name => syn::parse::<$name>(args).map(JobArgs::from),
                    )+
                }
            }
        }

        /// 任务参数枚举
        /// 
        /// 包含所有类型任务的参数变体
        pub enum JobArgs {
            $(
                /// 任务参数变体
                $name($trigger_type),
            )+
        }

        $(
            /// 任务参数结构体
            /// 
            /// 存储特定类型任务的参数
            #[derive(Debug, Clone, PartialEq, Eq, Hash)]
            struct $name($trigger_type);

            /// 为任务参数实现解析 trait
            impl syn::parse::Parse for $name {
                /// 解析任务参数
                /// 
                /// 从宏输入中解析参数值，并验证格式正确性
                fn parse(input: syn::parse::ParseStream) -> syn::Result<Self> {
                    let trigger = input.parse::<syn::$trigger_type>().map_err(|mut err| {
                        err.combine(syn::Error::new(
                            err.span(),
                            concat!("无效的任务定义，期望格式：#[", stringify!($lower), "(", stringify!($trigger_runtime_type), ")]"),
                        ));
                        err
                    })?;

                    // 如果有逗号，说明有额外的未知属性
                    if input.peek(Token![,]) {
                        return Err(syn::Error::new(
                            Span::call_site(),
                            "指定了未知的属性键",
                        ));
                    }
                    Ok($name(trigger))
                }
            }

            /// 为任务参数实现转换 trait
            impl From<$name> for JobArgs {
                fn from($lower: $name) -> Self {
                    Self::$name($lower.0)
                }
            }
        )+
    };
}

// 生成所有支持的任务类型
#[rustfmt::skip]
job_args_parse!(
    OneShot, LitInt, one_shot, u64, 
    FixDelay, LitInt, fix_delay, u64, 
    FixRate, LitInt, fix_rate, u64, 
    Cron, LitStr, cron, str,
);

/// 任务定义结构体
/// 
/// 表示一个完整的任务定义，包含函数信息和调度参数
pub(crate) struct Job {
    /// 被注解的处理函数名
    name: syn::Ident,

    /// 传递给任务宏的参数
    args: JobArgs,

    /// 被注解的处理函数的 AST
    ast: syn::ItemFn,

    /// 要复制到生成结构体的文档注释属性（如果有）
    doc_attributes: Vec<syn::Attribute>,
}

impl Job {
    /// 创建新的任务定义
    /// 
    /// 从宏参数和函数定义构建任务
    /// 
    /// 参数:
    /// - `args`: 解析后的任务参数
    /// - `ast`: 函数的抽象语法树
    /// 
    /// 返回: 新的 Job 实例
    /// 
    /// 错误: 当函数不是异步函数时返回错误
    fn new(args: JobArgs, ast: ItemFn) -> syn::Result<Self> {
        let name = ast.sig.ident.clone();

        // 尝试提取文档注释，以便重新应用到生成的结构体上
        // 注意：多行文档注释会被转换为多个文档属性
        let doc_attributes = ast
            .attrs
            .iter()
            .filter(|attr| attr.path().is_ident("doc"))
            .cloned()
            .collect();

        // 检查函数是否为异步函数
        if ast.sig.asyncness.is_none() {
            return Err(syn::Error::new_spanned(
                ast.sig.fn_token,
                "只支持异步函数",
            ));
        }

        Ok(Self {
            name,
            args,
            ast,
            doc_attributes,
        })
    }
}

/// 为 Job 实现代码生成 trait
/// 
/// 生成任务注册和管理的相关代码
impl ToTokens for Job {
    /// 生成任务代码
    /// 
    /// 为每个任务生成：
    /// 1. 同名的标记结构体
    /// 2. TypedHandlerRegistrar trait 实现
    /// 3. 自动任务注册代码
    fn to_tokens(&self, output: &mut TokenStream2) {
        let Self {
            name,
            ast,
            args,
            doc_attributes,
        } = self;

        #[allow(unused_variables)] // 当禁用 force-pub 特性时使用
        let vis = &ast.vis;

        // 根据任务类型生成相应的注册代码
        let register_stream = match args {
            JobArgs::OneShot(literal) => {
                quote! { __jobs.add_job(::lib_job::job::Job::one_shot(#literal).run(#name))}
            }
            JobArgs::FixDelay(literal) => {
                quote! { __jobs.add_job(::lib_job::job::Job::fix_delay(#literal).run(#name))}
            }
            JobArgs::FixRate(literal) => {
                quote! { __jobs.add_job(::lib_job::job::Job::fix_rate(#literal).run(#name))}
            }
            JobArgs::Cron(literal) => {
                quote! { __jobs.add_job(::lib_job::job::Job::cron(#literal).run(#name))}
            }
        };
        
        // 生成最终的代码
        let stream = quote! {
            #(#doc_attributes)*
            #[allow(non_camel_case_types, missing_docs)]
            #vis struct #name;

            impl ::lib_job::handler::TypedHandlerRegistrar for #name {
                fn install_job(&self, mut __jobs: ::lib_job::Jobs) -> ::lib_job::Jobs {
                    use ::lib_job::JobConfigurator;
                    use ::lib_job::job::JobBuilder;
                    #ast
                    #register_stream
                }
            }

            ::lib_job::submit_typed_handler!(#name);
        };

        output.extend(stream);
    }
}

/// 任务宏的统一处理函数
/// 
/// 这个函数是所有任务宏的共同入口，负责：
/// 1. 解析宏参数
/// 2. 解析目标函数
/// 3. 创建任务定义
/// 4. 生成最终代码
/// 
/// 参数:
/// - `job_type`: 任务类型
/// - `args`: 宏参数 TokenStream
/// - `input`: 被标记的函数 TokenStream
/// 
/// 返回: 生成的任务代码 TokenStream
pub(crate) fn with_job(job_type: JobType, args: TokenStream, input: TokenStream) -> TokenStream {
    // 解析任务参数
    let args = match job_type.parse_args(args) {
        Ok(job) => job,
        // 解析错误时，让 IDE 保持正常工作
        Err(err) => return input_and_compile_error(input, err),
    };

    // 解析目标函数的 AST
    let ast = match syn::parse::<syn::ItemFn>(input.clone()) {
        Ok(ast) => ast,
        // 解析错误时，让 IDE 保持正常工作
        Err(err) => return input_and_compile_error(input, err),
    };

    // 创建任务定义并生成代码
    match Job::new(args, ast) {
        Ok(job) => job.into_token_stream().into(),
        // 宏相关错误时，让 IDE 保持正常工作
        Err(err) => input_and_compile_error(input, err),
    }
}

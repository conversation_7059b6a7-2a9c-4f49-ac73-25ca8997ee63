//! 依赖注入宏实现
//! 
//! 这个模块实现了 `#[derive(Service)]` 宏，为结构体自动生成依赖注入代码。
//! 该宏支持多种注入类型和服务模式，提供灵活的依赖管理机制。
//! 
//! # 功能特性
//! 
//! - **多种注入类型**: 支持组件、配置、函数调用等多种注入方式
//! - **服务模式**: 支持单例服务和原型服务
//! - **gRPC 集成**: 自动注册 gRPC 服务
//! - **类型安全**: 编译时检查依赖关系
//! - **灵活配置**: 支持可选依赖和引用类型
//! 
//! # 注入类型
//! 
//! ## 基本注入类型
//! - `#[inject(component)]`: 注入应用组件
//! - `#[inject(config)]`: 注入配置对象
//! - `#[inject(func = call())]`: 调用函数获取值
//! 
//! ## 引用类型
//! - `ComponentRef<T>`: 组件的轻量级引用
//! - `ConfigRef<T>`: 配置对象的引用包装
//! - `Option<T>`: 可选注入，失败时为 None
//! 
//! # 服务模式
//! 
//! ## 单例服务（默认）
//! ```rust
//! #[derive(Service)]
//! struct UserService {
//!     #[inject(component)]
//!     database: Database,
//! }
//! ```
//! 
//! ## 原型服务
//! ```rust
//! #[derive(Service)]
//! #[service(prototype)]
//! struct RequestHandler {
//!     request_id: String,  // 构造参数
//!     #[inject(component)]
//!     service: SharedService,
//! }
//! ```
//! 
//! ## gRPC 服务
//! ```rust
//! #[derive(Service)]
//! #[service(grpc = "MyServiceServer")]
//! struct GrpcService {
//!     #[inject(component)]
//!     business_logic: BusinessLogic,
//! }
//! ```

use proc_macro2::{Span, TokenStream};
use quote::{quote, ToTokens};
use syn::{
    AngleBracketedGenericArguments, GenericArgument, Meta, MetaList, PathArguments, Token, Type,
    TypePath,
};

/// 依赖注入错误提示函数
/// 
/// 生成标准的错误消息，用于不支持的结构类型
fn inject_error_tip() -> syn::Error {
    syn::Error::new(
        Span::call_site(),
        "依赖注入服务只支持具名字段的结构体",
    )
}

/// 可注入类型枚举
/// 
/// 定义了所有支持的依赖注入类型，每种类型都有不同的处理方式和优先级
#[derive(Debug)]
enum InjectableType {
    /// 可选类型 (Option<T>)
    /// 注入失败时值为 None，不会导致构建失败
    Option,
    /// 组件注入
    /// 从应用容器中获取指定类型的组件实例
    Component(syn::Path),
    /// 配置注入
    /// 从配置系统中获取指定类型的配置对象
    Config(syn::Path),
    /// 组件引用
    /// 获取组件的轻量级引用，适用于共享访问
    ComponentRef(syn::Path),
    /// 配置引用
    /// 获取配置对象的引用包装
    ConfigRef(syn::Path),
    /// 函数调用
    /// 通过调用指定函数获取值
    FuncCall(syn::ExprCall),
    /// 原型参数
    /// 用于原型服务的构造参数
    PrototypeArg(syn::Type),
}

impl InjectableType {
    /// 获取注入类型的优先级顺序
    /// 
    /// 返回值越小优先级越高，用于控制字段的处理顺序
    /// 
    /// 返回: 优先级数值 (0-6)
    fn order(&self) -> u8 {
        match self {
            Self::Option => 0,
            Self::Component(_) => 1,
            Self::Config(_) => 2,
            Self::ComponentRef(_) => 3,
            Self::ConfigRef(_) => 4,
            Self::FuncCall(_) => 5,
            Self::PrototypeArg(_) => 6,
        }
    }

    /// 检查是否为原型服务的构造参数
    /// 
    /// 原型参数需要在函数签名中声明，而不是在函数体中初始化
    /// 
    /// 返回: 如果是原型参数则返回 true
    fn is_arg(&self) -> bool {
        matches!(self, Self::PrototypeArg(_))
    }
}

/// 注入属性枚举
/// 
/// 表示 `#[inject]` 属性的不同类型
enum InjectableAttr {
    /// 组件注入属性
    Component,
    /// 配置注入属性
    Config,
    /// 函数调用注入属性
    FuncCall(syn::ExprCall),
}

/// 可注入字段结构体
/// 
/// 表示一个可以被依赖注入的结构体字段
struct Injectable {
    /// 是否为原型服务
    /// 原型服务的字段处理方式与单例服务不同
    is_prototype: bool,
    /// 注入类型
    ty: InjectableType,
    /// 字段名
    field_name: syn::Ident,
}

impl Injectable {
    /// 创建新的可注入字段
    /// 
    /// 分析结构体字段，确定其注入类型和处理方式
    /// 
    /// 参数:
    /// - `field`: 结构体字段定义
    /// - `is_prototype`: 是否为原型服务
    /// 
    /// 返回: 新的 Injectable 实例
    /// 
    /// 错误: 当字段类型不支持或缺少注入定义时返回错误
    fn new(field: syn::Field, is_prototype: bool) -> syn::Result<Self> {
        let ty = Self::compute_type(&field, is_prototype)?;
        let field_name = field.ident.ok_or_else(inject_error_tip)?;
        Ok(Self {
            is_prototype,
            ty,
            field_name,
        })
    }

    /// 计算字段的注入类型
    /// 
    /// 根据字段的类型和注解确定如何进行依赖注入
    /// 
    /// 参数:
    /// - `field`: 要分析的字段
    /// - `is_prototype`: 是否为原型服务
    /// 
    /// 返回: 确定的注入类型
    /// 
    /// 错误: 当字段类型不支持或注解格式错误时返回错误
    fn compute_type(field: &syn::Field, is_prototype: bool) -> syn::Result<InjectableType> {
        if let syn::Type::Path(path) = &field.ty {
            let ty = &path.path;
            let inject_attr = field
                .attrs
                .iter()
                .find(|attr| attr.path().is_ident("inject"));

            // 处理显式的 inject 注解
            if let Some(inject_attr) = inject_attr {
                if let Meta::List(MetaList { tokens, .. }) = &inject_attr.meta {
                    let attr = syn::parse::<InjectableAttr>(tokens.clone().into())?;
                    return Ok(attr.make_type(ty));
                } else {
                    Err(syn::Error::new_spanned(
                inject_attr,
                "无效的注入定义，期望格式：#[inject(component|config|func(args))]",
                    ))?;
                }
            }
            
            let last_path_segment = ty.segments.last().ok_or_else(inject_error_tip)?;
            
            // 处理 ComponentRef<T> 类型
            if last_path_segment.ident == "ComponentRef" {
                return Ok(InjectableType::ComponentRef(Self::get_argument_type(
                    &last_path_segment.arguments,
                )?));
            }
            
            // 处理 ConfigRef<T> 类型
            if last_path_segment.ident == "ConfigRef" {
                return Ok(InjectableType::ConfigRef(Self::get_argument_type(
                    &last_path_segment.arguments,
                )?));
            }
            
            // 处理 Option<T> 类型（仅限非原型服务）
            if !is_prototype && last_path_segment.ident == "Option" {
                return Ok(InjectableType::Option);
            }
        }
        
        // 原型服务的未注解字段作为构造参数
        if is_prototype {
            Ok(InjectableType::PrototypeArg(field.ty.clone()))
        } else {
            let field_name = &field
                .ident
                .clone()
                .map(|ident| ident.to_string())
                .ok_or_else(inject_error_tip)?;
            Err(syn::Error::new_spanned(
            field,
            format!(
                "{field_name} 字段缺少注入定义，期望格式：#[inject(component|config|func(args))]",
            )))
        }
    }

    /// 从泛型参数中获取类型路径
    /// 
    /// 提取 ComponentRef<T> 或 ConfigRef<T> 中的 T 类型
    /// 
    /// 参数:
    /// - `path_args`: 路径的泛型参数
    /// 
    /// 返回: 提取的类型路径
    /// 
    /// 错误: 当泛型参数格式不正确时返回错误
    fn get_argument_type(path_args: &PathArguments) -> syn::Result<syn::Path> {
        if let PathArguments::AngleBracketed(AngleBracketedGenericArguments { args, .. }) =
            path_args
        {
            let ty = args.last().ok_or_else(inject_error_tip)?;
            if let GenericArgument::Type(Type::Path(TypePath { path, .. })) = ty {
                return Ok(path.clone());
            }
        }
        Err(inject_error_tip())
    }
}

/// 为 InjectableAttr 实现解析 trait
/// 
/// 解析 #[inject] 属性的内容
impl syn::parse::Parse for InjectableAttr {
    /// 解析注入属性
    /// 
    /// 支持的格式：
    /// - `component`: 组件注入
    /// - `config`: 配置注入  
    /// - `func = function_call()`: 函数调用注入
    fn parse(input: syn::parse::ParseStream) -> syn::Result<Self> {
        let name = input.parse::<syn::Path>()?;
        if name.is_ident("component") {
            return Ok(Self::Component);
        }
        if name.is_ident("config") {
            return Ok(Self::Config);
        }
        if name.is_ident("func") {
            input.parse::<Token![=]>()?;
            let func_call = input.parse::<syn::ExprCall>()?;
            return Ok(Self::FuncCall(func_call));
        }
        Err(syn::Error::new(
            Span::call_site(),
            "无效的注入定义，期望格式：#[inject(component|config|func(args))]",
        ))
    }
}

impl InjectableAttr {
    /// 将属性转换为注入类型
    /// 
    /// 参数:
    /// - `ty`: 字段的类型路径
    /// 
    /// 返回: 对应的注入类型
    fn make_type(self, ty: &syn::Path) -> InjectableType {
        match self {
            Self::Component => InjectableType::Component(ty.clone()),
            Self::Config => InjectableType::Config(ty.clone()),
            Self::FuncCall(func_call) => InjectableType::FuncCall(func_call),
        }
    }
}

/// 为 Injectable 实现代码生成 trait
/// 
/// 根据注入类型生成相应的初始化代码
impl ToTokens for Injectable {
    /// 生成字段初始化代码
    /// 
    /// 根据不同的注入类型生成不同的初始化逻辑
    fn to_tokens(&self, tokens: &mut TokenStream) {
        let Self {
            is_prototype,
            ty,
            field_name,
        } = self;
        match ty {
            // 可选类型：初始化为 None
            InjectableType::Option => {
                tokens.extend(quote! {
                    let #field_name = None;
                });
            }
            // 组件注入：从应用容器获取组件，如果不存在则自动创建
            InjectableType::Component(type_path) => {
                if *is_prototype {
                    tokens.extend(quote! {
                        let #field_name = ::lib_core::app::app::App::global().try_get_component::<#type_path>()?;
                    });
                } else {
                    tokens.extend(quote! {
                        let #field_name = app.get_or_create_component::<#type_path>()?;
                    });
                }
            }
            // 配置注入：从配置系统获取配置
            InjectableType::Config(type_path) => {
                if *is_prototype {
                    tokens.extend(quote! {
                        let #field_name = ::lib_core::app::app::App::global().get_config::<#type_path>()?;
                    });
                } else {
                    tokens.extend(quote! {
                        let #field_name = app.get_config::<#type_path>()?;
                    });
                }
            }
            // 组件引用：获取组件的轻量级引用
            InjectableType::ComponentRef(type_path) => {
                if *is_prototype {
                    tokens.extend(quote! {
                        let #field_name = ::lib_core::app::app::App::global().try_get_component_ref::<#type_path>()?;
                    });
                } else {
                    tokens.extend(quote! {
                        let #field_name = app.try_get_component_ref::<#type_path>()?;
                    });
                }
            }
            // 配置引用：包装配置对象
            InjectableType::ConfigRef(type_path) => {
                if *is_prototype {
                    tokens.extend(quote! {
                        let #field_name = ::lib_core::config::ConfigRef::new(::lib_core::app::app::App::global().get_config::<#type_path>()?);
                    });
                } else {
                    tokens.extend(quote! {
                        let #field_name = ::lib_core::config::ConfigRef::new(app.get_config::<#type_path>()?);
                    });
                }
            }
            // 函数调用：直接调用指定函数
            InjectableType::FuncCall(func_call) => {
                tokens.extend(quote! {
                    let #field_name = #func_call;
                });
            }
            // 原型参数：作为函数参数声明
            InjectableType::PrototypeArg(type_path) => {
                tokens.extend(quote! {
                    #field_name: #type_path
                });
            }
        }
    }
}

/// 服务属性枚举
/// 
/// 定义服务的特殊配置选项
enum ServiceAttr {
    /// gRPC 服务配置
    /// 参数为 gRPC 服务器类型的路径
    Grpc(syn::Path),
    /// 原型服务配置
    /// 参数为构建方法的名称
    Prototype(syn::LitStr),
    /// 服务优先级配置
    /// 参数为优先级数值，数值越小优先级越高
    Priority(syn::LitInt),
}

/// 服务结构体
/// 
/// 表示一个完整的服务定义，包含所有字段和配置信息
struct Service {
    /// 泛型参数
    generics: syn::Generics,
    /// 结构体名称
    ident: proc_macro2::Ident,
    /// 服务属性配置
    attr: Option<ServiceAttr>,
    /// 服务优先级
    priority: i32,
    /// 可注入字段列表
    fields: Vec<Injectable>,
}

impl Service {
    /// 创建新的服务定义
    /// 
    /// 从 derive 输入中解析服务定义
    /// 
    /// 参数:
    /// - `input`: derive 宏的输入
    /// 
    /// 返回: 新的 Service 实例
    /// 
    /// 错误: 当输入不是支持的结构体类型时返回错误
    fn new(input: syn::DeriveInput) -> syn::Result<Self> {
        let syn::DeriveInput {
            attrs,
            ident,
            generics,
            data,
            ..
        } = input;
        
        // 解析服务属性
        let service_attr = attrs
            .iter()
            .find(|a| a.path().is_ident("service"))
            .and_then(|attr| attr.parse_args_with(Self::parse_service_attr).ok());

        let is_prototype = matches!(&service_attr, Some(ServiceAttr::Prototype(_)));
        
        // 获取优先级
        let priority = match &service_attr {
            Some(ServiceAttr::Priority(lit_int)) => {
                lit_int.base10_parse::<i32>().unwrap_or(0)
            }
            _ => 0,
        };
        
        // 解析结构体字段
        let mut fields = if let syn::Data::Struct(data) = data {
            data.fields
                .into_iter()
                .map(|f| Injectable::new(f, is_prototype))
                .collect::<syn::Result<Vec<_>>>()?
        } else {
            return Err(inject_error_tip());
        };
        
        // 按优先级排序字段
        fields.sort_by_key(|f| f.ty.order());

        Ok(Self {
            generics,
            ident,
            attr: service_attr,
            priority,
            fields,
        })
    }
    
    /// 解析服务属性
    /// 
    /// 从 #[service] 属性中解析配置选项
    /// 
    /// 支持的格式：
    /// - `#[service(grpc = "ServerType")]`: gRPC 服务
    /// - `#[service(prototype)]`: 原型服务（默认构建方法名为 "build"）
    /// - `#[service(prototype = "method_name")]`: 原型服务（自定义构建方法名）
    fn parse_service_attr(input: syn::parse::ParseStream) -> syn::Result<ServiceAttr> {
        let mut grpc: Option<syn::Path> = None;
        let mut prototype: Option<syn::LitStr> = None;
        let mut priority: Option<syn::LitInt> = None;

        while !input.is_empty() {
            let ident: syn::Ident = input.parse()?;

            if input.peek(syn::Token![=]) {
                input.parse::<syn::Token![=]>()?;
                
                match ident.to_string().as_str() {
                    "grpc" => {
                        if grpc.is_some() || prototype.is_some() || priority.is_some() {
                            return Err(syn::Error::new_spanned(
                                ident,
                                "只能指定 `grpc`、`prototype` 或 `priority` 中的一个",
                            ));
                        }
                        let value: syn::LitStr = input.parse()?;
                        grpc = Some(value.parse()?);
                    }
                    "prototype" => {
                        if prototype.is_some() || grpc.is_some() || priority.is_some() {
                            return Err(syn::Error::new_spanned(
                                ident,
                                "只能指定 `grpc`、`prototype` 或 `priority` 中的一个",
                            ));
                        }
                        let value: syn::LitStr = input.parse()?;
                        prototype = Some(value);
                    }
                    "priority" => {
                        if priority.is_some() || grpc.is_some() || prototype.is_some() {
                            return Err(syn::Error::new_spanned(
                                ident,
                                "只能指定 `grpc`、`prototype` 或 `priority` 中的一个",
                            ));
                        }
                        let value: syn::LitInt = input.parse()?;
                        priority = Some(value);
                    }
                    other => {
                        return Err(syn::Error::new_spanned(
                            ident,
                            format!("#[service(...)] 中的未知键 `{}`，期望 `grpc`、`prototype` 或 `priority`", other),
                        ));
                    }
                }
            } else {
                // 标志形式：#[service(prototype)]
                match ident.to_string().as_str() {
                    "prototype" => {
                        if prototype.is_some() || grpc.is_some() || priority.is_some() {
                            return Err(syn::Error::new_spanned(
                                ident,
                                "只能指定 `grpc`、`prototype` 或 `priority` 中的一个",
                            ));
                        }
                        // 默认构建方法名为 "build"
                        prototype = Some(syn::LitStr::new("build", Span::call_site()));
                    }
                    "grpc" => {
                        return Err(syn::Error::new_spanned(
                            ident,
                            "`grpc` 必须指定值，格式如：`grpc = \"...\"`",
                        ));
                    }
                    "priority" => {
                        return Err(syn::Error::new_spanned(
                            ident,
                            "`priority` 必须指定值，格式如：`priority = 1`",
                        ));
                    }
                    other => {
                        return Err(syn::Error::new_spanned(
                            ident,
                            format!("#[service(...)] 中的未知键 `{}`", other),
                        ));
                    }
                }
            }

            // 跳过逗号分隔符
            if input.peek(syn::Token![,]) {
                input.parse::<syn::Token![,]>()?;
            }
        }

        match (grpc, prototype, priority) {
            (Some(path), None, None) => Ok(ServiceAttr::Grpc(path)),
            (None, Some(litstr_opt), None) => Ok(ServiceAttr::Prototype(litstr_opt)),
            (None, None, Some(litint_opt)) => Ok(ServiceAttr::Priority(litint_opt)),
            (None, None, None) => {
                // 对于普通服务，不需要指定任何属性
                // 返回一个错误，但这个错误会被忽略，因为service_attr是Optional的
                Err(syn::Error::new(
                    input.span(),
                    "没有指定服务属性，将使用默认设置",
                ))
            },
            _ => Err(syn::Error::new(
                input.span(),
                "只能指定 `grpc`、`prototype` 或 `priority` 中的一个",
            )),
        }
    }
}

/// 为 Service 实现代码生成 trait
/// 
/// 根据服务类型生成不同的实现代码
impl ToTokens for Service {
    /// 生成服务实现代码
    /// 
    /// 根据服务属性生成不同类型的代码：
    /// - 原型服务：生成构建方法
    /// - gRPC 服务：生成服务注册代码
    /// - 普通服务：生成 Service trait 实现
    fn to_tokens(&self, tokens: &mut TokenStream) {
        let Self {
            generics,
            ident,
            attr,
            priority,
            fields,
        } = self;
        let field_names: Vec<&syn::Ident> = fields.iter().map(|f| &f.field_name).collect();

        let output = match attr {
            // 原型服务：生成构建方法
            Some(ServiceAttr::Prototype(build)) => {
                let fn_name = syn::Ident::new(&build.value(), build.span());
                let (args, fields): (Vec<&Injectable>, Vec<&Injectable>) =
                    fields.iter().partition(|f| f.ty.is_arg());
                let syn::Generics {
                    lt_token,
                    params,
                    gt_token,
                    ..
                } = generics;
                quote! {
                    impl #lt_token #params #gt_token #ident #generics {
                        pub fn #fn_name(#(#args),*) -> ::lib_core::error::Result<Self> {
                            use ::lib_core::app::plugin::ComponentRegistry;
                            use ::lib_core::config::ConfigRegistry;
                            #(#fields)*
                            Ok(Self { #(#field_names),* })
                        }
                    }
                }
            }
            // 其他服务类型：生成完整的服务实现
            _ => {
                let service_registrar =
                    syn::Ident::new(&format!("__ServiceRegistrarFor_{ident}"), ident.span());
                let service_installer = match attr {
                    // gRPC 服务：同时注册组件和 gRPC 服务
                    Some(ServiceAttr::Grpc(server)) => {
                        quote! {
                            use ::lib_core::app::plugin::MutableComponentRegistry;
                            use ::lib_grpc::GrpcConfigurator;
                            let service = #ident::build(app)?;
                            let grpc_server = #server::new(service.clone());
                            app.add_component(service).add_service(grpc_server);
                        }
                    }
                    // 普通服务：只注册组件
                    _ => {
                        quote! {
                            use ::lib_core::app::plugin::MutableComponentRegistry;
                            let service = #ident::build(app)?;
                            app.add_component(service);
                        }
                    }
                };

                quote! {
                    impl ::lib_core::app::plugin::service::Service for #ident {
                        fn build<R>(app: &mut R) -> ::lib_core::error::Result<Self>
                        where
                            R: ::lib_core::app::plugin::service::SmartComponentRegistry + ::lib_core::config::ConfigRegistry
                        {
                            #(#fields)*
                            Ok(Self { #(#field_names),* })
                        }
                    }
                    
                    #[allow(non_camel_case_types)]
                    struct #service_registrar;
                    impl ::lib_core::app::plugin::service::ServiceRegistrar for #service_registrar {
                        fn install_service(&self, app: &mut ::lib_core::app::app::AppBuilder) -> ::lib_core::error::Result<()> {
                            #service_installer
                            Ok(())
                        }
                        
                        fn priority(&self) -> i32 {
                            #priority
                        }
                        
                        fn type_id(&self) -> ::std::any::TypeId {
                            ::std::any::TypeId::of::<#ident>()
                        }
                        
                        fn type_name(&self) -> &'static str {
                            ::std::any::type_name::<#ident>()
                        }
                    }
                    ::lib_core::submit_service!(#service_registrar);
                }
            }
        };
        tokens.extend(output);
    }
}

/// 展开 Service 派生宏
/// 
/// 这是 `#[derive(Service)]` 宏的主入口函数
/// 
/// 参数:
/// - `input`: derive 宏的输入
/// 
/// 返回: 生成的实现代码
/// 
/// 错误: 当输入格式不正确时返回错误
pub(crate) fn expand_derive(input: syn::DeriveInput) -> syn::Result<TokenStream> {
    Ok(Service::new(input)?.into_token_stream())
}

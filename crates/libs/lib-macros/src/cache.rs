//! 缓存宏实现
//! 
//! 这个模块实现了 `#[cache]` 宏，为异步函数提供透明的 Redis 缓存功能。
//! 该宏通过代码生成，在函数执行前检查缓存，在函数执行后存储结果，
//! 从而实现无侵入的缓存机制。
//! 
//! # 功能特性
//! 
//! - **透明缓存**: 对原函数逻辑无侵入
//! - **自动序列化**: 使用 serde_json 进行结果序列化/反序列化
//! - **过期控制**: 支持设置缓存过期时间
//! - **条件缓存**: 支持缓存条件和排除条件
//! - **类型安全**: 保持原函数的类型签名和泛型
//! - **错误处理**: 优雅处理缓存失败，不影响业务逻辑
//! 
//! # 缓存键生成
//! 
//! 缓存键通过格式字符串生成，支持插入函数参数：
//! ```rust
//! #[cache("user:{user_id}:profile:{profile_id}")]
//! async fn get_user_profile(user_id: u64, profile_id: u64) -> UserProfile {
//!     // 函数实现
//! }
//! ```
//! 
//! # Result 类型处理
//! 
//! 对于返回 Result<T, E> 的函数，宏会特殊处理：
//! - 只有成功的结果（Ok 值）会被缓存
//! - unless 条件中的 `result` 变量指向 T 类型，而不是 Result<T, E>
//! - 错误结果不会被缓存，确保错误状态的及时性

use proc_macro::TokenStream;
use quote::quote;
use syn::{parse_macro_input, Expr, ExprAssign, ItemFn, Lit, Token};

/// 缓存参数结构体
/// 
/// 存储从宏参数中解析出的缓存配置
struct CacheArgs {
    /// 缓存键的格式字符串
    /// 支持使用 format! 宏语法插入函数参数
    key: String,
    /// 缓存过期时间（秒）
    /// None 表示永不过期
    expire: Option<u64>,
    /// 缓存条件表达式
    /// 在函数执行前计算，为 false 时跳过缓存
    condition: Option<Expr>,
    /// 排除条件表达式  
    /// 在函数执行后计算，为 true 时不缓存结果
    unless: Option<Expr>,
}

/// 为 CacheArgs 实现解析 trait
/// 
/// 从宏参数中解析缓存配置
impl syn::parse::Parse for CacheArgs {
    /// 解析缓存宏参数
    /// 
    /// 支持的语法格式：
    /// ```rust
    /// #[cache("key_pattern")]
    /// #[cache("key_pattern", expire = 300)]
    /// #[cache("key_pattern", condition = some_condition)]
    /// #[cache("key_pattern", unless = some_condition)]
    /// #[cache("key_pattern", expire = 300, condition = cond, unless = unless_cond)]
    /// ```
    /// 
    /// 参数:
    /// - `input`: 宏的输入参数流
    /// 
    /// 返回: 解析后的缓存参数
    /// 
    /// 错误: 当参数格式不正确时返回解析错误
    fn parse(input: syn::parse::ParseStream) -> syn::Result<Self> {
        // 解析必需的键模式参数
        let key = input.parse::<syn::LitStr>().map_err(|mut err| {
            err.combine(syn::Error::new(
                err.span(),
                r#"无效的缓存定义，期望格式：#[cache("<键模式>", expire = <秒数>, condition = <布尔表达式>, unless = <布尔表达式>)]"#,
            ));

            err
        })?.value();

        let mut expire = None;
        let mut condition = None;
        let mut unless = None;
        
        // 解析可选的命名参数
        while input.peek(Token![,]) {
            input.parse::<Token![,]>()?;
            let assign = input.parse::<ExprAssign>()?;
            let ident = match *assign.left {
                Expr::Path(ref path) => path.path.get_ident().map(|id| id.to_string()),
                _ => None,
            };

            match ident.as_deref() {
                Some("expire") => {
                    if let Expr::Lit(expr_lit) = *assign.right {
                        if let Lit::Int(lit_int) = expr_lit.lit {
                            expire = Some(lit_int.base10_parse()?);
                        } else {
                            return Err(syn::Error::new_spanned(
                                expr_lit,
                                "expire 参数必须是整数",
                            ));
                        }
                    }
                }
                Some("condition") => {
                    condition = Some(*assign.right);
                }
                Some("unless") => {
                    unless = Some(*assign.right);
                }
                Some(name) => {
                    return Err(syn::Error::new_spanned(
                        assign.left,
                        format!("未知的命名参数 `{}`", name),
                    ));
                }
                None => {
                    return Err(syn::Error::new_spanned(assign.left, "无效的赋值表达式"));
                }
            }
        }

        Ok(Self {
            key,
            expire,
            condition,
            unless,
        })
    }
}

/// 从 Result<T, E> 类型中提取 Ok 类型 T
/// 
/// 这个函数用于处理返回 Result 类型的函数，确保缓存的类型正确性
/// 
/// 参数:
/// - `ty`: 要分析的类型
/// 
/// 返回: 如果是 Result<T, E> 类型，返回 Some(&T)，否则返回 None
fn extract_ok_type_from_result(ty: &syn::Type) -> Option<&syn::Type> {
    if let syn::Type::Path(type_path) = ty {
        let segment = type_path.path.segments.last()?;
        if segment.ident == "Result" {
            if let syn::PathArguments::AngleBracketed(generic_args) = &segment.arguments {
                if let Some(syn::GenericArgument::Type(ok_ty)) = generic_args.args.first() {
                    return Some(ok_ty);
                }
            }
        }
    }
    None
}

/// 缓存宏的主入口函数
/// 
/// 这个函数负责：
/// 1. 解析宏参数和目标函数
/// 2. 分析函数返回类型
/// 3. 生成缓存逻辑代码
/// 4. 处理不同的返回类型（Result vs 普通类型）
/// 
/// 参数:
/// - `attr`: 宏的参数 TokenStream
/// - `item`: 被标记的函数 TokenStream
/// 
/// 返回: 生成的缓存函数 TokenStream
pub fn cache(attr: TokenStream, item: TokenStream) -> TokenStream {
    let input_fn = parse_macro_input!(item as ItemFn);
    let args = parse_macro_input!(attr as CacheArgs);

    // 提取函数的各个组成部分
    let vis = &input_fn.vis;           // 可见性
    let sig = &input_fn.sig;           // 函数签名
    let ident = &sig.ident;            // 函数名
    let inputs = &sig.inputs;          // 参数列表
    let output = &sig.output;          // 返回类型
    let asyncness = &sig.asyncness;    // async 关键字
    let generics = &sig.generics;      // 泛型参数
    let where_clause = &sig.generics.where_clause;  // where 子句
    let attrs = &input_fn.attrs;       // 属性列表
    let user_block = &input_fn.block;  // 原始函数体

    // 获取返回类型
    let ret_type = match &sig.output {
        syn::ReturnType::Type(_, ty) => ty,
        syn::ReturnType::Default => {
            return syn::Error::new_spanned(sig, "缓存函数必须有返回值")
                .to_compile_error()
                .into();
        }
    };

    let cache_key_fmt = args.key;
    
    // 根据是否有过期时间生成不同的 Redis SET 语句
    let redis_set_stmt = match args.expire {
        Some(expire_sec) => {
            quote! {
                let _: ::std::result::Result<redis::Value, ()> = redis.set_ex(&cache_key, cache_value, #expire_sec).await
                    .map_err(|err| ::tracing::error!("为键 {} 设置缓存失败: {:?}", cache_key, err));
            }
        }
        None => {
            quote! {
                let _: ::std::result::Result<redis::Value, ()> = redis.set(&cache_key, cache_value).await
                    .map_err(|err| ::tracing::error!("为键 {} 设置缓存失败: {:?}", cache_key, err));
            }
        }
    };
    
    // 生成条件检查代码
    let condition_check = match &args.condition {
        Some(expr) => quote! {
            if !(#expr) {
                return (|| async #user_block)().await;
            }
        },
        None => quote! {},
    };

    // 根据返回类型生成不同的缓存逻辑
    let gen_code = match extract_ok_type_from_result(ret_type) {
        // 处理 Result<T, E> 类型的函数
        Some(inner_type) => {
            let unless_check = match &args.unless {
                Some(expr) => quote! {
                    if (#expr) {
                        return Ok(result);
                    }
                },
                None => quote! {},
            };
            quote! {
                #(#attrs)*
                #vis #asyncness fn #ident #generics(#inputs) #output #where_clause {
                    #condition_check

                    use lib_cache::redis::{self, AsyncCommands};
                    use lib_core::{plugin::ComponentRegistry, tracing, App};

                    let mut redis = App::global()
                        .get_component::<lib_cache::Redis>()
                        .expect("未找到 redis 组件");

                    let cache_key = format!(#cache_key_fmt);

                    // 尝试从缓存获取结果
                    if let Ok(Some(cache_value)) = redis.get::<_, Option<String>>(&cache_key).await {
                        match ::serde_json::from_str::<#inner_type>(&cache_value) {
                            Ok(value) => return Ok(value),
                            Err(e) => {
                                ::tracing::error!("缓存键 {} 解码错误: {:?}", cache_key, e);
                            }
                        }
                    }

                    // 执行原始函数
                    let result: #ret_type = (|| async #user_block)().await;
                    let result: #inner_type = result?;

                    #unless_check

                    // 序列化并存储结果到缓存
                    match ::serde_json::to_string(&result) {
                        Ok(cache_value) => {
                            #redis_set_stmt
                        }
                        Err(err) => {
                            ::lib_core::tracing::error!("缓存键 {} 编码失败: {:?}", cache_key, err);
                        }
                    }

                    Ok(result)
                }
            }
        }
        // 处理普通类型的函数
        None => {
            let unless_check = match &args.unless {
                Some(expr) => quote! {
                    if (#expr) {
                        return result;
                    }
                },
                None => quote! {},
            };
            quote! {
                #(#attrs)*
                #vis #asyncness fn #ident #generics(#inputs) #output #where_clause {
                    #condition_check

                    use lib_cache::redis::{self, AsyncCommands};
                    use lib_core::{plugin::ComponentRegistry, App};

                    let mut redis = App::global()
                        .get_component::<lib_cache::Redis>()
                        .expect("未找到 redis 组件");

                    let cache_key = format!(#cache_key_fmt);

                    // 尝试从缓存获取结果
                    if let Ok(Some(cache_value)) = redis.get::<_, Option<String>>(&cache_key).await {
                        match ::serde_json::from_str::<#ret_type>(&cache_value) {
                            Ok(value) => return value,
                            Err(e) => {
                                ::lib_core::tracing::error!("缓存键 {} 解码错误: {:?}", cache_key, e);
                            }
                        }
                    }

                    // 执行原始函数
                    let result: #ret_type = (|| async #user_block)().await;

                    #unless_check

                    // 序列化并存储结果到缓存
                    match ::serde_json::to_string(&result) {
                        Ok(cache_value) => {
                            #redis_set_stmt
                        }
                        Err(err) => {
                            ::lib_core::tracing::error!("缓存键 {} 编码失败: {:?}", cache_key, err);
                        }
                    }

                    result
                }
            }
        }
    };

    gen_code.into()
}

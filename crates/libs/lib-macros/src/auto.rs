//! 自动配置宏实现
//!
//! 这个模块实现了 `#[auto_config]` 宏，用于自动配置应用程序组件。
//! 该宏通过编译时代码转换，自动在 `App::new()` 调用后插入相应的配置代码，
//! 减少样板代码并简化应用程序的设置过程。
//!
//! # 工作原理
//!
//! 1. **语法解析**: 解析宏参数，确定需要启用的配置器
//! 2. **AST 遍历**: 遍历函数的抽象语法树，查找 `App::new()` 调用
//! 3. **代码插入**: 在找到的调用点后自动插入配置方法调用
//! 4. **代码生成**: 生成最终的 TokenStream
//!
//! # 支持的配置器
//! - `JobConfigurator`: 自动添加通过宏标记的定时任务

use crate::input_and_compile_error;
use proc_macro::TokenStream;
use quote::quote;
use quote::ToTokens;
use syn::{Expr, ExprCall, ExprMethodCall};
use syn::{ItemFn, Stmt, Token};

/// 配置参数结构体
///
/// 存储从宏参数中解析出的配置选项
struct ConfigArgs {
    /// 是否启用任务配置器
    /// 当宏参数包含 `JobConfigurator` 时为 true
    job: bool,
}

/// 为 ConfigArgs 实现解析 trait
///
/// 从宏的输入参数中解析配置选项
impl syn::parse::Parse for ConfigArgs {
    /// 解析宏参数
    ///
    /// 支持的语法格式：
    /// - `#[auto_config(JobConfigurator)]`
    /// - `#[auto_config(JobConfigurator, OtherConfigurator)]` (未来扩展)
    ///
    /// 参数:
    /// - `args`: 宏的输入参数流
    ///
    /// 返回: 解析后的配置参数
    ///
    /// 错误: 当参数格式不正确时返回解析错误
    fn parse(args: syn::parse::ParseStream) -> syn::Result<Self> {
        let mut job = false;

        // 循环解析所有参数
        while !args.is_empty() {
            let ident = args.parse::<syn::Ident>().map_err(|mut err| {
                err.combine(syn::Error::new(
                    err.span(),
                    r#"无效的自动配置参数，期望格式：#[auto_config(Configurator)]"#,
                ));

                err
            })?;

            // 检查是否为任务配置器
            if ident == "JobConfigurator" {
                job = true;
            }

            // 如果没有逗号，说明参数解析完毕
            if !args.peek(Token![,]) {
                break;
            }
            args.parse::<Token![,]>()?;
        }

        Ok(ConfigArgs { job })
    }
}

/// 应用配置结构体
///
/// 包含配置参数和要转换的函数 AST
struct AppConfig {
    /// 解析后的配置参数
    args: ConfigArgs,
    /// 原始函数的抽象语法树
    ast: ItemFn,
}

impl AppConfig {
    /// 创建新的应用配置实例
    ///
    /// 参数:
    /// - `args`: 配置参数
    /// - `ast`: 函数的抽象语法树
    ///
    /// 返回: 新的 AppConfig 实例
    fn new(args: ConfigArgs, ast: ItemFn) -> Self {
        Self { args, ast }
    }
}

/// 为 AppConfig 实现代码生成 trait
///
/// 将修改后的函数转换为最终的 TokenStream
impl ToTokens for AppConfig {
    /// 生成最终的代码
    ///
    /// 这个方法会：
    /// 1. 克隆原始函数
    /// 2. 遍历并转换函数体中的每个语句
    /// 3. 在 `App::new()` 调用后插入配置代码
    ///
    /// 参数:
    /// - `output`: 输出的 TokenStream
    fn to_tokens(&self, output: &mut proc_macro2::TokenStream) {
        let args = &self.args;
        let mut input_fn = self.ast.clone();

        // 处理函数体中的每个语句
        input_fn.block.stmts = input_fn
            .block
            .stmts
            .into_iter()
            .map(|stmt| process_stmt(stmt, args))
            .collect();

        // 生成输出的 TokenStream
        output.extend(quote! {
            #input_fn
        });
    }
}

/// 处理单个语句
///
/// 递归处理语句中的表达式，查找需要修改的 `App::new()` 调用
///
/// 参数:
/// - `stmt`: 要处理的语句
/// - `args`: 配置参数
///
/// 返回: 处理后的语句
fn process_stmt(stmt: Stmt, args: &ConfigArgs) -> Stmt {
    match stmt {
        // 处理表达式语句
        Stmt::Expr(expr, semi) => Stmt::Expr(process_expr(expr, args), semi),
        // 其他语句类型保持不变
        other => other,
    }
}

/// 处理表达式
///
/// 递归遍历表达式树，查找并修改目标表达式
///
/// 参数:
/// - `expr`: 要处理的表达式
/// - `args`: 配置参数
///
/// 返回: 处理后的表达式
fn process_expr(expr: Expr, args: &ConfigArgs) -> Expr {
    match expr {
        // 处理方法调用表达式
        Expr::MethodCall(mut method_call) => {
            // 递归处理接收者表达式
            method_call.receiver = Box::new(process_expr(*method_call.receiver, args));
            Expr::MethodCall(method_call)
        }
        // 处理函数调用表达式
        Expr::Call(mut call) => {
            // 递归处理函数表达式
            call.func = Box::new(process_expr(*call.func, args));

            // 检查是否为 App::new() 调用
            if let Expr::Path(ref expr_path) = *call.func {
                if is_app_new_call(&expr_path.path) {
                    // 在 App::new() 调用后添加配置方法
                    return add_method_call(call, args);
                }
            }

            Expr::Call(call)
        }
        // 处理 await 表达式
        Expr::Await(mut expr_await) => {
            // 递归处理基础表达式
            expr_await.base = Box::new(process_expr(*expr_await.base, args));
            Expr::Await(expr_await)
        }
        // 其他表达式类型保持不变
        other => other,
    }
}

/// 添加方法调用
///
/// 在 App::new() 调用后添加相应的配置方法调用
///
/// 参数:
/// - `call`: 原始的函数调用表达式
/// - `args`: 配置参数
///
/// 返回: 修改后的表达式
fn add_method_call(call: ExprCall, args: &ConfigArgs) -> Expr {
    let mut expr = Expr::Call(call);

    // 如果启用了任务配置器，添加 add_jobs 方法调用
    if args.job {
        expr = Expr::MethodCall(ExprMethodCall {
            attrs: vec![],
            receiver: Box::new(expr),
            dot_token: Default::default(),
            method: syn::parse_quote!(add_jobs),
            turbofish: None,
            paren_token: Default::default(),
            args: {
                let mut punctuated = syn::punctuated::Punctuated::new();
                // 调用 auto_jobs() 函数获取自动注册的任务
                punctuated.push(syn::parse_quote!(::lib_job::handler::auto_jobs()));
                punctuated
            },
        });
    }
    expr
}

/// 检查路径是否为 App::new 调用
///
/// 判断给定的路径是否匹配 `App::new` 模式
///
/// 参数:
/// - `path`: 要检查的路径
///
/// 返回: 如果是 App::new 调用则返回 true
fn is_app_new_call(path: &syn::Path) -> bool {
    // 检查路径是否为 `App::new` 格式
    path.segments.len() == 2 && path.segments[0].ident == "App" && path.segments[1].ident == "new"
}

/// 自动配置宏的主入口函数
///
/// 这个函数协调整个宏的处理流程：
/// 1. 解析宏参数
/// 2. 解析目标函数
/// 3. 应用代码转换
/// 4. 生成最终代码
///
/// 参数:
/// - `args`: 宏的参数 TokenStream
/// - `input`: 被标记的函数 TokenStream
///
/// 返回: 转换后的 TokenStream
pub(crate) fn config(args: TokenStream, input: TokenStream) -> TokenStream {
    // 解析宏参数
    let args = match syn::parse(args) {
        Ok(config) => config,
        Err(e) => return input_and_compile_error(input, e),
    };

    // 解析目标函数的 AST
    let ast = match syn::parse::<syn::ItemFn>(input.clone()) {
        Ok(ast) => ast,
        // 解析错误时，让 IDE 保持正常工作
        Err(err) => return input_and_compile_error(input, err),
    };

    // 应用配置转换并生成代码
    AppConfig::new(args, ast).into_token_stream().into()
}

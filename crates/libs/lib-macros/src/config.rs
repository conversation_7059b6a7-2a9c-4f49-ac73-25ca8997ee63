//! 配置派生宏实现
//! 
//! 这个模块实现了 `#[derive(Configurable)]` 宏，用于自动为结构体实现
//! `Configurable` trait。该宏可以让结构体从配置文件中自动加载配置信息。
//! 
//! # 工作原理
//! 
//! 1. **属性解析**: 从 `#[config_prefix]` 属性中提取配置前缀
//! 2. **trait 实现**: 自动生成 `Configurable` trait 的实现
//! 3. **前缀配置**: 设置配置文件中对应的前缀路径
//! 
//! # 使用示例
//! 
//! ```rust
//! #[derive(Configurable)]
//! #[config_prefix = "database"]
//! struct DatabaseConfig {
//!     host: String,
//!     port: u16,
//!     username: String,
//!     password: String,
//! }
//! ```
//! 
//! 对应的配置文件：
//! ```toml
//! [database]
//! host = "localhost"
//! port = 5432
//! username = "admin"
//! password = "secret"
//! ```

use proc_macro2::{Span, TokenStream};
use quote::quote;

/// 展开 Configurable 派生宏
/// 
/// 这个函数是 `#[derive(Configurable)]` 宏的主要实现，
/// 负责生成 `Configurable` trait 的实现代码。
/// 
/// 参数:
/// - `input`: 标记了 `#[derive(Configurable)]` 的结构体定义
/// 
/// 返回: 生成的 trait 实现代码
/// 
/// 错误: 当缺少 `#[config_prefix]` 属性或属性格式不正确时返回错误
/// 
/// # 生成的代码
/// 
/// 对于输入的结构体，会生成如下形式的代码：
/// ```rust
/// impl ::lib_core::config::Configurable for StructName {
///     fn config_prefix() -> &'static str {
///         "配置前缀"
///     }
/// }
/// ```
pub(crate) fn expand_derive(input: syn::DeriveInput) -> syn::Result<TokenStream> {
    let prefix = get_prefix(&input)?;
    let ident = input.ident;

    let output = quote! {
        impl ::lib_core::config::Configurable for #ident {
            fn config_prefix() -> &'static str {
                    #prefix
            }
        }
    };

    Ok(output)
}

/// 从结构体属性中获取配置前缀
/// 
/// 这个函数解析结构体的 `#[config_prefix]` 属性，提取配置前缀字符串。
/// 
/// 参数:
/// - `input`: 要解析的结构体定义
/// 
/// 返回: 配置前缀字符串字面量
/// 
/// 错误: 当缺少属性或属性格式不正确时返回错误
/// 
/// # 支持的属性格式
/// 
/// ```rust
/// #[config_prefix = "database"]          // 基本格式
/// #[config_prefix = "app.database"]      // 嵌套配置
/// #[config_prefix = "services.user"]     // 服务配置
/// ```
fn get_prefix(input: &syn::DeriveInput) -> syn::Result<syn::LitStr> {
    // 查找 config_prefix 属性
    let attr = input
        .attrs
        .iter()
        .filter(|attr| attr.path().is_ident("config_prefix"))
        .next_back();

    // 解析属性值
    if let Some(syn::Attribute {
        meta: syn::Meta::NameValue(name_value),
        ..
    }) = attr
    {
        if name_value.path.is_ident("config_prefix") {
            if let syn::Expr::Lit(syn::ExprLit {
                lit: syn::Lit::Str(lit),
                ..
            }) = &name_value.value
            {
                return Ok(lit.clone());
            }
        }
    }
    
    // 如果没有找到有效的属性，返回错误
    Err(syn::Error::new(
        Span::call_site(),
        "缺少 Configurable 必需的属性，期望格式：#[config_prefix=\"前缀\"]",
    ))
}

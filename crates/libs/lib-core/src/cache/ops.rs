use redis::AsyncCommands;
use serde::{de::DeserializeOwned, Serialize};
use serde_json;
use crate::app::app::App;
use crate::app::plugin::ComponentRegistry;
use crate::BusinessError;
use redis::aio::ConnectionManager;

/// 设置redis key（String）
pub async fn redis_set_string(key: &str, value: &str) -> anyhow::Result<()> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    
    let mut conn = redis;
    let _: () = conn.set(key, value).await?;
    Ok(())
}

/// 获取redis key（String）
pub async fn redis_get_string(key: &str) -> anyhow::Result<String> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let v: String = conn.get(key).await?;
    Ok(v)
}

/// 删除redis key，返回删除的数量
pub async fn redis_del_key(key: &str) -> anyhow::Result<u64> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let deleted: u64 = conn.del(key).await?;
    Ok(deleted)
}

/// 设置字符串，带过期时间（秒）
pub async fn redis_set_ex(key: &str, value: &str, expire_secs: u64) -> anyhow::Result<()> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let _: () = conn.set_ex(key, value, expire_secs).await?;
    Ok(())
}

/// 设置自定义类型，自动序列化为JSON
pub async fn redis_set_json<T: Serialize>(
    key: &str,
    value: &T,
    expire_secs: Option<u64>,
) -> anyhow::Result<()> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let json = serde_json::to_string(value)?;
    if let Some(secs) = expire_secs {
        let _: () = conn.set_ex(key, json, secs).await?;
    } else {
        let _: () = conn.set(key, json).await?;
    }
    Ok(())
}

/// 获取自定义类型，自动反序列化
pub async fn redis_get_json<T: DeserializeOwned>(key: &str) -> anyhow::Result<Option<T>> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let v: Option<String> = conn.get(key).await?;
    if let Some(json) = v {
        Ok(Some(serde_json::from_str(&json)?))
    } else {
        Ok(None)
    }
}

/// LPUSH 列表
pub async fn redis_lpush(key: &str, value: &str) -> anyhow::Result<()> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let _: () = conn.lpush(key, value).await?;
    Ok(())
}

/// RPUSH 列表
pub async fn redis_rpush(key: &str, value: &str) -> anyhow::Result<()> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let _: () = conn.rpush(key, value).await?;
    Ok(())
}

/// LRANGE 获取列表
pub async fn redis_lrange(key: &str, start: isize, stop: isize) -> anyhow::Result<Vec<String>> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let v: Vec<String> = conn.lrange(key, start, stop).await?;
    Ok(v)
}

/// LPOP
pub async fn redis_lpop(key: &str) -> anyhow::Result<Option<String>> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let v: Option<String> = conn.lpop(key, None).await?;
    Ok(v)
}

/// RPOP
pub async fn redis_rpop(key: &str) -> anyhow::Result<Option<String>> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let v: Option<String> = conn.rpop(key, None).await?;
    Ok(v)
}

/// SADD 向集合添加成员
pub async fn redis_sadd(key: &str, member: &str) -> anyhow::Result<u64> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let added: u64 = conn.sadd(key, member).await?;
    Ok(added)
}

/// SREM 从集合移除成员
pub async fn redis_srem(key: &str, member: &str) -> anyhow::Result<u64> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let removed: u64 = conn.srem(key, member).await?;
    Ok(removed)
}

/// SMEMBERS 获取集合所有成员
pub async fn redis_smembers(key: &str) -> anyhow::Result<Vec<String>> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let members: Vec<String> = conn.smembers(key).await?;
    Ok(members)
}

/// SCARD 获取集合成员数量
pub async fn redis_scard(key: &str) -> anyhow::Result<u64> {
    let redis = App::global()
        .get_component::<ConnectionManager>()
        .ok_or_else(|| BusinessError::new(500, "获取Redis连接失败".to_string()))?;
    let mut conn = redis;
    let count: u64 = conn.scard(key).await?;
    Ok(count)
}

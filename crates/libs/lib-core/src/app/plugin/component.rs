use std::{any::Any, ops::Deref, sync::Arc};

/// 组件的动态特征引用
#[derive(Debu<PERSON>, Clone)]
pub struct DynComponentRef(Arc<dyn Any + Send + Sync>);

impl DynComponentRef {
    /// 构造器
    pub fn new<T>(component: T) -> Self
    where
        T: Any + Send + Sync,
    {
        Self(Arc::new(component))
    }

    /// 下转型为指定类型
    pub fn downcast<T>(self) -> Option<ComponentRef<T>>
    where
        T: Any + Send + Sync,
    {
        self.0.downcast::<T>().ok().map(ComponentRef::new)
    }
}

/// 指定类型的组件引用
#[derive(Debug, Clone)]
pub struct ComponentRef<T>(Arc<T>);

impl<T> ComponentRef<T> {
    fn new(target_ref: Arc<T>) -> Self {
        Self(target_ref)
    }

    /// 获取组件的原始指针
    #[inline]
    pub fn into_raw(self) -> *const T {
        Arc::into_raw(self.0)
    }
}

impl<T> Deref for ComponentRef<T> {
    type Target = T;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

//! Service 是一个支持编译时依赖注入的特殊组件
use crate::error::Result;

use crate::app::app::AppBuilder;
use crate::config::ConfigRegistry;
pub use inventory::submit;
use tracing::debug;
pub use lib_macros::Service;
use crate::app::plugin::ComponentRegistry;
use std::any::{Any, TypeId};
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use once_cell::sync::Lazy;

/// 智能组件注册表，支持自动依赖注入
pub trait SmartComponentRegistry: ComponentRegistry {
    /// 智能获取组件，如果组件不存在则尝试创建
    fn get_or_create_component<T>(&mut self) -> Result<T>
    where
        T: Clone + Send + Sync + 'static;
}

/// Service 是一个特殊的组件，可以将依赖的组件作为字段成员注入
pub trait Service: Clone + Sized + 'static {
    /// 构造 Service 组件
    fn build<R>(registry: &mut R) -> Result<Self>
    where
        R: SmartComponentRegistry + ConfigRegistry;
}

/// 三级缓存系统，用于解决循环依赖
/// 
/// 参考Spring的三级缓存机制：
/// - 一级缓存：完全初始化的单例对象
/// - 二级缓存：早期单例对象（已实例化但未完全初始化）
/// - 三级缓存：单例工厂（用于创建早期引用）
#[derive(Default)]
pub struct ThreeLevelCache {
    /// 一级缓存：完全初始化的单例对象
    /// singletonObjects
    singleton_objects: HashMap<TypeId, Arc<dyn Any + Send + Sync>>,
    
    /// 二级缓存：早期单例对象（已实例化但未完全初始化）
    /// earlySingletonObjects
    early_singleton_objects: HashMap<TypeId, Arc<dyn Any + Send + Sync>>,
    
    /// 三级缓存：单例工厂
    /// singletonFactories
    singleton_factories: HashMap<TypeId, Box<dyn Fn() -> Arc<dyn Any + Send + Sync> + Send + Sync>>,
    
    /// 正在创建的单例标记
    /// singletonsCurrentlyInCreation
    singletons_currently_in_creation: HashMap<TypeId, bool>,
}

impl ThreeLevelCache {
    /// 获取单例对象
    pub fn get_singleton<T>(&mut self, type_id: TypeId) -> Option<Arc<T>>
    where
        T: Any + Send + Sync,
    {
        // 1. 从一级缓存获取
        if let Some(obj) = self.singleton_objects.get(&type_id) {
            return obj.clone().downcast::<T>().ok();
        }
        
        // 2. 如果正在创建中，从二级缓存获取
        if self.singletons_currently_in_creation.contains_key(&type_id) {
            if let Some(obj) = self.early_singleton_objects.get(&type_id) {
                return obj.clone().downcast::<T>().ok();
            }
            
            // 3. 从三级缓存获取工厂并创建早期引用
            if let Some(factory) = self.singleton_factories.remove(&type_id) {
                let early_obj = factory();
                self.early_singleton_objects.insert(type_id, early_obj.clone());
                return early_obj.downcast::<T>().ok();
            }
        }
        
        None
    }
    
    /// 添加单例工厂到三级缓存
    pub fn add_singleton_factory<T, F>(&mut self, factory: F)
    where
        T: Any + Send + Sync,
        F: Fn() -> Arc<T> + Send + Sync + 'static,
    {
        let type_id = TypeId::of::<T>();
        let wrapped_factory = Box::new(move || -> Arc<dyn Any + Send + Sync> {
            factory() as Arc<dyn Any + Send + Sync>
        });
        self.singleton_factories.insert(type_id, wrapped_factory);
    }
    
    /// 标记单例正在创建
    pub fn before_singleton_creation<T>(&mut self)
    where
        T: Any + Send + Sync,
    {
        let type_id = TypeId::of::<T>();
        self.singletons_currently_in_creation.insert(type_id, true);
    }
    
    /// 完成单例创建，移动到一级缓存
    pub fn after_singleton_creation<T>(&mut self, singleton: Arc<T>)
    where
        T: Any + Send + Sync,
    {
        let type_id = TypeId::of::<T>();
        
        // 移除创建标记
        self.singletons_currently_in_creation.remove(&type_id);
        
        // 从二级缓存移除
        self.early_singleton_objects.remove(&type_id);
        
        // 添加到一级缓存
        self.singleton_objects.insert(type_id, singleton as Arc<dyn Any + Send + Sync>);
    }
    
    /// 检查是否正在创建中
    pub fn is_currently_in_creation<T>(&self) -> bool
    where
        T: Any + Send + Sync,
    {
        let type_id = TypeId::of::<T>();
        self.singletons_currently_in_creation.contains_key(&type_id)
    }
}

/// 全局三级缓存实例
static GLOBAL_THREE_LEVEL_CACHE: Lazy<Mutex<ThreeLevelCache>> = Lazy::new(|| {
    Mutex::new(ThreeLevelCache::default())
});

/// 获取全局三级缓存
pub fn get_global_cache() -> &'static Mutex<ThreeLevelCache> {
    &GLOBAL_THREE_LEVEL_CACHE
}

//////////////////////////////////////////////////
/// 将 Service 组件安装到 App 中
pub trait ServiceRegistrar: Send + Sync + 'static {
    /// 将 Service 组件安装到 App 中
    fn install_service(&self, app: &mut AppBuilder) -> Result<()>;
    
    /// 获取服务优先级，数值越小优先级越高
    fn priority(&self) -> i32 {
        0 // 默认优先级
    }
    
    /// 获取服务类型ID
    fn type_id(&self) -> TypeId;
    
    /// 获取服务类型名称
    fn type_name(&self) -> &'static str;
}

inventory::collect!(&'static dyn ServiceRegistrar);

/// 自动配置
#[macro_export]
macro_rules! submit_service {
    ($ty:ident) => {
        ::lib_core::app::plugin::service::submit! {
            &$ty as &dyn ::lib_core::app::plugin::service::ServiceRegistrar
        }
    };
}

/// 查找所有 ServiceRegistrar 并将它们安装到应用中
/// 支持按优先级排序，优先级数值越小越先执行
/// 集成三级缓存机制解决循环依赖
pub fn auto_inject_service(app: &mut AppBuilder) -> Result<()> {
    // 收集所有服务注册器并按优先级排序
    let mut registrars: Vec<&dyn ServiceRegistrar> = inventory::iter::<&dyn ServiceRegistrar>()
        .map(|r| *r)
        .collect();
    
    // 添加调试信息
    debug!("Found {} services before sorting", registrars.len());
    for (i, registrar) in registrars.iter().enumerate() {
        debug!("Service {}: priority = {}, type = {}", i, registrar.priority(), registrar.type_name());
    }
    
    registrars.sort_by_key(|r| r.priority());
    
    // 排序后的调试信息
    debug!("Services after sorting by priority:");
    for (i, registrar) in registrars.iter().enumerate() {
        debug!("Service {}: priority = {}, type = {}", i, registrar.priority(), registrar.type_name());
    }
    
    // 使用三级缓存机制安装服务
    for registrar in registrars {
        install_service_with_cache(registrar, app)?;
    }
    
    Ok(())
}

/// 使用三级缓存机制安装服务
fn install_service_with_cache(registrar: &dyn ServiceRegistrar, app: &mut AppBuilder) -> Result<()> {
    let type_id = registrar.type_id();
    let type_name = registrar.type_name();
    
    // 由于我们无法直接通过 TypeId 检查组件是否存在，我们使用另一种策略：
    // 在创建服务前检查是否正在创建中，如果是则跳过
    
    // 检查是否已经在一级缓存中
    {
        let cache = get_global_cache().lock().unwrap();
        if cache.singleton_objects.contains_key(&type_id) {
            debug!("Service {} already exists in singleton cache", type_name);
            return Ok(());
        }
    }
    
    // 检查是否正在创建中（循环依赖检测和重复创建检测）
    {
        let cache = get_global_cache().lock().unwrap();
        if cache.is_currently_in_creation_by_type_id(type_id) {
            debug!("Service {} is currently being created, skipping", type_name);
            // 返回早期引用或工厂创建的对象
            return Ok(());
        }
    }
    
    // 标记正在创建
    {
        let mut cache = get_global_cache().lock().unwrap();
        cache.before_singleton_creation_by_type_id(type_id);
    }
    
    // 创建服务
    let result = registrar.install_service(app);
    
    // 完成创建标记
    {
        let mut cache = get_global_cache().lock().unwrap();
        cache.after_singleton_creation_by_type_id(type_id);
    }
    
    result
}

/// 扩展ThreeLevelCache以支持TypeId操作
impl ThreeLevelCache {
    /// 通过TypeId检查是否正在创建中
    pub fn is_currently_in_creation_by_type_id(&self, type_id: TypeId) -> bool {
        self.singletons_currently_in_creation.contains_key(&type_id)
    }
    
    /// 通过TypeId标记正在创建
    pub fn before_singleton_creation_by_type_id(&mut self, type_id: TypeId) {
        self.singletons_currently_in_creation.insert(type_id, true);
    }
    
    /// 通过TypeId完成创建
    pub fn after_singleton_creation_by_type_id(&mut self, type_id: TypeId) {
        self.singletons_currently_in_creation.remove(&type_id);
    }
}



/// 为AppBuilder实现SmartComponentRegistry
impl SmartComponentRegistry for crate::app::app::AppBuilder {
    fn get_or_create_component<T>(&mut self) -> Result<T>
    where
        T: Clone + Send + Sync + 'static,
    {
        let type_id = std::any::TypeId::of::<T>();
        let type_name = std::any::type_name::<T>();
        
        // 1. 先尝试从现有组件中获取
        if let Some(component) = self.get_component::<T>() {
            debug!("Found existing component: {}", type_name);
            return Ok(component);
        }
        
        // 2. 尝试从三级缓存获取
        {
            let mut cache = get_global_cache().lock().unwrap();
            if let Some(component) = cache.get_singleton::<T>(type_id) {
                debug!("Found component in cache: {}", type_name);
                return Ok(T::clone(&component));
            }
        }
        
        // 3. 检查是否正在创建中（循环依赖检测）
        {
            let cache = get_global_cache().lock().unwrap();
            if cache.is_currently_in_creation_by_type_id(type_id) {
                debug!("Circular dependency detected for service {}, creating early reference", type_name);
                
                // 对于循环依赖，我们需要创建一个早期引用
                // 这里暂时返回错误，让调用者知道需要延迟解析
                return Err(crate::error::AppError::ComponentNotExist(type_name));
            }
        }
        
        // 4. 查找对应的ServiceRegistrar并创建服务
        debug!("Searching for ServiceRegistrar for: {}", type_name);
        debug!("Target TypeId: {:?}", type_id);
        
        // 直接检查目标类型的 TypeId
        debug!("Direct TypeId check for target type: {:?}", std::any::TypeId::of::<T>());
        
        for registrar in inventory::iter::<&dyn ServiceRegistrar>() {
            let registrar_type_id = registrar.type_id();
            let registrar_type_name = registrar.type_name();
            debug!("Checking registrar: {} (TypeId: {:?})", registrar_type_name, registrar_type_id);
            
            // 额外的调试信息
            if registrar_type_name.contains("SysMerchantAuthorizedPermissionService") {
                debug!("Found target service registrar! TypeId match: {}", registrar_type_id == type_id);
                debug!("String name match: {}", registrar_type_name == type_name);
            }
            
            // 临时使用类型名称匹配，直到解决 TypeId 不匹配问题
            // TODO: 调查为什么所有 ServiceRegistrar 返回相同的 TypeId
            if registrar_type_name == type_name {
                debug!("Found ServiceRegistrar for {} (by name), creating service...", type_name);
                
                // 标记正在创建
                {
                    let mut cache = get_global_cache().lock().unwrap();
                    cache.before_singleton_creation_by_type_id(type_id);
                }
                
                // 创建服务
                let create_result = registrar.install_service(self);
                
                // 完成创建标记
                {
                    let mut cache = get_global_cache().lock().unwrap();
                    cache.after_singleton_creation_by_type_id(type_id);
                }
                
                // 检查创建结果
                match create_result {
                    Ok(()) => {
                        // 再次尝试获取组件
                        if let Some(component) = self.get_component::<T>() {
                            debug!("Successfully created and retrieved component: {}", type_name);
                            return Ok(component);
                        } else {
                            debug!("Component created but not found in registry: {}", type_name);
                        }
                    }
                    Err(err) => {
                        debug!("Failed to create component {}: {:?}", type_name, err);
                        return Err(err);
                    }
                }
                
                break;
            }
        }
        
        debug!("No ServiceRegistrar found for: {}", type_name);
        Err(crate::error::AppError::ComponentNotExist(type_name))
    }
}

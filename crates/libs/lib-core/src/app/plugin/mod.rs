/// 组件定义
pub mod component;
pub mod service;

pub use component::*;
pub use service::*;

use crate::app::app::AppBuilder;
use crate::error::AppError;
use crate::error::Result;
use async_trait::async_trait;
use std::{
    any::{self, Any},
    ops::Deref,
    sync::Arc,
};

/// 插件引用
#[derive(Clone)]
pub struct PluginRef(Arc<dyn Plugin>);

/// 定义的插件接口
#[async_trait]
pub trait Plugin: Any + Send + Sync {
    /// 配置添加此插件的 `App`。
    async fn build(&self, _app: &mut AppBuilder) {}

    /// 配置添加此插件的 `App`。
    /// 立即插件不会被添加到注册表中，
    /// 插件无法获取注册表中注册的组件。
    fn immediately_build(&self, _app: &mut AppBuilder) {}

    /// 为 [`Plugin`] 配置名称，主要用于检查插件
    /// 唯一性和调试。
    fn name(&self) -> &str {
        std::any::type_name::<Self>()
    }

    /// 要依赖的插件列表。该插件将在此列表中的插件之后构建。
    fn dependencies(&self) -> Vec<&str> {
        vec![]
    }

    /// 插件是否应该在添加时立即构建
    fn immediately(&self) -> bool {
        false
    }
}

impl PluginRef {
    pub(crate) fn new<T: Plugin>(plugin: T) -> Self {
        Self(Arc::new(plugin))
    }
}

impl Deref for PluginRef {
    type Target = dyn Plugin;

    fn deref(&self) -> &Self::Target {
        &*self.0
    }
}

/// 组件注册表
pub trait ComponentRegistry {
    /// 获取指定类型的组件引用
    fn get_component_ref<T>(&self) -> Option<ComponentRef<T>>
    where
        T: Any + Send + Sync;

    /// 获取指定类型的组件引用。
    /// 如果组件不存在，将会恐慌。
    fn get_expect_component_ref<T>(&self) -> ComponentRef<T>
    where
        T: Clone + Send + Sync + 'static,
    {
        self.get_component_ref().unwrap_or_else(|| {
            panic!(
                "{} component not exists in registry",
                std::any::type_name::<T>()
            )
        })
    }

    /// 获取指定类型的组件引用。
    /// 如果组件不存在，将返回 AppError::ComponentNotExist。
    fn try_get_component_ref<T>(&self) -> Result<ComponentRef<T>>
    where
        T: Clone + Send + Sync + 'static,
    {
        self.get_component_ref()
            .ok_or_else(|| AppError::ComponentNotExist(std::any::type_name::<T>()))
    }

    /// 获取指定类型的组件
    fn get_component<T>(&self) -> Option<T>
    where
        T: Clone + Send + Sync + 'static;

    /// 获取指定类型的组件。
    /// 如果组件不存在，将会恐慌。
    fn get_expect_component<T>(&self) -> T
    where
        T: Clone + Send + Sync + 'static,
    {
        self.get_component().unwrap_or_else(|| {
            panic!(
                "{} component not exists in registry",
                std::any::type_name::<T>()
            )
        })
    }

    /// 获取指定类型的组件。
    /// 如果组件不存在，将返回 AppError::ComponentNotExist。
    fn try_get_component<T>(&self) -> Result<T>
    where
        T: Clone + Send + Sync + 'static,
    {
        self.get_component()
            .ok_or_else(|| AppError::ComponentNotExist(std::any::type_name::<T>()))
    }

    /// 注册表中是否存在指定类型的组件？
    fn has_component<T>(&self) -> bool
    where
        T: Any + Send + Sync;
}

/// 可变组件注册表
pub trait MutableComponentRegistry: ComponentRegistry {
    /// 将组件添加到注册表
    fn add_component<C>(&mut self, component: C) -> &mut Self
    where
        C: Clone + any::Any + Send + Sync;
}

use crate::app::plugin::component::ComponentRef;
use crate::app::plugin::component::DynComponentRef;
use crate::app::plugin::{service, ComponentRegistry, MutableComponentRegistry, Plugin, PluginRef};
use crate::config::env::Env;
use crate::config::toml::TomlConfigRegistry;
use crate::config::ConfigRegistry;
use crate::error::Result;
use crate::logger::BoxLayer;
use crate::logger::LogPlugin;
use dashmap::DashMap;
use once_cell::sync::Lazy;
use std::any::{Any, TypeId};
use std::str::FromStr;
use std::sync::RwLock;
use std::{collections::HashSet, future::Future, path::Path, sync::Arc};
use tracing::log;
use tracing_subscriber::Layer;

type Registry<T> = DashMap<TypeId, T>;
type Scheduler<T> = dyn FnOnce(Arc<App>) -> Box<dyn Future<Output = Result<T>> + Send>;

/// 运行中的应用程序
#[derive(Default)]
pub struct App {
    env: Env,
    /// 组件
    components: Registry<DynComponentRef>,
    config: TomlConfigRegistry,
}

/// AppBuilder：正在构建的应用程序
/// 应用程序由三个重要部分组成：
/// - 插件管理
/// - 组件管理
/// - 配置管理
pub struct AppBuilder {
    pub(crate) env: Env,
    /// 追踪层
    pub(crate) layers: Vec<BoxLayer>,
    /// 插件
    pub(crate) plugin_registry: Registry<PluginRef>,
    /// 组件
    components: Registry<DynComponentRef>,
    /// 从 `config_path` 读取的配置
    config: TomlConfigRegistry,
    /// 任务
    schedulers: Vec<Box<Scheduler<String>>>,
    shutdown_hooks: Vec<Box<Scheduler<String>>>,
}

impl App {
    /// 准备构建应用程序
    #[allow(clippy::new_ret_no_self)]
    pub fn new() -> AppBuilder {
        AppBuilder::default()
    }

    /// 当前活跃的环境
    /// * [Env]
    pub fn get_env(&self) -> Env {
        self.env
    }

    /// 返回当前配置的全局 [`App`] 实例。
    ///
    /// **注意**：这个全局App在应用程序构建后初始化，
    /// 请在应用程序运行时使用它，不要在构建过程中使用，
    /// 比如在插件构建过程中。
    pub fn global() -> Arc<App> {
        GLOBAL_APP
            .read()
            .expect("GLOBAL_APP RwLock poisoned")
            .clone()
    }

    fn set_global(app: Arc<App>) {
        let mut global_app = GLOBAL_APP.write().expect("GLOBAL_APP RwLock poisoned");
        *global_app = app;
    }
}

static GLOBAL_APP: Lazy<RwLock<Arc<App>>> = Lazy::new(|| RwLock::new(Arc::new(App::default())));

unsafe impl Send for AppBuilder {}
unsafe impl Sync for AppBuilder {}

impl AppBuilder {
    /// 智能推断配置文件路径
    /// 从当前执行的二进制文件位置查找 config 目录
    fn infer_config_path() -> String {
        let Ok(exe_path) = std::env::current_exe() else {
            return "./config/app.toml".to_string();
        };

        let Some(project_root) = Self::find_project_root(&exe_path) else {
            return "./config/app.toml".to_string();
        };

        // exe_name = test-app
        if let Some(exe_name) = exe_path.file_stem().and_then(|s| s.to_str()) {
            // 优先查找服务特定的config目录：crates/services/{service_name}/config/
            let service_config_path = format!(
                "{}/crates/services/{}/config/app.toml",
                project_root, exe_name
            );
            if Path::new(&service_config_path).exists() {
                return service_config_path;
            }
        }

        // 查找项目根目录的config目录
        let root_config_path = format!("{}/config/app.toml", project_root);
        if Path::new(&root_config_path).exists() {
            return root_config_path;
        }

        // 最后的回退选项
        "./config/app.toml".to_string()
    }

    /// 从二进制文件路径向上查找项目根目录
    /// 项目根目录的特征：包含 Cargo.toml 文件
    fn find_project_root(exe_path: &Path) -> Option<String> {
        let mut current = exe_path.parent()?;

        // 向上查找最多5层目录
        for _ in 0..5 {
            if current.join("Cargo.toml").exists() {
                return current.to_str().map(String::from);
            }
            current = current.parent()?;
        }

        None
    }

    /// 当前活跃的环境
    /// * [Env]
    #[inline]
    pub fn get_env(&self) -> Env {
        self.env
    }

    /// 添加插件
    pub fn add_plugin<T: Plugin>(&mut self, plugin: T) -> &mut Self {
        log::debug!("added plugin: {}", plugin.name());
        if plugin.immediately() {
            plugin.immediately_build(self);
            return self;
        }
        let plugin_id = TypeId::of::<T>();
        if self.plugin_registry.contains_key(&plugin_id) {
            let plugin_name = plugin.name();
            panic!("Error adding plugin {plugin_name}: plugin was already added in application")
        }
        self.plugin_registry
            .insert(plugin_id, PluginRef::new(plugin));
        self
    }

    /// 如果 [`Plugin`] 已经被添加，返回 `true`。
    #[inline]
    pub fn is_plugin_added<T: Plugin>(&self) -> bool {
        self.plugin_registry.contains_key(&TypeId::of::<T>())
    }

    /// 配置文件的路径，默认是 `./config/app.toml`。
    /// 应用程序根据 `APP_ENY` 环境变量自动读取
    /// 同目录下的环境配置文件，
    /// 如 `./config/app-dev.toml`。
    /// 环境配置文件具有更高的优先级，将
    /// 覆盖主配置文件的配置项。
    pub fn use_config_file(&mut self, config_path: &str) -> &mut Self {
        self.config = TomlConfigRegistry::new(Path::new(config_path), self.env)
            .expect("config file load failed");
        self
    }

    /// 使用现有的toml字符串来配置应用程序。
    /// 例如，使用 include_str!('app.toml') 将文件编译到程序中。
    ///
    /// **注意**：此配置方法仅支持一个配置内容，不支持多环境。
    pub fn use_config_str(&mut self, toml_content: &str) -> &mut Self {
        self.config =
            TomlConfigRegistry::from_str(toml_content).expect("config content parse failed");
        self
    }

    /// 添加 [tracing_subscriber::layer]
    pub fn add_layer<L>(&mut self, layer: L) -> &mut Self
    where
        L: Layer<tracing_subscriber::Registry> + Send + Sync + 'static,
    {
        self.layers.push(Box::new(layer));
        self
    }

    /// 添加计划任务
    pub fn add_scheduler<T>(&mut self, scheduler: T) -> &mut Self
    where
        T: FnOnce(Arc<App>) -> Box<dyn Future<Output = Result<String>> + Send> + 'static,
    {
        self.schedulers.push(Box::new(scheduler));
        self
    }

    /// 添加关闭钩子
    pub fn add_shutdown_hook<T>(&mut self, hook: T) -> &mut Self
    where
        T: FnOnce(Arc<App>) -> Box<dyn Future<Output = Result<String>> + Send> + 'static,
    {
        self.shutdown_hooks.push(Box::new(hook));
        self
    }

    /// `run` 方法适用于包含调度逻辑的应用程序，
    /// 如 web、job 和
    pub async fn run(&mut self) {
        match self.inner_run().await {
            Err(e) => {
                log::error!("{:?}", e);
            }
            _ => { /* ignore */ }
        }
    }

    async fn inner_run(&mut self) -> Result<()> {
        // 1. 构建插件
        self.build_plugins().await;

        // 2. 服务依赖注入
        service::auto_inject_service(self)?;

        // 3. 调度
        self.schedule().await
    }

    /// 与 [`run`] 方法不同，`build` 方法适用于不包含调度逻辑的应用程序。
    /// 此方法返回构建的App，开发者可以自己实现命令行和任务调度等逻辑。
    pub async fn build(&mut self) -> Result<Arc<App>> {
        // 1. 构建插件
        self.build_plugins().await;

        // 2. 服务依赖注入
        service::auto_inject_service(self)?;

        Ok(self.build_app())
    }

    async fn build_plugins(&mut self) {
        LogPlugin.immediately_build(self);

        let registry = std::mem::take(&mut self.plugin_registry);
        let mut to_register = registry
            .iter()
            .map(|e| e.value().to_owned())
            .collect::<Vec<_>>();
        let mut registered: HashSet<String> = HashSet::new();

        while !to_register.is_empty() {
            let mut progress = false;
            let mut next_round = vec![];

            for plugin in to_register {
                let deps = plugin.dependencies();
                if deps.iter().all(|dep| registered.contains(*dep)) {
                    plugin.build(self).await;
                    registered.insert(plugin.name().to_string());
                    log::info!("{} plugin registered", plugin.name());
                    progress = true;
                } else {
                    next_round.push(plugin);
                }
            }

            if !progress {
                panic!("Cyclic dependency detected or missing dependencies for some plugins");
            }

            to_register = next_round;
        }
        self.plugin_registry = registry;
    }

    async fn schedule(&mut self) -> Result<()> {
        let app = self.build_app();

        let schedulers = std::mem::take(&mut self.schedulers);
        let mut handles = vec![];
        for task in schedulers {
            let poll_future = task(app.clone());
            let poll_future = Box::into_pin(poll_future);
            handles.push(tokio::spawn(poll_future));
        }

        while let Some(handle) = handles.pop() {
            match handle.await? {
                Err(e) => log::error!("{:?}", e),
                Ok(msg) => log::info!("scheduled result: {}", msg),
            }
        }

        // FILO：先构建的插件添加的钩子应该后执行
        while let Some(hook) = self.shutdown_hooks.pop() {
            let result = Box::into_pin(hook(app.clone())).await?;
            log::info!("shutdown result: {result}");
        }
        Ok(())
    }

    fn build_app(&mut self) -> Arc<App> {
        let components = std::mem::take(&mut self.components);
        let config = std::mem::take(&mut self.config);
        let app = Arc::new(App {
            env: self.env,
            components,
            config,
        });
        App::set_global(app.clone());
        app
    }
}

impl Default for AppBuilder {
    fn default() -> Self {
        let env = Env::init();
        log::info!("App env: {:?}", env);
        // let config = TomlConfigRegistry::new(Path::new("./crates/services/config/app.toml"), env).unwrap();
        // crates/services/test-app/config/app.toml
        // let config = TomlConfigRegistry::new(Path::new("crates/services/test-app/config/app.toml"), env).unwrap();
        // 智能推断配置文件路径
        let config_path = Self::infer_config_path();
        log::debug!("Inferred config path: {}", config_path);

        let config =
            TomlConfigRegistry::new(Path::new(&config_path), env).expect("toml config load failed");
        Self {
            env,
            config,
            layers: Default::default(),
            plugin_registry: Default::default(),
            components: Default::default(),
            schedulers: Default::default(),
            shutdown_hooks: Default::default(),
        }
    }
}

impl ConfigRegistry for App {
    fn get_config<T>(&self) -> Result<T>
    where
        T: serde::de::DeserializeOwned + crate::config::Configurable,
    {
        self.config.get_config::<T>()
    }
}

impl ConfigRegistry for AppBuilder {
    fn get_config<T>(&self) -> Result<T>
    where
        T: serde::de::DeserializeOwned + crate::config::Configurable,
    {
        self.config.get_config::<T>()
    }
}

macro_rules! impl_component_registry {
    ($ty:ident) => {
        impl ComponentRegistry for $ty {
            fn get_component_ref<T>(&self) -> Option<ComponentRef<T>>
            where
                T: Any + Send + Sync,
            {
                let component_id = TypeId::of::<T>();
                let pair = self.components.get(&component_id)?;
                let component_ref = pair.value().clone();
                component_ref.downcast::<T>()
            }

            fn get_component<T>(&self) -> Option<T>
            where
                T: Clone + Send + Sync + 'static,
            {
                let component_ref = self.get_component_ref();
                component_ref.map(|arc| T::clone(&arc))
            }

            fn has_component<T>(&self) -> bool
            where
                T: Any + Send + Sync,
            {
                let component_id = TypeId::of::<T>();
                self.components.contains_key(&component_id)
            }
        }
    };
}

impl_component_registry!(App);
impl_component_registry!(AppBuilder);

impl MutableComponentRegistry for AppBuilder {
    /// 将组件添加到注册表
    fn add_component<C>(&mut self, component: C) -> &mut Self
    where
        C: Clone + Any + Send + Sync,
    {
        let component_id = TypeId::of::<C>();
        let component_name = std::any::type_name::<C>();
        log::debug!("added component: {}", component_name);
        if self.components.contains_key(&component_id) {
            log::debug!("Component {} already exists, skipping", component_name);
            return self;
        }
        self.components
            .insert(component_id, DynComponentRef::new(component));
        self
    }
}

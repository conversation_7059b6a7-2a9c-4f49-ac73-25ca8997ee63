use chrono::{DateTime, Local, Utc};

/// 简化的日期时间工具类
/// 专门用于JWT和认证相关的时间操作
pub struct DateTimeUtils;

impl DateTimeUtils {
    /// 获取当前UTC时间
    pub fn now_utc() -> DateTime<Utc> {
        Utc::now()
    }

    /// 获取当前本地时间
    pub fn now_local() -> DateTime<Local> {
        Local::now()
    }

    /// 时间戳转换为本地时间
    pub fn timestamp_to_local(timestamp: i64) -> Result<DateTime<Local>, &'static str> {
        let utc_dt = DateTime::from_timestamp(timestamp, 0)
            .ok_or("无效的时间戳")?;
        Ok(utc_dt.with_timezone(&Local))
    }

    /// 本地时间转换为时间戳
    pub fn local_to_timestamp(dt: &DateTime<Local>) -> i64 {
        dt.timestamp()
    }

    /// 格式化为标准日期时间字符串 (YYYY-MM-DD HH:MM:SS)
    pub fn format_datetime(dt: &DateTime<Local>) -> String {
        dt.format("%Y-%m-%d %H:%M:%S").to_string()
    }

    /// 格式化为日期字符串 (YYYY-MM-DD)
    pub fn format_date(dt: &DateTime<Local>) -> String {
        dt.format("%Y-%m-%d").to_string()
    }

    /// 格式化为时间字符串 (HH:MM:SS)
    pub fn format_time(dt: &DateTime<Local>) -> String {
        dt.format("%H:%M:%S").to_string()
    }
} 
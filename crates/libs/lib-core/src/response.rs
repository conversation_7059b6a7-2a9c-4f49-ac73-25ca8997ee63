use axum::{
    http::StatusCode,
    response::{IntoResponse, Json, Response},
};
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use tracing::error;
use utoipa::schema;
use utoipa::ToSchema;

/// 通用API响应结构体
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(
    example = json!({
        "code": 200,
        "message": "操作成功",
        "data": "具体数据内容",
        "timestamp": 1684903200
    })
)]
pub struct ApiResult<T>
where
    T: ToSchema,
{
    /// 响应状态码
    pub code: i32,
    /// 响应消息
    pub message: String,
    /// 响应数据
    pub data: Option<T>,
    /// 时间戳（可选）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub timestamp: Option<i64>,
}
/// 为ApiResult实现From trait，支持从Result转换
impl<T, E: std::fmt::Display> From<Result<T, E>> for ApiResult<T>
where
    T: Serialize + ToSchema,
{
    fn from(result: Result<T, E>) -> Self {
        match result {
            Ok(data) => ApiResult::success(data),
            Err(e) => ApiResult::error(500, e.to_string()),
        }
    }
}

/// 为ApiResult提供便利方法支持链式调用
impl<T: ToSchema> ApiResult<T> {
    /// 将Result<T, E>转换为ApiResult<T>，并自定义错误码和消息
    pub fn from_result<E: std::fmt::Display>(
        result: Result<T, E>,
        error_code: i32,
        error_message: impl Into<String>,
    ) -> Self
    where
        T: Serialize + ToSchema,
    {
        match result {
            Ok(data) => ApiResult::success(data),
            Err(_) => ApiResult::error(error_code, error_message.into()),
        }
    }

    /// 将Result<T, E>转换为ApiResult<T>，使用自定义错误消息格式化函数
    pub fn from_result_with_formatter<E>(
        result: Result<T, E>,
        error_formatter: impl FnOnce(E) -> (i32, String),
    ) -> Self
    where
        T: Serialize + ToSchema,
    {
        match result {
            Ok(data) => ApiResult::success(data),
            Err(e) => {
                let (code, message) = error_formatter(e);
                ApiResult::error(code, message)
            }
        }
    }

    /// 检查是否为成功响应
    pub fn is_success(&self) -> bool
    where
        T: ToSchema,
    {
        self.code == 200
    }

    /// 检查是否为错误响应
    pub fn is_error(&self) -> bool
    where
        T: ToSchema,
    {
        self.code != 200
    }

    /// 获取数据，如果是错误则返回None
    pub fn data(self) -> Option<T>
    where
        T: ToSchema,
    {
        if self.is_success() { self.data } else { None }
    }

    /// 获取数据，如果是错误则使用默认值
    pub fn data_or(self, default: T) -> T
    where
        T: ToSchema,
    {
        if self.is_success() {
            self.data.unwrap_or(default)
        } else {
            default
        }
    }

    /// 将ApiResult<T>转换为ApiResult<U>，保留错误信息
    pub fn into_result<U>(self) -> ApiResult<U>
    where
        U: ToSchema,
    {
        ApiResult {
            code: self.code,
            message: self.message,
            data: None, // 类型转换时丢弃数据
            timestamp: self.timestamp,
        }
    }
}

impl<T> ApiResult<T>
where
    T: Serialize + ToSchema,
{
    /// 创建成功响应（无时间戳）
    pub fn success(data: T) -> Self {
        Self {
            code: 200,
            message: "success".to_string(),
            data: Some(data),
            timestamp: None,
        }
    }

    /// 创建成功响应（带时间戳）
    pub fn success_with_timestamp(data: T) -> Self {
        Self {
            code: 200,
            message: "success".to_string(),
            data: Some(data),
            timestamp: Some(chrono::Utc::now().timestamp()),
        }
    }

    /// 创建成功响应，带自定义消息（无时间戳）
    pub fn success_with_message_and_data(data: T, message: String) -> Self {
        Self {
            code: 200,
            message,
            data: Some(data),
            timestamp: None,
        }
    }
    /// 创建成功响应，带自定义消息
    pub fn success_with_message(message: String) -> Self {
        Self {
            code: 200,
            message,
            data: None,
            timestamp: None,
        }
    }

    /// 创建成功响应，带自定义消息和时间戳
    pub fn success_with_message_and_timestamp(data: T, message: String) -> Self {
        Self {
            code: 200,
            message,
            data: Some(data),
            timestamp: Some(chrono::Utc::now().timestamp()),
        }
    }

    /// 创建错误响应（无时间戳）
    pub fn error(code: i32, message: String) -> ApiResult<T> {
        ApiResult {
            code,
            message,
            data: None,
            timestamp: None,
        }
    }

    /// 创建错误响应（带时间戳）
    pub fn error_with_timestamp(code: i32, message: String) -> ApiResult<T> {
        ApiResult {
            code,
            message,
            data: None,
            timestamp: Some(chrono::Utc::now().timestamp()),
        }
    }

    /// 创建错误响应，带数据（无时间戳）
    pub fn error_with_data(code: i32, message: String, data: T) -> Self {
        Self {
            code,
            message,
            data: Some(data),
            timestamp: None,
        }
    }

    /// 创建错误响应，带数据和时间戳
    pub fn error_with_data_and_timestamp(code: i32, message: String, data: T) -> Self {
        Self {
            code,
            message,
            data: Some(data),
            timestamp: Some(chrono::Utc::now().timestamp()),
        }
    }

    /// 添加时间戳到现有响应
    pub fn with_timestamp(mut self) -> Self {
        self.timestamp = Some(chrono::Utc::now().timestamp());
        self
    }

    /// 移除时间戳
    pub fn without_timestamp(mut self) -> Self {
        self.timestamp = None;
        self
    }
}

/// 为空数据类型实现特殊方法
impl ApiResult<()> {
    /// 创建仅包含消息的成功响应（无时间戳）
    pub fn ok(message: String) -> Self {
        Self {
            code: 200,
            message,
            data: None,
            timestamp: None,
        }
    }

    /// 创建仅包含消息的成功响应（带时间戳）
    pub fn ok_with_timestamp(message: String) -> Self {
        Self {
            code: 200,
            message,
            data: None,
            timestamp: Some(chrono::Utc::now().timestamp()),
        }
    }

    /// 创建简单的成功响应（无时间戳）
    pub fn ok_simple() -> Self {
        Self {
            code: 200,
            message: "操作成功".to_string(),
            data: None,
            timestamp: None,
        }
    }

    /// 创建简单的成功响应（带时间戳）
    pub fn ok_simple_with_timestamp() -> Self {
        Self {
            code: 200,
            message: "操作成功".to_string(),
            data: None,
            timestamp: Some(chrono::Utc::now().timestamp()),
        }
    }
}

/// 状态码映射宏 - 生成精确的状态码映射逻辑
macro_rules! status_code_mapping {
    (
        $(
            $code:literal => $status:ident,
        )*
    ) => {
        fn map_status_code(code: i32) -> StatusCode {
            match code {
                $(
                    $code => StatusCode::$status,
                )*
                // 其他状态码的处理
                200..=299 => StatusCode::OK,
                400..=499 => StatusCode::from_u16(code as u16).unwrap_or(StatusCode::BAD_REQUEST),
                500..=599 => StatusCode::from_u16(code as u16).unwrap_or(StatusCode::INTERNAL_SERVER_ERROR),
                _ => StatusCode::OK,
            }
        }
    };
}

// 使用宏生成状态码映射函数
status_code_mapping! {
    // 2xx 成功状态码
    200 => OK,
    201 => CREATED,
    202 => ACCEPTED,
    204 => NO_CONTENT,
    206 => PARTIAL_CONTENT,

    // 3xx 重定向状态码
    301 => MOVED_PERMANENTLY,
    302 => FOUND,
    304 => NOT_MODIFIED,
    307 => TEMPORARY_REDIRECT,
    308 => PERMANENT_REDIRECT,

    // 4xx 客户端错误状态码
    400 => BAD_REQUEST,
    401 => UNAUTHORIZED,
    402 => PAYMENT_REQUIRED,
    403 => FORBIDDEN,
    404 => NOT_FOUND,
    405 => METHOD_NOT_ALLOWED,
    406 => NOT_ACCEPTABLE,
    408 => REQUEST_TIMEOUT,
    409 => CONFLICT,
    410 => GONE,
    411 => LENGTH_REQUIRED,
    412 => PRECONDITION_FAILED,
    413 => PAYLOAD_TOO_LARGE,
    414 => URI_TOO_LONG,
    415 => UNSUPPORTED_MEDIA_TYPE,
    416 => RANGE_NOT_SATISFIABLE,
    417 => EXPECTATION_FAILED,
    418 => IM_A_TEAPOT,
    421 => MISDIRECTED_REQUEST,
    422 => UNPROCESSABLE_ENTITY,
    423 => LOCKED,
    424 => FAILED_DEPENDENCY,
    425 => TOO_EARLY,
    426 => UPGRADE_REQUIRED,
    428 => PRECONDITION_REQUIRED,
    429 => TOO_MANY_REQUESTS,
    431 => REQUEST_HEADER_FIELDS_TOO_LARGE,
    451 => UNAVAILABLE_FOR_LEGAL_REASONS,

    // 5xx 服务器错误状态码
    500 => INTERNAL_SERVER_ERROR,
    501 => NOT_IMPLEMENTED,
    502 => BAD_GATEWAY,
    503 => SERVICE_UNAVAILABLE,
    504 => GATEWAY_TIMEOUT,
    505 => HTTP_VERSION_NOT_SUPPORTED,
    506 => VARIANT_ALSO_NEGOTIATES,
    507 => INSUFFICIENT_STORAGE,
    508 => LOOP_DETECTED,
    510 => NOT_EXTENDED,
    511 => NETWORK_AUTHENTICATION_REQUIRED,
}

/// 实现IntoResponse trait，使ApiResult可以直接作为响应返回
impl<T> IntoResponse for ApiResult<T>
where
    T: Serialize + ToSchema,
{
    fn into_response(self) -> Response {
        let status = map_status_code(self.code);

        // 对服务器错误进行日志记录
        if self.code >= 500 {
            error!("Internal Server Error: {}", self.message);
        }

        (status, Json(self)).into_response()
    }
}
/// 常用错误码定义
pub mod codes {
    /// 成功
    pub const SUCCESS: i32 = 200;
    /// 参数错误
    pub const BAD_REQUEST: i32 = 400;
    /// 未认证
    pub const UNAUTHORIZED: i32 = 401;
    /// 权限不足
    pub const FORBIDDEN: i32 = 403;
    /// 资源不存在
    pub const NOT_FOUND: i32 = 404;
    /// 请求方法不允许
    pub const METHOD_NOT_ALLOWED: i32 = 405;
    /// 请求冲突
    pub const CONFLICT: i32 = 409;
    /// 服务器内部错误
    pub const INTERNAL_SERVER_ERROR: i32 = 500;
    /// 服务不可用
    pub const SERVICE_UNAVAILABLE: i32 = 503;
}

/// 快速响应构建器
pub struct ResponseBuilder;

impl ResponseBuilder {
    /// 成功响应（无时间戳）
    pub fn ok<T: Serialize + ToSchema>(data: T) -> ApiResult<T> {
        ApiResult::success(data)
    }

    /// 成功响应（带时间戳）
    pub fn ok_with_timestamp<T: Serialize + ToSchema>(data: T) -> ApiResult<T> {
        ApiResult::success_with_timestamp(data)
    }

    /// 成功响应，带消息（无时间戳）
    pub fn ok_with_msg<T: Serialize + ToSchema>(data: T, message: &str) -> ApiResult<T> {
        ApiResult::success_with_message_and_data(data, message.to_string())
    }

    /// 成功响应，带消息和时间戳
    pub fn ok_with_msg_and_timestamp<T: Serialize + ToSchema>(
        data: T,
        message: &str,
    ) -> ApiResult<T> {
        ApiResult::success_with_message_and_timestamp(data, message.to_string())
    }

    /// 简单成功（无时间戳）
    pub fn ok_simple() -> ApiResult<()> {
        ApiResult::ok_simple()
    }

    /// 简单成功（带时间戳）
    pub fn ok_simple_with_timestamp() -> ApiResult<()> {
        ApiResult::ok_simple_with_timestamp()
    }

    /// 成功消息（无时间戳）
    pub fn ok_msg(message: &str) -> ApiResult<()> {
        ApiResult::ok(message.to_string())
    }

    /// 成功消息（带时间戳）
    pub fn ok_msg_with_timestamp(message: &str) -> ApiResult<()> {
        ApiResult::ok_with_timestamp(message.to_string())
    }

    /// 参数错误（无时间戳）
    pub fn bad_request(message: &str) -> ApiResult<()> {
        ApiResult::<()>::error(codes::BAD_REQUEST, message.to_string())
    }

    /// 参数错误（带时间戳）
    pub fn bad_request_with_timestamp(message: &str) -> ApiResult<()> {
        ApiResult::<()>::error_with_timestamp(codes::BAD_REQUEST, message.to_string())
    }

    /// 未认证（无时间戳）
    pub fn unauthorized(message: &str) -> ApiResult<()> {
        ApiResult::<()>::error(codes::UNAUTHORIZED, message.to_string())
    }

    /// 未认证（带时间戳）
    pub fn unauthorized_with_timestamp(message: &str) -> ApiResult<()> {
        ApiResult::<()>::error_with_timestamp(codes::UNAUTHORIZED, message.to_string())
    }

    /// 权限不足（无时间戳）
    pub fn forbidden(message: &str) -> ApiResult<()> {
        ApiResult::<()>::error(codes::FORBIDDEN, message.to_string())
    }

    /// 权限不足（带时间戳）
    pub fn forbidden_with_timestamp(message: &str) -> ApiResult<()> {
        ApiResult::<()>::error_with_timestamp(codes::FORBIDDEN, message.to_string())
    }

    /// 资源不存在（无时间戳）
    pub fn not_found(message: &str) -> ApiResult<()> {
        ApiResult::<()>::error(codes::NOT_FOUND, message.to_string())
    }

    /// 资源不存在（带时间戳）
    pub fn not_found_with_timestamp(message: &str) -> ApiResult<()> {
        ApiResult::<()>::error_with_timestamp(codes::NOT_FOUND, message.to_string())
    }

    /// 服务器错误（无时间戳）
    pub fn internal_error(message: &str) -> ApiResult<()> {
        ApiResult::<()>::error(codes::INTERNAL_SERVER_ERROR, message.to_string())
    }

    /// 服务器错误（带时间戳）
    pub fn internal_error_with_timestamp(message: &str) -> ApiResult<()> {
        ApiResult::<()>::error_with_timestamp(codes::INTERNAL_SERVER_ERROR, message.to_string())
    }

    /// 自定义错误（无时间戳）
    pub fn error(code: i32, message: &str) -> ApiResult<()> {
        ApiResult::<()>::error(code, message.to_string())
    }

    /// 自定义错误（带时间戳）
    pub fn error_with_timestamp(code: i32, message: &str) -> ApiResult<()> {
        ApiResult::<()>::error_with_timestamp(code, message.to_string())
    }
}

/// 分页数据响应
#[derive(Debug, Clone, Serialize, Deserialize, ToSchema)]
#[schema(
    example = json!({
        "items": ["数据项列表"],
        "total": 100,
        "page": 1,
        "page_size": 10,
        "total_pages": 10
    })
)]
pub struct PageData<T: ToSchema> {
    /// 数据列表
    pub items: Vec<T>,
    /// 总数量
    #[schema(example = 100)]
    pub total: u64,
    /// 当前页码
    #[schema(example = 1)]
    pub page: u64,
    /// 每页大小
    #[schema(example = 10)]
    pub page_size: u64,
    /// 总页数
    #[schema(example = 10)]
    pub total_pages: u64,
}

impl<T: ToSchema> PageData<T> {
    pub fn new(items: Vec<T>, total: u64, page: u64, page_size: u64) -> Self {
        let total_pages = if total == 0 {
            0
        } else {
            (total + page_size - 1) / page_size
        };

        Self {
            items,
            total,
            page,
            page_size,
            total_pages,
        }
    }
}

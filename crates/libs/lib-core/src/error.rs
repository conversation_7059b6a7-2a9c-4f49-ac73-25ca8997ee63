//! 错误处理模块
//! 
//! 这个模块定义了整个应用程序的错误类型和错误处理机制，包括：
//! - 统一的应用程序错误类型 `AppError`
//! - HTTP 响应转换
//! - 错误码映射
//! - 便利的构造方法
//! - 类型别名定义

use crate::response::codes;
use crate::ApiResult;
use axum::response::{IntoResponse, Response};
use axum::Json;
use http::StatusCode;
use std::io::{self, ErrorKind};
use thiserror::Error;

/// 应用程序错误类型
/// 
/// 这个枚举定义了应用程序中可能发生的所有错误类型，
/// 每种错误都有相应的错误消息和处理逻辑
#[derive(Error, Debug)]
pub enum AppError {
    /// 系统内部错误
    /// 
    /// 用于表示应用程序内部逻辑错误或不可预期的错误情况
    #[error("{0} server internal err")]
    Internal(String),
    
    /// 组件不存在错误
    /// 
    /// 当尝试访问未注册或不存在的组件时触发此错误
    #[error("{0} component not exists")]
    ComponentNotExist(&'static str),

    /// `.env` 文件读取失败
    /// 
    /// 当无法读取或解析环境变量文件时发生
    #[error(transparent)]
    EnvError(#[from] dotenvy::Error),

    /// 文件 IO 错误
    /// 
    /// 包装标准库的 IO 错误，用于文件读写操作失败的情况
    #[error(transparent)]
    IOError(#[from] io::Error),

    /// TOML 文件解析错误
    /// 
    /// 当配置文件格式不正确或无法解析时发生
    #[error(transparent)]
    TomlParseError(#[from] toml::de::Error),

    /// TOML 文件配置合并错误
    /// 
    /// 当多个配置文件合并时发生冲突或格式不兼容时触发
    #[error("merge toml error: {0}")]
    TomlMergeError(String),

    /// Tokio 异步任务连接失败
    /// 
    /// 当异步任务执行失败或被取消时发生
    #[error(transparent)]
    JoinError(#[from] tokio::task::JoinError),

    /// TOML 文件中配置反序列化到 Rust 结构体失败
    /// 
    /// 当配置文件的结构与定义的 Rust 结构体不匹配时发生
    #[error("Failed to deserialize the configuration of prefix \"{0}\": {1}")]
    DeserializeErr(&'static str, toml::de::Error),

    /// 其他运行时错误
    /// 
    /// 包装 anyhow::Error，用于处理其他未明确分类的错误
    #[error(transparent)]
    OtherError(#[from] anyhow::Error),
}

/// 为 AppError 实现 IntoResponse trait
/// 
/// 这个实现将应用程序错误转换为 HTTP 响应，包括：
/// - 错误状态码映射
/// - 错误消息本地化
/// - 日志记录
/// - 统一的 API 响应格式
impl IntoResponse for AppError {
    fn into_response(self) -> Response {
        // 根据错误类型决定状态码、错误码和消息
        let (status_code, error_code, message) = match &self {
            // 内部错误 - 记录为 error 级别日志
            Self::Internal(_) => {
                tracing::error!("{}", self);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    codes::INTERNAL_SERVER_ERROR,
                    "系统内部错误",
                )
            }
            // 其他错误 - 记录为 warn 级别日志
            _ => {
                tracing::warn!("{}", self);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    codes::INTERNAL_SERVER_ERROR,
                    "服务器内部错误",
                )
            }
        };

        // 构建统一的 API 响应格式
        let api_result: ApiResult<()> = ApiResult::error(error_code, message.to_string());
        (status_code, Json(api_result)).into_response()
    }
}

/// AppError 的便利构造方法实现
/// 
/// 提供了创建常见错误类型的简便方法
impl AppError {
    /// 创建内部错误
    /// 
    /// 参数:
    /// - `msg`: 错误消息，可以是任何能够转换为 String 的类型
    /// 
    /// 返回: AppError::Internal 变体
    pub fn internal<T: Into<String>>(msg: T) -> Self {
        Self::Internal(msg.into())
    }
}

/// AppError 的 IO 错误相关方法
impl AppError {
    /// 从指定的 IO 错误类型和消息创建 AppError
    /// 
    /// 参数:
    /// - `kind`: IO 错误类型
    /// - `msg`: 错误消息
    /// 
    /// 返回: 包装了 IO 错误的 AppError
    /// 
    /// # 示例
    /// ```
    /// use std::io::ErrorKind;
    /// let error = AppError::from_io(ErrorKind::NotFound, "配置文件未找到");
    /// ```
    pub fn from_io(kind: ErrorKind, msg: &str) -> Self {
        AppError::IOError(io::Error::new(kind, msg))
    }
}

/// 应用程序结果类型别名
/// 
/// 这是一个便利的类型别名，将 `std::result::Result<T, AppError>` 简化为 `Result<T>`
/// 在整个应用程序中统一使用此类型来处理可能的错误情况
/// 
/// # 用法
/// ```
/// use crate::error::Result;
/// 
/// fn some_operation() -> Result<String> {
///     Ok("操作成功".to_string())
/// }
/// ```
pub type Result<T> = std::result::Result<T, AppError>;

pub mod env;
pub mod toml;

use crate::error::Result as AppResult;
use std::{ops::Deref, sync::Arc};

/// Configurable 特性标记结构体是否可以从 [ConfigRegistry] 读取配置
pub trait Configurable {
    /// 用于读取 toml 配置的前缀。
    /// 如果需要加载外部配置，你需要重写这个方法
    fn config_prefix() -> &'static str;
}

/// ConfigRegistry 是配置管理的核心特性
pub trait ConfigRegistry {
    /// 根据 Configurable 的 `config_prefix` 获取配置项
    fn get_config<T>(&self) -> AppResult<T>
    where
        T: serde::de::DeserializeOwned + Configurable;
}

/// ConfigRef 通过 Arc 避免大型结构体的克隆
#[derive(Debug, Clone)]
pub struct ConfigRef<T: Configurable>(Arc<T>);

impl<T: Configurable> ConfigRef<T> {
    /// 构造函数
    pub fn new(config: T) -> Self {
        Self(Arc::new(config))
    }
}

impl<T> Deref for ConfigRef<T>
where
    T: Configurable,
{
    type Target = T;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

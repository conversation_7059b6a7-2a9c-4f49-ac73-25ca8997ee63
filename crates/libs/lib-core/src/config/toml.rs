use crate::config::env::Env;
use crate::config::{ConfigRegistry, Configurable};
use crate::error::Result;
use crate::AppError;
use anyhow::Context;
use serde::de::DeserializeOwned;
use serde_toml_merge::merge_tables;
use std::fs;
use std::path::Path;
use std::str::FromStr;
use toml::Table;
use tracing::log;

/// 基于 Toml 的配置管理
#[derive(Default)]
pub struct TomlConfigRegistry {
    config: Table,
}

impl ConfigRegistry for TomlConfigRegistry {
    fn get_config<T>(&self) -> Result<T>
    where
        T: DeserializeOwned + Configurable,
    {
        let prefix = T::config_prefix();
        let table = self.get_by_prefix(prefix);
        T::deserialize(table.to_owned()).map_err(|e| AppError::DeserializeErr(prefix, e))
    }
}

impl TomlConfigRegistry {
    /// 从配置文件读取配置。
    /// 如果同一目录中存在对应[活跃环境][Env]的配置文件，
    /// 环境配置文件将与主配置文件进行合并。
    pub fn new(config_path: &Path, env: Env) -> Result<Self> {
        let config = Self::load_config(config_path, env)?;
        Ok(Self { config })
    }

    /// 获取指定前缀的所有配置
    pub fn get_by_prefix(&self, prefix: &str) -> Table {
        match self.config.get(prefix) {
            Some(toml::Value::Table(table)) => table.clone(),
            _ => Table::new(),
        }
    }

    /// 加载 toml 配置
    fn load_config(config_path: &Path, env: Env) -> Result<Table> {
        let config_file_content = fs::read_to_string(config_path);
        let main_toml_str = match config_file_content {
            Err(e) => {
                log::warn!("读取配置文件失败 {:?}: {}", config_path, e);
                return Ok(Table::new());
            }
            Ok(content) => super::env::interpolate(&content),
        };

        let main_table = toml::from_str::<Table>(main_toml_str.as_str())
            .with_context(|| format!("解析 toml 文件失败，路径：{:?}", config_path))?;

        let config_table: Table = match env.get_config_path(config_path) {
            Ok(env_path) => {
                let env_path = env_path.as_path();
                if !env_path.exists() {
                    return Ok(main_table);
                }
                log::info!("{:?} 环境配置文件已激活", env);

                let env_toml_str = fs::read_to_string(env_path)
                    .with_context(|| format!("读取配置文件失败 {:?}", env_path))?;
                let env_toml_str = super::env::interpolate(&env_toml_str);
                let env_table =
                    toml::from_str::<Table>(env_toml_str.as_str()).with_context(|| {
                        format!("解析 toml 文件失败，路径：{:?}", env_path)
                    })?;
                merge_tables(main_table, env_table)
                    .map_err(|e| AppError::TomlMergeError(e.to_string()))
                    .with_context(|| {
                        format!("合并配置文件失败 {:?} 和 {:?}", config_path, env_path)
                    })?
            }
            Err(_) => {
                log::debug!("未找到 {:?} 环境配置", env);
                main_table
            }
        };

        Ok(config_table)
    }
}

impl FromStr for TomlConfigRegistry {
    type Err = AppError;

    fn from_str(str: &str) -> std::result::Result<Self, Self::Err> {
        let config = toml::from_str::<Table>(str)?;
        Ok(Self { config })
    }
}

use crate::error::{AppError, Result};
use anyhow::Context;
use std::{
    env,
    ffi::OsStr,
    io::Error<PERSON><PERSON>,
    path::{Path, PathBuf},
};
use tracing::log;

/// 应用环境
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, Default)]
pub enum Env {
    /// 开发环境
    #[default]
    Dev,
    /// 测试环境
    Test,
    /// 生产环境
    Prod,
}

impl Env {
    /// 从 `.env` 文件初始化环境变量，并读取 `APP_ENV` 来确定应用的活跃环境。
    pub fn init() -> Self {
        match dotenvy::dotenv() {
            Ok(path) => log::debug!(
                "Loaded the environment variable file under the path: \"{:?}\"",
                path
            ),
            Err(e) => log::debug!("Environment variable file not found: {}", e),
        }

        Self::from_env()
    }

    /// 读取 `APP_ENV` 来确定活跃应用的环境。
    /// 如果没有 `APP_ENV` 变量，默认为 Dev
    pub fn from_env() -> Self {
        match env::var("APP_ENV") {
            Ok(var) => Self::from_string(var),
            Err(_) => Self::Dev,
        }
    }

    /// 解析字符串以获得对应的环境
    pub fn from_string<S: Into<String>>(str: S) -> Self {
        match str.into() {
            s if s.eq_ignore_ascii_case("dev") => Self::Dev,
            s if s.eq_ignore_ascii_case("test") => Self::Test,
            s if s.eq_ignore_ascii_case("prod") => Self::Prod,
            _ => Self::Dev,
        }
    }

    pub(crate) fn get_config_path(&self, path: &Path) -> Result<PathBuf> {
        let stem = path.file_stem().and_then(OsStr::to_str).unwrap_or("");
        let ext = path.extension().and_then(OsStr::to_str).unwrap_or("");
        let canonicalize = path
            .canonicalize()
            .with_context(|| format!("canonicalize {:?} failed", path))?;
        let parent = canonicalize
            .parent()
            .ok_or_else(|| AppError::from_io(ErrorKind::NotFound, "config file path not found"))?;
        Ok(match self {
            Self::Dev => parent.join(format!("{stem}-dev.{ext}")),
            Self::Test => parent.join(format!("{stem}-test.{ext}")),
            Self::Prod => parent.join(format!("{stem}-prod.{ext}")),
        })
    }
}

pub(crate) fn interpolate(template: &str) -> String {
    let mut result = String::new();
    let mut i = 0;
    let chars: Vec<char> = template.chars().collect();

    while i < chars.len() {
        if chars[i] == '$' && i + 1 < chars.len() && chars[i + 1] == '{' {
            // find "}"
            let mut j = i + 2; // Skip `${`
            while j < chars.len() && chars[j] != '}' {
                j += 1;
            }

            if j < chars.len() && chars[j] == '}' {
                // extract var_name & default_value
                let placeholder: String = chars[i + 2..j].iter().collect();

                // find default_value
                if let Some(pos) = placeholder.find(':') {
                    let var_name = &placeholder[..pos];
                    if let Ok(value) = env::var(var_name) {
                        result.push_str(&value);
                    } else {
                        result.push_str(&placeholder[pos + 1..]);
                    }
                } else if let Ok(value) = env::var(&placeholder) {
                    result.push_str(&value);
                } else {
                    result.push_str("${");
                    result.push_str(&placeholder);
                    result.push('}');
                }

                i = j + 1; // move to next
            } else {
                result.push('$');
                i += 1;
            }
        } else {
            result.push(chars[i]);
            i += 1;
        }
    }

    result
}

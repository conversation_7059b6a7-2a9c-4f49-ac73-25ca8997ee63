//! 日志系统模块
//!
//! 这个模块提供了完整的日志功能，包括：
//! - 基于 tracing 的日志系统
//! - 多种输出格式支持 (compact, pretty, json)
//! - 文件日志轮转
//! - 按日期创建文件夹
//! - 本地时间支持，避免时区问题
//! - 灵活的日志级别和字段配置



/// 日志配置模块
pub mod config;

use crate::app::app::AppBuilder;
use crate::app::plugin::Plugin;
use crate::config::ConfigRegistry;
use chrono;
use config::{Format, LogLevel, LoggerConfig, TimeStyle, WithFields};
use nu_ansi_term::Color;
use std::sync::OnceLock;
use tracing::log::warn;
use tracing_appender::non_blocking::WorkerGuard;
use tracing_error::ErrorLayer;
use tracing_subscriber::filter::EnvFilter;
use tracing_subscriber::fmt::time::{ChronoUtc, FormatTime, SystemTime, Uptime};
use tracing_subscriber::layer::SubscriberExt;
use tracing_subscriber::util::SubscriberInitExt;
use tracing_subscriber::Registry;
use tracing_subscriber::{
    fmt::{self, MakeWriter},
    Layer,
};

/// 自定义时间格式化器，确保使用本地时间
///
/// 这个格式化器解决了时区问题，确保日志中显示的是本地时间
/// 而不是UTC时间，避免了8小时时差的问题
struct CustomLocalTime;

/// 为自定义时间格式化器实现 FormatTime trait
impl FormatTime for CustomLocalTime {
    /// 格式化时间为本地时间字符串
    ///
    /// 格式: YYYY-MM-DD HH:MM:SS
    fn format_time(&self, w: &mut fmt::format::Writer<'_>) -> std::fmt::Result {
        write!(w, "{}", chrono::Local::now().format("%Y-%m-%d %H:%M:%S"))
    }
}

/// 装箱的 [Tracing Layer](https://docs.rs/tracing-subscriber/latest/tracing_subscriber/layer/index.html)
///
/// 这是一个类型别名，用于简化 Layer 类型的使用
pub type BoxLayer = Box<dyn Layer<Registry> + Send + Sync + 'static>;

/// 基于 [tracing](https://docs.rs/tracing) 的内置日志插件
///
/// 这个插件负责初始化整个日志系统，包括：
/// - 配置日志级别和格式
/// - 设置文件输出
/// - 配置时间格式
/// - 启用错误回溯
pub(crate) struct LogPlugin;

/// 为 LogPlugin 实现 Plugin trait
impl Plugin for LogPlugin {
    /// 立即构建日志插件
    ///
    /// 这个方法会在应用程序启动时立即执行，用于初始化日志系统
    fn immediately_build(&self, app: &mut AppBuilder) {
        // 从配置中读取日志设置
        let config = app
            .get_config::<LoggerConfig>()
            .expect("日志插件配置加载失败");

        // 根据配置启用状态显示日志级别
        if config.enable {
            let level = match config.level {
                LogLevel::Off => Color::LightRed.paint("Disabled"),
                LogLevel::Trace => Color::Purple.paint("TRACE"),
                LogLevel::Debug => Color::Blue.paint("DEBUG"),
                LogLevel::Info => Color::Green.paint("INFO "),
                LogLevel::Warn => Color::Yellow.paint("WARN "),
                LogLevel::Error => Color::Red.paint("ERROR"),
            };
            println!("logger: {}\n", level);
        } else {
            println!("logger: {}\n", Color::LightRed.paint("Disabled"));
        }

        // 如果启用了美观回溯，设置环境变量
        if config.pretty_backtrace {
            unsafe {
                std::env::set_var("RUST_BACKTRACE", "1");
            }
            warn!(
                "美观回溯已启用（这对开发很有帮助，但在生产环境有运行时成本。可通过配置中的 `logger.pretty_backtrace` 禁用）"
            );
        }

        // 获取并配置日志层
        let layers = std::mem::take(&mut app.layers);
        let layers = config.config_subscriber(layers);

        // 构建环境过滤器
        let env_filter = config.build_env_filter();

        // 初始化 tracing 订阅器
        tracing_subscriber::registry()
            .with(layers)
            .with(env_filter)
            .with(ErrorLayer::default())
            .init();
    }

    /// 指示这个插件需要立即构建
    ///
    /// 返回 true 表示这个插件在应用启动时就需要立即初始化
    fn immediately(&self) -> bool {
        true
    }
}

/// 保持非阻塞文件追加器的工作守卫
///
/// 这个静态变量用于保持文件日志的非阻塞写入器的生命周期
/// 防止在应用运行期间被垃圾回收
static NONBLOCKING_WORK_GUARD_KEEP: OnceLock<WorkerGuard> = OnceLock::new();

/// LoggerConfig 的扩展实现
///
/// 这些方法用于配置和构建日志订阅器的各个层
impl LoggerConfig {
    /// 配置订阅器的层
    ///
    /// 这个方法负责：
    /// - 配置文件日志输出（如果启用）
    /// - 配置控制台日志输出
    /// - 按日期创建日志文件夹
    fn config_subscriber(&self, mut layers: Vec<BoxLayer>) -> Vec<BoxLayer> {
        // 配置文件日志
        if let Some(file_logger) = &self.file_appender {
            if file_logger.enable {                
                // 按日期创建文件夹
                let today = chrono::Local::now().format("%Y-%m-%d").to_string();
                let log_dir = format!("{}/{}", file_logger.dir, today);

                // 确保日志目录存在
                if let Err(e) = std::fs::create_dir_all(&log_dir) {
                    eprintln!("创建日志目录失败 {}: {}", log_dir, e);
                    return layers;
                }

                // 创建文件追加器
                let file_appender = tracing_appender::rolling::Builder::default()
                    .max_log_files(file_logger.max_log_files)
                    .filename_prefix(&file_logger.filename_prefix)
                    .filename_suffix(&file_logger.filename_suffix)
                    .rotation(file_logger.rotation.clone().into())
                    .build(&log_dir)
                    .expect("日志文件追加器初始化失败");

                // 根据配置选择阻塞或非阻塞写入
                let file_appender_layer = if file_logger.non_blocking {
                    let (non_blocking_file_appender, work_guard) =
                        tracing_appender::non_blocking(file_appender);
                    NONBLOCKING_WORK_GUARD_KEEP.set(work_guard).unwrap();
                    self.build_fmt_layer(non_blocking_file_appender, &file_logger.format, false)
                } else {
                    self.build_fmt_layer(file_appender, &file_logger.format, false)
                };
                layers.push(file_appender_layer);
            }
        }

        // 如果启用了日志，添加控制台输出层
        if self.enable {
            layers.push(self.build_fmt_layer(std::io::stdout, &self.format, true));
        }

        layers
    }

    /// 构建带有时间格式化器的格式化层
    ///
    /// 参数:
    /// - `make_writer`: 写入器创建器
    /// - `format`: 日志格式
    /// - `ansi`: 是否启用 ANSI 颜色代码
    fn build_fmt_layer<W2>(&self, make_writer: W2, format: &Format, ansi: bool) -> BoxLayer
    where
        W2: for<'writer> MakeWriter<'writer> + Sync + Send + 'static,
    {
        let Self {
            time_style,
            time_pattern,
            ..
        } = &self;

        // 根据时间样式选择不同的时间格式化器
        match time_style {
            TimeStyle::SystemTime => {
                self.build_layer_with_timer(make_writer, format, SystemTime, ansi)
            }
            TimeStyle::Uptime => {
                self.build_layer_with_timer(make_writer, format, Uptime::default(), ansi)
            }
            TimeStyle::ChronoLocal => {
                // 使用自定义本地时间格式化器确保时间正确
                self.build_layer_with_timer(make_writer, format, CustomLocalTime, ansi)
            }
            TimeStyle::ChronoUtc => self.build_layer_with_timer(
                make_writer,
                format,
                ChronoUtc::new(time_pattern.to_string()),
                ansi,
            ),
            TimeStyle::None => self.build_layer_without_timer(make_writer, format, ansi),
        }
    }

    /// 构建带有指定时间格式化器的层
    ///
    /// 这个方法创建一个完整配置的格式化层，包括：
    /// - 时间格式化器
    /// - 输出字段配置
    /// - 格式样式
    fn build_layer_with_timer<W2, T>(
        &self,
        make_writer: W2,
        format: &Format,
        timer: T,
        ansi: bool,
    ) -> BoxLayer
    where
        W2: for<'writer> MakeWriter<'writer> + Sync + Send + 'static,
        T: FormatTime + Sync + Send + 'static,
    {
        // 创建基础格式化层
        let mut layer = fmt::Layer::default()
            .with_ansi(ansi)
            .with_writer(make_writer)
            .with_timer(timer);

        // 根据配置添加额外字段
        for field in &self.with_fields {
            match field {
                WithFields::File => layer = layer.with_file(true),
                WithFields::LineNumber => layer = layer.with_line_number(true),
                WithFields::ThreadId => layer = layer.with_thread_ids(true),
                WithFields::ThreadName => layer = layer.with_thread_names(true),
                WithFields::InternalErrors => layer = layer.log_internal_errors(true),
                WithFields::Target => layer = layer.with_target(true),
            }
        }

        // 根据格式配置应用相应的格式化器
        match format {
            Format::Compact => layer.compact().boxed(),
            Format::Pretty => layer.pretty().boxed(),
            Format::Json => layer.json().boxed(),
        }
    }

    /// 构建不带时间的格式化层
    ///
    /// 当时间样式设置为 None 时使用此方法
    fn build_layer_without_timer<W2>(
        &self,
        make_writer: W2,
        format: &Format,
        ansi: bool,
    ) -> BoxLayer
    where
        W2: for<'writer> MakeWriter<'writer> + Sync + Send + 'static,
    {
        // 创建不带时间的基础格式化层
        let mut layer = fmt::Layer::default()
            .with_ansi(ansi)
            .with_writer(make_writer)
            .without_time();

        // 根据配置添加额外字段
        for field in &self.with_fields {
            match field {
                WithFields::File => layer = layer.with_file(true),
                WithFields::LineNumber => layer = layer.with_line_number(true),
                WithFields::ThreadId => layer = layer.with_thread_ids(true),
                WithFields::ThreadName => layer = layer.with_thread_names(true),
                WithFields::InternalErrors => layer = layer.log_internal_errors(true),
                WithFields::Target => layer = layer.with_target(true),
            }
        }

        // 根据格式配置应用相应的格式化器
        match format {
            Format::Compact => layer.compact().boxed(),
            Format::Pretty => layer.pretty().boxed(),
            Format::Json => layer.json().boxed(),
        }
    }

    /// 构建环境过滤器
    ///
    /// 这个方法负责：
    /// - 首先尝试从环境变量读取过滤器配置
    /// - 如果失败，则使用配置文件中的设置
    /// - 支持自定义过滤器字符串
    fn build_env_filter(&self) -> EnvFilter {
        // 尝试从环境变量创建过滤器
        match EnvFilter::try_from_default_env() {
            Ok(env_filter) => env_filter,
            Err(_) => {
                // 如果环境变量不存在，使用配置文件中的设置
                let LoggerConfig {
                    override_filter,
                    level,
                    ..
                } = self;

                // 优先使用自定义过滤器，否则使用配置的日志级别
                let directive = match override_filter {
                    Some(dir) => dir.into(),
                    None => format!("{level}"),
                };
                EnvFilter::try_new(directive).expect("日志初始化失败")
            }
        }
    }
}

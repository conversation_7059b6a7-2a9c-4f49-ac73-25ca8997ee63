use crate::config::Configurable;
use schemars::JsonSchema;
use serde::Deserialize;
use std::fmt::Display;

/// 为 LoggerConfig 实现 Configurable trait
impl Configurable for LoggerConfig {
    /// 配置前缀，用于从配置文件中读取日志相关配置
    fn config_prefix() -> &'static str {
        "logger"
    }
}

/// 日志器配置结构体
/// 包含日志系统的所有配置选项，如级别、格式、输出目标等
#[derive(Debug, Clone, JsonSchema, Deserialize)]
pub(crate) struct LoggerConfig {
    /// 是否启用日志功能
    #[serde(default = "default_true")]
    pub enable: bool,

    /// 启用美观的错误堆栈回溯显示，开发环境应该开启。
    /// 在性能敏感的生产环境部署中应关闭此功能。
    #[serde(default)]
    pub pretty_backtrace: bool,

    /// 设置日志级别。
    ///
    /// * 选项: `trace` | `debug` | `info` | `warn` | `error`
    #[serde(default)]
    pub level: LogLevel,

    /// 设置日志格式。
    ///
    /// * 选项: `compact` | `pretty` | `json`
    #[serde(default)]
    pub format: Format,

    /// 事件时间戳的格式化器。
    #[serde(default)]
    pub time_style: TimeStyle,

    /// 时间格式模式，使用 chrono 格式字符串
    /// 例如: "%Y-%m-%d %H:%M:%S"
    #[serde(default)]
    pub time_pattern: ChronoTimePattern,

    /// 要包含在日志输出中的额外字段
    /// 可以选择包含文件名、行号、线程ID等信息
    #[serde(default)]
    pub with_fields: Vec<WithFields>,

    /// 覆盖我们的自定义跟踪过滤器。
    ///
    /// 如果您想查看内部库的跟踪信息，请设置为您自己的过滤器。
    /// 更多信息请参见 [这里](https://docs.rs/tracing-subscriber/latest/tracing_subscriber/filter/struct.EnvFilter.html#directives)
    pub override_filter: Option<String>,

    /// 如果您想将日志写入文件，请设置此项
    #[serde(rename = "file")]
    pub file_appender: Option<LoggerFileAppender>,
}

/// 日志级别枚举
/// 定义了日志的详细程度，从最详细的 trace 到只记录错误的 error
#[derive(Debug, Default, Clone, JsonSchema, Deserialize)]
pub(crate) enum LogLevel {
    /// "off" 级别（关闭日志）。
    #[serde(rename = "off")]
    Off,
    /// "trace" 级别（跟踪）。
    #[serde(rename = "trace")]
    Trace,
    /// "debug" 级别（调试）。
    #[serde(rename = "debug")]
    Debug,
    /// "info" 级别（信息）。
    #[serde(rename = "info")]
    #[default]
    Info,
    /// "warn" 级别（警告）。
    #[serde(rename = "warn")]
    Warn,
    /// "error" 级别（错误）。
    #[serde(rename = "error")]
    Error,
}

/// 为 LogLevel 实现 Display trait，用于将日志级别转换为字符串
impl Display for LogLevel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "{}",
            match self {
                Self::Off => "off",
                Self::Trace => "trace",
                Self::Debug => "debug",
                Self::Info => "info",
                Self::Warn => "warn",
                Self::Error => "error",
            }
        )
    }
}

/// 日志输出格式枚举
/// 定义了日志的显示格式，影响日志的可读性和解析能力
#[derive(Debug, Default, Clone, JsonSchema, Deserialize)]
pub(crate) enum Format {
    /// 紧凑格式 - 节省空间，适合大量日志输出
    #[serde(rename = "compact")]
    #[default]
    Compact,
    /// 美观格式 - 易于阅读，适合开发调试
    #[serde(rename = "pretty")]
    Pretty,
    /// JSON 格式 - 结构化数据，适合日志收集系统
    #[serde(rename = "json")]
    Json,
}

/// 时间样式选项
/// 参考: https://docs.rs/tracing-subscriber/latest/tracing_subscriber/fmt/time/index.html
#[derive(Debug, Default, Clone, JsonSchema, Deserialize)]
pub(crate) enum TimeStyle {
    /// 检索并打印当前的系统时钟时间。
    #[serde(rename = "system")]
    SystemTime,
    /// 检索并打印自某个纪元以来的相对运行时间。
    #[serde(rename = "uptime")]
    Uptime,
    /// 使用 chrono crate 实现的 FormatTime 格式化本地时间和 UTC 时间。
    #[default]
    #[serde(rename = "local")]
    ChronoLocal,
    /// 使用 chrono crate 的格式化器格式化当前 UTC 时间。
    #[serde(rename = "utc")]
    ChronoUtc,
    /// 不显示时间
    #[serde(rename = "none")]
    None,
}

/// Chrono 时间模式包装器
/// 包装 chrono 时间格式字符串，用于自定义时间显示格式
#[derive(Debug, Clone, JsonSchema, Deserialize)]
#[serde(transparent)]
pub(crate) struct ChronoTimePattern(String);

/// 为 ChronoTimePattern 提供默认时间格式
impl Default for ChronoTimePattern {
    fn default() -> Self {
        Self("%Y-%m-%dT%H:%M:%S".to_string())
    }
}

/// 为 ChronoTimePattern 实现 ToString trait
#[allow(clippy::to_string_trait_impl)]
impl ToString for ChronoTimePattern {
    fn to_string(&self) -> String {
        self.0.to_string()
    }
}

/// 日志字段选项枚举
/// 定义了可以包含在日志输出中的额外信息字段
#[derive(Debug, Clone, JsonSchema, Deserialize)]
#[serde(rename_all = "snake_case")]
pub(crate) enum WithFields {
    /// 包含源文件名
    File,
    /// 包含行号
    LineNumber,
    /// 包含线程ID
    ThreadId,
    /// 包含线程名称
    ThreadName,
    /// 包含内部错误信息
    InternalErrors,
    /// 包含事件目标（通常是模块路径）
    Target,
}

/// 文件日志追加器配置
/// 配置日志文件的输出行为，包括文件位置、轮转策略等
#[derive(Debug, Clone, JsonSchema, Deserialize)]
pub(crate) struct LoggerFileAppender {
    /// 是否启用文件日志
    pub enable: bool,
    /// 是否使用非阻塞写入（推荐启用以提高性能）
    #[serde(default = "default_true")]
    pub non_blocking: bool,
    /// 文件日志的输出格式
    #[serde(default)]
    pub format: Format,
    /// 日志文件轮转策略
    #[serde(default)]
    pub rotation: Rotation,
    /// 日志文件存储目录
    #[serde(default = "default_dir")]
    pub dir: String,
    /// 日志文件名前缀
    #[serde(default = "default_prefix")]
    pub filename_prefix: String,
    /// 日志文件名后缀
    #[serde(default = "default_suffix")]
    pub filename_suffix: String,
    /// 最大保留的日志文件数量
    #[serde(default = "default_max_log_files")]
    pub max_log_files: usize,
}

/// 日志文件轮转策略枚举
/// 定义了何时创建新的日志文件
#[derive(Debug, Default, Clone, JsonSchema, Deserialize)]
pub(crate) enum Rotation {
    /// 每分钟轮转
    #[serde(rename = "minutely")]
    Minutely,
    /// 每小时轮转
    #[serde(rename = "hourly")]
    Hourly,
    /// 每天轮转（默认）
    #[serde(rename = "daily")]
    #[default]
    Daily,
    /// 永不轮转
    #[serde(rename = "never")]
    Never,
}

/// 默认日志目录
fn default_dir() -> String {
    "./logs".to_string()
}

/// 默认日志文件名前缀
fn default_prefix() -> String {
    "app".to_string()
}

/// 默认日志文件名后缀
fn default_suffix() -> String {
    "log".to_string()
}

/// 默认最大日志文件数量
fn default_max_log_files() -> usize {
    365
}

/// 默认 true 值函数
fn default_true() -> bool {
    true
}

/// 将自定义 Rotation 枚举转换为 tracing_appender 的 Rotation 类型
#[allow(clippy::from_over_into)]
impl Into<tracing_appender::rolling::Rotation> for Rotation {
    fn into(self) -> tracing_appender::rolling::Rotation {
        match self {
            Self::Minutely => tracing_appender::rolling::Rotation::MINUTELY,
            Self::Hourly => tracing_appender::rolling::Rotation::HOURLY,
            Self::Daily => tracing_appender::rolling::Rotation::DAILY,
            Self::Never => tracing_appender::rolling::Rotation::NEVER,
        }
    }
}

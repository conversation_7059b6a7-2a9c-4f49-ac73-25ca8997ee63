use serde::Serialize;
use utoipa::ToSchema;

use crate::response::PageData;

/// Repository 层分页数据构建器（无 ToSchema 约束）
/// 专门用于 Repository 层构建分页数据，然后在 Service 层转换为 PageData<T: ToSchema>
#[derive(Debug, Clone, Serialize)]
pub struct PageDataBuilder<T> {
    /// 数据列表
    pub items: Vec<T>,
    /// 总数量
    pub total: u64,
    /// 当前页码
    pub page: u64,
    /// 每页大小
    pub page_size: u64,
    /// 总页数
    pub total_pages: u64,
}

impl<T> PageDataBuilder<T> {
    /// 创建新的分页数据构建器
    pub fn new(items: Vec<T>, total: u64, page: u64, page_size: u64) -> Self {
        let total_pages = if total == 0 {
            0
        } else {
            (total + page_size - 1) / page_size
        };

        Self {
            items,
            total,
            page,
            page_size,
            total_pages,
        }
    }

    /// 转换为 PageData（需要目标类型实现 ToSchema）
    pub fn into_page_data<U>(self) -> PageData<U>
    where
        U: ToSchema,
        T: Into<U>,
    {
        let converted_items: Vec<U> = self.items.into_iter().map(|item| item.into()).collect();
        PageData::new(converted_items, self.total, self.page, self.page_size)
    }

    /// 通过映射函数转换为 PageData
    pub fn map_into_page_data<U, F>(self, mapper: F) -> PageData<U>
    where
        U: ToSchema,
        F: FnMut(T) -> U,
    {
        let converted_items: Vec<U> = self.items.into_iter().map(mapper).collect();
        PageData::new(converted_items, self.total, self.page, self.page_size)
    }

    /// 获取是否为空的页面
    pub fn is_empty(&self) -> bool {
        self.items.is_empty()
    }

    /// 获取数据项数量
    pub fn len(&self) -> usize {
        self.items.len()
    }
}

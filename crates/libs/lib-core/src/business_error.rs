use std::fmt;

/// 业务错误结构
///
/// 简单的错误结构，包含状态码和错误消息
/// 使用方式：BusinessError::new(404, "用户不存在")
#[derive(Debug, <PERSON>lone)]
pub struct BusinessError {
    /// HTTP 状态码
    pub code: i32,
    /// 错误消息
    pub message: String,
}

impl BusinessError {
    /// 创建新的业务错误
    ///
    /// # 参数
    /// * `code` - HTTP状态码
    /// * `message` - 错误消息
    pub fn new(code: i32, message: impl Into<String>) -> Self {
        Self {
            code,
            message: message.into(),
        }
    }

    /// 获取错误码
    pub fn code(&self) -> i32 {
        self.code
    }

    /// 获取错误消息
    pub fn message(&self) -> &str {
        &self.message
    }
}

impl fmt::Display for BusinessError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.message)
    }
}

impl std::error::Error for BusinessError {}

/// Option 扩展，提供业务错误转换
pub trait BusinessOption<T> {
    /// 将 None 转换为业务错误
    fn business_ok_or(self, code: i32, message: impl Into<String>) -> Result<T, BusinessError>;
}

impl<T> BusinessOption<T> for Option<T> {
    fn business_ok_or(self, code: i32, message: impl Into<String>) -> Result<T, BusinessError> {
        self.ok_or_else(|| BusinessError::new(code, message))
    }
}

/// Result 扩展，提供业务错误转换
pub trait BusinessResult<T, E> {
    /// 将错误转换为业务错误
    fn business_map_err<F>(self, f: F) -> Result<T, BusinessError>
    where
        F: FnOnce(E) -> BusinessError;

    /// 快速映射为业务错误
    fn map_business_err(self, code: i32, message: impl Into<String>) -> Result<T, BusinessError>;
}

impl<T, E> BusinessResult<T, E> for Result<T, E> {
    fn business_map_err<F>(self, f: F) -> Result<T, BusinessError>
    where
        F: FnOnce(E) -> BusinessError,
    {
        self.map_err(f)
    }

    fn map_business_err(self, code: i32, message: impl Into<String>) -> Result<T, BusinessError> {
        self.map_err(|_| BusinessError::new(code, message))
    }
}
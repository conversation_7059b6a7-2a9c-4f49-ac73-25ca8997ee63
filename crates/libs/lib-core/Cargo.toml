[package]
name = "lib-core"
version.workspace = true
edition.workspace = true

[dependencies]
lib-macros = { path = "../lib-macros" }
redis = { workspace = true, features = ["connection-manager", "tokio-comp", "json"] }
chrono = { workspace = true }
thiserror = { workspace = true }
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }
anyhow = { workspace = true }
axum = { workspace = true }
http = { workspace = true }
once_cell = { workspace = true }
# API文档生成支持
utoipa = { workspace = true, features = ["chrono"] }
async-trait = { workspace = true }
dashmap = { workspace = true }
toml = { workspace = true }
tokio = { workspace = true }
nu-ansi-term = { workspace = true }
serde-toml-merge = { workspace = true }
schemars = { workspace = true, features = ["derive"] }
dotenvy = { workspace = true }
inventory = { workspace = true }
tracing = { workspace = true, features = ["log"] }
tracing-appender = { workspace = true }
tracing-error = { workspace = true }
tracing-subscriber = { workspace = true, features = [
    "json",
    "env-filter",
    "tracing-log",
    "chrono",
] }
indexmap = "2.9.0"
[dev-dependencies]
tokio = { workspace = true, features = ["test-util"] }
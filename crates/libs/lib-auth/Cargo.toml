[package]
name = "lib-auth"
version.workspace = true
edition.workspace = true

[dependencies]
chrono = { workspace = true, features = ["serde"] }
lib-core = { path = "../lib-core" }
lib-macros = { path = "../lib-macros" }


anyhow = { workspace = true }
serde = { workspace = true }
thiserror = { workspace = true }
axum = { workspace = true }
tracing = { workspace = true }
uuid = { workspace = true }
sea-orm = { workspace = true }
jsonwebtoken = { workspace = true }
schemars = { workspace = true }
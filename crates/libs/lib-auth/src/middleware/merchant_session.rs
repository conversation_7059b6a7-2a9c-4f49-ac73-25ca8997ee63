use crate::config::SecurityConfig;
use anyhow::Error as AnyhowError;
use jsonwebtoken::{decode, encode, Algorithm, Decoding<PERSON>ey, Encoding<PERSON><PERSON>, Header, Validation};
use lib_core::cache::{redis_del_key, redis_get_json, redis_set_json};
use lib_core::DateTimeUtils;
use serde::{Deserialize, Serialize};
use thiserror::Error;
use uuid::Uuid;

/// 在线用户信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct OnlineUserInfo {
    pub user_id: Uuid,
    pub username: String,
    pub user_type: MerchantUserType,
    pub current_merchant_id: MerchantId,
    pub login_time: i64,
    pub last_active: i64,
    pub expires_at: i64,
}

/// 商户在线统计信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MerchantOnlineStats {
    pub merchant_id: MerchantId,
    pub admin_count: usize,
    pub employee_count: usize,
    pub total_count: usize,
    pub admins: Vec<OnlineUserInfo>,
    pub employees: Vec<OnlineUserInfo>,
}

/// 商户用户类型枚举
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
pub enum MerchantUserType {
    /// 商户管理员
    MerchantAdmin,
    /// 商户员工
    MerchantEmployee,
}

/// 商户ID类型别名
pub type MerchantId = i64;

/// JWT载荷结构（只包含session_id）
#[derive(Debug, Serialize, Deserialize)]
struct SessionTokenClaims {
    /// Session ID - 这是JWT的唯一目的
    session_id: String,
}

/// 商户认证错误类型
#[derive(Error, Debug)]
pub enum MerchantAuthError {
    #[error("Session not found")]
    SessionNotFound,
    #[error("Session expired")]
    SessionExpired,
    #[error("Invalid session")]
    InvalidSession,
    #[error("Invalid token")]
    InvalidToken,
    #[error("Invalid merchant context")]
    InvalidMerchantContext,
    #[error("Merchant access denied")]
    MerchantAccessDenied,
    #[error("JWT error: {0}")]
    JwtError(#[from] jsonwebtoken::errors::Error),
    #[error("Redis error: {0}")]
    RedisError(#[from] AnyhowError),
}

/// Session ID 生成器
pub fn generate_session_id() -> String {
    format!("sess_{}", Uuid::new_v4().simple())
}

/// 创建Session Token（JWT包装的Session ID）
pub fn create_session_token(
    session_id: &str,
    config: &SecurityConfig,
) -> Result<String, MerchantAuthError> {
    let claims = SessionTokenClaims {
        session_id: session_id.to_string(),
    };

    let token = encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(
            config
                .merchant
                .jwt_secret()
                .as_bytes(),
        ),
    )?;

    Ok(token)
}

/// 验证Session Token并提取Session ID
pub fn verify_session_token(
    token: &str,
    config: &SecurityConfig,
) -> Result<String, MerchantAuthError> {
    let mut validation = Validation::new(Algorithm::HS256);
    validation.validate_exp = false; // 🔥 关键：不验证JWT过期时间，由Redis Session控制过期
    validation.required_spec_claims.clear(); // 清除所有必需的标准声明

    let token_data = decode::<SessionTokenClaims>(
        token,
        &DecodingKey::from_secret(
            config
                .merchant
                .jwt_secret()
                .as_bytes(),
        ),
        &validation,
    )?;

    Ok(token_data.claims.session_id)
}

/// 商户认证Session结构体
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Eq)]
pub struct MerchantAuthSession {
    /// 用户唯一ID
    pub user_id: Uuid,
    /// 用户名
    pub username: String,
    /// 用户类型
    pub user_type: MerchantUserType,
    /// 用户所属的商户ID列表（用于验证切换商户的权限）
    pub merchant_ids: Vec<MerchantId>,
    /// 当前激活的商户ID
    pub current_merchant_id: MerchantId,
    /// 当前商户下的角色编码列表
    pub current_role_codes: Vec<String>,
    /// 当前商户下的权限代码列表
    pub current_permissions: Vec<String>,
    /// 创建时间，Unix 时间戳
    pub created_at: i64,
    /// 最后访问时间，Unix 时间戳
    pub last_accessed: i64,
    /// 过期时间，Unix 时间戳
    pub expires_at: i64,
}

#[derive(Debug, Clone)]
pub struct MerchantToken(pub String);

// 提供便利方法
impl MerchantToken {
    pub fn as_str(&self) -> &str {
        &self.0
    }

    pub fn into_string(self) -> String {
        self.0
    }
}

impl MerchantAuthSession {
    /// 创建一个新的商户认证Session
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    /// - `username`: 用户名
    /// - `user_type`: 用户类型
    /// - `merchant_ids`: 用户所属的商户ID列表
    /// - `current_merchant_id`: 当前激活的商户ID
    /// - `current_role_codes`: 用户在当前商户下的角色编码列表
    /// - `current_permissions`: 用户在当前商户下的权限代码列表
    /// - `config`: 应用程序配置
    ///
    /// # 返回
    /// - `Result<String, MerchantAuthError>`: 成功返回JWT Token，失败返回错误
    pub async fn create_session(
        user_id: Uuid,
        username: String,
        user_type: MerchantUserType,
        merchant_ids: Vec<MerchantId>,
        current_merchant_id: MerchantId,
        current_role_codes: Vec<String>,
        current_permissions: Vec<String>,
        config: &SecurityConfig,
    ) -> Result<String, MerchantAuthError> {
        // 🔥 新增：先清理用户的旧session（避免重复登录）
        if let Err(e) = Self::cleanup_user_existing_sessions(user_id).await {
            tracing::warn!("清理用户 {} 的旧session失败: {:?}", user_id, e);
        }

        let now = DateTimeUtils::now_utc();
        let session_id = generate_session_id();
        let expires_at = now + chrono::Duration::seconds(config.merchant.jwt_access_token_exp_sec as i64);

        let session = MerchantAuthSession {
            user_id,
            username: username.clone(),
            user_type: user_type.clone(),
            merchant_ids: merchant_ids.clone(),
            current_merchant_id,
            current_role_codes,
            current_permissions,
            created_at: now.timestamp(),
            last_accessed: now.timestamp(),
            expires_at: expires_at.timestamp(),
        };

        // 存储到Redis，设置过期时间
        let redis_key = format!("session:{}", session_id);
        redis_set_json(
            &redis_key,
            &session,
            Some(config.merchant.jwt_access_token_exp_sec as u64),
        )
        .await
        .map_err(MerchantAuthError::RedisError)?;

        // 维护在线用户索引
        Self::add_online_user_index(&session, &session_id).await?;

        // 创建JWT包装的token
        let token = create_session_token(&session_id, config)?;

        tracing::info!(
            "创建新session: 用户={}, 类型={:?}, 当前商户={}, session_id={}",
            username,
            user_type,
            current_merchant_id,
            session_id
        );

        Ok(token)
    }


    /// 验证JWT Token并获取Session数据
    ///
    /// # 参数
    /// - `token`: JWT Token
    /// - `config`: 应用程序配置
    ///
    /// # 返回
    /// - `Result<MerchantAuthSession, MerchantAuthError>`: 成功返回Session数据，失败返回错误
    pub async fn verify_session(
        token: &str,
        config: &SecurityConfig,
    ) -> Result<MerchantAuthSession, MerchantAuthError> {
        // 先验证JWT并提取session_id
        let session_id = verify_session_token(token, config)?;

        let redis_key = format!("session:{}", session_id);

        // 从Redis获取Session数据
        let session_data: Option<MerchantAuthSession> = redis_get_json(&redis_key)
            .await
            .map_err(MerchantAuthError::RedisError)?;

        let mut session = session_data.ok_or(MerchantAuthError::SessionNotFound)?;

        // 检查是否过期
        let now = DateTimeUtils::now_utc().timestamp();
        if now > session.expires_at {
            // 清理过期Session
            let _ = redis_del_key(&redis_key).await;
            return Err(MerchantAuthError::SessionExpired);
        }

        // 修复：更新最后访问时间并重新设置TTL，实现自动续期
        session.last_accessed = now;
        
        // 重新计算过期时间，实现滑动过期
        let new_expires_at = now + config.merchant.jwt_access_token_exp_sec as i64;
        session.expires_at = new_expires_at;
        
        // 关键修复：重新设置Redis TTL，实现自动续期
        redis_set_json(
            &redis_key, 
            &session, 
            Some(config.merchant.jwt_access_token_exp_sec)
        )
        .await
        .map_err(MerchantAuthError::RedisError)?;

        Ok(session)
    }

    /// 刷新Session过期时间
    pub async fn refresh_session(
        token: &str,
        config: &SecurityConfig,
    ) -> Result<String, MerchantAuthError> {
        // 先验证JWT并提取session_id
        let session_id = verify_session_token(token, config)?;

        let redis_key = format!("session:{}", session_id);

        let mut session: MerchantAuthSession = redis_get_json(&redis_key)
            .await
            .map_err(MerchantAuthError::RedisError)?
            .ok_or(MerchantAuthError::SessionNotFound)?;

        let now = DateTimeUtils::now_utc();
        session.last_accessed = now.timestamp();
        session.expires_at = (now
            + chrono::Duration::seconds(config.merchant.jwt_access_token_exp_sec as i64))
        .timestamp();

        redis_set_json(
            &redis_key,
            &session,
            Some(config.merchant.jwt_access_token_exp_sec as u64),
        )
        .await
        .map_err(MerchantAuthError::RedisError)?;

        // 返回新的JWT token
        let new_token = create_session_token(&session_id, config)?;
        Ok(new_token)
    }

    /// 删除Session（登出）
    pub async fn destroy_session(
        token: &str,
        config: &SecurityConfig,
    ) -> Result<(), MerchantAuthError> {
        // 先验证JWT并提取session_id
        let session_id = verify_session_token(token, config)?;

        let redis_key = format!("session:{}", session_id);

        // 在删除前获取session信息（用于清理在线索引）
        let session: Option<MerchantAuthSession> = redis_get_json(&redis_key)
            .await
            .map_err(MerchantAuthError::RedisError)?;

        // 删除session
        redis_del_key(&redis_key)
            .await
            .map_err(MerchantAuthError::RedisError)?;

        // 新增：清理在线用户索引
        if let Some(session) = session {
            let _ = Self::remove_online_user_index(&session, &session_id).await;
        }

        // 清除RLS租户上下文（防御性清除）
        if let Err(e) = crate::middleware::merchant_auth_middleware::clear_tenant_context().await {
            tracing::warn!("清除租户上下文失败: {:?}", e);
        }

        Ok(())
    }

    /// 切换当前激活的商户
    ///
    /// 注意：切换商户会重新设置权限信息，需要外部提供新商户的权限数据
    pub async fn switch_merchant_with_permissions(
        token: &str,
        new_merchant_id: MerchantId,
        new_role_codes: Vec<String>,
        new_permissions: Vec<String>,
        config: &SecurityConfig,
        user_type : MerchantUserType,
    ) -> Result<String, MerchantAuthError> {
        // 先清除旧的租户上下文（防止上下文混乱）
        if let Err(e) = crate::middleware::merchant_auth_middleware::clear_tenant_context().await {
            tracing::warn!("清除旧租户上下文失败: {:?}", e);
        }

        // 先验证JWT并提取session_id
        let session_id = verify_session_token(token, config)?;

        let redis_key = format!("session:{}", session_id);

        let mut session: MerchantAuthSession = redis_get_json(&redis_key)
            .await
            .map_err(MerchantAuthError::RedisError)?
            .ok_or(MerchantAuthError::SessionNotFound)?;

        // 检查用户是否有权限访问这个商户
        if !session.merchant_ids.contains(&new_merchant_id) {
            return Err(MerchantAuthError::MerchantAccessDenied);
        }

        // 修复：更新Session信息，包括重新计算过期时间和TTL
        session.current_merchant_id = new_merchant_id;
        session.current_role_codes = new_role_codes;
        session.current_permissions = new_permissions;
        session.user_type = user_type;
        let now = DateTimeUtils::now_utc().timestamp();
        session.last_accessed = now;
        
        // 重新计算过期时间
        session.expires_at = now + config.merchant.jwt_access_token_exp_sec as i64;

        // 修复：保存更新后的Session并重新设置TTL
        redis_set_json(
            &redis_key, 
            &session, 
            Some(config.merchant.jwt_access_token_exp_sec)
        )
        .await
        .map_err(MerchantAuthError::RedisError)?;

        // 返回新的JWT token
        let new_token = create_session_token(&session_id, config)?;

        tracing::info!(
            "用户 {} 切换到商户 {}, session_id={}",
            session.username,
            new_merchant_id,
            session_id
        );

        Ok(new_token)
    }

    /// 获取当前商户的角色编码列表
    pub fn get_current_merchant_role_codes(&self) -> Vec<String> {
        self.current_role_codes.clone()
    }

    /// 获取当前商户的权限代码列表
    pub fn get_current_merchant_permissions(&self) -> Vec<String> {
        self.current_permissions.clone()
    }

    /// 检查用户是否是商户管理员
    pub fn is_merchant_admin(&self) -> bool {
        matches!(self.user_type, MerchantUserType::MerchantAdmin)
    }

    /// 检查用户在当前商户下是否具有指定角色编码
    pub fn has_role_code(&self, role_code: &str) -> bool {
        self.current_role_codes.contains(&ToString::to_string(role_code))
    }

    /// 检查用户在当前商户下是否具有MERCHANT_ADMIN角色
    pub fn is_current_merchant_admin(&self) -> bool {
        self.has_role_code("MERCHANT_ADMIN")
    }

    /// 检查用户是否有权限访问指定商户
    pub fn has_merchant_access(&self, merchant_id: MerchantId) -> bool {
        self.merchant_ids.contains(&merchant_id)
    }

    /// 检查用户在当前商户下是否拥有指定权限（支持通配符）
    pub fn has_permission(&self, permission_code: &str) -> bool {
        use crate::permissions::permission_matcher;
        permission_matcher::has_permission(&self.current_permissions, permission_code)
    }

    /// 检查用户在当前商户下是否拥有所有指定权限（AND逻辑，支持通配符）
    pub fn has_all_permissions(&self, permission_codes: &[&str]) -> bool {
        use crate::permissions::permission_matcher;
        let required_permissions: Vec<String> =
            permission_codes.iter().map(|s| ToString::to_string(s)).collect();
        permission_matcher::has_all_permissions(&self.current_permissions, &required_permissions)
    }

    /// 检查用户在当前商户下是否拥有任意一个指定权限（OR逻辑，支持通配符）
    pub fn has_any_permission(&self, permission_codes: &[&str]) -> bool {
        use crate::permissions::permission_matcher;
        let required_permissions: Vec<String> =
            permission_codes.iter().map(|s| ToString::to_string(s)).collect();
        permission_matcher::has_any_permission(&self.current_permissions, &required_permissions)
    }

    /// 获取Session的友好显示信息（用于日志和调试）
    pub fn display_info(&self) -> String {
        format!(
            "用户: {}, 类型: {:?}, 当前商户: {}, 创建时间: {}, 过期时间: {}",
            self.username,
            self.user_type,
            self.current_merchant_id,
            DateTimeUtils::timestamp_to_local(self.created_at)
                .map(|dt| DateTimeUtils::format_datetime(&dt))
                .unwrap_or_else(|_| "无效时间".to_string()),
            DateTimeUtils::timestamp_to_local(self.expires_at)
                .map(|dt| DateTimeUtils::format_datetime(&dt))
                .unwrap_or_else(|_| "无效时间".to_string())
        )
    }

    /// 检查Session是否即将过期
    pub fn is_near_expiry(&self, threshold_seconds: i64) -> bool {
        let now = DateTimeUtils::now_utc();
        let current_timestamp = now.timestamp();
        let expires_in = self.expires_at - current_timestamp;

        expires_in <= threshold_seconds && expires_in > 0
    }

    /// 获取Session剩余有效时间（秒）
    pub fn remaining_seconds(&self) -> i64 {
        let now = DateTimeUtils::now_utc();
        let current_timestamp = now.timestamp();
        (self.expires_at - current_timestamp).max(0)
    }

    /// 获取Session友好的剩余时间显示
    pub fn remaining_time_display(&self) -> String {
        let remaining = self.remaining_seconds();

        if remaining <= 0 {
            "已过期".to_string()
        } else if remaining < 60 {
            format!("{}秒", remaining)
        } else if remaining < 3600 {
            format!("{}分钟", remaining / 60)
        } else if remaining < 86400 {
            format!("{}小时", remaining / 3600)
        } else {
            format!("{}天", remaining / 86400)
        }
    }

    // ==================== 在线用户统计功能 ====================

    /// 添加在线用户索引
    ///
    /// # Redis数据结构
    /// - `online:merchant:{merchant_id}:admins` -> Set<user_id:session_id>
    /// - `online:merchant:{merchant_id}:employees` -> Set<user_id:session_id>
    /// - `online:user:{user_id}` -> session_id (用于快速清理)
    async fn add_online_user_index(
        session: &MerchantAuthSession,
        session_id: &str,
    ) -> Result<(), MerchantAuthError> {
        use lib_core::cache::{redis_sadd, redis_set_ex};

        let user_session_key = format!("{}:{}", session.user_id, session_id);
        let user_type_str = match session.user_type {
            MerchantUserType::MerchantAdmin => "admins",
            MerchantUserType::MerchantEmployee => "employees",
        };

        // 为当前商户添加在线用户
        let merchant_key = format!(
            "online:merchant:{}:{}",
            session.current_merchant_id, user_type_str
        );
        redis_sadd(&merchant_key, &user_session_key)
            .await
            .map_err(|e| {
                MerchantAuthError::RedisError(anyhow::anyhow!("添加在线用户索引失败: {}", e))
            })?;

        // 设置用户当前session映射（用于清理），过期时间与session一致
        let user_key = format!("online:user:{}", session.user_id);
        let ttl = (session.expires_at - DateTimeUtils::now_utc().timestamp()) as u64;
        redis_set_ex(&user_key, session_id, ttl)
            .await
            .map_err(|e| {
                MerchantAuthError::RedisError(anyhow::anyhow!("设置用户session映射失败: {}", e))
            })?;

        tracing::debug!(
            "添加在线用户索引: 用户={}, 商户={}, 类型={}, session={}",
            session.user_id,
            session.current_merchant_id,
            user_type_str,
            session_id
        );
        Ok(())
    }

    /// 移除在线用户索引
    async fn remove_online_user_index(
        session: &MerchantAuthSession,
        session_id: &str,
    ) -> Result<(), MerchantAuthError> {
        use lib_core::cache::{redis_del_key, redis_srem};

        let user_session_key = format!("{}:{}", session.user_id, session_id);
        let user_type_str = match session.user_type {
            MerchantUserType::MerchantAdmin => "admins",
            MerchantUserType::MerchantEmployee => "employees",
        };

        // 从所有相关商户的在线列表中移除
        for merchant_id in &session.merchant_ids {
            let merchant_key = format!("online:merchant:{}:{}", merchant_id, user_type_str);
            let _ = redis_srem(&merchant_key, &user_session_key).await;
        }

        // 清理用户session映射
        let user_key = format!("online:user:{}", session.user_id);
        let _ = redis_del_key(&user_key).await;

        tracing::debug!(
            "移除在线用户索引: 用户={}, session={}",
            session.user_id,
            session_id
        );
        Ok(())
    }

    /// 获取指定商户的在线管理员列表
    pub async fn get_online_admins(
        merchant_id: MerchantId,
    ) -> Result<Vec<OnlineUserInfo>, MerchantAuthError> {
        Self::get_online_users_by_type(merchant_id, "admins").await
    }

    /// 获取指定商户的在线员工列表
    pub async fn get_online_employees(
        merchant_id: MerchantId,
    ) -> Result<Vec<OnlineUserInfo>, MerchantAuthError> {
        Self::get_online_users_by_type(merchant_id, "employees").await
    }

    /// 获取指定商户的所有在线用户
    pub async fn get_all_online_users(
        merchant_id: MerchantId,
    ) -> Result<MerchantOnlineStats, MerchantAuthError> {
        let admins = Self::get_online_admins(merchant_id).await?;
        let employees = Self::get_online_employees(merchant_id).await?;

        Ok(MerchantOnlineStats {
            merchant_id,
            admin_count: admins.len(),
            employee_count: employees.len(),
            total_count: admins.len() + employees.len(),
            admins,
            employees,
        })
    }

    /// 按用户类型获取在线用户
    async fn get_online_users_by_type(
        merchant_id: MerchantId,
        user_type: &str,
    ) -> Result<Vec<OnlineUserInfo>, MerchantAuthError> {
        use lib_core::cache::{redis_get_json, redis_smembers};

        let merchant_key = format!("online:merchant:{}:{}", merchant_id, user_type);
        let user_sessions: Vec<String> = redis_smembers(&merchant_key).await.map_err(|e| {
            MerchantAuthError::RedisError(anyhow::anyhow!("获取在线用户列表失败: {}", e))
        })?;

        let mut online_users = Vec::new();

        for user_session in user_sessions {
            // user_session格式: "user_id:session_id"
            let parts: Vec<&str> = user_session.split(':').collect();
            if parts.len() != 2 {
                continue;
            }

            let session_id = parts[1];
            let session_key = format!("session:{}", session_id);

            // 获取完整的session信息
            if let Ok(Some(session)) = redis_get_json::<MerchantAuthSession>(&session_key).await {
                // 检查session是否过期
                let now = DateTimeUtils::now_utc().timestamp();
                if now <= session.expires_at {
                    online_users.push(OnlineUserInfo {
                        user_id: session.user_id,
                        username: session.username,
                        user_type: session.user_type,
                        current_merchant_id: session.current_merchant_id,
                        login_time: session.created_at,
                        last_active: session.last_accessed,
                        expires_at: session.expires_at,
                    });
                } else {
                    // 清理过期的索引
                    let _ = Self::remove_online_user_index(&session, session_id).await;
                }
            }
        }

        // 按最后活跃时间排序
        online_users.sort_by(|a, b| b.last_active.cmp(&a.last_active));
        Ok(online_users)
    }

    /// 清理指定用户的所有在线状态和session
    /// 这个方法比cleanup_user_existing_sessions更彻底，会清理所有相关数据
    pub async fn cleanup_user_online_status(user_id: Uuid) -> Result<(), MerchantAuthError> {
        use lib_core::cache::{redis_del_key, redis_get_json, redis_get_string};

        tracing::info!("开始清理用户 {} 的所有在线状态", user_id);

        let user_key = format!("online:user:{}", user_id);
        if let Ok(session_id) = redis_get_string(&user_key).await {
            let session_key = format!("session:{}", session_id);
            if let Ok(Some(session)) = redis_get_json::<MerchantAuthSession>(&session_key).await {
                // 清理在线用户索引
                if let Err(e) = Self::remove_online_user_index(&session, &session_id).await {
                    tracing::warn!("清理用户 {} 在线索引失败: {:?}", user_id, e);
                }

                // 删除session数据
                if let Err(e) = redis_del_key(&session_key).await {
                    tracing::warn!("删除用户 {} session数据失败: {:?}", user_id, e);
                }

                tracing::info!("成功清理用户 {} 的session和在线状态", user_id);
            } else {
                // session数据不存在，直接清理用户映射
                let _ = redis_del_key(&user_key).await;
                tracing::debug!("用户 {} 的session数据不存在，已清理用户映射", user_id);
            }
        } else {
            tracing::debug!("用户 {} 没有在线状态需要清理", user_id);
        }

        Ok(())
    }

    /// 清理用户的旧session和在线索引
    /// 避免重复登录时产生多个session
    async fn cleanup_user_existing_sessions(user_id: Uuid) -> Result<(), MerchantAuthError> {
        use lib_core::cache::{redis_del_key, redis_get_json, redis_get_string};

        tracing::debug!("清理用户 {} 的旧session", user_id);

        // 1. 查找用户当前的session ID
        let user_key = format!("online:user:{}", user_id);
        if let Ok(old_session_id) = redis_get_string(&user_key).await {
            tracing::debug!("找到用户 {} 的旧session: {}", user_id, old_session_id);

            // 2. 获取旧session数据（用于清理在线索引）
            let old_session_key = format!("session:{}", old_session_id);
            if let Ok(Some(old_session)) =
                redis_get_json::<MerchantAuthSession>(&old_session_key).await
            {
                // 3. 清理旧的在线用户索引
                if let Err(e) = Self::remove_online_user_index(&old_session, &old_session_id).await
                {
                    tracing::warn!("清理用户 {} 旧的在线索引失败: {:?}", user_id, e);
                }

                // 4. 删除旧的session数据
                if let Err(e) = redis_del_key(&old_session_key).await {
                    tracing::warn!("删除用户 {} 旧的session数据失败: {:?}", user_id, e);
                } else {
                    tracing::info!("成功清理用户 {} 的旧session: {}", user_id, old_session_id);
                }
            } else {
                // session数据不存在，只清理索引
                let _ = redis_del_key(&user_key).await;
                tracing::debug!("用户 {} 的旧session数据不存在，仅清理索引", user_id);
            }
        } else {
            tracing::debug!("用户 {} 没有旧的session", user_id);
        }

        Ok(())
    }
}

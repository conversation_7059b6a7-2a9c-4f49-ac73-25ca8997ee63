use crate::config::SecurityConfig;
use anyhow::<PERSON>rror as AnyhowError;
use chrono::{Duration, Utc};
use jsonwebtoken::errors::ErrorKind as JwtErrorKind;
use jsonwebtoken::{decode, encode, Decoding<PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, Header, Validation};
use serde::{Deserialize, Serialize};
use thiserror::Error;
use tracing::error;

/// Token类型枚举
#[derive(Debug, Serialize, Deserialize, Clone, Eq, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum TokenType {
    Access,
    Refresh,
}

/// 自定义认证错误类型，用于更清晰地表示 JWT 处理过程中可能出现的错误
#[derive(Error, Debug)]
pub enum AuthError {
    #[error("JWT encoding error: {0}")]
    JwtEncodingError(#[from] jsonwebtoken::errors::Error), // JWT 编码错误
    #[error("JWT decoding error: {0}")]
    JwtDecodingError(jsonwebtoken::errors::Error), // JWT 解码错误 (未From，手动处理)
    #[error("Invalid token")]
    InvalidToken, // 无效令牌
    #[error("Expired signature")]
    ExpiredSignature, // 签名已过期
    #[error("Invalid issuer")]
    InvalidIssuer, // 无效的签发者
    #[error("Invalid audience")]
    InvalidAudience, // 无效的接收者
    #[error("Invalid token type")]
    InvalidTokenType, // 无效的token类型
    #[error("Other validation error")]
    OtherValidationError, // 其他验证错误
    #[error("Anyhow error: {0}")]
    Anyhow(#[from] AnyhowError), // Anyhow 错误
}

/// 认证令牌结构体，表示 JWT Payload 中的用户相关信息和标准声明
#[derive(Debug, Serialize, Deserialize, Clone, Eq, PartialEq)]
pub struct AuthToken {
    pub id: String,               // 用户唯一ID
    pub account: String,          // 用户账号或用户名
    pub permissions: Vec<String>, // 用户权限集合
    pub role_codes: Vec<String>,  // 用户所属的角色编码集合
    pub typ: TokenType,           // Token类型
    pub exp: usize,               // 过期时间 (Expiration Time)，Unix 时间戳
    pub iat: usize,               // 签发时间 (Issued At)，Unix 时间戳
    pub iss: String,              // 签发者 (Issuer)
    pub aud: String,              // 接收者 (Audience)
}

/// 刷新令牌结构体，只包含最小必要信息
#[derive(Debug, Serialize, Deserialize, Clone, Eq, PartialEq)]
pub struct RefreshToken {
    pub id: String,     // 用户唯一ID
    pub typ: TokenType, // Token类型（固定为Refresh）
    pub exp: usize,     // 过期时间 (Expiration Time)，Unix 时间戳
    pub iat: usize,     // 签发时间 (Issued At)，Unix 时间戳
    pub iss: String,    // 签发者 (Issuer)
    pub aud: String,    // 接收者 (Audience)
}

impl AuthToken {
    /// 创建一个新的认证令牌 (JWT)
    ///
    /// # 参数
    /// - `id`: 用户ID
    /// - `account`: 用户账号
    /// - `permissions`: 用户权限列表
    /// - `role_codes`: 用户角色编码列表
    /// - `config`: 应用程序配置，包含 JWT 密钥和过期时间
    ///
    /// # 返回
    /// - `Result<String, AuthError>`: 成功返回签名的 JWT 字符串，失败返回 AuthError
    pub fn create_token(
        id: String,
        account: String,
        permissions: Vec<String>,
        role_codes: Vec<String>,
        config: &SecurityConfig,
    ) -> Result<String, AuthError> {
        let now = Utc::now();
        // 计算令牌过期时间 (Access Token 的过期时间)
        let expiration = now + Duration::seconds(config.system.jwt_access_token_exp_sec as i64);

        // 构建 AuthToken 结构体作为 Claims
        let claims = AuthToken {
            id,
            account,
            permissions,
            role_codes,
            typ: TokenType::Access,               // 明确标记为Access Token
            exp: expiration.timestamp() as usize, // 过期时间转为 Unix 时间戳
            iat: now.timestamp() as usize,        // 签发时间转为 Unix 时间戳
            iss: config.system.jwt_issuer().to_string(),
            aud: config.system.jwt_audience().to_string(),
        };

        // 创建默认 JWT Header (使用 HS256 算法)
        let header = Header::default();
        // 使用配置中的 secret key 创建编码密钥
        let encoding_key = EncodingKey::from_secret(
            config.system
                .jwt_secret()
                .as_bytes(),
        );

        // 使用 Header、Claims 和编码密钥生成并签名 JWT 字符串
        encode(&header, &claims, &encoding_key).map_err(AuthError::JwtEncodingError) // 转换为自定义错误
    }

    /// 验证 JWT 字符串并解析出 AuthToken (Claims)
    /// 只接受Access Token类型
    ///
    /// # 参数
    /// - `secret`: 用于验证签名的 secret key
    /// - `token`: 需要验证的 JWT 字符串
    ///
    /// # 返回
    /// - `Result<AuthToken, AuthError>`: 成功返回解析出的 AuthToken，失败返回 AuthError
    pub fn verify(secret: &str, token: &str) -> Result<AuthToken, AuthError> {
        // 使用 secret key 创建解码密钥
        let decoding_key = DecodingKey::from_secret(secret.as_ref());
        // 创建默认验证配置
        let mut validation = Validation::default();
        // 设置时间误差容忍度 (leeway)，0 表示不容忍误差
        validation.leeway = 0;

        // 解码并验证 JWT
        let token_data = decode::<AuthToken>(token, &decoding_key, &validation).map_err(|err| {
            match *err.kind() {
                // 根据错误类型转换为自定义错误
                JwtErrorKind::InvalidToken => AuthError::InvalidToken,
                JwtErrorKind::InvalidIssuer => AuthError::InvalidIssuer,
                JwtErrorKind::ExpiredSignature => AuthError::ExpiredSignature,
                JwtErrorKind::InvalidAudience => AuthError::InvalidAudience,
                // 捕获其他类型的解码错误
                _ => AuthError::JwtDecodingError(err),
            }
        })?;

        // 验证token类型必须是Access
        if token_data.claims.typ != TokenType::Access {
            return Err(AuthError::InvalidTokenType);
        }

        Ok(token_data.claims)
    }

    /// 创建对应的Refresh Token
    /// 只包含用户ID和必要的JWT标准字段
    ///
    /// # 参数
    /// - `config`: 应用程序配置，包含 Refresh Token 的过期时间
    ///
    /// # 返回
    /// - `Result<String, AuthError>`: 成功返回新的 Refresh Token 字符串，失败返回 AuthError
    pub fn create_refresh_token(&self, config: &SecurityConfig) -> Result<String, AuthError> {
        let now = Utc::now();
        // 计算 Refresh Token 的过期时间
        let expiration = now + Duration::seconds(config.system.jwt_refresh_token_exp_sec as i64);

        // 创建RefreshToken，只包含最小信息
        let refresh_claims = RefreshToken {
            id: self.id.clone(),
            typ: TokenType::Refresh,
            exp: expiration.timestamp() as usize,
            iat: now.timestamp() as usize,
            iss: config.system.jwt_issuer().to_string(),
            aud: config.system.jwt_audience().to_string(),
        };

        // Create a new token using the refresh claims and the secret
        let header = Header::default();
        let encoding_key = EncodingKey::from_secret(
            config.system
                .jwt_secret()
                .as_bytes(),
        );

        encode(&header, &refresh_claims, &encoding_key).map_err(AuthError::JwtEncodingError)
    }
}

impl RefreshToken {
    /// 验证 Refresh Token
    /// 只接受Refresh Token类型
    ///
    /// # 参数
    /// - `secret`: 用于验证签名的 secret key
    /// - `token`: 需要验证的 JWT 字符串
    ///
    /// # 返回
    /// - `Result<RefreshToken, AuthError>`: 成功返回解析出的 RefreshToken，失败返回 AuthError
    pub fn verify(secret: &str, token: &str) -> Result<RefreshToken, AuthError> {
        // 使用 secret key 创建解码密钥
        let decoding_key = DecodingKey::from_secret(secret.as_ref());
        // 创建默认验证配置
        let mut validation = Validation::default();
        // 设置时间误差容忍度 (leeway)，0 表示不容忍误差
        validation.leeway = 0;

        // 解码并验证 JWT
        let token_data =
            decode::<RefreshToken>(token, &decoding_key, &validation).map_err(|err| match *err
                .kind()
            {
                // 根据错误类型转换为自定义错误
                JwtErrorKind::InvalidToken => AuthError::InvalidToken,
                JwtErrorKind::InvalidIssuer => AuthError::InvalidIssuer,
                JwtErrorKind::ExpiredSignature => AuthError::ExpiredSignature,
                JwtErrorKind::InvalidAudience => AuthError::InvalidAudience,
                // 捕获其他类型的解码错误
                _ => AuthError::JwtDecodingError(err),
            })?;

        // 验证token类型必须是Refresh
        if token_data.claims.typ != TokenType::Refresh {
            return Err(AuthError::InvalidTokenType);
        }

        Ok(token_data.claims)
    }
}

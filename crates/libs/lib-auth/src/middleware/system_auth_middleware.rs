use crate::config::SecurityConfig;
use crate::middleware::sys_jwt::{AuthError, AuthToken};
use axum::{
    extract::Request,
    http::{header::AUTHORIZATION, HeaderMap},
    middleware::Next,
    response::{IntoResponse, Response},
};
use lib_core::app::app::App;
use lib_core::app::plugin::ComponentRegistry;
use lib_core::cache::{redis_del_key, redis_get_string, redis_set_ex};
use lib_core::config::ConfigRegistry;
use lib_core::{BusinessError, ResponseBuilder};
use sea_orm::{ConnectionTrait, DatabaseBackend, DatabaseConnection, Statement};
use std::time::{SystemTime, UNIX_EPOCH};
use tracing::{error, info, warn};

/// 从请求头中提取Bearer token
fn extract_bearer_token(headers: &HeaderMap) -> Option<String> {
    headers
        .get(AUTHORIZATION)
        .and_then(|auth_header| auth_header.to_str().ok())
        .and_then(|auth_str| {
            if auth_str.starts_with("Bearer ") {
                Some(auth_str[7..].to_string())
            } else {
                None
            }
        })
}

/// 检查token是否需要提醒客户端刷新
fn should_notify_refresh(exp: usize, threshold_sec: u64) -> bool {
    let current_time = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs() as usize;

    // 如果token在阈值时间内会过期，则提醒客户端刷新
    exp.saturating_sub(current_time) <= threshold_sec as usize
}

/// JWT认证中间件
///
/// 优化后的功能：
/// 1. 从Authorization header提取Bearer token
/// 2. 验证JWT token的有效性（无状态，不查Redis）
/// 3. 🔥 新增：验证token类型必须是Access Token
/// 4. 如果token快过期，通过响应头提醒客户端主动刷新
/// 5. 将认证信息存储到请求扩展中
/// 6. 设置系统管理员RSL上下文
pub async fn jwt_auth_middleware(mut request: Request, next: Next) -> Result<Response, Response> {
    let headers = request.headers();

    // 提取Bearer token
    let token = match extract_bearer_token(headers) {
        Some(token) => token,
        None => {
            let error_response =
                ResponseBuilder::unauthorized("缺少Authorization header或格式错误");
            return Err(error_response.into_response());
        }
    };

    // 运行时获取特定配置
    let security_config = App::global()
        .get_config::<SecurityConfig>()
        .map_err(|e| BusinessError::new(500, format!("获取配置失败: {}", e)))
        .unwrap();

    // 验证JWT token（无状态验证，不查Redis）
    // AuthToken::verify 现在会自动验证token类型必须是Access
    let claims = match AuthToken::verify(
        security_config.system.jwt_secret(),
        &token,
    ) {
        Ok(claims) => claims,
        Err(err) => {
            error!("JWT验证失败: {:?}", err);
            let message = match err {
                AuthError::ExpiredSignature => "Token已过期",
                AuthError::InvalidToken => "无效的Token",
                AuthError::InvalidIssuer => "Token签发者无效",
                AuthError::InvalidAudience => "Token接收者无效",
                AuthError::InvalidTokenType => "无效的Token类型，请使用Access Token",
                _ => "Token验证失败",
            };
            let error_response = ResponseBuilder::unauthorized(message);
            return Err(error_response.into_response());
        }
    };

    // 设置系统管理员RSL上下文
    if let Err(err) = set_system_admin_context(&claims).await {
        error!("设置系统管理员RSL上下文失败: {:?}", err);
        let error_response = ResponseBuilder::internal_error("上下文设置失败");
        return Err(error_response.into_response());
    }

    // 将认证信息存储到请求扩展中
    request.extensions_mut().insert(claims.clone());

    // 处理请求
    let mut response = next.run(request).await;

    // 如果token即将过期，通过响应头提醒客户端主动刷新
    if security_config.system.jwt_auto_refresh_enabled
        && should_notify_refresh(claims.exp, security_config.system.jwt_refresh_threshold_sec)
    {
        info!("Token即将过期，请主动刷新，用户: {}", claims.account);
        response
            .headers_mut()
            .insert("X-Token-Expired", "true".parse().unwrap());
    }

    Ok(response)
}

/// 从请求扩展中获取当前认证用户信息
pub fn get_current_user(request: &Request) -> Option<&AuthToken> {
    request.extensions().get::<AuthToken>()
}

/// 生成Redis存储Refresh Token的键
fn get_user_refresh_token_key(user_id: &str) -> String {
    format!("auth:refresh_token:{}", user_id)
}

/// 存储Refresh Token到Redis
pub async fn store_refresh_token(
    user_id: &str,
    refresh_token: &str,
    expire_sec: u64,
) -> Result<(), String> {
    let key = get_user_refresh_token_key(user_id);
    redis_set_ex(&key, refresh_token, expire_sec)
        .await
        .map_err(|err| {
            error!("存储refresh token到Redis失败: {:?}", err);
            "存储refresh token失败".to_string()
        })
}

/// 从Redis获取Refresh Token
pub async fn get_refresh_token(user_id: &str) -> Result<String, String> {
    let key = get_user_refresh_token_key(user_id);
    redis_get_string(&key).await.map_err(|err| {
        warn!("从Redis获取refresh token失败: {:?}", err);
        "refresh token不存在或已过期".to_string()
    })
}

/// 从Redis删除Refresh Token
pub async fn delete_refresh_token(user_id: &str) -> Result<u64, String> {
    let key = get_user_refresh_token_key(user_id);
    redis_del_key(&key).await.map_err(|err| {
        error!("删除refresh token失败: {:?}", err);
        "删除refresh token失败".to_string()
    })
}

/// 设置系统管理员RSL上下文
///
/// # 参数
/// - `auth_token`: 系统管理员认证Token
///
/// # 返回
/// - `Result<(), String>`: 成功返回空，失败返回错误信息
async fn set_system_admin_context(auth_token: &AuthToken) -> Result<(), String> {
    // 运行时获取数据库连接组件
    let db = App::global()
        .get_component::<DatabaseConnection>()
        .ok_or_else(|| BusinessError::new(500, "获取数据库连接失败".to_string()))
        .expect("获取数据库连接组件失败");

    // 调用PostgreSQL函数设置系统管理员上下文
    let sql = format!("SELECT set_system_admin_context('{}')", auth_token.id);

    db.execute(Statement::from_string(DatabaseBackend::Postgres, sql))
        .await
        .map_err(|e| format!("设置系统管理员RSL上下文失败: {}", e))?;

    tracing::debug!(
        "系统管理员RSL上下文设置成功: user_id={}, account={}",
        auth_token.id,
        auth_token.account
    );

    Ok(())
}

/// 清除系统管理员RSL上下文
///
/// # 返回
/// - `Result<(), String>`: 成功返回空，失败返回错误信息
pub async fn clear_system_admin_context() -> Result<(), String> {
    // 运行时获取数据库连接组件
    let db = App::global()
        .get_component::<DatabaseConnection>()
        .ok_or_else(|| BusinessError::new(500, "获取数据库连接失败".to_string()))
        .expect("获取数据库连接组件失败");

    // 调用PostgreSQL函数清除会话上下文
    let sql = "SELECT clear_session_context()";

    db.execute(Statement::from_string(
        DatabaseBackend::Postgres,
        sql.to_string(),
    ))
    .await
    .map_err(|e| format!("清除RSL上下文失败: {}", e))?;

    tracing::debug!("系统管理员RSL上下文已清除");
    Ok(())
}

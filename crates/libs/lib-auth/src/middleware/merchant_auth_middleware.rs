use crate::config::SecurityConfig;
use crate::middleware::merchant_session::{
    MerchantAuthError, MerchantAuthSession, MerchantId, MerchantToken,
};
use axum::{
    extract::Request,
    http::header::AUTHORIZATION,
    middleware::Next,
    response::{IntoResponse, Response},
};
use lib_core::app::app::App;
use lib_core::app::plugin::ComponentRegistry;
use lib_core::config::ConfigRegistry;
use lib_core::response::ResponseBuilder;
use lib_core::BusinessError;
use sea_orm::{ConnectionTrait, DatabaseBackend, DatabaseConnection, Statement};

/// 商户Session认证中间件（包含RLS租户上下文设置）
///
/// # 功能
/// 1. 验证JWT Token并获取Session数据
/// 2. 设置数据库RLS租户上下文
/// 3. 确保多租户数据隔离
///
/// # 参数
/// - `request`: HTTP请求
/// - `next`: 下一个中间件
///
/// # 返回
/// - `Result<Response, Response>`: 成功返回响应，失败返回错误响应
pub async fn merchant_jwt_auth_middleware(
    mut request: Request,
    next: Next,
) -> Result<Response, Response> {
    let auth_header = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|h| h.to_str().ok());

    let token = if let Some(auth_header) = auth_header {
        if auth_header.starts_with("Bearer ") {
            auth_header.trim_start_matches("Bearer ").trim().to_string()
        } else {
            return Err(ResponseBuilder::unauthorized("无效的Authorization头格式").into_response());
        }
    } else {
        return Err(ResponseBuilder::unauthorized("缺少Authorization头").into_response());
    };

    // 运行时获取特定配置
    let security_config = App::global()
        .get_config::<SecurityConfig>()
        .map_err(|e| BusinessError::new(500, format!("获取配置失败: {}", e)))
        .unwrap();

    // 验证JWT Token并获取Session数据
    let session = match MerchantAuthSession::verify_session(&token, &security_config).await {
        Ok(session) => session,
        Err(err) => {
            let error_message = match err {
                MerchantAuthError::SessionNotFound => "Session不存在",
                MerchantAuthError::SessionExpired => "Session已过期",
                MerchantAuthError::InvalidSession => "无效的Session",
                MerchantAuthError::InvalidToken => "无效的Token",
                MerchantAuthError::JwtError(_) => "Token验证失败",
                MerchantAuthError::RedisError(_) => "服务器内部错误",
                _ => "认证失败",
            };
            return Err(ResponseBuilder::unauthorized(error_message).into_response());
        }
    };

    // 关键：设置数据库RLS租户上下文
    if let Err(err) = set_tenant_context(&session).await {
        tracing::error!("设置租户上下文失败: {:?}", err);
        return Err(ResponseBuilder::internal_error("租户上下文设置失败").into_response());
    }

    // 将Session和Token添加到请求扩展中，供后续中间件使用
    request.extensions_mut().insert(session);
    request.extensions_mut().insert(MerchantToken(token));

    Ok(next.run(request).await)
}

/// 商户管理员中间件
///
/// 检查用户是否为商户管理员
pub async fn merchant_admin_middleware(request: Request, next: Next) -> Result<Response, Response> {
    if let Some(session) = request.extensions().get::<MerchantAuthSession>() {
        if session.is_merchant_admin() {
            Ok(next.run(request).await)
        } else {
            Err(ResponseBuilder::forbidden("需要商户管理员权限").into_response())
        }
    } else {
        Err(ResponseBuilder::unauthorized("缺少认证信息").into_response())
    }
}

/// 从请求中获取当前商户用户信息
///
/// # 参数
/// - `request`: HTTP请求
///
/// # 返回
/// - `Option<&MerchantAuthSession>`: 商户认证Session信息，如果不存在则返回None
pub fn get_current_merchant_user(request: &Request) -> Option<&MerchantAuthSession> {
    request.extensions().get::<MerchantAuthSession>()
}

/// 从请求中获取当前商户ID
///
/// # 参数
/// - `request`: HTTP请求
///
/// # 返回
/// - `Option<MerchantId>`: 当前商户ID，如果不存在则返回None
pub fn get_current_merchant_id(request: &Request) -> Option<MerchantId> {
    request
        .extensions()
        .get::<MerchantAuthSession>()
        .map(|session| session.current_merchant_id)
}

// ==================== 简化的Session刷新功能 ====================

/// 刷新商户Session（推荐方案）
/// 直接使用当前JWT token刷新Session
///
/// # 参数
/// - `current_token`: 当前JWT token
/// - `config`: 应用程序配置
///
/// # 返回
/// - `Result<(), MerchantAuthError>`: 成功返回空，失败返回错误
///
/// # 说明
/// 🔥 优化：在JWT包装Redis Session的架构下，刷新非常简单：
/// 1. 验证当前JWT token
/// 2. 更新Redis中Session的过期时间
/// 3. 前端继续使用原JWT即可（因为JWT只包含session_id，内容不变）
// pub async fn refresh_merchant_session(
//     current_token: &str,
//     config: &SecurityConfig,
// ) -> Result<(), MerchantAuthError> {
//     MerchantAuthSession::refresh_session(current_token, config).await
// }

/// 刷新商户Session（兼容性方法，返回原JWT）
/// 为了保持API兼容性而存在，实际上返回的JWT和输入的JWT内容完全相同
///
/// # 参数
/// - `current_token`: 当前JWT token
/// - `config`: 应用程序配置
///
/// # 返回
/// - `Result<String, MerchantAuthError>`: 成功返回原JWT，失败返回错误
pub async fn refresh_merchant_session_compat(
    current_token: &str,
    config: &SecurityConfig,
) -> Result<String, MerchantAuthError> {
    MerchantAuthSession::refresh_session(current_token, config).await
}

/// 延长Session过期时间（无需返回新token）
/// 适用于定期"心跳"延长Session有效期的场景
///
/// # 参数
/// - `current_token`: 当前JWT token
/// - `config`: 应用程序配置
///
/// # 返回
/// - `Result<(), MerchantAuthError>`: 成功返回空，失败返回错误
pub async fn extend_session_expiry(
    current_token: &str,
    config: &SecurityConfig,
) -> Result<(), MerchantAuthError> {
    use crate::middleware::merchant_session::verify_session_token;

    // 验证JWT并提取session_id
    let session_id = verify_session_token(current_token, config)?;
    let session_key = format!("session:{}", session_id);

    // 获取并更新Session
    let mut session: MerchantAuthSession = lib_core::cache::redis_get_json(&session_key)
        .await
        .map_err(MerchantAuthError::RedisError)?
        .ok_or(MerchantAuthError::SessionNotFound)?;

    // 检查是否已过期
    let now = lib_core::DateTimeUtils::now_utc().timestamp();
    if now > session.expires_at {
        let _ = lib_core::cache::redis_del_key(&session_key).await;
        return Err(MerchantAuthError::SessionExpired);
    }

    // 延长过期时间
    session.last_accessed = now;
    session.expires_at = (lib_core::DateTimeUtils::now_utc()
        + chrono::Duration::seconds(config.merchant.jwt_access_token_exp_sec as i64))
    .timestamp();

    // 保存更新的Session
    lib_core::cache::redis_set_json(
        &session_key,
        &session,
        Some(config.merchant.jwt_access_token_exp_sec as u64),
    )
    .await
    .map_err(MerchantAuthError::RedisError)?;

    Ok(())
}

// ==================== RLS 租户上下文设置 ====================

/// 设置数据库RLS租户上下文
///
/// # 功能
/// 1. 为当前数据库连接设置商户用户上下文
/// 2. 启用RLS策略
/// 3. 设置用户上下文信息
///
/// # 参数
/// - `session`: 商户认证Session
///
/// # 返回
/// - `Result<(), MerchantAuthError>`: 成功返回空，失败返回错误
pub async fn set_tenant_context(session: &MerchantAuthSession) -> Result<(), MerchantAuthError> {
    // 运行时获取数据库连接组件
    let db = App::global()
        .get_component::<DatabaseConnection>()
        .ok_or_else(|| BusinessError::new(500, "获取数据库连接失败".to_string()))
        .expect("获取数据库连接组件失败");

    // 🔥 调用PostgreSQL函数设置商户用户上下文
    let sql = format!(
        "SELECT set_merchant_user_context('{}', {})",
        session.user_id, session.current_merchant_id
    );

    db.execute(Statement::from_string(DatabaseBackend::Postgres, sql))
        .await
        .map_err(|e| {
            MerchantAuthError::RedisError(anyhow::anyhow!("设置商户用户RSL上下文失败: {}", e))
        })?;

    tracing::debug!(
        "商户用户RSL上下文设置成功: user_id={}, merchant_id={}, user_type={:?}",
        session.user_id,
        session.current_merchant_id,
        session.user_type
    );

    Ok(())
}

/// 清除数据库RLS租户上下文
///
/// # 返回
/// - `Result<(), MerchantAuthError>`: 成功返回空，失败返回错误
pub async fn clear_tenant_context() -> Result<(), MerchantAuthError> {
    // 运行时获取数据库连接组件
    let db = App::global()
        .get_component::<DatabaseConnection>()
        .ok_or_else(|| BusinessError::new(500, "获取数据库连接失败".to_string()))
        .expect("获取数据库连接组件失败");

    // 调用PostgreSQL函数清除会话上下文
    let sql = "SELECT clear_session_context()";

    db.execute(Statement::from_string(
        DatabaseBackend::Postgres,
        sql.to_string(),
    ))
    .await
    .map_err(|e| MerchantAuthError::RedisError(anyhow::anyhow!("清除RSL上下文失败: {}", e)))?;

    tracing::debug!("RSL租户上下文已清除");
    Ok(())
}

/// 获取PostgreSQL RLS策略示例SQL
///
/// # 返回
/// - `Vec<String>`: RLS策略创建SQL示例
pub fn get_rls_policy_examples() -> Vec<String> {
    vec![
        // 订单表的RLS策略示例
        r#"
-- 启用RLS
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

-- 创建租户隔离策略
CREATE POLICY orders_tenant_isolation ON orders
    FOR ALL
    TO PUBLIC
    USING (merchant_id = current_setting('app.current_tenant_id')::bigint);
        "#
        .to_string(),
        // 客户表的RLS策略示例
        r#"
-- 启用RLS
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;

-- 创建租户隔离策略
CREATE POLICY customers_tenant_isolation ON customers
    FOR ALL
    TO PUBLIC
    USING (merchant_id = current_setting('app.current_tenant_id')::bigint);
        "#
        .to_string(),
        // 基于用户类型的更细粒度策略示例
        r#"
-- 敏感数据表：只有管理员可以访问
ALTER TABLE merchant_financial_data ENABLE ROW LEVEL SECURITY;

CREATE POLICY financial_data_admin_only ON merchant_financial_data
    FOR ALL
    TO PUBLIC
    USING (
        merchant_id = current_setting('app.current_tenant_id')::bigint 
        AND current_setting('app.current_user_type') = 'admin'
    );
        "#
        .to_string(),
    ]
}

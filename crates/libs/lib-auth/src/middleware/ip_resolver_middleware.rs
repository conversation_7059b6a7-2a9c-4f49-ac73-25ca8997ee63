use axum::{
    extract::{ConnectInfo, Request},
    middleware::Next,
    response::Response,
};
use std::net::{IpAddr, SocketAddr};
use tracing::{debug, warn};

/// 客户端IP地址结构体
#[derive(Debug, Clone)]
pub struct ClientIp(pub String);

/// IP解析中间件
/// 按优先级从HTTP头或连接信息中解析客户端真实IP地址
pub async fn ip_resolver_middleware(
    ConnectInfo(socket_addr): ConnectInfo<SocketAddr>,
    mut request: Request,
    next: Next,
) -> Response {
    let client_ip_str = extract_client_ip(&request, socket_addr);
    debug!("解析到客户端IP: {}", client_ip_str);

    // 将IP地址存储到请求扩展中
    let client_ip = ClientIp(client_ip_str);
    request.extensions_mut().insert(client_ip);

    next.run(request).await
}

/// 从请求中提取客户端真实IP地址
/// 按优先级检查各种代理头和连接信息
fn extract_client_ip(request: &Request, socket_addr: SocketAddr) -> String {
    let headers = request.headers();

    // 优先级1: X-Real-IP (最常用的真实IP头)
    if let Some(real_ip) = headers.get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            if let Some(ip) = parse_and_validate_ip(ip_str) {
                debug!("从X-Real-IP头获取IP: {}", ip);
                return ip;
            }
        }
    }

    // 优先级2: X-Forwarded-For (可能包含多个IP，取第一个)
    if let Some(forwarded_for) = headers.get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded_for.to_str() {
            // X-Forwarded-For格式: client_ip, proxy1_ip, proxy2_ip
            let first_ip = forwarded_str.split(',').next().unwrap_or("").trim();
            if let Some(ip) = parse_and_validate_ip(first_ip) {
                debug!("从X-Forwarded-For头获取IP: {}", ip);
                return ip;
            }
        }
    }

    // 优先级3: CF-Connecting-IP (Cloudflare)
    if let Some(cf_ip) = headers.get("cf-connecting-ip") {
        if let Ok(ip_str) = cf_ip.to_str() {
            if let Some(ip) = parse_and_validate_ip(ip_str) {
                debug!("从CF-Connecting-IP头获取IP: {}", ip);
                return ip;
            }
        }
    }

    // 优先级4: X-Client-IP
    if let Some(client_ip) = headers.get("x-client-ip") {
        if let Ok(ip_str) = client_ip.to_str() {
            if let Some(ip) = parse_and_validate_ip(ip_str) {
                debug!("从X-Client-IP头获取IP: {}", ip);
                return ip;
            }
        }
    }

    // 优先级5: X-Cluster-Client-IP
    if let Some(cluster_ip) = headers.get("x-cluster-client-ip") {
        if let Ok(ip_str) = cluster_ip.to_str() {
            if let Some(ip) = parse_and_validate_ip(ip_str) {
                debug!("从X-Cluster-Client-IP头获取IP: {}", ip);
                return ip;
            }
        }
    }

    // 最后选择: 使用TCP连接的远程地址
    let ip = socket_addr.ip();
    let resolved_ip = normalize_ip(ip);
    debug!("从TCP连接获取IP: {}", resolved_ip);
    resolved_ip
}

/// 解析和验证IP地址字符串
fn parse_and_validate_ip(ip_str: &str) -> Option<String> {
    if ip_str.is_empty() {
        return None;
    }

    // 尝试解析为IP地址
    match ip_str.parse::<IpAddr>() {
        Ok(ip) => {
            let normalized = normalize_ip(ip);
            // 过滤掉私有IP和本地回环地址（在生产环境中可能需要）
            if is_valid_public_ip(&ip) {
                Some(normalized)
            } else {
                // 在开发环境中，我们可能也需要记录私有IP
                Some(normalized)
            }
        }
        Err(_) => {
            warn!("无效的IP地址格式: {}", ip_str);
            None
        }
    }
}

/// 标准化IP地址格式
/// 将IPv4映射的IPv6地址转换为IPv4格式
fn normalize_ip(ip: IpAddr) -> String {
    match ip {
        IpAddr::V4(ipv4) => ipv4.to_string(),
        IpAddr::V6(ipv6) => {
            // 检查是否为IPv4映射的IPv6地址 (::ffff:x.x.x.x)
            if let Some(ipv4) = ipv6.to_ipv4_mapped() {
                ipv4.to_string()
            } else {
                ipv6.to_string()
            }
        }
    }
}

/// 检查是否为有效的公网IP地址
/// 在生产环境中可能需要过滤私有IP，但在开发环境中通常需要保留
fn is_valid_public_ip(ip: &IpAddr) -> bool {
    match ip {
        IpAddr::V4(ipv4) => {
            // 这里可以根据需要添加私有IP过滤逻辑
            // 目前返回true，即接受所有IPv4地址（包括私有IP）
            !ipv4.is_loopback() && !ipv4.is_unspecified()
        }
        IpAddr::V6(ipv6) => {
            // IPv6地址验证
            !ipv6.is_loopback() && !ipv6.is_unspecified()
        }
    }
}

/// 获取当前请求的客户端IP地址
/// 从请求扩展中提取之前解析的IP
pub fn get_client_ip(request: &Request) -> Option<String> {
    request
        .extensions()
        .get::<ClientIp>()
        .map(|client_ip| client_ip.0.clone())
}

/// 检查IP是否在白名单中（可用于安全控制）
pub fn is_ip_whitelisted(ip: &str, whitelist: &[String]) -> bool {
    whitelist.iter().any(|allowed_ip| {
        // 支持CIDR格式的IP范围匹配（这里简化实现）
        allowed_ip == ip || allowed_ip == "*"
    })
}

/// 检查IP是否在黑名单中
pub fn is_ip_blacklisted(ip: &str, blacklist: &[String]) -> bool {
    blacklist.iter().any(|blocked_ip| blocked_ip == ip)
}

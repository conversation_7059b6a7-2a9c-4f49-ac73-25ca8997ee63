use lib_macros::Configurable;
use schemars::JsonSchema;
use serde::Deserialize;

/// 系统用户安全配置
#[derive(Debug, Clone, JsonSchema, Deserialize)]
pub struct SystemSecurityConfig {
    /// 登录失败重试次数
    #[serde(default = "default_login_fail_retry")]
    pub login_fail_retry: u32,

    /// 登录失败重试等待时间（秒）
    #[serde(default = "default_login_fail_retry_wait_sec")]
    pub login_fail_retry_wait_sec: u64,

    /// 删除数据时回收天数
    #[serde(default = "default_trash_recycle_days")]
    pub trash_recycle_days: u32,

    /// JWT 密钥（生产环境中必须配置）
    pub jwt_secret: Option<String>,

    /// JWT 访问令牌过期时间（秒）
    #[serde(default = "default_jwt_access_token_exp_sec")]
    pub jwt_access_token_exp_sec: u64,

    /// JWT 刷新令牌过期时间（秒）
    #[serde(default = "default_jwt_refresh_token_exp_sec")]
    pub jwt_refresh_token_exp_sec: u64,

    /// JWT 签发者
    #[serde(default = "default_jwt_issuer")]
    pub jwt_issuer: String,

    /// JWT 接收者
    #[serde(default = "default_jwt_audience")]
    pub jwt_audience: String,

    /// JWT自动刷新开关
    #[serde(default = "default_jwt_auto_refresh_enabled")]
    pub jwt_auto_refresh_enabled: bool,

    /// JWT自动刷新阈值（秒）- 在token过期前多少秒开始自动续期
    #[serde(default = "default_jwt_refresh_threshold_sec")]
    pub jwt_refresh_threshold_sec: u64,
}

/// 商户用户安全配置
#[derive(Debug, Clone, JsonSchema, Deserialize)]
pub struct MerchantSecurityConfig {
    /// 商户JWT 密钥（生产环境中必须配置，与系统JWT密钥隔离）
    pub jwt_secret: Option<String>,

    /// 商户JWT 访问令牌过期时间（秒）- 通常比系统用户更长
    #[serde(default = "default_merchant_jwt_access_token_exp_sec")]
    pub jwt_access_token_exp_sec: u64,

    /// 商户JWT 刷新令牌过期时间（秒）
    #[serde(default = "default_merchant_jwt_refresh_token_exp_sec")]
    pub jwt_refresh_token_exp_sec: u64,

    /// 商户JWT自动刷新开关
    #[serde(default = "default_merchant_jwt_auto_refresh_enabled")]
    pub jwt_auto_refresh_enabled: bool,

    /// 商户JWT自动刷新阈值（秒）- 在token过期前多少秒开始自动续期
    #[serde(default = "default_merchant_jwt_refresh_threshold_sec")]
    pub jwt_refresh_threshold_sec: u64,

    /// 商户登录失败重试次数（可以与系统用户不同）
    #[serde(default = "default_merchant_login_fail_retry")]
    pub login_fail_retry: u32,

    /// 商户登录失败重试等待时间（秒）
    #[serde(default = "default_merchant_login_fail_retry_wait_sec")]
    pub login_fail_retry_wait_sec: u64,
}

/// 聚合安全配置 - 包含系统和商户配置
#[derive(Debug, Configurable, Clone, JsonSchema, Deserialize)]
#[config_prefix = "security"]
pub struct SecurityConfig {
    /// 系统用户安全配置
    pub system: SystemSecurityConfig,

    /// 商户用户安全配置
    pub merchant: MerchantSecurityConfig,
}

/// 默认登录失败重试次数
fn default_login_fail_retry() -> u32 {
    5
}

/// 默认登录失败重试等待时间（5分钟）
fn default_login_fail_retry_wait_sec() -> u64 {
    300
}

/// 默认删除数据回收天数（30天）
fn default_trash_recycle_days() -> u32 {
    30
}

/// 默认JWT访问令牌过期时间（1小时）
fn default_jwt_access_token_exp_sec() -> u64 {
    3600
}

/// 默认JWT刷新令牌过期时间（7天）
fn default_jwt_refresh_token_exp_sec() -> u64 {
    604800
}

/// 默认JWT签发者
fn default_jwt_issuer() -> String {
    "invoice-app".to_string()
}

/// 默认JWT接收者
fn default_jwt_audience() -> String {
    "invoice-app".to_string()
}

/// 默认JWT自动刷新开关（启用）
fn default_jwt_auto_refresh_enabled() -> bool {
    true
}

/// 默认JWT自动刷新阈值（5分钟）
fn default_jwt_refresh_threshold_sec() -> u64 {
    300
}

// ==================== 商户用户安全配置默认值 ====================

/// 默认商户JWT访问令牌过期时间（8小时）- 比系统用户更长
fn default_merchant_jwt_access_token_exp_sec() -> u64 {
    28800
}

/// 默认商户JWT刷新令牌过期时间（30天）- 比系统用户更长
fn default_merchant_jwt_refresh_token_exp_sec() -> u64 {
    2592000
}

/// 默认商户JWT自动刷新开关（启用）
fn default_merchant_jwt_auto_refresh_enabled() -> bool {
    true
}

/// 默认商户JWT自动刷新阈值（30分钟）- 比系统用户更宽松
fn default_merchant_jwt_refresh_threshold_sec() -> u64 {
    1800
}

/// 默认商户登录失败重试次数（比系统用户更宽松）
fn default_merchant_login_fail_retry() -> u32 {
    5
}

/// 默认商户登录失败重试等待时间（3分钟）
fn default_merchant_login_fail_retry_wait_sec() -> u64 {
    180
}

impl SystemSecurityConfig {
    /// 获取系统JWT密钥，如果未配置则返回默认值
    /// 
    /// 注意：生产环境必须配置自定义的jwt_secret
    pub fn jwt_secret(&self) -> &str {
        self.jwt_secret.as_deref().unwrap_or("default-system-secret-key-change-in-production")
    }

    /// 检查是否使用了默认的系统JWT密钥（不安全）
    pub fn is_using_default_jwt_secret(&self) -> bool {
        self.jwt_secret.is_none() || 
        self.jwt_secret.as_deref() == Some("default-system-secret-key-change-in-production")
    }

    /// 获取JWT签发者
    pub fn jwt_issuer(&self) -> &str {
        &self.jwt_issuer
    }

    /// 获取JWT接收者
    pub fn jwt_audience(&self) -> &str {
        &self.jwt_audience
    }
}

impl MerchantSecurityConfig {
    /// 获取商户JWT密钥，如果未配置则返回默认值
    /// 
    /// 注意：生产环境必须配置自定义的jwt_secret，且应与系统JWT密钥不同
    pub fn jwt_secret(&self) -> &str {
        self.jwt_secret.as_deref().unwrap_or("default-merchant-secret-key-change-in-production")
    }

    /// 检查是否使用了默认的商户JWT密钥（不安全）
    pub fn is_using_default_jwt_secret(&self) -> bool {
        self.jwt_secret.is_none() || 
        self.jwt_secret.as_deref() == Some("default-merchant-secret-key-change-in-production")
    }

    /// 检查商户JWT密钥是否与系统JWT密钥相同（不安全）
    pub fn is_same_as_system_jwt_secret(&self, system_config: &SystemSecurityConfig) -> bool {
        self.jwt_secret() == system_config.jwt_secret()
    }
}

impl SecurityConfig {
    /// 便利方法：快速获取系统配置
    pub fn system(&self) -> &SystemSecurityConfig {
        &self.system
    }
    
    /// 便利方法：快速获取商户配置
    pub fn merchant(&self) -> &MerchantSecurityConfig {
        &self.merchant
    }

    /// 跨配置安全验证
    pub fn validate_security(&self) -> Result<(), String> {
        // 检查系统JWT密钥是否使用默认值
        if self.system.is_using_default_jwt_secret() {
            return Err("系统JWT密钥使用默认值，生产环境不安全".to_string());
        }

        // 检查商户JWT密钥是否使用默认值
        if self.merchant.is_using_default_jwt_secret() {
            return Err("商户JWT密钥使用默认值，生产环境不安全".to_string());
        }

        // 检查系统和商户JWT密钥是否相同
        if self.merchant.is_same_as_system_jwt_secret(&self.system) {
            return Err("系统和商户JWT密钥不能相同，存在安全风险".to_string());
        }

        Ok(())
    }

    // ==================== 向后兼容方法 ====================
    // 保持原有API不变，内部委托给system配置
    
    /// 获取系统JWT密钥（向后兼容）
    pub fn jwt_secret(&self) -> &str {
        self.system.jwt_secret()
    }

    /// 检查是否使用了默认的JWT密钥（向后兼容）
    pub fn is_using_default_jwt_secret(&self) -> bool {
        self.system.is_using_default_jwt_secret()
    }

    /// 获取JWT签发者（向后兼容）
    pub fn jwt_issuer(&self) -> &str {
        self.system.jwt_issuer()
    }

    /// 获取JWT接收者（向后兼容）
    pub fn jwt_audience(&self) -> &str {
        self.system.jwt_audience()
    }
}

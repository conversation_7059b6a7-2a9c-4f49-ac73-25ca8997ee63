pub mod config;
pub mod middleware;
pub mod permissions;

// 便利导出
pub use permissions::system_middleware::{
    require_all_permissions, require_any_permissions, require_permission, require_role,
};

// 导出主要的中间件
pub use middleware::{
    merchant_auth_middleware::{
        clear_tenant_context, get_current_merchant_id, get_current_merchant_user,
        merchant_admin_middleware, merchant_jwt_auth_middleware, set_tenant_context,
    },
    merchant_session::{
        MerchantAuthError, MerchantAuthSession, MerchantId, MerchantOnlineStats, MerchantUserType,
        MerchantToken, OnlineUserInfo,
    },
    sys_jwt::{AuthError, AuthToken},
    system_auth_middleware::{get_current_user, jwt_auth_middleware},
};

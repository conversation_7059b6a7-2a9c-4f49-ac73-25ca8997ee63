/// 通用权限匹配模块
/// 
/// 支持多级通配符权限匹配，可以在系统权限和商户权限中复用
/// 
/// 权限匹配规则：
/// 1. 精确匹配：用户权限完全等于所需权限
/// 2. 末尾通配符：用户权限以 * 结尾，所需权限以用户权限前缀开头
/// 3. 多级通配符：支持按分隔符（默认为:）进行分段匹配
///
/// 示例：
/// - 用户有 "sys:user:*"，可以访问 "sys:user:page", "sys:user:create" 等
/// - 用户有 "sys:*"，可以访问所有 "sys:" 开头的权限  
/// - 用户有 "sys:*:*"，可以访问 "sys:user:page", "sys:role:create" 等
/// - 用户有 "*:*:*"，拥有所有三段式权限
/// - 用户有 "*"，拥有所有权限（超级管理员）

/// 权限匹配器结构体
pub struct PermissionMatcher {
    /// 权限分隔符，默认为 ":"
    separator: char,
}

impl Default for PermissionMatcher {
    fn default() -> Self {
        Self::new(':')
    }
}

impl PermissionMatcher {
    /// 创建新的权限匹配器
    pub fn new(separator: char) -> Self {
        Self { separator }
    }

    /// 检查用户是否拥有指定权限
    /// 
    /// # 参数
    /// - `user_permissions`: 用户拥有的权限列表
    /// - `required_permission`: 所需权限
    /// 
    /// # 返回
    /// - `bool`: 是否拥有权限
    pub fn has_permission(&self, user_permissions: &[String], required_permission: &str) -> bool {
        for user_perm in user_permissions {
            // 精确匹配
            if user_perm == required_permission {
                return true;
            }

            // 通配符匹配
            if user_perm.contains('*') {
                if self.matches_wildcard_pattern(user_perm, required_permission) {
                    return true;
                }
            }
        }
        false
    }

    /// 检查是否拥有所有指定权限（AND逻辑）
    pub fn has_all_permissions(&self, user_permissions: &[String], required_permissions: &[String]) -> bool {
        required_permissions
            .iter()
            .all(|perm| self.has_permission(user_permissions, perm))
    }

    /// 检查是否拥有任意一个指定权限（OR逻辑）
    pub fn has_any_permission(&self, user_permissions: &[String], required_permissions: &[String]) -> bool {
        required_permissions
            .iter()
            .any(|perm| self.has_permission(user_permissions, perm))
    }

    /// 通配符模式匹配函数
    ///
    /// 支持以下模式：
    /// - 末尾通配符：permission:*
    /// - 多级通配符：*:*:*, sys:*:*, *:user:*
    /// - 完全通配符：*:*:* 可匹配任意段数的权限（如 cc, XX:Xx, sys:user:create）
    /// - 全通配符：*
    fn matches_wildcard_pattern(&self, pattern: &str, permission: &str) -> bool {
        // 如果权限是空字符串，只有模式为*才匹配
        if permission.is_empty() {
            return pattern == "*";
        }

        // 如果模式就是单个*，匹配所有非空权限
        if pattern == "*" {
            return true;
        }

        // 如果模式以*结尾且不包含其他*，使用简单的前缀匹配
        if pattern.ends_with('*') && pattern.matches('*').count() == 1 {
            let prefix = &pattern[..pattern.len() - 1];
            return permission.starts_with(prefix);
        }

        // 多级通配符匹配：按分隔符分段匹配
        let pattern_parts: Vec<&str> = pattern.split(self.separator).collect();
        let permission_parts: Vec<&str> = permission.split(self.separator).collect();

        // 检测完全通配符模式（所有段都是*）
        let is_full_wildcard = pattern_parts.iter().all(|part| *part == "*");
        
        // 如果是完全通配符，且通配符段数 >= 权限段数，则匹配
        if is_full_wildcard && pattern_parts.len() >= permission_parts.len() {
            return true;
        }

        // 原有逻辑：段数必须完全匹配（用于非完全通配符的情况）
        if pattern_parts.len() != permission_parts.len() {
            return false;
        }

        // 逐段匹配
        for (i, pattern_part) in pattern_parts.iter().enumerate() {
            if i >= permission_parts.len() {
                return false;
            }

            // 如果模式段是*，跳过这段（匹配任意内容）
            if *pattern_part == "*" {
                continue;
            }

            // 如果模式段以*结尾，进行前缀匹配
            if pattern_part.ends_with('*') {
                let prefix = &pattern_part[..pattern_part.len() - 1];
                if !permission_parts[i].starts_with(prefix) {
                    return false;
                }
            } else {
                // 精确匹配这一段
                if pattern_part != &permission_parts[i] {
                    return false;
                }
            }
        }

        true
    }
}

/// 全局默认权限匹配器实例（使用 ':' 作为分隔符）
static DEFAULT_MATCHER: PermissionMatcher = PermissionMatcher { separator: ':' };

/// 便利函数：使用默认分隔符检查权限
pub fn has_permission(user_permissions: &[String], required_permission: &str) -> bool {
    DEFAULT_MATCHER.has_permission(user_permissions, required_permission)
}

/// 便利函数：使用默认分隔符检查所有权限
pub fn has_all_permissions(user_permissions: &[String], required_permissions: &[String]) -> bool {
    DEFAULT_MATCHER.has_all_permissions(user_permissions, required_permissions)
}

/// 便利函数：使用默认分隔符检查任意权限
pub fn has_any_permission(user_permissions: &[String], required_permissions: &[String]) -> bool {
    DEFAULT_MATCHER.has_any_permission(user_permissions, required_permissions)
}
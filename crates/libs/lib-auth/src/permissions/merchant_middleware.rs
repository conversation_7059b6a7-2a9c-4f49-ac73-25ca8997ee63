/// 商户权限中间件
///
/// 提供与系统权限中间件一致的设计模式，统一商户权限验证接口
///
/// ## 核心功能：
/// 1. 单个权限验证（基于当前商户）
/// 2. 多权限AND验证（必须全部满足）
/// 3. 多权限OR验证（满足任意一个即可）
/// 4. 商户管理员验证
/// 5. 商户员工验证
///
/// ## 使用示例：
/// ```rust
/// use lib_auth::permissions::merchant_middleware::*;
///
/// // 单个权限验证
/// require_merchant_permission("orders:view");
///
/// // 多权限AND验证
/// require_merchant_all_permissions(&["orders:view", "orders:create"]);
///
/// // 多权限OR验证
/// require_merchant_any_permissions(&["orders:admin", "orders:super"]);
///
/// // 管理员权限
/// require_merchant_admin();
///
/// // 员工权限
/// require_merchant_employee()
/// ```

use crate::middleware::merchant_session::{MerchantAuthSession, MerchantUserType};
use crate::permissions::permission_matcher;
use axum::{
    extract::Request,
    middleware::Next,
    response::{IntoResponse, Response},
};
use lib_core::response::ResponseBuilder;
use std::future::Future;
use std::pin::Pin;
use tracing::{info, warn};


/// 商户权限匹配函数 - 复用通用权限匹配逻辑
fn has_merchant_permission(user_permissions: &[String], required_permission: &str) -> bool {
    permission_matcher::has_permission(user_permissions, required_permission)
}

/// 检查是否拥有所有指定商户权限
fn has_all_merchant_permissions(
    user_permissions: &[String],
    required_permissions: &[String],
) -> bool {
    permission_matcher::has_all_permissions(user_permissions, required_permissions)
}

/// 检查是否拥有任意一个指定商户权限
fn has_any_merchant_permission(
    user_permissions: &[String],
    required_permissions: &[String],
) -> bool {
    permission_matcher::has_any_permission(user_permissions, required_permissions)
}

/// 从请求中获取商户认证Session
fn get_merchant_session(request: &Request) -> Option<&MerchantAuthSession> {
    request.extensions().get::<MerchantAuthSession>()
}

// ==================== 基础权限中间件 ====================

/// 创建商户权限中间件 - 验证单个权限
pub fn create_merchant_permission_middleware(
    permission: impl Into<String> + Clone + Send + 'static,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    move |request: Request, next: Next| {
        let permission = permission.clone().into();
        Box::pin(async move {
            match get_merchant_session(&request) {
                Some(session) => {
                    let user_permissions = session.get_current_merchant_permissions();
                    if has_merchant_permission(&user_permissions, &permission) {
                        info!(
                            "商户权限验证通过: 用户={}, 商户={}, 权限={}",
                            session.username, session.current_merchant_id, permission
                        );
                        Ok(next.run(request).await)
                    } else {
                        warn!(
                            "商户权限验证失败: 用户={}, 商户={}, 需要权限={}, 用户权限={:?}",
                            session.username,
                            session.current_merchant_id,
                            permission,
                            user_permissions
                        );
                        let error_response =
                            ResponseBuilder::forbidden(&format!("缺少权限: {}", permission));
                        Err(error_response.into_response())
                    }
                }
                None => {
                    warn!("未认证商户用户尝试访问需要权限 {} 的接口", permission);
                    let error_response = ResponseBuilder::unauthorized("需要商户认证");
                    Err(error_response.into_response())
                }
            }
        })
    }
}

/// 创建商户管理员权限中间件
pub fn create_merchant_admin_middleware()
-> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    move |request: Request, next: Next| {
        Box::pin(async move {
            match get_merchant_session(&request) {
                Some(session) => {
                    if session.is_merchant_admin() {
                        info!(
                            "商户管理员权限验证通过: 用户={}, 商户={}",
                            session.username, session.current_merchant_id
                        );
                        Ok(next.run(request).await)
                    } else {
                        warn!(
                            "商户管理员权限验证失败: 用户={}, 商户={}, 用户类型={:?}",
                            session.username, session.current_merchant_id, session.user_type
                        );
                        let error_response = ResponseBuilder::forbidden("需要商户管理员权限");
                        Err(error_response.into_response())
                    }
                }
                None => {
                    warn!("未认证商户用户尝试访问管理员接口");
                    let error_response = ResponseBuilder::unauthorized("需要商户认证");
                    Err(error_response.into_response())
                }
            }
        })
    }
}

/// 创建商户员工权限中间件
pub fn create_merchant_employee_middleware()
-> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    move |request: Request, next: Next| {
        Box::pin(async move {
            match get_merchant_session(&request) {
                Some(session) => {
                    if session.user_type == MerchantUserType::MerchantEmployee {
                        info!(
                            "商户员工权限验证通过: 用户={}, 商户={}",
                            session.username, session.current_merchant_id
                        );
                        Ok(next.run(request).await)
                    } else {
                        warn!(
                            "商户员工权限验证失败: 用户={}, 商户={}, 用户类型={:?}",
                            session.username, session.current_merchant_id, session.user_type
                        );
                        let error_response = ResponseBuilder::forbidden("需要商户员工权限");
                        Err(error_response.into_response())
                    }
                }
                None => {
                    warn!("未认证商户用户尝试访问员工接口");
                    let error_response = ResponseBuilder::unauthorized("需要商户认证");
                    Err(error_response.into_response())
                }
            }
        })
    }
}

/// 创建商户多权限AND中间件 - 需要所有权限
pub fn create_merchant_all_permissions_middleware(
    permissions: impl Into<Vec<String>> + Clone + Send + 'static,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    move |request: Request, next: Next| {
        let permissions = permissions.clone().into();
        Box::pin(async move {
            match get_merchant_session(&request) {
                Some(session) => {
                    let user_permissions = session.get_current_merchant_permissions();
                    if has_all_merchant_permissions(&user_permissions, &permissions) {
                        info!(
                            "商户多权限AND验证通过: 用户={}, 商户={}, 权限={:?}",
                            session.username, session.current_merchant_id, permissions
                        );
                        Ok(next.run(request).await)
                    } else {
                        let missing_permissions: Vec<&String> = permissions
                            .iter()
                            .filter(|perm| !has_merchant_permission(&user_permissions, perm))
                            .collect();

                        warn!(
                            "商户多权限AND验证失败: 用户={}, 商户={}, 缺少权限={:?}, 用户权限={:?}",
                            session.username,
                            session.current_merchant_id,
                            missing_permissions,
                            user_permissions
                        );
                        let error_response = ResponseBuilder::forbidden(&format!(
                            "缺少权限: {:?}",
                            missing_permissions
                        ));
                        Err(error_response.into_response())
                    }
                }
                None => {
                    warn!("未认证商户用户尝试访问需要多权限的接口");
                    let error_response = ResponseBuilder::unauthorized("需要商户认证");
                    Err(error_response.into_response())
                }
            }
        })
    }
}

/// 创建商户多权限OR中间件 - 需要任一权限
pub fn create_merchant_any_permissions_middleware(
    permissions: impl Into<Vec<String>> + Clone + Send + 'static,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    move |request: Request, next: Next| {
        let permissions = permissions.clone().into();
        Box::pin(async move {
            match get_merchant_session(&request) {
                Some(session) => {
                    let user_permissions = session.get_current_merchant_permissions();
                    if has_any_merchant_permission(&user_permissions, &permissions) {
                        info!(
                            "商户多权限OR验证通过: 用户={}, 商户={}, 权限={:?}",
                            session.username, session.current_merchant_id, permissions
                        );
                        Ok(next.run(request).await)
                    } else {
                        warn!(
                            "商户多权限OR验证失败: 用户={}, 商户={}, 需要权限={:?}, 用户权限={:?}",
                            session.username,
                            session.current_merchant_id,
                            permissions,
                            user_permissions
                        );
                        let error_response = ResponseBuilder::forbidden(&format!(
                            "需要以下权限之一: {:?}",
                            permissions
                        ));
                        Err(error_response.into_response())
                    }
                }
                None => {
                    warn!("未认证商户用户尝试访问需要任意权限的接口");
                    let error_response = ResponseBuilder::unauthorized("需要商户认证");
                    Err(error_response.into_response())
                }
            }
        })
    }
}

// ==================== 便利函数 ====================

/// 便利函数：创建商户权限检查
pub fn require_merchant_permission(
    permission: &str,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    create_merchant_permission_middleware(permission.to_string())
}

/// 便利函数：创建商户管理员检查
pub fn require_merchant_admin()
-> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    create_merchant_admin_middleware()
}

/// 便利函数：创建商户员工检查
pub fn require_merchant_employee()
-> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    create_merchant_employee_middleware()
}

/// 便利函数：创建商户多权限AND检查
pub fn require_merchant_all_permissions(
    permissions: &[&str],
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    let permissions: Vec<String> = permissions.iter().map(|s| s.to_string()).collect();
    create_merchant_all_permissions_middleware(permissions)
}

/// 便利函数：创建商户多权限OR检查
pub fn require_merchant_any_permissions(
    permissions: &[&str],
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    let permissions: Vec<String> = permissions.iter().map(|s| s.to_string()).collect();
    create_merchant_any_permissions_middleware(permissions)
}

// ==================== 组合便利函数 ====================

/// 便利函数：商户管理员 + 特定权限
pub fn require_merchant_admin_with_permission(
    permission: &str,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    let permission = permission.to_string();
    move |request: Request, next: Next| {
        let permission = permission.clone();
        Box::pin(async move {
            match get_merchant_session(&request) {
                Some(session) => {
                    // 检查管理员权限
                    if !session.is_merchant_admin() {
                        warn!(
                            "商户管理员权限验证失败: 用户={}, 商户={}, 用户类型={:?}",
                            session.username, session.current_merchant_id, session.user_type
                        );
                        let error_response = ResponseBuilder::forbidden("需要商户管理员权限");
                        return Err(error_response.into_response());
                    }

                    // 检查特定权限
                    let user_permissions = session.get_current_merchant_permissions();
                    if has_merchant_permission(&user_permissions, &permission) {
                        info!(
                            "商户管理员+权限验证通过: 用户={}, 商户={}, 权限={}",
                            session.username, session.current_merchant_id, permission
                        );
                        Ok(next.run(request).await)
                    } else {
                        warn!(
                            "商户管理员+权限验证失败: 用户={}, 商户={}, 需要权限={}, 用户权限={:?}",
                            session.username,
                            session.current_merchant_id,
                            permission,
                            user_permissions
                        );
                        let error_response =
                            ResponseBuilder::forbidden(&format!("管理员缺少权限: {}", permission));
                        Err(error_response.into_response())
                    }
                }
                None => {
                    warn!("未认证商户用户尝试访问管理员+权限接口");
                    let error_response = ResponseBuilder::unauthorized("需要商户认证");
                    Err(error_response.into_response())
                }
            }
        })
    }
}

/// 便利函数：商户员工 + 特定权限
pub fn require_merchant_employee_with_permission(
    permission: &str,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    let permission = permission.to_string();
    move |request: Request, next: Next| {
        let permission = permission.clone();
        Box::pin(async move {
            match get_merchant_session(&request) {
                Some(session) => {
                    // 检查员工权限
                    if session.user_type != MerchantUserType::MerchantEmployee {
                        warn!(
                            "商户员工权限验证失败: 用户={}, 商户={}, 用户类型={:?}",
                            session.username, session.current_merchant_id, session.user_type
                        );
                        let error_response = ResponseBuilder::forbidden("需要商户员工权限");
                        return Err(error_response.into_response());
                    }

                    // 检查特定权限
                    let user_permissions = session.get_current_merchant_permissions();
                    if has_merchant_permission(&user_permissions, &permission) {
                        info!(
                            "商户员工+权限验证通过: 用户={}, 商户={}, 权限={}",
                            session.username, session.current_merchant_id, permission
                        );
                        Ok(next.run(request).await)
                    } else {
                        warn!(
                            "商户员工+权限验证失败: 用户={}, 商户={}, 需要权限={}, 用户权限={:?}",
                            session.username,
                            session.current_merchant_id,
                            permission,
                            user_permissions
                        );
                        let error_response =
                            ResponseBuilder::forbidden(&format!("员工缺少权限: {}", permission));
                        Err(error_response.into_response())
                    }
                }
                None => {
                    warn!("未认证商户用户尝试访问员工+权限接口");
                    let error_response = ResponseBuilder::unauthorized("需要商户认证");
                    Err(error_response.into_response())
                }
            }
        })
    }
}
 
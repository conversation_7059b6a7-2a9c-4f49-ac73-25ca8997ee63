use crate::middleware::system_auth_middleware::get_current_user;
use crate::permissions::permission_matcher;
use axum::{
    extract::Request,
    middleware::Next,
    response::{IntoResponse, Response},
};
use lib_core::response::ResponseBuilder;
use std::future::Future;
use std::pin::Pin;
use tracing::log::warn;

/// 权限匹配函数 - 支持多级通配符权限
///
/// 权限匹配规则：
/// 1. 精确匹配：用户权限完全等于所需权限
/// 2. 末尾通配符：用户权限以 * 结尾，所需权限以用户权限前缀开头
/// 3. 多级通配符：支持按分隔符（默认为:）进行分段匹配
///
/// 示例：
/// - 用户有 "sys:user:*"，可以访问 "sys:user:page", "sys:user:create" 等
/// - 用户有 "sys:*"，可以访问所有 "sys:" 开头的权限  
/// - 用户有 "sys:*:*"，可以访问 "sys:user:page", "sys:role:create" 等
/// - 用户有 "*:*:*"，拥有所有三段式权限
/// - 用户有 "*"，拥有所有权限（超级管理员）
fn has_permission(user_permissions: &[String], required_permission: &str) -> bool {
    permission_matcher::has_permission(user_permissions, required_permission)
}

/// 检查是否拥有所有指定权限
fn has_all_permissions(user_permissions: &[String], required_permissions: &[String]) -> bool {
    permission_matcher::has_all_permissions(user_permissions, required_permissions)
}

/// 检查是否拥有任意一个指定权限
fn has_any_permission(user_permissions: &[String], required_permissions: &[String]) -> bool {
    permission_matcher::has_any_permission(user_permissions, required_permissions)
}

/// 创建运行时权限中间件 - 支持字符串权限
pub fn create_runtime_permission_middleware(
    permission: impl Into<String> + Clone + Send + 'static,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    move |request: Request, next: Next| {
        let permission = permission.clone().into();
        Box::pin(async move {
            match get_current_user(&request) {
                Some(user) => {
                    if has_permission(&user.permissions, &permission) {
                        Ok(next.run(request).await)
                    } else {
                        warn!(
                            "用户 {} 尝试访问需要权限 {} 的接口，但权限不足。用户权限: {:?}",
                            user.account, permission, user.permissions
                        );
                        let error_response =
                            ResponseBuilder::forbidden(&format!("缺少权限: {}", permission));
                        Err(error_response.into_response())
                    }
                }
                None => {
                    warn!("未认证用户尝试访问需要权限 {} 的接口", permission);
                    let error_response = ResponseBuilder::unauthorized("需要认证");
                    Err(error_response.into_response())
                }
            }
        })
    }
}

/// 创建运行时角色中间件 - 支持字符串角色
pub fn create_runtime_role_middleware(
    role: impl Into<String> + Clone + Send + 'static,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    move |request: Request, next: Next| {
        let role = role.clone().into();
        Box::pin(async move {
            match get_current_user(&request) {
                Some(user) => {
                    // 使用角色编码进行匹配
                    if user.role_codes.contains(&role.to_string()) {
                        Ok(next.run(request).await)
                    } else {
                        warn!(
                            "用户 {} 尝试访问需要角色 {} 的接口，但角色不足。用户角色: {:?}",
                            user.account, role, user.role_codes
                        );
                        let error_response =
                            ResponseBuilder::forbidden(&format!("需要角色: {}", role));
                        Err(error_response.into_response())
                    }
                }
                None => {
                    warn!("未认证用户尝试访问需要角色 {} 的接口", role);
                    let error_response = ResponseBuilder::unauthorized("需要认证");
                    Err(error_response.into_response())
                }
            }
        })
    }
}

/// 创建多权限AND中间件 - 需要所有权限
pub fn create_runtime_all_permissions_middleware(
    permissions: impl Into<Vec<String>> + Clone + Send + 'static,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    move |request: Request, next: Next| {
        let permissions = permissions.clone().into();
        Box::pin(async move {
            match get_current_user(&request) {
                Some(user) => {
                    if has_all_permissions(&user.permissions, &permissions) {
                        Ok(next.run(request).await)
                    } else {
                        let missing_permissions: Vec<&String> = permissions
                            .iter()
                            .filter(|perm| !has_permission(&user.permissions, perm))
                            .collect();

                        warn!(
                            "用户 {} 缺少权限: {:?}。用户权限: {:?}",
                            user.account, missing_permissions, user.permissions
                        );
                        let error_response = ResponseBuilder::forbidden(&format!(
                            "缺少权限: {:?}",
                            missing_permissions
                        ));
                        Err(error_response.into_response())
                    }
                }
                None => {
                    warn!("未认证用户尝试访问需要多权限的接口");
                    let error_response = ResponseBuilder::unauthorized("需要认证");
                    Err(error_response.into_response())
                }
            }
        })
    }
}

/// 创建多权限OR中间件 - 需要任一权限
pub fn create_runtime_any_permissions_middleware(
    permissions: impl Into<Vec<String>> + Clone + Send + 'static,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    move |request: Request, next: Next| {
        let permissions = permissions.clone().into();
        Box::pin(async move {
            match get_current_user(&request) {
                Some(user) => {
                    if has_any_permission(&user.permissions, &permissions) {
                        Ok(next.run(request).await)
                    } else {
                        warn!(
                            "用户 {} 缺少权限（任意一个）: {:?}。用户权限: {:?}",
                            user.account, permissions, user.permissions
                        );
                        let error_response = ResponseBuilder::forbidden(&format!(
                            "需要以下权限之一: {:?}",
                            permissions
                        ));
                        Err(error_response.into_response())
                    }
                }
                None => {
                    warn!("未认证用户尝试访问需要任意权限的接口");
                    let error_response = ResponseBuilder::unauthorized("需要认证");
                    Err(error_response.into_response())
                }
            }
        })
    }
}

/// 便利函数：创建权限检查
pub fn require_permission(
    permission: &str,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    create_runtime_permission_middleware(permission.to_string())
}

/// 便利函数：创建角色检查
pub fn require_role(
    role: &str,
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    create_runtime_role_middleware(role.to_string())
}

/// 便利函数：创建多权限AND检查
pub fn require_all_permissions(
    permissions: &[&str],
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    let permissions: Vec<String> = permissions.iter().map(|s| s.to_string()).collect();
    create_runtime_all_permissions_middleware(permissions)
}

/// 便利函数：创建多权限OR检查
pub fn require_any_permissions(
    permissions: &[&str],
) -> impl Fn(Request, Next) -> Pin<Box<dyn Future<Output = Result<Response, Response>> + Send>> + Clone
{
    let permissions: Vec<String> = permissions.iter().map(|s| s.to_string()).collect();
    create_runtime_any_permissions_middleware(permissions)
}

//! Object acl module

use crate::acl::acl_common::{
    build_get_object_acl_request, build_put_object_acl_request, parse_object_acl_from_xml,
};
use crate::common::VersionIdOnlyOptions;
use crate::object::object_common::ObjectAcl;
use crate::Client;
use crate::Result;
use async_trait::async_trait;

pub type PutObjectAclOptions = VersionIdOnlyOptions;
pub type GetObjectAclOptions = VersionIdOnlyOptions;

#[async_trait]
pub trait ObjectAclOperations {
    /// Get an object's acl.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getobjectacl>
    async fn get_object_acl<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<GetObjectAclOptions>,
    ) -> Result<ObjectAcl>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Put an object's acl. If you want to restore the object's acl to follow bucket acl settings, pass acl as `ObjectAcl::Default`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobjectacl>
    async fn put_object_acl<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        acl: ObjectAcl,
        options: Option<GetObjectAclOptions>,
    ) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;
}

#[async_trait]
impl ObjectAclOperations for Client {
    /// Get an object's acl.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getobjectacl>
    async fn get_object_acl<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<GetObjectAclOptions>,
    ) -> Result<ObjectAcl>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let request =
            build_get_object_acl_request(bucket_name.as_ref(), object_key.as_ref(), &options)?;
        let (_, content) = self.do_request::<String>(request).await?;
        parse_object_acl_from_xml(&content)
    }

    /// Put an object's acl. If you want to restore the object's acl to follow bucket acl settings, pass acl as `ObjectAcl::Default`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobjectacl>
    async fn put_object_acl<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        acl: ObjectAcl,
        options: Option<GetObjectAclOptions>,
    ) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let request =
            build_put_object_acl_request(bucket_name.as_ref(), object_key.as_ref(), acl, &options)?;
        let _ = self.do_request::<()>(request).await?;
        Ok(())
    }
}

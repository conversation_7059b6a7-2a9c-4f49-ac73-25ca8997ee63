use lib_macros::Configurable;
use schemars::JsonSchema;
use serde::Deserialize;

#[derive(Debug, Configurable, Clone, JsonSchema, Deserialize)]
#[config_prefix = "storage"]
pub struct StorageConfig {
    pub oss_access_key_id: String,     // 阿里云 AccessKey ID
    pub oss_access_key_secret: String, // 阿里云 AccessKey Secret
    pub oss_endpoint: String,          // 阿里云 OSS Endpoint (例如：华东1杭州)
    pub oss_bucket_name: String,       // 您在OSS上创建的 Bucket 名称
    pub oss_region: String,            // Bucket 所在的区域 (例如：cn-hangzhou)

    pub oss_custom_domain: Option<String>, // 如果您的 Bucket 绑定了自定义域名，可以在这里配置
}

impl StorageConfig {
    /// 获取默认的存储桶名称
    pub fn default_bucket_name(&self) -> &str {
        &self.oss_bucket_name
    }

    /// 获取 OSS 地域端点
    pub fn endpoint(&self) -> &str {
        &self.oss_endpoint
    }

    /// 获取 OSS 地域标识
    pub fn region(&self) -> &str {
        &self.oss_region
    }

    /// 获取自定义域名（如果配置了）
    pub fn custom_domain(&self) -> Option<&str> {
        self.oss_custom_domain.as_deref()
    }

    /// 构建标准的 OSS 访问 URL
    ///
    /// # 参数
    /// * `bucket_name` - 存储桶名称（可选，默认使用配置中的桶）
    /// * `object_key` - 对象键（文件路径）
    /// * `use_https` - 是否使用 HTTPS（默认 true）
    ///
    /// # 返回
    /// * `String` - 完整的 OSS 访问 URL
    /// * 输出: https://invoicebookoss.oss-cn-shenzhen.aliyuncs.com/images/photo.jpg
    /// ```
    pub fn build_oss_url(
        &self,
        bucket_name: Option<&str>,
        object_key: &str,
        use_https: Option<bool>,
    ) -> String {
        let bucket = bucket_name.unwrap_or(&self.oss_bucket_name);
        let scheme = if use_https.unwrap_or(true) {
            "https"
        } else {
            "http"
        };

        format!(
            "{}://{}.{}/{}",
            scheme, bucket, self.oss_endpoint, object_key
        )
    }

    /// 构建自定义域名访问 URL
    ///
    /// # 参数
    /// * `object_key` - 对象键（文件路径）
    /// * `use_https` - 是否使用 HTTPS（默认 true）
    ///
    /// # 返回
    /// * `Option<String>` - 如果配置了自定义域名，返回完整的访问 URL，否则返回 None
    /// * 输出: https://cdn.example.com/images/photo.jpg
    pub fn build_custom_domain_url(
        &self,
        object_key: &str,
        use_https: Option<bool>,
    ) -> Option<String> {
        if let Some(custom_domain) = &self.oss_custom_domain {
            let scheme = if use_https.unwrap_or(true) {
                "https"
            } else {
                "http"
            };
            Some(format!("{}://{}/{}", scheme, custom_domain, object_key))
        } else {
            None
        }
    }

    /// 构建最佳访问 URL（优先使用自定义域名）
    ///
    /// # 参数
    /// * `bucket_name` - 存储桶名称（可选，默认使用配置中的桶）
    /// * `object_key` - 对象键（文件路径）
    /// * `use_https` - 是否使用 HTTPS（默认 true）
    ///
    /// # 返回
    /// * `String` - 最佳的访问 URL（优先自定义域名，否则使用标准 OSS URL）
    /// * 如果配置了自定义域名: https://cdn.example.com/images/photo.jpg
    /// * 否则: https://invoicebookoss.oss-cn-shenzhen.aliyuncs.com/images/photo.jpg
    pub fn build_best_url(
        &self,
        bucket_name: Option<&str>,
        object_key: &str,
        use_https: Option<bool>,
    ) -> String {
        // 优先使用自定义域名
        if let Some(custom_url) = self.build_custom_domain_url(object_key, use_https) {
            custom_url
        } else {
            self.build_oss_url(bucket_name, object_key, use_https)
        }
    }

    /// 构建内网访问 URL
    ///
    /// # 参数
    /// * `bucket_name` - 存储桶名称（可选，默认使用配置中的桶）
    /// * `object_key` - 对象键（文件路径）
    /// * `use_https` - 是否使用 HTTPS（默认 true）
    ///
    /// # 返回
    /// * `String` - 内网访问 URL
    /// * 输出: https://invoicebookoss.oss-cn-shenzhen-internal.aliyuncs.com/images/photo.jpg
    pub fn build_internal_url(
        &self,
        bucket_name: Option<&str>,
        object_key: &str,
        use_https: Option<bool>,
    ) -> String {
        let bucket = bucket_name.unwrap_or(&self.oss_bucket_name);
        let scheme = if use_https.unwrap_or(true) {
            "https"
        } else {
            "http"
        };

        // 将 endpoint 转换为内网 endpoint
        let internal_endpoint = if self.oss_endpoint.contains("-internal") {
            self.oss_endpoint.clone()
        } else {
            self.oss_endpoint
                .replace(".aliyuncs.com", "-internal.aliyuncs.com")
        };

        format!(
            "{}://{}.{}/{}",
            scheme, bucket, internal_endpoint, object_key
        )
    }

    /// 获取对象的完整路径（包含存储桶）
    ///
    /// # 参数
    /// * `bucket_name` - 存储桶名称（可选，默认使用配置中的桶）
    /// * `object_key` - 对象键（文件路径）
    ///
    /// # 返回
    /// * `String` - 完整的对象路径
    /// * 输出: invoicebookoss/images/photo.jpg
    pub fn get_full_object_path(&self, bucket_name: Option<&str>, object_key: &str) -> String {
        let bucket = bucket_name.unwrap_or(&self.oss_bucket_name);
        format!("{}/{}", bucket, object_key)
    }

    /// 检查是否配置了自定义域名
    ///
    /// # 返回
    /// * `bool` - 是否配置了自定义域名
    pub fn has_custom_domain(&self) -> bool {
        self.oss_custom_domain.is_some() && !self.oss_custom_domain.as_ref().unwrap().is_empty()
    }

    /// 获取存储桶的根 URL
    ///
    /// # 参数
    /// * `bucket_name` - 存储桶名称（可选，默认使用配置中的桶）
    /// * `use_https` - 是否使用 HTTPS（默认 true）
    ///
    /// # 返回
    /// * `String` - 存储桶的根 URL
    /// * 输出: https://invoicebookoss.oss-cn-shenzhen.aliyuncs.com
    pub fn get_bucket_root_url(
        &self,
        bucket_name: Option<&str>,
        use_https: Option<bool>,
    ) -> String {
        let bucket = bucket_name.unwrap_or(&self.oss_bucket_name);
        let scheme = if use_https.unwrap_or(true) {
            "https"
        } else {
            "http"
        };

        format!("{}://{}.{}", scheme, bucket, self.oss_endpoint)
    }

    /// 构建签名 URL 的基础信息
    ///
    /// # 参数
    /// * `bucket_name` - 存储桶名称（可选，默认使用配置中的桶）
    /// * `object_key` - 对象键（文件路径）
    ///
    /// # 返回
    /// * `(String, String, String)` - (bucket_name, endpoint, object_key)
    /// * bucket: "invoicebookoss"
    /// * endpoint: "oss-cn-shenzhen.aliyuncs.com"
    /// * key: "images/photo.jpg"
    pub fn get_presign_info(
        &self,
        bucket_name: Option<&str>,
        object_key: &str,
    ) -> (String, String, String) {
        let bucket = bucket_name.unwrap_or(&self.oss_bucket_name).to_string();
        let endpoint = self.oss_endpoint.clone();
        let object_key = object_key.to_string();

        (bucket, endpoint, object_key)
    }
}

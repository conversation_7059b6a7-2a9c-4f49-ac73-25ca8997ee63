//! Object tagging module

use std::collections::HashMap;

use async_trait::async_trait;

use crate::{Client, Result};
use crate::tagging::tagging_common::{build_delete_object_tag_request, build_get_object_tag_request, build_put_object_tag_request, parse_tags_from_xml, DeleteObjectTagOptions, GetObjectTagOptions, PutObjectTagOptions};

#[async_trait]
pub trait ObjectTagOperations {
    /// Get object taggings
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getobjecttagging>
    async fn get_object_tags<S1, S2>(&self, bucket_name: S1, object_key: S2, options: Option<GetObjectTagOptions>) -> Result<HashMap<String, String>>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Put object taggings
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobjecttagging>
    async fn put_object_tags<S1, S2>(&self, bucket_name: S1, object_key: S2, tags: HashMap<String, String>, options: Option<PutObjectTagOptions>) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Delete object taggings
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/deleteobjecttagging>
    async fn delete_object_tags<S1, S2>(&self, bucket_name: S1, object_key: S2, options: Option<DeleteObjectTagOptions>) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;
}

#[async_trait]
impl ObjectTagOperations for Client {
    /// Get object taggings
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getobjecttagging>
    async fn get_object_tags<S1, S2>(&self, bucket_name: S1, object_key: S2, options: Option<GetObjectTagOptions>) -> Result<HashMap<String, String>>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let request = build_get_object_tag_request(bucket_name.as_ref(), object_key.as_ref(), &options)?;
        let (_, xml) = self.do_request::<String>(request).await?;
        parse_tags_from_xml(xml)
    }

    /// Put object taggings
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobjecttagging>
    async fn put_object_tags<S1, S2>(&self, bucket_name: S1, object_key: S2, tags: HashMap<String, String>, options: Option<PutObjectTagOptions>) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let request = build_put_object_tag_request(bucket_name.as_ref(), object_key.as_ref(), &tags, &options)?;
        let _ = self.do_request::<()>(request).await?;
        Ok(())
    }

    /// Delete object taggings
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/deleteobjecttagging>
    async fn delete_object_tags<S1, S2>(&self, bucket_name: S1, object_key: S2, options: Option<DeleteObjectTagOptions>) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let request = build_delete_object_tag_request(bucket_name.as_ref(), object_key.as_ref(), &options)?;
        let _ = self.do_request::<()>(request).await?;
        Ok(())
    }
}
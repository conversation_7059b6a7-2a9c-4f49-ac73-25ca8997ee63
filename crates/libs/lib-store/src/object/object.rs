use std::path::Path;

use async_trait::async_trait;
use base64::{prelude::BASE64_STANDARD, Engine};
use futures::TryStreamExt;
use reqwest::StatusCode;
use tokio::io::AsyncWriteExt;

use crate::object::object_common::{
    build_copy_object_request, build_delete_multiple_objects_request, build_get_object_request, build_head_object_request,
    build_put_object_request, build_restore_object_request, AppendObjectOptions,
    AppendObjectResult, CopyObjectOptions, CopyObjectResult, DeleteMultipleObjectsConfig,
    DeleteMultipleObjectsResult, DeleteObjectOptions, DeleteObjectResult, GetObjectMetadataOptions, GetObjectOptions,
    GetObjectResult, HeadObjectOptions, ObjectMetadata,
    PutObjectOptions, PutObjectResult, RestoreObjectRequest,
    RestoreObjectResult,
};
use crate::{
    error::Error, request::{OssRequest, RequestMeth<PERSON>}, util::{validate_bucket_name, validate_object_key, validate_path}, ByteStream,
    Client,
    RequestBody,
    Result,
};

#[async_trait]
pub trait ObjectOperations {
    /// Uploads a file to a specified bucket and object key.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn put_object_from_file<S1, S2, P>(
        &self,
        bucket_name: S1,
        object_key: S2,
        file_path: P,
        options: Option<PutObjectOptions>,
    ) -> Result<PutObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        P: AsRef<Path> + Send;

    /// Create an object from buffer. If you are going to upload a large file, it is recommended to use `upload_file` instead.
    /// And, it is recommended to set `mime_type` in `options`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn put_object_from_buffer<S1, S2, B>(
        &self,
        bucket_name: S1,
        object_key: S2,
        buffer: B,
        options: Option<PutObjectOptions>,
    ) -> Result<PutObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        B: Into<Vec<u8>> + Send;

    /// Create an object from base64 string.
    /// And, it is recommended to set `mime_type` in `options`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn put_object_from_base64<S1, S2, S3>(
        &self,
        bucket_name: S1,
        object_key: S2,
        base64_string: S3,
        options: Option<PutObjectOptions>,
    ) -> Result<PutObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send;

    /// Append object.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/appendobject>
    async fn append_object_from_file<S1, S2, P>(
        &self,
        bucket_name: S1,
        object_key: S2,
        file_path: P,
        position: u64,
        options: Option<AppendObjectOptions>,
    ) -> Result<AppendObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        P: AsRef<Path> + Send;

    /// Append object from buffer. suitable for small size content
    /// And, it is recommended to set `mime_type` in `options`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn append_object_from_buffer<S1, S2, B>(
        &self,
        bucket_name: S1,
        object_key: S2,
        buffer: B,
        position: u64,
        options: Option<AppendObjectOptions>,
    ) -> Result<AppendObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        B: Into<Vec<u8>> + Send;

    /// Append object from base64 string. suitable for small size content
    /// And, it is recommended to set `mime_type` in `options`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn append_object_from_base64<S1, S2, S3>(
        &self,
        bucket_name: S1,
        object_key: S2,
        base64_string: S3,
        position: u64,
        options: Option<AppendObjectOptions>,
    ) -> Result<AppendObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send;

    /// Download object to local file
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getobject>
    async fn get_object_to_file<S1, S2, P>(
        &self,
        bucket_name: S1,
        object_key: S2,
        file_path: P,
        options: Option<GetObjectOptions>,
    ) -> Result<GetObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        P: AsRef<Path> + Send;

    /// Create a "folder"
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn create_folder<S1, S2>(&self, bucket_name: S1, object_key: S2) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Delete a "folder". if the folder contains any object, it will not be deleted
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/deleteobject>
    async fn delete_folder<S1, S2>(&self, bucket_name: S1, object_key: S2) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Get object metadata
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getobjectmeta>
    async fn get_object_metadata<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<GetObjectMetadataOptions>,
    ) -> Result<ObjectMetadata>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Check if the object exists or not using get object metadata
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getobjectmeta>
    async fn exists<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<GetObjectMetadataOptions>,
    ) -> Result<bool>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Head object
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/headobject>
    async fn head_object<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<HeadObjectOptions>,
    ) -> Result<ObjectMetadata>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Copy files (Objects) between the same or different Buckets within the same region.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/copyobject>
    async fn copy_object<S1, S2, S3, S4>(
        &self,
        source_bucket_name: S1,
        source_object_key: S2,
        dest_bucket_name: S3,
        dest_object_key: S4,
        options: Option<CopyObjectOptions>,
    ) -> Result<CopyObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send,
        S4: AsRef<str> + Send;

    /// Delete an object
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/deleteobject>
    async fn delete_object<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<DeleteObjectOptions>,
    ) -> Result<DeleteObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Delete multiple objects
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/deletemultipleobjects>
    async fn delete_multiple_objects<'c, S1, S2>(
        &self,
        bucket_name: S1,
        config: DeleteMultipleObjectsConfig<'c, S2>,
    ) -> Result<DeleteMultipleObjectsResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send + Sync;

    /// Restore object
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/restoreobject>
    async fn restore_object<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        config: RestoreObjectRequest,
    ) -> Result<RestoreObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Clean retored object
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/cleanrestoredobject>
    async fn clean_restored_object<S1, S2>(&self, bucket_name: S1, object_key: S2) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;
}

#[async_trait]
impl ObjectOperations for Client {
    /// The `object_key` constraints:
    ///
    /// - length between [1, 1023]
    /// - must NOT starts or ends with `/` or `\`. e.g. `path/to/subfolder/some-file.txt`
    /// - the `file_path` specify full path to the file to be uploaded
    /// - the file must exist and must be readable
    /// - file length less than 5GB
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn put_object_from_file<S1, S2, P>(
        &self,
        bucket_name: S1,
        object_key: S2,
        file_path: P,
        options: Option<PutObjectOptions>,
    ) -> Result<PutObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        P: AsRef<Path> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();

        let object_key = object_key.strip_prefix("/").unwrap_or(object_key);
        let object_key = object_key.strip_suffix("/").unwrap_or(object_key);

        let file_path = file_path.as_ref();

        let with_callback = if let Some(opt) = &options {
            opt.callback.is_some()
        } else {
            false
        };

        let request = build_put_object_request(
            bucket_name,
            object_key,
            RequestBody::File(file_path.to_path_buf(), None),
            &options,
        )?;

        let (headers, content) = self.do_request::<String>(request).await?;

        if with_callback {
            Ok(PutObjectResult::CallbackResponse(content))
        } else {
            Ok(PutObjectResult::ApiResponse(headers.into()))
        }
    }

    /// Create an object from buffer. If you are going to upload a large file, it is recommended to use `upload_file` instead.
    /// And, it is recommended to set `mime_type` in `options`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn put_object_from_buffer<S1, S2, B>(
        &self,
        bucket_name: S1,
        object_key: S2,
        buffer: B,
        options: Option<PutObjectOptions>,
    ) -> Result<PutObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        B: Into<Vec<u8>> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();

        let object_key = object_key.strip_prefix("/").unwrap_or(object_key);
        let object_key = object_key.strip_suffix("/").unwrap_or(object_key);

        let with_callback = if let Some(opt) = &options {
            opt.callback.is_some()
        } else {
            false
        };

        let request = build_put_object_request(
            bucket_name,
            object_key,
            RequestBody::Bytes(buffer.into()),
            &options,
        )?;

        let (headers, content) = self.do_request::<String>(request).await?;

        if with_callback {
            Ok(PutObjectResult::CallbackResponse(content))
        } else {
            Ok(PutObjectResult::ApiResponse(headers.into()))
        }
    }

    /// Create an object from base64 string.
    /// And, it is recommended to set `mime_type` in `options`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn put_object_from_base64<S1, S2, S3>(
        &self,
        bucket_name: S1,
        object_key: S2,
        base64_string: S3,
        options: Option<PutObjectOptions>,
    ) -> Result<PutObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send,
    {
        let data = if let Ok(d) = BASE64_STANDARD.decode(base64_string.as_ref()) {
            d
        } else {
            return Err(Error::Other("Decoding base64 string failed".to_string()));
        };

        self.put_object_from_buffer(bucket_name, object_key, data, options)
            .await
    }

    /// Append object.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/appendobject>
    async fn append_object_from_file<S1, S2, P>(
        &self,
        bucket_name: S1,
        object_key: S2,
        file_path: P,
        position: u64,
        options: Option<AppendObjectOptions>,
    ) -> Result<AppendObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        P: AsRef<Path> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();

        let object_key = object_key.strip_prefix("/").unwrap_or(object_key);
        let object_key = object_key.strip_suffix("/").unwrap_or(object_key);

        let file_path = file_path.as_ref();

        let mut request = build_put_object_request(
            bucket_name,
            object_key,
            RequestBody::File(file_path.to_path_buf(), None),
            &options,
        )?;

        // alter the request method and add append object query parameters
        request = request
            .method(RequestMethod::Post)
            .add_query("append", "")
            .add_query("position", position.to_string());

        let (headers, _) = self.do_request::<()>(request).await?;

        Ok(headers.into())
    }

    /// Append object from buffer. suitable for small size content
    /// And, it is recommended to set `mime_type` in `options`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn append_object_from_buffer<S1, S2, B>(
        &self,
        bucket_name: S1,
        object_key: S2,
        buffer: B,
        position: u64,
        options: Option<AppendObjectOptions>,
    ) -> Result<AppendObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        B: Into<Vec<u8>> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();

        let object_key = object_key.strip_prefix("/").unwrap_or(object_key);
        let object_key = object_key.strip_suffix("/").unwrap_or(object_key);

        let mut request = build_put_object_request(
            bucket_name,
            object_key,
            RequestBody::Bytes(buffer.into()),
            &options,
        )?;

        // alter the request method and add append object query parameters
        request = request
            .method(RequestMethod::Post)
            .add_query("append", "")
            .add_query("position", position.to_string());

        let (headers, _) = self.do_request::<()>(request).await?;

        Ok(headers.into())
    }

    /// Append object from base64 string. suitable for small size content
    /// And, it is recommended to set `mime_type` in `options`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn append_object_from_base64<S1, S2, S3>(
        &self,
        bucket_name: S1,
        object_key: S2,
        base64_string: S3,
        position: u64,
        options: Option<AppendObjectOptions>,
    ) -> Result<AppendObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send,
    {
        let data = if let Ok(d) = BASE64_STANDARD.decode(base64_string.as_ref()) {
            d
        } else {
            return Err(Error::Other("Decoding base64 string failed".to_string()));
        };

        self.append_object_from_buffer(bucket_name, object_key, data, position, options)
            .await
    }

    /// Download oss object to local file.
    /// `file_path` is the full file path to save.
    /// If the `file_path` parent path does not exist, it will be created
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getobject>
    async fn get_object_to_file<S1, S2, P>(
        &self,
        bucket_name: S1,
        object_key: S2,
        file_path: P,
        options: Option<GetObjectOptions>,
    ) -> Result<GetObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        P: AsRef<Path> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();
        let file_path = file_path.as_ref();

        let file_path = if file_path.is_relative() {
            file_path.canonicalize()?
        } else {
            file_path.to_path_buf()
        };

        if !validate_path(&file_path) {
            return Err(Error::Other(format!(
                "invalid file path: {:?}",
                file_path.as_os_str().to_str()
            )));
        }

        // check parent path
        if let Some(parent_path) = file_path.parent() {
            if !parent_path.exists() {
                std::fs::create_dir_all(parent_path)?;
            }
        }

        let request = build_get_object_request(bucket_name, object_key, &options)?;

        let (_, mut stream) = self.do_request::<ByteStream>(request).await?;

        let mut file = tokio::fs::File::create(&file_path).await?;

        while let Some(chunk) = stream.try_next().await? {
            file.write_all(&chunk).await?;
        }

        file.flush().await?;

        Ok(GetObjectResult)
    }

    /// Create a "folder".
    /// The `object_key` must ends with `/`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putobject>
    async fn create_folder<S1, S2>(&self, bucket_name: S1, object_key: S2) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();
        let object_key = object_key.strip_prefix("/").unwrap_or(object_key);
        let object_key = if object_key.ends_with("/") {
            object_key.to_string()
        } else {
            format!("{}/", object_key)
        };

        if !validate_bucket_name(bucket_name) {
            return Err(Error::Other(format!(
                "invalid bucket name: {}",
                bucket_name
            )));
        }

        let request = OssRequest::new()
            .method(RequestMethod::Put)
            .bucket(bucket_name)
            .object(object_key)
            .body(RequestBody::Empty)
            .content_length(0);

        let _ = self.do_request::<()>(request).await?;

        Ok(())
    }

    /// Delete a "folder". if the folder contains any object, it will not be deleted
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/deleteobject>
    async fn delete_folder<S1, S2>(&self, bucket_name: S1, object_key: S2) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();
        let object_key = object_key.strip_prefix("/").unwrap_or(object_key);
        let object_key = if object_key.ends_with("/") {
            object_key.to_string()
        } else {
            format!("{}/", object_key)
        };

        if !validate_bucket_name(bucket_name) {
            return Err(Error::Other(format!(
                "invalid bucket name: {}",
                bucket_name
            )));
        }

        let request = OssRequest::new()
            .method(RequestMethod::Delete)
            .bucket(bucket_name)
            .object(object_key);

        let _ = self.do_request::<()>(request).await?;

        Ok(())
    }

    /// Get object metadata.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getobjectmeta>
    async fn get_object_metadata<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<GetObjectMetadataOptions>,
    ) -> Result<ObjectMetadata>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();

        if !validate_bucket_name(bucket_name) {
            return Err(Error::Other(format!(
                "invalid bucket name: {}",
                bucket_name
            )));
        }

        if !validate_object_key(object_key) {
            return Err(Error::Other(format!("invalid object key: {}", object_key)));
        }

        let mut request = OssRequest::new()
            .method(RequestMethod::Head)
            .bucket(bucket_name)
            .object(object_key)
            .add_query("objectMeta", "");

        if let Some(options) = &options {
            if let Some(s) = &options.version_id {
                request = request.add_query("versionId", s);
            }
        }

        let (headers, _) = self.do_request::<()>(request).await?;
        Ok(ObjectMetadata::from(headers))
    }

    /// Check if the object exists or not using get object metadata
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getobjectmeta>
    async fn exists<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<GetObjectMetadataOptions>,
    ) -> Result<bool>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        match self
            .get_object_metadata(bucket_name, object_key, options)
            .await
        {
            Ok(_) => Ok(true),
            Err(e) => match e {
                Error::StatusError(status) if status == StatusCode::NOT_FOUND => Ok(false),
                _ => Err(e),
            },
        }
    }

    /// Get more detail object metadata
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/headobject>
    async fn head_object<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<HeadObjectOptions>,
    ) -> Result<ObjectMetadata>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();

        let request = build_head_object_request(bucket_name, object_key, &options)?;

        let (headers, _) = self.do_request::<()>(request).await?;
        Ok(ObjectMetadata::from(headers))
    }

    /// Copy files (Objects) between the same or different Buckets within the same region.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/copyobject>
    async fn copy_object<S1, S2, S3, S4>(
        &self,
        source_bucket_name: S1,
        source_object_key: S2,
        dest_bucket_name: S3,
        dest_object_key: S4,
        options: Option<CopyObjectOptions>,
    ) -> Result<CopyObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send,
        S4: AsRef<str> + Send,
    {
        let request = build_copy_object_request(
            source_bucket_name.as_ref(),
            source_object_key.as_ref(),
            dest_bucket_name.as_ref(),
            dest_object_key.as_ref(),
            &options,
        )?;

        let (_, _) = self.do_request::<()>(request).await?;

        Ok(CopyObjectResult)
    }

    /// Delete an object
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/deleteobject>
    async fn delete_object<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<DeleteObjectOptions>,
    ) -> Result<DeleteObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();

        if !validate_bucket_name(bucket_name) {
            return Err(Error::Other(format!(
                "invalid bucket name: {}",
                bucket_name
            )));
        }

        if !validate_object_key(object_key) {
            return Err(Error::Other(format!("invalid object key: {}", object_key)));
        }

        let mut request = OssRequest::new()
            .method(RequestMethod::Delete)
            .bucket(bucket_name)
            .object(object_key);

        if let Some(options) = options {
            if let Some(s) = options.version_id {
                request = request.add_query("versionId", s);
            }
        }

        let _ = self.do_request::<()>(request).await?;

        Ok(DeleteObjectResult)
    }

    /// Delete multiple objects
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/deletemultipleobjects>
    async fn delete_multiple_objects<'c, S1, S2>(
        &self,
        bucket_name: S1,
        config: DeleteMultipleObjectsConfig<'c, S2>,
    ) -> Result<DeleteMultipleObjectsResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send + Sync,
    {
        let bucket_name = bucket_name.as_ref();

        let request = build_delete_multiple_objects_request(bucket_name, config)?;

        let (_, content) = self.do_request::<String>(request).await?;

        DeleteMultipleObjectsResult::from_xml(&content)
    }

    /// Restore object
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/restoreobject>
    async fn restore_object<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        config: RestoreObjectRequest,
    ) -> Result<RestoreObjectResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let request =
            build_restore_object_request(bucket_name.as_ref(), object_key.as_ref(), config)?;
        let (headers, _) = self.do_request::<()>(request).await?;
        Ok(headers.into())
    }

    /// Clean retored object
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/cleanrestoredobject>
    async fn clean_restored_object<S1, S2>(&self, bucket_name: S1, object_key: S2) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();

        if !validate_bucket_name(bucket_name) {
            return Err(Error::Other(format!(
                "invalid bucket name: {}",
                bucket_name
            )));
        }

        if !validate_object_key(object_key) {
            return Err(Error::Other(format!("invalid object key: {}", object_key)));
        }

        let request = OssRequest::new()
            .method(RequestMethod::Post)
            .bucket(bucket_name)
            .object(object_key)
            .add_query("cleanRestoredObject", "");

        let _ = self.do_request::<()>(request).await?;

        Ok(())
    }
}

//! Bucket cname

use crate::cname::cname_common::{CnameInfo, ListCnameResult};
use crate::{
    error::Error, request::{OssRequest, RequestMethod},
    util::validate_bucket_name,
    Client,
    Result,
};
use async_trait::async_trait;

#[async_trait]
pub trait CnameOperations {
    /// List bucket cname
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/listcname>
    async fn list_cname<S>(&self, bucket_name: S) -> Result<Vec<CnameInfo>>
    where
        S: AsRef<str> + Send;
}

#[async_trait]
impl CnameOperations for Client {
    // List bucket cname
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/listcname>
    async fn list_cname<S>(&self, bucket_name: S) -> Result<Vec<CnameInfo>>
    where
        S: AsRef<str> + Send,
    {
        let bucket_name = bucket_name.as_ref();

        if !validate_bucket_name(bucket_name) {
            return Err(Error::Other(format!(
                "invalid bucket name: {}",
                bucket_name
            )));
        }

        let request = OssRequest::new()
            .method(RequestMethod::Get)
            .bucket(bucket_name)
            .add_query("cname", "");

        let (_, xml) = self.do_request::<String>(request).await?;

        let ListCnameResult { cnames } = ListCnameResult::from_xml(&xml)?;

        Ok(cnames)
    }
}

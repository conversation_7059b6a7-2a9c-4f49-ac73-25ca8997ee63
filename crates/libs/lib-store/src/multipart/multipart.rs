//! Mutlipart uploads related operations module

use std::{ops::Range, path::Path};

use async_trait::async_trait;
use base64::{prelude::BASE64_STANDARD, Engine};

use crate::multipart::multipart_common::{
    build_complete_multipart_uploads_request, build_initiate_multipart_uploads_request,
    build_list_multipart_uploads_request, build_list_parts_request, build_upload_part_copy_request,
    build_upload_part_request, CompleteMultipartUploadApiResponse, CompleteMultipartUploadOptions,
    CompleteMultipartUploadRequest, CompleteMultipartUploadResult, InitiateMultipartUploadOptions, InitiateMultipartUploadResult,
    ListMultipartUploadsOptions, ListMultipartUploadsResult, ListPartsOptions,
    ListPartsResult, UploadPartCopyOptions,
    UploadPartCopyRequest, UploadPartCopyResult, UploadPartRequest,
    UploadPartResult,
};
use crate::{
    error::Error, request::{OssRequest, RequestMethod}, util::{validate_bucket_name, validate_object_key},
    Client,
    RequestBody,
    Result,
};

#[async_trait]
pub trait MultipartUploadsOperations {
    /// List multipart uploads which are initialized but not completed nor aborted.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/listmultipartuploads>
    async fn list_multipart_uploads<S>(
        &self,
        bucket_name: S,
        options: Option<ListMultipartUploadsOptions>,
    ) -> Result<ListMultipartUploadsResult>
    where
        S: AsRef<str> + Send;

    /// List parts which uploaded successfully associated with the given `upload_id`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/listparts>
    async fn list_parts<S1, S2, S3>(
        &self,
        bucket_name: S1,
        object_key: S2,
        upload_id: S3,
        options: Option<ListPartsOptions>,
    ) -> Result<ListPartsResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send;

    /// Initiate multipart uploads
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/initiatemultipartupload>
    async fn initiate_multipart_uploads<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<InitiateMultipartUploadOptions>,
    ) -> Result<InitiateMultipartUploadResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Upload part of a file. the caller should take responsibility to make sure the range is valid.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/uploadpart>
    async fn upload_part_from_file<S1, S2, P>(
        &self,
        bucket_name: S1,
        object_key: S2,
        file_path: P,
        range: Range<u64>,
        params: UploadPartRequest,
    ) -> Result<UploadPartResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        P: AsRef<Path> + Send;

    /// Upload part from buffer.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/uploadpart>
    async fn upload_part_from_buffer<S1, S2, B>(
        &self,
        bucket_name: S1,
        object_key: S2,
        buffer: B,
        params: UploadPartRequest,
    ) -> Result<UploadPartResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        B: Into<Vec<u8>> + Send;

    /// Upload part from base64 string.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/uploadpart>
    async fn upload_part_from_base64<S1, S2, S3>(
        &self,
        bucket_name: S1,
        object_key: S2,
        base64_string: S3,
        params: UploadPartRequest,
    ) -> Result<UploadPartResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send;

    /// When you want to copy a file larger than 1GB, you must use `upload_part_copy`.
    /// First, initiate a multipart upload and get `uploadId`, then call this method to upload parts of the source object.
    /// Finally complete the multipart upload by invoking `complete_multipart_uploads`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/uploadpartcopy>
    async fn upload_part_copy<S1, S2>(
        &self,
        bucket_name: S1,
        dest_object_key: S2,
        data: UploadPartCopyRequest,
        options: Option<UploadPartCopyOptions>,
    ) -> Result<UploadPartCopyResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// Complete multipart uploads
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/completemultipartupload>
    async fn complete_multipart_uploads<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        data: CompleteMultipartUploadRequest,
        options: Option<CompleteMultipartUploadOptions>,
    ) -> Result<CompleteMultipartUploadResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;

    /// About multipart uploads
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/abortmultipartupload>
    async fn abort_multipart_uploads<S1, S2, S3>(
        &self,
        bucket_name: S1,
        object_key: S2,
        upload_id: S3,
    ) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send;
}

#[async_trait]
impl MultipartUploadsOperations for Client {
    /// List multipart uploads which are initialized but not completed nor aborted.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/listmultipartuploads>
    async fn list_multipart_uploads<S>(
        &self,
        bucket_name: S,
        options: Option<ListMultipartUploadsOptions>,
    ) -> Result<ListMultipartUploadsResult>
    where
        S: AsRef<str> + Send,
    {
        if !validate_bucket_name(bucket_name.as_ref()) {
            return Err(Error::Other(format!(
                "invalid bucket name: {}",
                bucket_name.as_ref()
            )));
        }
        let request = build_list_multipart_uploads_request(bucket_name.as_ref(), &options)?;
        let (_, xml) = self.do_request::<String>(request).await?;

        ListMultipartUploadsResult::from_xml(&xml)
    }

    /// List parts which uploaded successfully associated with the given `upload_id`
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/listparts>
    async fn list_parts<S1, S2, S3>(
        &self,
        bucket_name: S1,
        object_key: S2,
        upload_id: S3,
        options: Option<ListPartsOptions>,
    ) -> Result<ListPartsResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send,
    {
        let request = build_list_parts_request(
            bucket_name.as_ref(),
            object_key.as_ref(),
            upload_id.as_ref(),
            &options,
        )?;
        let (_, xml) = self.do_request::<String>(request).await?;
        ListPartsResult::from_xml(&xml)
    }

    /// Initiate multipart uploads
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/initiatemultipartupload>
    async fn initiate_multipart_uploads<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        options: Option<InitiateMultipartUploadOptions>,
    ) -> Result<InitiateMultipartUploadResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let request = build_initiate_multipart_uploads_request(
            bucket_name.as_ref(),
            object_key.as_ref(),
            &options,
        )?;
        let (_, xml) = self.do_request::<String>(request).await?;
        InitiateMultipartUploadResult::from_xml(&xml)
    }

    /// Upload part of a file. the caller should take responsibility to make sure the range is valid.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/uploadpart>
    async fn upload_part_from_file<S1, S2, P>(
        &self,
        bucket_name: S1,
        object_key: S2,
        file_path: P,
        range: Range<u64>,
        params: UploadPartRequest,
    ) -> Result<UploadPartResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        P: AsRef<Path> + Send,
    {
        let request = build_upload_part_request(
            bucket_name.as_ref(),
            object_key.as_ref(),
            RequestBody::File(file_path.as_ref().to_path_buf(), Some(range)),
            params,
        )?;

        let (headers, _) = self.do_request::<()>(request).await?;

        Ok(headers.into())
    }

    /// Upload part from buffer.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/uploadpart>
    async fn upload_part_from_buffer<S1, S2, B>(
        &self,
        bucket_name: S1,
        object_key: S2,
        buffer: B,
        params: UploadPartRequest,
    ) -> Result<UploadPartResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        B: Into<Vec<u8>> + Send,
    {
        let request = build_upload_part_request(
            bucket_name.as_ref(),
            object_key.as_ref(),
            RequestBody::Bytes(buffer.into()),
            params,
        )?;

        let (headers, _) = self.do_request::<()>(request).await?;

        Ok(headers.into())
    }

    /// Upload part from base64 string.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/uploadpart>
    async fn upload_part_from_base64<S1, S2, S3>(
        &self,
        bucket_name: S1,
        object_key: S2,
        base64_string: S3,
        params: UploadPartRequest,
    ) -> Result<UploadPartResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send,
    {
        let data = BASE64_STANDARD.decode(base64_string.as_ref())?;
        self.upload_part_from_buffer(bucket_name, object_key, data, params)
            .await
    }

    /// When you want to copy a file larger than 1GB, you must use `upload_part_copy`.
    /// First, initiate a multipart upload and get `uploadId`, then call this method to upload parts of the source object.
    /// Finally complete the multipart upload by invoking `complete_multipart_uploads`
    ///
    /// Offical document: <https://help.aliyun.com/zh/oss/developer-reference/uploadpartcopy>
    async fn upload_part_copy<S1, S2>(
        &self,
        bucket_name: S1,
        dest_object_key: S2,
        data: UploadPartCopyRequest,
        options: Option<UploadPartCopyOptions>,
    ) -> Result<UploadPartCopyResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = dest_object_key.as_ref();
        let requet = build_upload_part_copy_request(bucket_name, object_key, data, &options)?;
        let (_, xml) = self.do_request::<String>(requet).await?;
        UploadPartCopyResult::from_xml(&xml)
    }

    /// Complete multipart uploads
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/completemultipartupload>
    async fn complete_multipart_uploads<S1, S2>(
        &self,
        bucket_name: S1,
        object_key: S2,
        data: CompleteMultipartUploadRequest,
        options: Option<CompleteMultipartUploadOptions>,
    ) -> Result<CompleteMultipartUploadResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let with_callback = if let Some(opt) = &options {
            opt.callback.is_some()
        } else {
            false
        };

        let request = build_complete_multipart_uploads_request(
            bucket_name.as_ref(),
            object_key.as_ref(),
            data,
            &options,
        )?;
        let (_, content) = self.do_request::<String>(request).await?;

        if with_callback {
            Ok(CompleteMultipartUploadResult::CallbackResponse(content))
        } else {
            Ok(CompleteMultipartUploadResult::ApiResponse(
                CompleteMultipartUploadApiResponse::from_xml(&content)?,
            ))
        }
    }

    /// About multipart uploads
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/abortmultipartupload>
    async fn abort_multipart_uploads<S1, S2, S3>(
        &self,
        bucket_name: S1,
        object_key: S2,
        upload_id: S3,
    ) -> Result<()>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send,
    {
        let bucket_name = bucket_name.as_ref();
        let object_key = object_key.as_ref();

        if !validate_bucket_name(bucket_name) {
            return Err(Error::Other(format!(
                "invalid bucket name: {}",
                bucket_name
            )));
        }

        if !validate_object_key(object_key) {
            return Err(Error::Other(format!("invalid object key: {}", object_key)));
        }

        if upload_id.as_ref().is_empty() {
            return Err(Error::Other("invalid upload id: [empty]".to_string()));
        }

        let request = OssRequest::new()
            .method(RequestMethod::Delete)
            .bucket(bucket_name)
            .object(object_key)
            .add_query("uploadId", upload_id);

        self.do_request::<()>(request).await?;

        Ok(())
    }
}

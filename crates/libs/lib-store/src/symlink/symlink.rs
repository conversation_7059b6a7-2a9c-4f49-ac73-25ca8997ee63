//! Object symlink module

use async_trait::async_trait;

use crate::symlink::symlink_common::{
    build_get_symlink_request, build_put_symlink_request, GetSymlinkOptions, PutSymlinkOptions,
    PutSymlinkResult,
};
use crate::{Client, Result};

#[async_trait]
pub trait ObjectSymlinkOperations {
    /// Put a symlink object.
    ///
    /// `target_object_key` should be a full and valid object key.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putsymlink>
    async fn put_symlink<S1, S2, S3>(
        &self,
        bucket_name: S1,
        symlink_object_key: S2,
        target_object_key: S3,
        options: Option<PutSymlinkOptions>,
    ) -> Result<PutSymlinkResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send;

    /// Get a symlink object. The returned string is the target object key
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getsymlink>
    async fn get_symlink<S1, S2>(
        &self,
        bucket_name: S1,
        symlink_object_key: S2,
        options: Option<GetSymlinkOptions>,
    ) -> Result<String>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send;
}

#[async_trait]
impl ObjectSymlinkOperations for Client {
    /// Put a symlink object.
    ///
    /// `target_object_key` should be a full and valid object key.
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/putsymlink>
    async fn put_symlink<S1, S2, S3>(
        &self,
        bucket_name: S1,
        symlink_object_key: S2,
        target_object_key: S3,
        options: Option<PutSymlinkOptions>,
    ) -> Result<PutSymlinkResult>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
        S3: AsRef<str> + Send,
    {
        let request = build_put_symlink_request(
            bucket_name.as_ref(),
            symlink_object_key.as_ref(),
            target_object_key.as_ref(),
            &options,
        )?;
        let (headers, _) = self.do_request::<()>(request).await?;
        Ok(headers.into())
    }

    /// Get a symlink object. The returned string is the target object key
    ///
    /// Official document: <https://help.aliyun.com/zh/oss/developer-reference/getsymlink>
    async fn get_symlink<S1, S2>(
        &self,
        bucket_name: S1,
        symlink_object_key: S2,
        options: Option<GetSymlinkOptions>,
    ) -> Result<String>
    where
        S1: AsRef<str> + Send,
        S2: AsRef<str> + Send,
    {
        let request =
            build_get_symlink_request(bucket_name.as_ref(), symlink_object_key.as_ref(), &options)?;
        let (headers, _) = self.do_request::<()>(request).await?;
        let object_key = headers
            .get("x-oss-symlink-target")
            .unwrap_or(&String::new())
            .to_string();

        Ok(urlencoding::decode(&object_key)?.to_string())
    }
}

[package]
name = "lib-store"
version.workspace = true
edition.workspace = true

[dependencies]
lib-core = { path = "../lib-core" }
lib-macros = { path = "../lib-macros" }
serde = { workspace = true, features = ["derive"] }
schemars = { workspace = true }
async-trait = "0.1.88"
log = "0.4.27"

bytes = "1.10.1"
futures = "0.3.31"
reqwest = { version = "0.12.15", features = ["stream"] }
serde_json = "1.0.140"
tokio = "1.45.0"
tokio-util = "0.7.15"
url = "2.5.4"
hex = "0.4.3"
quick-xml = "0.37.5"
urlencoding = "2.1.3"
thiserror = "2.0.12"
base64 = "0.22.1"
chrono = "0.4.41"
hmac = "0.12.1"
sha2 = "0.10.9"
mime_guess = "2.0.5"
md5 = "0.7.0"


[features]
# Default features, using async request to call Aliyun OSS API
default = ["async", "native-tls"]
async = []
# Enable `rustls-tls` feature on `reqwest` crate
rust-tls = ["reqwest/rustls-tls"]
native-tls = ["reqwest/native-tls"]
# Enable serialization/deserialization on data types. Usful if you are using this crate for backend API
serde-support = []
# Using camelCase for serialization/deserialization. default is `snake_case`
serde-camelcase = ["serde-support"]

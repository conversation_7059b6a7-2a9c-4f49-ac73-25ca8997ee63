# 任务调度库
# 
# 这个库提供了完整的任务调度功能，包括：
# - 基于 tokio-cron-scheduler 的异步任务调度
# - 支持多种调度策略（一次性、固定延迟、固定频率、Cron表达式）
# - 依赖注入和参数提取
# - 自动任务发现和注册
# - 插件化的任务管理

[package]
name = "lib-job"
version.workspace = true
edition.workspace = true

[dependencies]
lib-core = { path = "../lib-core" }
lib-macros = { path = "../lib-macros" }
tokio-cron-scheduler = { workspace = true, features = ["signal"] }
anyhow = { workspace = true }
serde = { workspace = true }
tracing = { workspace = true, features = ["log"] }
uuid = { workspace = true, features = ["v4"] }
chrono = { workspace = true }
inventory = { workspace = true }
async-trait = {workspace = true}

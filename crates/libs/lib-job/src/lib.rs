//! 任务调度库
//! 
//! 这个库提供了完整的任务调度功能，包括：
//! - 基于 tokio-cron-scheduler 的任务调度系统
//! - 多种调度策略支持（一次性、固定延迟、固定频率、Cron表达式）
//! - 任务处理器和参数提取器
//! - 插件化的任务管理
//! - 宏支持，简化任务定义

/// 参数提取器模块
/// 提供从应用上下文中提取参数的功能
pub mod extractor;

/// 任务处理器模块
/// 定义任务处理器的trait和实现
pub mod handler;

/// 任务定义模块
/// 包含任务的基本结构和构建器
pub mod job;

/////////////////job-macros/////////////////////
/// 使用这些过程宏需要添加 `lib-job` 依赖
/// 
/// Cron 表达式任务宏
pub use lib_macros::cron;

/// 固定延迟任务宏
pub use lib_macros::fix_delay;

/// 固定频率任务宏
pub use lib_macros::fix_rate;

/// 一次性任务宏
pub use lib_macros::one_shot;

use anyhow::Context;
use async_trait::async_trait;
use job::Job;
use lib_core::app::app::{App, AppBuilder};
use lib_core::app::plugin::component::ComponentRef;
use lib_core::app::plugin::{ComponentRegistry, MutableComponentRegistry, Plugin};
use lib_core::error::Result;
use std::ops::Deref;
use std::sync::Arc;
use uuid::Uuid;

/// 任务集合
/// 
/// 这个结构体用于管理多个任务，提供了添加、合并等操作
#[derive(Clone, Default)]
pub struct Jobs(Vec<Job>);

impl Jobs {
    /// 创建一个新的空任务集合
    pub fn new() -> Self {
        Self::default()
    }
    
    /// 创建包含单个任务的任务集合
    fn single(job: Job) -> Self {
        Self(vec![job])
    }

    /// 添加一个任务到集合中
    /// 
    /// 参数:
    /// - `job`: 要添加的任务
    /// 
    /// 返回: 更新后的任务集合
    pub fn add_job(mut self, job: Job) -> Self {
        self.0.push(job);
        self
    }

    /// 添加多个任务到集合中
    /// 
    /// 参数:
    /// - `jobs`: 要添加的任务集合
    /// 
    /// 返回: 合并后的任务集合
    pub fn add_jobs(mut self, jobs: Jobs) -> Self {
        for job in jobs.0 {
            self.0.push(job);
        }
        self
    }

    /// 合并另一个任务集合到当前集合
    /// 
    /// 参数:
    /// - `jobs`: 要合并的任务集合
    fn merge(&mut self, jobs: Jobs) {
        for job in jobs.0 {
            self.0.push(job);
        }
    }
}

/// 为 Jobs 实现 Deref trait，使其可以像 Vec<Job> 一样使用
impl Deref for Jobs {
    type Target = Vec<Job>;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

/// 任务ID类型别名
/// 使用 UUID 作为任务的唯一标识符
pub type JobId = Uuid;

/// 任务调度器类型别名
/// 基于 tokio-cron-scheduler 的任务调度器
pub type JobScheduler = tokio_cron_scheduler::JobScheduler;

/// 任务配置器 trait
/// 
/// 定义了添加任务到应用程序的接口
pub trait JobConfigurator {
    /// 添加单个任务
    /// 
    /// 参数:
    /// - `job`: 要添加的任务
    /// 
    /// 返回: 自身的可变引用，支持链式调用
    fn add_job(&mut self, job: Job) -> &mut Self;
    
    /// 添加多个任务
    /// 
    /// 参数:
    /// - `job`: 任务集合
    /// 
    /// 返回: 自身的可变引用，支持链式调用
    fn add_jobs(&mut self, job: Jobs) -> &mut Self;
}

/// 为 AppBuilder 实现 JobConfigurator trait
/// 
/// 这允许在应用构建阶段添加任务
impl JobConfigurator for AppBuilder {
    /// 添加单个任务到应用程序
    /// 
    /// 如果已经存在任务组件，则添加到现有集合中；
    /// 否则创建新的任务组件
    fn add_job(&mut self, job: Job) -> &mut Self {
        if let Some(jobs) = self.get_component_ref::<Jobs>() {
            // 注意：这里使用 unsafe 代码直接修改组件
            // 这是因为组件系统的设计限制
            unsafe {
                let raw_ptr = ComponentRef::into_raw(jobs);
                let jobs = &mut *(raw_ptr as *mut Vec<Job>);
                jobs.push(job);
            }
            self
        } else {
            self.add_component(Jobs::single(job))
        }
    }

    /// 添加多个任务到应用程序
    /// 
    /// 如果已经存在任务组件，则合并到现有集合中；
    /// 否则创建新的任务组件
    fn add_jobs(&mut self, new_jobs: Jobs) -> &mut Self {
        if let Some(jobs) = self.get_component_ref::<Jobs>() {
            // 注意：这里使用 unsafe 代码直接修改组件
            unsafe {
                let raw_ptr = ComponentRef::into_raw(jobs);
                let jobs = &mut *(raw_ptr as *mut Jobs);
                jobs.merge(new_jobs);
            }
            self
        } else {
            self.add_component(new_jobs)
        }
    }
}

/// 任务插件
/// 
/// 这个插件负责初始化和启动任务调度系统
pub struct JobPlugin;

/// 为 JobPlugin 实现 Plugin trait
#[async_trait]
impl Plugin for JobPlugin {
    /// 构建插件
    /// 
    /// 向应用程序添加任务调度器
    async fn build(&self, app: &mut AppBuilder) {
        app.add_scheduler(|app: Arc<App>| Box::new(Self::schedule(app)));
    }
}

impl JobPlugin {
    /// 任务调度主逻辑
    /// 
    /// 这个方法负责：
    /// - 从应用程序获取注册的任务
    /// - 初始化任务调度器
    /// - 添加所有任务到调度器
    /// - 配置关闭处理器
    /// - 启动调度器
    /// 
    /// 参数:
    /// - `app`: 应用程序实例
    /// 
    /// 返回: 调度结果消息
    async fn schedule(app: Arc<App>) -> Result<String> {
        // 获取注册的任务
        let jobs = app.get_component_ref::<Jobs>();

        let jobs = match jobs {
            None => {
                let msg = "没有注册任务，任务调度器不会启动。";
                tracing::info!(msg);
                return Ok(msg.to_string());
            }
            Some(jobs) => jobs,
        };

        // 初始化任务调度器
        let mut sched = JobScheduler::new().await.context("任务调度器初始化失败")?;

        // 添加所有任务到调度器
        for job in jobs.deref().iter() {
            sched
                .add(job.to_owned().build(app.clone()))
                .await
                .context("添加任务失败")?;
        }

        // 配置在 Ctrl+C 时关闭调度器
        sched.shutdown_on_ctrl_c();

        // 添加关闭处理器
        sched.set_shutdown_handler(Box::new(|| {
            Box::pin(async move {
                tracing::info!("任务调度器关闭完成");
            })
        }));

        // 启动调度器
        sched.start().await.context("任务调度器启动失败")?;

        Ok("任务调度器已启动".to_string())
    }
}

//! 任务定义模块
//! 
//! 这个模块定义了任务的核心结构，包括：
//! - 任务触发器类型（一次性、固定延迟、固定频率、Cron表达式）
//! - 任务构建器
//! - 任务执行逻辑

use crate::{
    handler::{BoxedHandler, Handler},
    JobId, JobScheduler,
};
use anyhow::Context;
use std::{sync::Arc, time::Duration};
use lib_core::app::app::App;

/// 任务触发器类型
/// 
/// 定义了任务的执行时机和频率
#[derive(Clone)]
enum Trigger {
    /// 一次性任务
    /// 参数：延迟秒数
    OneShot(u64),
    
    /// 固定延迟任务
    /// 参数：间隔秒数（任务完成后等待指定时间再执行下一次）
    FixedDelay(u64),
    
    /// 固定频率任务
    /// 参数：间隔秒数（按固定时间间隔执行，不考虑任务执行时间）
    FixedRate(u64),
    
    /// Cron 表达式任务
    /// 参数：Cron 表达式字符串
    Cron(String),
}

/// 任务结构体
/// 
/// 包含任务的触发器和处理器
#[derive(Clone)]
pub struct Job {
    /// 任务触发器，定义何时执行任务
    trigger: Trigger,
    /// 任务处理器，定义任务的具体执行逻辑
    handler: BoxedHandler,
}

/// 任务构建器
/// 
/// 用于构建任务，提供流畅的API
pub struct JobBuilder {
    /// 任务触发器
    trigger: Trigger,
}

impl Job {
    /// 创建一次性任务构建器
    /// 
    /// 一次性任务只会在指定延迟后执行一次
    /// 
    /// 参数:
    /// - `delay_seconds`: 延迟执行的秒数
    /// 
    /// 返回: 任务构建器
    /// 
    /// # 示例
    /// ```
    /// let job = Job::one_shot(60).run(|| async { println!("一分钟后执行"); });
    /// ```
    pub fn one_shot(delay_seconds: u64) -> JobBuilder {
        JobBuilder {
            trigger: Trigger::OneShot(delay_seconds),
        }
    }
    
    /// 创建固定延迟任务构建器
    /// 
    /// 固定延迟任务会在每次执行完成后等待指定时间再执行下一次
    /// 
    /// 注意: tokio-cron-scheduler 目前不支持真正的固定延迟
    /// 参考: <https://github.com/mvniekerk/tokio-cron-scheduler/issues/56>
    /// 
    /// 参数:
    /// - `seconds`: 每次执行间隔的秒数
    /// 
    /// 返回: 任务构建器
    /// 
    /// # 示例
    /// ```
    /// let job = Job::fix_delay(30).run(|| async { println!("每30秒执行一次"); });
    /// ```
    pub fn fix_delay(seconds: u64) -> JobBuilder {
        JobBuilder {
            trigger: Trigger::FixedDelay(seconds),
        }
    }
    
    /// 创建固定频率任务构建器
    /// 
    /// 固定频率任务会按照固定的时间间隔执行，不考虑任务的实际执行时间
    /// 
    /// 参数:
    /// - `seconds`: 执行频率的秒数间隔
    /// 
    /// 返回: 任务构建器
    /// 
    /// # 示例
    /// ```
    /// let job = Job::fix_rate(60).run(|| async { println!("每分钟执行一次"); });
    /// ```
    pub fn fix_rate(seconds: u64) -> JobBuilder {
        JobBuilder {
            trigger: Trigger::FixedRate(seconds),
        }
    }
    
    /// 创建 Cron 表达式任务构建器
    /// 
    /// 使用 Cron 表达式定义复杂的调度规则
    /// 
    /// 参数:
    /// - `cron`: Cron 表达式字符串（如 "0 0 12 * * *" 表示每天中午12点）
    /// 
    /// 返回: 任务构建器
    /// 
    /// # 示例
    /// ```
    /// // 每天上午 9 点执行
    /// let job = Job::cron("0 0 9 * * *").run(|| async { println!("早上9点执行"); });
    /// ```
    pub fn cron(cron: &str) -> JobBuilder {
        JobBuilder {
            trigger: Trigger::Cron(cron.to_string()),
        }
    }
    
    /// 构建 tokio-cron-scheduler 的任务
    /// 
    /// 这个方法将内部的任务表示转换为 tokio-cron-scheduler 可以执行的任务
    /// 
    /// 参数:
    /// - `app`: 应用程序实例，用于依赖注入
    /// 
    /// 返回: tokio-cron-scheduler 的 Job 实例
    pub(crate) fn build(self, app: Arc<App>) -> tokio_cron_scheduler::Job {
        let handler = self.handler;
        
        // 根据触发器类型创建不同的任务
        match self.trigger {
            // 一次性任务
            Trigger::OneShot(seconds) => tokio_cron_scheduler::Job::new_one_shot_async(
                Duration::from_secs(seconds),
                move |job_id, jobs| {
                    let handler = handler.clone();
                    let app = app.clone();
                    Box::pin(async move { handler.call(job_id, jobs, app).await })
                },
            ),
            // 固定延迟任务（目前实现为重复任务）
            // TODO: 等待上游库支持真正的固定延迟
            Trigger::FixedDelay(seconds) => tokio_cron_scheduler::Job::new_repeated_async(
                Duration::from_secs(seconds),
                move |job_id, jobs| {
                    Box::pin(Self::call(handler.clone(), job_id, jobs, app.clone()))
                },
            ),
            // 固定频率任务
            Trigger::FixedRate(seconds) => tokio_cron_scheduler::Job::new_repeated_async(
                Duration::from_secs(seconds),
                move |job_id, jobs| {
                    Box::pin(Self::call(handler.clone(), job_id, jobs, app.clone()))
                },
            ),
            // Cron 表达式任务
            Trigger::Cron(schedule) => tokio_cron_scheduler::Job::new_async_tz(
                schedule.as_str(),
                chrono::Local, // 使用本地时区
                move |job_id, jobs| {
                    Box::pin(Self::call(handler.clone(), job_id, jobs, app.clone()))
                },
            ),
        }
        .context("构建任务失败")
        .unwrap()
    }

    /// 调用任务处理器
    /// 
    /// 这是一个辅助方法，用于统一调用任务处理器
    /// 
    /// 参数:
    /// - `handler`: 任务处理器
    /// - `job_id`: 任务ID
    /// - `jobs`: 任务调度器
    /// - `app`: 应用程序实例
    async fn call(handler: BoxedHandler, job_id: JobId, jobs: JobScheduler, app: Arc<App>) {
        handler.call(job_id, jobs, app).await
    }
}

impl JobBuilder {
    /// 设置任务的处理器并完成任务构建
    /// 
    /// 这个方法接受一个处理器函数，并创建最终的任务实例
    /// 
    /// 泛型参数:
    /// - `H`: 处理器类型，必须实现 Handler trait
    /// - `A`: 处理器参数类型
    /// 
    /// 参数:
    /// - `handler`: 任务处理器函数
    /// 
    /// 返回: 完整的任务实例
    /// 
    /// # 示例
    /// ```
    /// let job = Job::cron("0 0 9 * * *")
    ///     .run(|| async {
    ///         println!("执行定时任务");
    ///     });
    /// ```
    pub fn run<H, A>(self, handler: H) -> Job
    where
        H: Handler<A> + Sync,
        A: 'static,
    {
        Job {
            trigger: self.trigger,
            handler: BoxedHandler::from_handler(handler),
        }
    }
}

//! 参数提取器模块
//! 
//! 这个模块定义了从应用上下文中提取参数的机制，包括：
//! - FromApp trait 定义了参数提取的接口
//! - Component 提取器，用于获取应用组件
//! - Config 提取器，用于获取配置信息
//! - JobId 和 JobScheduler 的直接提取
//! - 支持依赖注入的参数提取系统

use crate::{JobId, JobScheduler};
use async_trait::async_trait;
use lib_core::app::app::App;
use lib_core::app::plugin::ComponentRegistry;
use lib_core::config::{ConfigRegistry, Configurable};
use std::ops::{Deref, DerefMut};

/// 从应用上下文提取参数的 trait
/// 
/// 这个 trait 定义了如何从应用程序实例中提取任务处理器所需的参数。
/// 所有可以作为任务处理器参数的类型都必须实现这个 trait。
#[async_trait]
pub trait FromApp {
    /// 从应用上下文中异步提取参数
    /// 
    /// 参数:
    /// - `job_id`: 当前任务的唯一标识符
    /// - `scheduler`: 任务调度器实例
    /// - `app`: 应用程序实例，包含所有组件和配置
    /// 
    /// 返回: 提取的参数实例
    async fn from_app(job_id: &JobId, scheduler: &JobScheduler, app: &App) -> Self;
}

/// 组件包装器
/// 
/// 这个结构体包装了从应用程序中提取的组件，
/// 提供了对组件的安全访问
pub struct Component<T: Clone>(pub T);

/// 为 Component 实现 FromApp trait
/// 
/// 这个实现允许任务处理器直接注入应用程序组件
#[async_trait]
impl<T> FromApp for Component<T>
where
    T: Clone + Send + Sync + 'static,
{
    /// 从应用程序中提取指定类型的组件
    /// 
    /// 如果组件不存在，会触发 panic
    async fn from_app(_job_id: &JobId, _scheduler: &JobScheduler, app: &App) -> Self {
        match app.get_component_ref::<T>() {
            Some(component) => Component(T::clone(&component)),
            None => panic!(
                "组件类型 `{}` 不存在",
                std::any::type_name::<T>()
            ),
        }
    }
}

/// 为 Component 实现 Deref trait
/// 
/// 这允许 Component<T> 像 T 一样使用
impl<T: Clone> Deref for Component<T> {
    type Target = T;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

/// 为 Component 实现 DerefMut trait
/// 
/// 这允许通过 Component<T> 修改内部的 T
impl<T: Clone> DerefMut for Component<T> {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}

/// 为 JobId 实现 FromApp trait
/// 
/// 这允许任务处理器直接获取当前任务的ID
#[async_trait]
impl FromApp for JobId {
    /// 直接返回任务ID
    async fn from_app(job_id: &JobId, _scheduler: &JobScheduler, _app: &App) -> Self {
        *job_id
    }
}

/// 为 JobScheduler 实现 FromApp trait
/// 
/// 这允许任务处理器获取调度器实例，用于控制其他任务
#[async_trait]
impl FromApp for JobScheduler {
    /// 返回任务调度器的克隆
    async fn from_app(_job_id: &JobId, scheduler: &JobScheduler, _app: &App) -> Self {
        scheduler.clone()
    }
}

/// 配置包装器
/// 
/// 这个结构体包装了从应用程序中提取的配置，
/// 为任务处理器提供类型安全的配置访问
pub struct Config<T>(pub T)
where
    T: serde::de::DeserializeOwned + Configurable;

/// 为 Config 实现 FromApp trait
/// 
/// 这个实现允许任务处理器直接注入应用程序配置
#[async_trait]
impl<T> FromApp for Config<T>
where
    T: serde::de::DeserializeOwned + Configurable,
{
    /// 从应用程序中提取指定类型的配置
    /// 
    /// 如果配置提取失败，会触发 panic
    async fn from_app(_job_id: &JobId, _scheduler: &JobScheduler, app: &App) -> Self {
        match app.get_config::<T>() {
            Ok(config) => Config(config),
            Err(e) => panic!(
                "获取类型 {} 的配置失败: {}",
                std::any::type_name::<T>(),
                e
            ),
        }
    }
}

/// 为 Config 实现 Deref trait
/// 
/// 这允许 Config<T> 像 T 一样使用
impl<T> Deref for Config<T>
where
    T: serde::de::DeserializeOwned + Configurable,
{
    type Target = T;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

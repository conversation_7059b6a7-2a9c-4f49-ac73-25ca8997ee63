//! 任务处理器模块
//!
//! 这个模块定义了任务处理器的核心功能，包括：
//! - Handler trait 定义了任务处理器的接口
//! - 支持 0-15 个参数的处理器实现
//! - 类型擦除的 BoxedHandler 包装器
//! - 自动任务注册机制
//! - 类型化处理器支持

/// 重新导出 inventory 的 submit 宏，用于注册类型化处理器
pub use inventory::submit;


use crate::JobScheduler;
use crate::{extractor::FromApp, JobId, Jobs};
use lib_core::app::app::App;
use std::pin::Pin;
use std::{
    future::Future,
    sync::{Arc, Mutex},
};

/// 任务处理器 trait
///
/// 定义了任务处理器的核心接口，所有任务处理器都必须实现这个 trait
///
/// 泛型参数:
/// - `T`: 处理器参数类型，支持从 App 中提取的参数
pub trait Handler<T>: Clone + Send + Sized + 'static {
    /// 调用处理器时返回的 Future 类型
    type Future: Future<Output = ()> + Send + 'static;

    /// 调用处理器执行任务
    ///
    /// 参数:
    /// - `job_id`: 任务的唯一标识符
    /// - `scheduler`: 任务调度器实例
    /// - `app`: 应用程序实例，用于依赖注入
    ///
    /// 返回: 异步执行的 Future
    fn call(self, job_id: JobId, scheduler: JobScheduler, app: Arc<App>) -> Self::Future;
}

/// 无参数处理器的实现
///
/// 为没有参数的闭包函数实现 Handler trait
impl<F, Fut> Handler<()> for F
where
    F: FnOnce() -> Fut + Clone + Send + 'static,
    Fut: Future<Output = ()> + Send,
{
    type Future = Pin<Box<dyn Future<Output = ()> + Send>>;

    fn call(self, _job_id: JobId, _scheduler: JobScheduler, _app: Arc<App>) -> Self::Future {
        Box::pin(async move {
            self().await;
        })
    }
}

/// 生成 1-15 个参数的处理器实现的宏
/// 
/// 这个宏用于生成支持不同数量参数的 Handler 实现
#[rustfmt::skip]
macro_rules! all_the_tuples {
    ($name:ident) => {
        $name!([T1]);
        $name!([T1, T2]);
        $name!([T1, T2, T3]);
        $name!([T1, T2, T3, T4]);
        $name!([T1, T2, T3, T4, T5]);
        $name!([T1, T2, T3, T4, T5, T6]);
        $name!([T1, T2, T3, T4, T5, T6, T7]);
        $name!([T1, T2, T3, T4, T5, T6, T7, T8]);
        $name!([T1, T2, T3, T4, T5, T6, T7, T8, T9]);
        $name!([T1, T2, T3, T4, T5, T6, T7, T8, T9, T10]);
        $name!([T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11]);
        $name!([T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12]);
        $name!([T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13]);
        $name!([T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14]);
        $name!([T1, T2, T3, T4, T5, T6, T7, T8, T9, T10, T11, T12, T13, T14, T15]);
    };
}

/// 为多参数处理器实现 Handler trait 的宏
///
/// 这个宏为具有多个参数的闭包函数生成 Handler 实现
/// 参数会通过 FromApp trait 从应用上下文中提取
macro_rules! impl_handler {
    (
        [$($ty:ident),*]
    ) => {
        #[allow(non_snake_case, unused_mut)]
        impl<F, Fut, $($ty,)*> Handler<($($ty,)*)> for F
        where
            F: FnOnce($($ty,)*) -> Fut + Clone + Send + 'static,
            Fut: Future<Output = ()> + Send,
            $( $ty: FromApp + Send, )*
        {
            type Future = Pin<Box<dyn Future<Output = ()> + Send>>;

            fn call(self, job_id: JobId, scheduler: JobScheduler, app: Arc<App>) -> Self::Future {
                Box::pin(async move {
                    // 从应用上下文中提取每个参数
                    $(
                        let $ty = $ty::from_app(&job_id, &scheduler, &app).await;
                    )*

                    // 调用原始处理器函数
                    self($($ty,)*).await;
                })
            }
        }
    };
}

// 为所有支持的参数数量生成 Handler 实现
all_the_tuples!(impl_handler);

/// 类型擦除的处理器包装器
/// 这个结构体将不同类型的处理器包装成统一的类型，
/// 使得可以在运行时存储和调用不同类型的处理器
pub(crate) struct BoxedHandler(Mutex<Box<dyn ErasedHandler>>);

/// 为 BoxedHandler 实现 Clone trait
/// 由于内部使用了 trait object，需要特殊的克隆实现
impl Clone for BoxedHandler {
    fn clone(&self) -> Self {
        Self(Mutex::new(self.0.lock().unwrap().clone_box()))
    }
}

impl BoxedHandler {
    /// 从任意处理器创建 BoxedHandler
    ///
    /// 这个方法将实现了 Handler trait 的任何类型包装成 BoxedHandler
    ///
    /// 泛型参数:
    /// - `H`: 处理器类型
    /// - `T`: 处理器参数类型
    ///
    /// 参数:
    /// - `handler`: 要包装的处理器
    ///
    /// 返回: 包装后的 BoxedHandler
    pub(crate) fn from_handler<H, T>(handler: H) -> Self
    where
        H: Handler<T> + Sync,
        T: 'static,
    {
        Self(Mutex::new(Box::new(MakeErasedHandler {
            handler,
            caller: |handler, job_id, jobs, app| Box::pin(H::call(handler, job_id, jobs, app)),
        })))
    }

    /// 调用包装的处理器
    ///
    /// 参数:
    /// - `job_id`: 任务ID
    /// - `scheduler`: 任务调度器
    /// - `app`: 应用程序实例
    ///
    /// 返回: 异步执行的 Future
    pub(crate) fn call(
        self,
        job_id: JobId,
        scheduler: JobScheduler,
        app: Arc<App>,
    ) -> Pin<Box<dyn Future<Output = ()> + Send>> {
        self.0.into_inner().unwrap().call(job_id, scheduler, app)
    }
}

/// 类型擦除的处理器 trait
///
/// 这个 trait 定义了类型擦除后的处理器接口，
/// 使得不同类型的处理器可以统一存储和调用
pub(crate) trait ErasedHandler: Send {
    /// 克隆处理器
    ///
    /// 由于使用了 trait object，需要提供专门的克隆方法
    fn clone_box(&self) -> Box<dyn ErasedHandler>;

    /// 调用处理器
    ///
    /// 参数:
    /// - `job_id`: 任务ID
    /// - `scheduler`: 任务调度器
    /// - `app`: 应用程序实例
    ///
    /// 返回: 异步执行的 Future
    fn call(
        self: Box<Self>,
        job_id: JobId,
        scheduler: JobScheduler,
        app: Arc<App>,
    ) -> Pin<Box<dyn Future<Output = ()> + Send>>;
}

/// 处理器调用器类型
///
/// 这是一个函数指针类型，用于统一不同处理器的调用方式
type HandlerCaller<H> =
    fn(H, JobId, JobScheduler, Arc<App>) -> Pin<Box<dyn Future<Output = ()> + Send>>;

/// 类型擦除处理器的具体实现
///
/// 这个结构体包装了原始处理器和对应的调用函数
pub(crate) struct MakeErasedHandler<H> {
    /// 原始处理器
    pub(crate) handler: H,
    /// 处理器调用函数
    pub(crate) caller: HandlerCaller<H>,
}

/// 为 MakeErasedHandler 实现 Clone trait
impl<H> Clone for MakeErasedHandler<H>
where
    H: Clone,
{
    fn clone(&self) -> Self {
        Self {
            handler: self.handler.clone(),
            caller: self.caller,
        }
    }
}

/// 为 MakeErasedHandler 实现 ErasedHandler trait
impl<H> ErasedHandler for MakeErasedHandler<H>
where
    H: Clone + Send + Sync + 'static,
{
    fn clone_box(&self) -> Box<dyn ErasedHandler> {
        Box::new(self.clone())
    }

    fn call(
        self: Box<Self>,
        job_id: JobId,
        scheduler: JobScheduler,
        app: Arc<App>,
    ) -> Pin<Box<dyn Future<Output = ()> + Send>> {
        (self.caller)(self.handler, job_id, scheduler, app)
    }
}

/// 类型化处理器注册器 trait
///
/// 这个 trait 用于配置通过宏标记的任务处理器，
/// 支持自动发现和注册处理器
pub trait TypedHandlerRegistrar: Send + Sync + 'static {
    /// 安装任务到任务集合
    ///
    /// 参数:
    /// - `jobs`: 现有的任务集合
    ///
    /// 返回: 添加了新任务的任务集合
    fn install_job(&self, jobs: Jobs) -> Jobs;
}

/// 类型化任务 trait
///
/// 为任务集合提供添加类型化处理器的功能
pub trait TypedJob {
    /// 添加类型化处理器
    ///
    /// 泛型参数:
    /// - `F`: 处理器注册器类型
    ///
    /// 参数:
    /// - `factory`: 处理器注册器实例
    ///
    /// 返回: 更新后的任务集合
    fn typed_job<F: TypedHandlerRegistrar>(self, factory: F) -> Self;
}

/// 为 Jobs 实现 TypedJob trait
impl TypedJob for Jobs {
    fn typed_job<F: TypedHandlerRegistrar>(self, factory: F) -> Self {
        factory.install_job(self)
    }
}

// 收集所有注册的类型化处理器
// 这个宏调用会收集所有通过 submit! 宏注册的处理器
inventory::collect!(&'static dyn TypedHandlerRegistrar);

/// 提交类型化处理器的便利宏
///
/// 这个宏简化了类型化处理器的注册过程
///
/// 参数:
/// - `$ty`: 要注册的处理器类型
/// ```
#[macro_export]
macro_rules! submit_typed_handler {
    ($ty:ident) => {
        ::lib_job::handler::submit! {
            &$ty as &dyn ::lib_job::handler::TypedHandlerRegistrar
        }
    };
}

/// 自动收集所有注册的任务
///
/// 这个函数会遍历所有通过 inventory 机制注册的处理器，
/// 并将它们转换为任务集合
///
/// 返回: 包含所有自动注册任务的任务集合
pub fn auto_jobs() -> Jobs {
    let mut jobs = Jobs::new();

    // 遍历所有注册的处理器工厂
    for factory in inventory::iter::<&dyn TypedHandlerRegistrar> {
        jobs = factory.install_job(jobs);
    }

    jobs
}

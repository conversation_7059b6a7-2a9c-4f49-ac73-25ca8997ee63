[package]
name = "lib-data"
version.workspace = true
edition.workspace = true

[dependencies]
lib-core = { path = "../lib-core" }
lib-macros = { path = "../lib-macros" }
lib-web = { path = "../lib-web" }

serde = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true, features = ["log"] }
schemars = { workspace = true }
sea-orm = { workspace = true, features = ["sqlx-postgres",
    "runtime-tokio-rustls", "debug-print", "macros", "with-chrono", "with-time", "serde_json", "postgres-array"] }
async-trait = { workspace = true }
utoipa = { workspace = true, features = ["chrono"] }

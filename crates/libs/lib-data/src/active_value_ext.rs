/// ActiveValue 扩展工具
///
/// 提供便捷的方法来处理 Option<T> 到 ActiveValue<T> 的转换，
/// 减少在部分更新场景中的样板代码
use sea_orm::{ActiveValue, Set};


/// ActiveValue 扩展 trait
///
/// 为 ActiveValue 提供条件设置方法，简化部分更新的代码
pub trait ActiveValueExt<T> {
    /// 有条件地设置非空字段
    ///
    /// 如果 value 是 Some(val)，则将字段设置为 Set(val)
    /// 如果 value 是 None，则不修改字段（保持原值）
    ///
    /// # Arguments
    /// * `value` - 可选的新值
    ///
    /// # Examples
    /// ```
    /// use lib_data::active_value_ext::ActiveValueExt;
    /// use sea_orm::{ActiveValue, Set, NotSet};
    ///
    /// let mut field: ActiveValue<String> = NotSet;
    /// field.set_if_some(Some("new_value".to_string()));
    /// // field 现在是 Set("new_value".to_string())
    ///
    /// let mut field2: ActiveValue<String> = Set("old_value".to_string());
    /// field2.set_if_some(None::<String>);
    /// // field2 仍然是 Set("old_value".to_string())
    /// ```
    fn set_if_some(&mut self, value: Option<T>);
}

/// ActiveValue<Option<T>> 的扩展 trait
///
/// 专门处理可空字段的条件设置
pub trait ActiveValueNullableExt<T> {
    /// 有条件地设置可空字段
    ///
    /// 如果 value 是 Some(val)，则将字段设置为 Set(Some(val))
    /// 如果 value 是 None，则不修改字段（保持原值）
    ///
    /// # Arguments
    /// * `value` - 可选的新值
    ///
    /// # Examples
    /// ```
    /// use lib_data::active_value_ext::ActiveValueNullableExt;
    /// use sea_orm::{ActiveValue, Set, NotSet};
    ///
    /// let mut field: ActiveValue<Option<String>> = NotSet;
    /// field.set_if_some_nullable(Some("new_value".to_string()));
    /// // field 现在是 Set(Some("new_value".to_string()))
    ///
    /// field.set_if_some_nullable_to_null();
    /// // field 现在是 Set(None)
    /// ```
    fn set_if_some_nullable(&mut self, value: Option<T>);

    /// 将可空字段设置为 NULL
    ///
    /// 将字段设置为 Set(None)
    fn set_if_some_nullable_to_null(&mut self);
}

// 为 ActiveValue<T> 实现扩展方法
impl<T> ActiveValueExt<T> for ActiveValue<T>
where
    T: Into<sea_orm::Value>,
{
    fn set_if_some(&mut self, value: Option<T>) {
        if let Some(val) = value {
            *self = Set(val);
        }
    }
}

// 为 ActiveValue<Option<T>> 实现扩展方法
impl<T> ActiveValueNullableExt<T> for ActiveValue<Option<T>>
where
    T: Into<sea_orm::Value>,
    Option<T>: Into<sea_orm::Value>,
{
    fn set_if_some_nullable(&mut self, value: Option<T>) {
        if let Some(val) = value {
            *self = Set(Some(val));
        }
    }

    fn set_if_some_nullable_to_null(&mut self) {
        *self = Set(None);
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use sea_orm::NotSet;

    #[test]
    fn test_set_if_some() {
        let mut field: ActiveValue<String> = NotSet;

        // 测试设置值
        field.set_if_some(Some("test".to_string()));
        match field.clone() {
            Set(val) => assert_eq!(val, "test"),
            _ => panic!("Expected Set value"),
        }

        // 测试 None 不改变现有值
        field.set_if_some(None::<String>);
        match field {
            Set(val) => assert_eq!(val, "test"),
            _ => panic!("Expected Set value to remain unchanged"),
        }
    }

    #[test]
    fn test_set_if_some_nullable() {
        let mut field: ActiveValue<Option<String>> = NotSet;

        // 测试设置值
        field.set_if_some_nullable(Some("test".to_string()));
        match field.clone() {
            Set(Some(val)) => assert_eq!(val, "test"),
            _ => panic!("Expected Set(Some(value))"),
        }

        // 测试设置为 NULL
        field.set_if_some_nullable_to_null();
        match field {
            Set(None) => {}
            _ => panic!("Expected Set(None)"),
        }
    }
}

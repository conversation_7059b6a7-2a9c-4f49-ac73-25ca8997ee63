/// 数据库配置模块
pub mod config;
/// 分页功能模块
pub mod pagination;
/// ActiveValue 扩展工具模块
pub mod active_value_ext;

use anyhow::Context;
use async_trait::async_trait;
use config::SeaOrmConfig;
use lib_core::app::app::{App, AppBuilder};
use lib_core::app::plugin::{ComponentRegistry, MutableComponentRegistry, Plugin};
use lib_core::config::ConfigRegistry;
use lib_core::error::Result;
use sea_orm::{ConnectOptions, Database};
use std::sync::Arc;
use std::time::Duration;
use tracing::log::LevelFilter;

/// 数据库连接类型别名，方便外部使用
pub type DbConn = sea_orm::DatabaseConnection;

/// SeaORM数据库插件
/// 负责数据库连接的初始化、配置和生命周期管理
pub struct DataPlugin;

#[async_trait]
impl Plugin for DataPlugin {
    async fn build(&self, app: &mut AppBuilder) {
        // 1. 加载数据库配置
        let config = app
            .get_config::<SeaOrmConfig>()
            .expect("sea-orm plugin config load failed");

        // 2. 建立数据库连接
        let conn = Self::connect(&config)
            .await
            .expect("sea-orm plugin load failed");

        // 3. 将数据库连接注册为组件，并添加关闭钩子
        app.add_component(conn)
            .add_shutdown_hook(|app| Box::new(Self::close_db_connection(app)));
    }
}

impl DataPlugin {
    /// 连接数据库
    /// 根据配置创建数据库连接，支持连接池、超时等高级配置
    ///
    /// 该函数会根据配置文件中的数据库参数动态构建连接字符串和连接池参数。
    /// 支持最大连接数、最小空闲连接数、超时、schema等参数配置。
    /// 注意：当前配置为存储本地时间而不是UTC时间
    pub async fn connect(config: &config::SeaOrmConfig) -> Result<DbConn> {
        tracing::info!("Initializing database connection pool...");

        // 创建连接选项
        let mut opt = ConnectOptions::new(&config.uri);

        // 配置连接池参数
        opt.max_connections(config.max_connections) // 最大连接数
            .min_connections(config.min_connections) // 最小连接数
            .sqlx_logging(config.enable_logging); // SQL日志记录

        // 配置连接超时
        opt.connect_timeout(Duration::from_millis(config.connect_timeout));

        // 配置空闲超时
        opt.idle_timeout(Duration::from_millis(config.idle_timeout));

        // 配置获取连接超时
        opt.acquire_timeout(Duration::from_millis(config.acquire_timeout));

        // 配置连接最大生命周期
        opt.max_lifetime(Duration::from_millis(config.max_lifetime));

        // 配置SQL日志级别
        let sqlx_level = match config.sqlx_logging_level.to_lowercase().as_str() {
            "trace" => LevelFilter::Trace,
            "debug" => LevelFilter::Debug,
            "info" => LevelFilter::Info,
            "warn" => LevelFilter::Warn,
            "error" => LevelFilter::Error,
            _ => LevelFilter::Off,
        };
        opt.sqlx_logging_level(sqlx_level);

        // 设置PostgreSQL默认schema
        if !config.schema.is_empty() && config.schema != "public" {
            opt.set_schema_search_path(&config.schema);
        }

        // 建立数据库连接
        Ok(Database::connect(opt)
            .await
            .with_context(|| format!("sea-orm connection failed:{}", &config.uri))?)
    }

    /// 关闭数据库连接
    /// 在应用关闭时被调用，确保数据库连接正确关闭
    async fn close_db_connection(app: Arc<App>) -> Result<String> {
        // 获取数据库连接组件
        app.get_component::<DbConn>()
            .expect("sea-orm db connection not exists")
            .close() // 关闭连接
            .await
            .context("sea-orm db connection close failed")?;

        Ok("sea-orm db connection close successful!".into())
    }
}

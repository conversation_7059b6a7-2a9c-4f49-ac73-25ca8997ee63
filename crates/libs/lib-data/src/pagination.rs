use lib_core::PageDataBuilder;
use lib_core::response::PageData;
use sea_orm::prelude::async_trait::async_trait;
use sea_orm::{ConnectionTrait, EntityTrait, FromQueryResult, PaginatorTrait, Select};
use serde::{Deserialize, Serialize};
use thiserror::Error;
use utoipa::ToSchema;

/// 分页信息（0基索引）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Pagination {
    /// 页码（0基索引，即第一页为0）
    #[serde(default = "default_page")]
    pub page: u64,
    /// 每页大小
    #[serde(default = "default_size")]
    pub size: u64,
}

fn default_page() -> u64 {
    0
}

fn default_size() -> u64 {
    20
}

impl Pagination {
    /// 创建分页参数（1基索引转0基索引）
    ///
    /// # 参数
    /// * `page` - 页码（1基索引，即第一页为1）
    /// * `size` - 每页大小
    pub fn from_one_based(page: Option<u64>, size: Option<u64>) -> Self {
        Self {
            page: page.unwrap_or(1).saturating_sub(1), // 转换为0基索引
            size: size.unwrap_or(20),                  // 不再限制大小，由上层处理
        }
    }

    /// 创建分页参数（0基索引）
    ///
    /// # 参数
    /// * `page` - 页码（0基索引，即第一页为0）
    /// * `size` - 每页大小
    pub fn new(page: u64, size: u64) -> Self {
        Self {
            page,
            size, // 不再限制大小，由上层处理
        }
    }

    /// 获取偏移量
    pub fn offset(&self) -> u64 {
        self.page * self.size
    }

    /// 检查是否为第一页
    pub fn is_first_page(&self) -> bool {
        self.page == 0
    }
}

mod web {
    use super::Pagination;
    use crate::config::SeaOrmWebConfig;
    use lib_web::axum::extract::rejection::QueryRejection;
    use lib_web::axum::extract::{FromRequestParts, Query};
    use lib_web::axum::http::request::Parts;
    use lib_web::axum::response::IntoResponse;
    use lib_web::extractor::RequestPartsExt;
    use serde::Deserialize;
    use std::result::Result as StdResult;
    use thiserror::Error;

    #[derive(Debug, Error)]
    pub enum SeaOrmWebErr {
        #[error(transparent)]
        QueryRejection(#[from] QueryRejection),

        #[error(transparent)]
        WebError(#[from] lib_web::error::WebError),
    }

    impl IntoResponse for SeaOrmWebErr {
        fn into_response(self) -> lib_web::axum::response::Response {
            match self {
                Self::QueryRejection(e) => e.into_response(),
                Self::WebError(e) => e.into_response(),
            }
        }
    }

    #[derive(Debug, Clone, Deserialize)]
    struct OptionalPagination {
        page: Option<u64>,
        size: Option<u64>,
    }

    impl<S> FromRequestParts<S> for Pagination
    where
        S: Sync,
    {
        type Rejection = SeaOrmWebErr;

        async fn from_request_parts(
            parts: &mut Parts,
            _state: &S,
        ) -> StdResult<Self, Self::Rejection> {
            let Query(pagination) = Query::<OptionalPagination>::try_from_uri(&parts.uri)?;

            let config = parts.get_config::<SeaOrmWebConfig>()?;

            let size = match pagination.size {
                Some(size) => {
                    if size > config.max_page_size {
                        config.max_page_size
                    } else {
                        size
                    }
                }
                None => config.default_page_size,
            };

            let page = if config.one_indexed {
                pagination
                    .page
                    .map(|page| if page == 0 { 0 } else { page - 1 })
                    .unwrap_or(0)
            } else {
                pagination.page.unwrap_or(0)
            };

            Ok(Pagination { page, size })
        }
    }
}

#[derive(Debug, Error)]
pub enum OrmError {
    #[error(transparent)]
    DbErr(#[from] sea_orm::DbErr),
}

#[async_trait]
/// 用于任何可以分页结果的类型的特征
pub trait PaginationExt<'db, C, M>
where
    C: ConnectionTrait,
{
    /// 分页查询，直接返回PageData格式（推荐使用）
    ///
    /// 自动处理索引转换：输入0基索引，返回1基索引的PageData
    /// 这样业务层就不需要手动转换分页结果了
    async fn page(
        self,
        db: &'db C,
        pagination: &Pagination,
    ) -> std::result::Result<PageData<M>, OrmError>
    where
        M: ToSchema;

    /// 分页查询，支持自动类型转换（新增功能）
    ///
    /// 根据指定的泛型类型 T 自动转换查询结果
    /// 要求 M 实现了 Into<T>，实现零成本类型转换
    async fn page_as<T>(
        self,
        db: &'db C,
        pagination: &Pagination,
    ) -> std::result::Result<PageData<T>, OrmError>
    where
        M: Into<T> + Send + Sync,
        T: Send + Sync + ToSchema;

    /// 分页查询，返回 PageDataBuilder（用于 Repository 层）
    ///
    /// 返回不需要 ToSchema 约束的 PageDataBuilder，适用于 Repository 层
    /// 在 Service 层可以转换为 PageData<T: ToSchema>
    async fn page_builder(
        self,
        db: &'db C,
        pagination: &Pagination,
    ) -> std::result::Result<PageDataBuilder<M>, OrmError>;

    /// 分页查询，返回 PageDataBuilder 并支持类型转换
    ///
    /// 结合了 page_as 和 page_builder 的功能
    /// 返回转换后类型的 PageDataBuilder
    async fn page_builder_as<T>(
        self,
        db: &'db C,
        pagination: &Pagination,
    ) -> std::result::Result<PageDataBuilder<T>, OrmError>
    where
        M: Into<T> + Send + Sync,
        T: Send + Sync;
}

#[async_trait]
impl<'db, C, M, E> PaginationExt<'db, C, M> for Select<E>
where
    C: ConnectionTrait,
    E: EntityTrait<Model = M>,
    M: FromQueryResult + Sized + Send + Sync + 'db,
{
    async fn page(
        self,
        db: &'db C,
        pagination: &Pagination,
    ) -> std::result::Result<PageData<M>, OrmError>
    where
        M: ToSchema,
    {
        let total = self.clone().paginate(db, 1).num_items().await?;
        let content = self
            .paginate(db, pagination.size)
            .fetch_page(pagination.page)
            .await?;

        // 将0基索引转换为1基索引返回给前端
        Ok(PageData::new(
            content,
            total,
            pagination.page + 1, // 转换为1基索引
            pagination.size,
        ))
    }

    async fn page_as<T>(
        self,
        db: &'db C,
        pagination: &Pagination,
    ) -> std::result::Result<PageData<T>, OrmError>
    where
        M: Into<T> + Send + Sync,
        T: Send + Sync + ToSchema,
    {
        let total = self.clone().paginate(db, 1).num_items().await?;
        let raw_content = self
            .paginate(db, pagination.size)
            .fetch_page(pagination.page)
            .await?;

        // 自动转换类型：M -> T
        let converted_content: Vec<T> = raw_content.into_iter().map(|item| item.into()).collect();

        // 将0基索引转换为1基索引返回给前端
        Ok(PageData::new(
            converted_content,
            total,
            pagination.page + 1, // 转换为1基索引
            pagination.size,
        ))
    }

    async fn page_builder(
        self,
        db: &'db C,
        pagination: &Pagination,
    ) -> std::result::Result<PageDataBuilder<M>, OrmError> {
        let total = self.clone().paginate(db, 1).num_items().await?;
        let content = self
            .paginate(db, pagination.size)
            .fetch_page(pagination.page)
            .await?;

        // 将0基索引转换为1基索引返回给前端
        Ok(PageDataBuilder::new(
            content,
            total,
            pagination.page + 1, // 转换为1基索引
            pagination.size,
        ))
    }

    async fn page_builder_as<T>(
        self,
        db: &'db C,
        pagination: &Pagination,
    ) -> std::result::Result<PageDataBuilder<T>, OrmError>
    where
        M: Into<T> + Send + Sync,
        T: Send + Sync,
    {
        let total = self.clone().paginate(db, 1).num_items().await?;
        let raw_content = self
            .paginate(db, pagination.size)
            .fetch_page(pagination.page)
            .await?;

        // 自动转换类型：M -> T
        let converted_content: Vec<T> = raw_content.into_iter().map(|item| item.into()).collect();

        // 将0基索引转换为1基索引返回
        Ok(PageDataBuilder::new(
            converted_content,
            total,
            pagination.page + 1, // 转换为1基索引
            pagination.size,
        ))
    }
}

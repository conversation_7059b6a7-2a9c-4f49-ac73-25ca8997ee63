use lib_macros::Configurable;
use schemars::JsonSchema;
use serde::Deserialize;

/// 数据库配置
#[derive(Debug, Configurable, Clone, JsonSchema, Deserialize)]
#[config_prefix = "database"]
pub struct SeaOrmConfig {
    /// 数据库连接URI，例如：
    /// * Postgres: `postgres://root:12341234@localhost:5432/myapp_development`
    pub uri: String,

    /// 是否启用 `SQLx` 语句日志记录
    #[serde(default)]
    pub enable_logging: bool,

    /// SQL日志级别（trace, debug, info, warn, error）
    #[serde(default = "default_sqlx_logging_level")]
    pub sqlx_logging_level: String,

    /// 连接池最小连接数
    #[serde(default = "default_min_connections")]
    pub min_connections: u32,

    /// 连接池最大连接数
    #[serde(default = "default_max_connections")]
    pub max_connections: u32,

    /// 获取连接时的超时时间（毫秒）
    #[serde(default = "default_connect_timeout")]
    pub connect_timeout: u64,

    /// 单个连接的最大空闲时间（毫秒）
    /// 在空闲队列中停留时间超过此值的连接将被关闭
    /// 对于按使用量计费的数据库服务，这可以节省成本
    #[serde(default = "default_idle_timeout")]
    pub idle_timeout: u64,

    /// 获取连接的超时时间（毫秒）
    #[serde(default = "default_acquire_timeout")]
    pub acquire_timeout: u64,

    /// 连接的最大生命周期（毫秒）
    /// 防止连接长时间占用，定期更新连接
    #[serde(default = "default_max_lifetime")]
    pub max_lifetime: u64,

    /// PostgreSQL默认schema路径
    /// 对多租户应用或复杂数据库架构很重要
    #[serde(default = "default_schema")]
    pub schema: String,
}

fn default_min_connections() -> u32 {
    1
}

fn default_max_connections() -> u32 {
    10
}

fn default_connect_timeout() -> u64 {
    8000  // 8秒
}

fn default_idle_timeout() -> u64 {
    600000  // 10分钟
}

fn default_acquire_timeout() -> u64 {
    30000  // 30秒
}

fn default_max_lifetime() -> u64 {
    1800000  // 30分钟
}

fn default_sqlx_logging_level() -> String {
    "info".to_string()
}

fn default_schema() -> String {
    "public".to_string()
}

/// 数据库Web分页配置
#[derive(Debug, Configurable, Clone, JsonSchema, Deserialize)]
#[config_prefix = "database-web"]
pub struct SeaOrmWebConfig {
    /// 是否在请求参数中使用基于1的页码索引
    /// 默认为false，表示请求中的页码0等于第一页
    /// 如果设置为true，请求中的页码1将被视为第一页
    #[serde(default = "default_one_indexed")]
    pub one_indexed: bool,

    /// 最大页面大小限制
    /// 设置页面大小的上限，防止潜在的OOM攻击
    /// 默认为2000
    #[serde(default = "default_max_page_size")]
    pub max_page_size: u64,

    /// 默认页面大小
    #[serde(default = "default_page_size")]
    pub default_page_size: u64,
}

#[allow(dead_code)]
fn default_one_indexed() -> bool {
    false
}

#[allow(dead_code)]
fn default_max_page_size() -> u64 {
    2000
}

#[allow(dead_code)]
fn default_page_size() -> u64 {
    20
}

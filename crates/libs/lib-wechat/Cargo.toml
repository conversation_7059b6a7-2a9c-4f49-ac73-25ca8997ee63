[package]
name = "lib-wechat"
version.workspace = true
edition.workspace = true


[dependencies]
lib-core = { path = "../lib-core" }
lib-macros = { path = "../lib-macros" }

# 异步支持
async-trait = "0.1"
tokio = { version = "1.0", features = ["full"] }

# HTTP客户端
reqwest = { version = "0.11", features = ["json"] }

# 序列化/反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 配置和JSON Schema
schemars = "0.8"

# 日志
tracing = "0.1"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 加密相关（微信签名验证）
hex = "0.4"
sha1 = "0.10"
base64 = "0.22"

# URL编码
urlencoding = "2.1"

[dev-dependencies]
# 测试支持
tokio-test = "0.4"
mockito = "1.5"
wiremock = "0.6"

# 测试工具
assert_matches = "1.5"

# 日志订阅器（用于示例）
tracing-subscriber = "0.3"
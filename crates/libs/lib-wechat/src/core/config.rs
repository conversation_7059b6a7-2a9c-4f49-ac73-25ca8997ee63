use lib_macros::Configurable;
use schemars::JsonSchema;
use serde::Deserialize;

/// 微信小程序配置
/// 
/// 只负责存储配置数据，不涉及业务逻辑
#[derive(Debug, Configurable, Clone, JsonSchema, Deserialize)]
#[config_prefix = "wechat"]
pub struct WechatConfig {
    /// 微信小程序 AppID
    pub app_id: String,

    /// 微信小程序 AppSecret  
    pub app_secret: String,

    /// 请求超时时间（毫秒）
    #[serde(default = "default_request_timeout_ms")]
    pub request_timeout_ms: u64,

    /// 重试次数
    #[serde(default = "default_retry_count")]
    pub retry_count: u32,
}

impl WechatConfig {
    /// 验证配置有效性
    pub fn validate(&self) -> Result<(), String> {
        if self.app_id.is_empty() {
            return Err("微信AppID不能为空".to_string());
        }
        
        if self.app_secret.is_empty() {
            return Err("微信AppSecret不能为空".to_string());
        }
        
        if self.request_timeout_ms == 0 {
            return Err("请求超时时间必须大于0".to_string());
        }
        
        Ok(())
    }
}

// 默认值函数
fn default_request_timeout_ms() -> u64 {
    30000 // 30秒
}

fn default_retry_count() -> u32 {
    3
}
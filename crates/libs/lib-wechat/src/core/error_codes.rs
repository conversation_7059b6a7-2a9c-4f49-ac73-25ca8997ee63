/// 微信API常见错误码定义
///
/// 参考微信官方文档：https://developers.weixin.qq.com/miniprogram/dev/framework/usability/PublicErrno.html

/// 成功
pub const SUCCESS: i32 = 0;

/// 系统繁忙，此时请开发者稍候再试
pub const SYSTEM_BUSY: i32 = -1;

/// 不合法的凭证类型
pub const INVALID_CREDENTIAL: i32 = 40001;

/// 不合法的grant_type
pub const INVALID_GRANT_TYPE: i32 = 40002;

/// 不合法的OpenID
pub const INVALID_OPENID: i32 = 40003;

/// 不合法的媒体文件类型
pub const INVALID_MEDIA_TYPE: i32 = 40004;

/// 不合法的文件类型
pub const INVALID_FILE_TYPE: i32 = 40005;

/// 不合法的文件大小
pub const INVALID_FILE_SIZE: i32 = 40006;

/// 不合法的媒体文件ID
pub const INVALID_MEDIA_ID: i32 = 40007;

/// 不合法的消息类型
pub const INVALID_MESSAGE_TYPE: i32 = 40008;

/// 不合法的图片文件大小
pub const INVALID_IMAGE_SIZE: i32 = 40009;

/// 不合法的语音文件大小
pub const INVALID_VOICE_SIZE: i32 = 40010;

/// 不合法的视频文件大小
pub const INVALID_VIDEO_SIZE: i32 = 40011;

/// 不合法的缩略图文件大小
pub const INVALID_THUMB_SIZE: i32 = 40012;

/// 不合法的 APPID
pub const INVALID_APPID: i32 = 40013;

/// 不合法的access_token
pub const INVALID_ACCESS_TOKEN: i32 = 40014;

/// 不合法的菜单类型
pub const INVALID_MENU_TYPE: i32 = 40015;

/// 不合法的按钮个数
pub const INVALID_BUTTON_COUNT: i32 = 40016;

/// 不合法的按钮个数
pub const INVALID_BUTTON_COUNT_ALT: i32 = 40017;

/// 不合法的按钮名字长度
pub const INVALID_BUTTON_NAME_LENGTH: i32 = 40018;

/// 不合法的按钮KEY长度
pub const INVALID_BUTTON_KEY_LENGTH: i32 = 40019;

/// 不合法的按钮URL长度
pub const INVALID_BUTTON_URL_LENGTH: i32 = 40020;

/// 不合法的菜单版本号
pub const INVALID_MENU_VERSION: i32 = 40021;

/// 不合法的子菜单级数
pub const INVALID_SUB_MENU_LEVEL: i32 = 40022;

/// 不合法的子菜单按钮个数
pub const INVALID_SUB_MENU_BUTTON_COUNT: i32 = 40023;

/// 不合法的子菜单按钮类型
pub const INVALID_SUB_MENU_BUTTON_TYPE: i32 = 40024;

/// 不合法的子菜单按钮名字长度
pub const INVALID_SUB_MENU_BUTTON_NAME_LENGTH: i32 = 40025;

/// 不合法的子菜单按钮KEY长度
pub const INVALID_SUB_MENU_BUTTON_KEY_LENGTH: i32 = 40026;

/// 不合法的子菜单按钮URL长度
pub const INVALID_SUB_MENU_BUTTON_URL_LENGTH: i32 = 40027;

/// 不合法的自定义菜单使用用户
pub const INVALID_CUSTOM_MENU_USER: i32 = 40028;

/// js_code无效
pub const INVALID_CODE: i32 = 40029;

/// 不合法的refresh_token
pub const INVALID_REFRESH_TOKEN: i32 = 40030;

/// access_token超时
pub const ACCESS_TOKEN_TIMEOUT: i32 = 42001;

/// refresh_token超时
pub const REFRESH_TOKEN_TIMEOUT: i32 = 42002;

/// oauth_code超时
pub const OAUTH_CODE_TIMEOUT: i32 = 42003;

/// 需要GET请求
pub const REQUIRE_GET: i32 = 43001;

/// 需要POST请求
pub const REQUIRE_POST: i32 = 43002;

/// 需要HTTPS请求
pub const REQUIRE_HTTPS: i32 = 43003;

/// 需要接收者关注
pub const REQUIRE_SUBSCRIBE: i32 = 43004;

/// 需要好友关系
pub const REQUIRE_FRIEND: i32 = 43005;

/// 多媒体文件为空
pub const EMPTY_MEDIA_FILE: i32 = 44001;

/// POST的数据包为空
pub const EMPTY_POST_DATA: i32 = 44002;

/// 图文消息内容为空
pub const EMPTY_NEWS_CONTENT: i32 = 44003;

/// 文本消息内容为空
pub const EMPTY_TEXT_CONTENT: i32 = 44004;

/// 多媒体文件大小超过限制
pub const MEDIA_FILE_TOO_LARGE: i32 = 45001;

/// 消息内容超过限制
pub const MESSAGE_CONTENT_TOO_LARGE: i32 = 45002;

/// 标题字段超过限制
pub const TITLE_TOO_LONG: i32 = 45003;

/// 描述字段超过限制
pub const DESCRIPTION_TOO_LONG: i32 = 45004;

/// 链接字段超过限制
pub const URL_TOO_LONG: i32 = 45005;

/// 图片链接字段超过限制
pub const PIC_URL_TOO_LONG: i32 = 45006;

/// 语音播放时间超过限制
pub const VOICE_TIME_TOO_LONG: i32 = 45007;

/// 图文消息超过限制
pub const NEWS_COUNT_TOO_LARGE: i32 = 45008;

/// 接口调用超过限制
pub const API_CALL_LIMIT: i32 = 45009;

/// 创建菜单个数超过限制
pub const MENU_COUNT_LIMIT: i32 = 45010;

/// API调用太频繁，请稍候再试
pub const FREQ_LIMIT: i32 = 45011;

/// 回复时间超过限制
pub const REPLY_TIME_LIMIT: i32 = 45015;

/// 系统分组，不允许修改
pub const SYSTEM_GROUP_PROTECTED: i32 = 45016;

/// 分组名字过长
pub const GROUP_NAME_TOO_LONG: i32 = 45017;

/// 分组数量超过上限
pub const GROUP_COUNT_LIMIT: i32 = 45018;

/// 不存在媒体文件
pub const MEDIA_NOT_EXIST: i32 = 46001;

/// 不存在的菜单版本
pub const MENU_VERSION_NOT_EXIST: i32 = 46002;

/// 不存在的菜单数据
pub const MENU_DATA_NOT_EXIST: i32 = 46003;

/// 不存在的用户
pub const USER_NOT_EXIST: i32 = 46004;

/// 解析JSON/XML内容错误
pub const JSON_XML_PARSE_ERROR: i32 = 47001;

/// api功能未授权，请确认公众号已获得该接口
pub const API_UNAUTHORIZED: i32 = 48001;

/// 用户未授权该api
pub const USER_UNAUTHORIZED: i32 = 48002;

/// 接口禁用
pub const API_FORBIDDEN: i32 = 48003;

/// 用户拒绝授权API
pub const USER_REJECTED_API: i32 = 48004;

/// API未授权
pub const API_NOT_AUTHORIZED: i32 = 48005;

/// api禁止删除被自动回复和自定义菜单引用的素材
pub const CANNOT_DELETE_REFERENCED_MATERIAL: i32 = 48006;

/// 参数错误
pub const PARAM_ERROR: i32 = 50001;

/// 用户名或密码错误
pub const INVALID_USERNAME_PASSWORD: i32 = 50002;

/// 用户未关注公众号
pub const USER_NOT_SUBSCRIBED: i32 = 50005;

/// 用户被黑名单
pub const USER_IN_BLACKLIST: i32 = 50008;

/// 该公众号的粉丝超过上限
pub const FANS_LIMIT: i32 = 50009;

/// 微信号不存在
pub const WECHAT_ID_NOT_EXIST: i32 = 61451;

/// 微信号被封
pub const WECHAT_ID_BLOCKED: i32 = 61452;

/// 微信号异常
pub const WECHAT_ID_ABNORMAL: i32 = 61453;

/// 获取错误码的描述信息
pub fn get_error_message(code: i32) -> &'static str {
    match code {
        SUCCESS => "成功",
        SYSTEM_BUSY => "系统繁忙，此时请开发者稍候再试",
        INVALID_CREDENTIAL => "不合法的凭证类型",
        INVALID_GRANT_TYPE => "不合法的grant_type",
        INVALID_OPENID => "不合法的OpenID",
        INVALID_APPID => "不合法的APPID",
        INVALID_ACCESS_TOKEN => "不合法的access_token",
        INVALID_CODE => "js_code无效",
        ACCESS_TOKEN_TIMEOUT => "access_token超时",
        REFRESH_TOKEN_TIMEOUT => "refresh_token超时",
        OAUTH_CODE_TIMEOUT => "oauth_code超时",
        REQUIRE_GET => "需要GET请求",
        REQUIRE_POST => "需要POST请求",
        REQUIRE_HTTPS => "需要HTTPS请求",
        FREQ_LIMIT => "API调用太频繁，请稍候再试",
        API_UNAUTHORIZED => "api功能未授权，请确认小程序已获得该接口",
        USER_UNAUTHORIZED => "用户未授权该api",
        API_FORBIDDEN => "接口禁用",
        PARAM_ERROR => "参数错误",
        INVALID_USERNAME_PASSWORD => "用户名或密码错误",
        USER_NOT_SUBSCRIBED => "用户未关注公众号",
        _ => "未知错误",
    }
}

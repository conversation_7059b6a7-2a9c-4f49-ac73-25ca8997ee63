use serde::{Deserialize, Serialize};
use thiserror::Error;

/// 微信API错误类型
#[derive(Debug, Error)]
pub enum WechatError {
    /// HTTP请求错误
    #[error("微信API请求失败: {0}")]
    RequestError(#[from] reqwest::Error),
    
    /// JSON序列化/反序列化错误
    #[error("JSON处理错误: {0}")]
    JsonError(#[from] serde_json::Error),
    
    /// 微信API业务错误
    #[error("微信API错误: {code} - {message}")]
    ApiError { code: i32, message: String },
    
    /// 配置错误
    #[error("配置错误: {0}")]
    ConfigError(String),
    
    /// 参数错误
    #[error("参数错误: {0}")]
    ParamError(String),
    
    /// HTTP状态码错误
    #[error("HTTP错误: {0}")]
    HttpError(u16),
    
    /// 响应解析错误
    #[error("响应解析错误: {0}")]
    ParseError(String),
    
    /// 网络超时错误
    #[error("请求超时")]
    TimeoutError,
    
    /// 其他错误
    #[error("未知错误: {0}")]
    OtherError(String),
}

/// 微信API通用响应结构
#[derive(Debug, Serialize, Deserialize)]
pub struct WechatApiResponse<T> {
    pub errcode: Option<i32>,
    pub errmsg: Option<String>,
    #[serde(flatten)]
    pub data: Option<T>,
}

impl<T> WechatApiResponse<T> {
    /// 检查响应是否成功
    pub fn is_success(&self) -> bool {
        self.errcode.unwrap_or(0) == 0
    }

    /// 获取错误信息
    pub fn get_error(&self) -> Option<WechatError> {
        if let (Some(code), Some(msg)) = (self.errcode, &self.errmsg) {
            if code != 0 {
                return Some(WechatError::ApiError {
                    code,
                    message: msg.clone(),
                });
            }
        }
        None
    }

    /// 提取数据，如果有错误则返回错误
    pub fn into_result(self) -> Result<T, WechatError> {
        if let Some(error) = self.get_error() {
            Err(error)
        } else {
            self.data.ok_or_else(|| WechatError::OtherError("响应数据为空".to_string()))
        }
    }
}

pub type WechatResult<T> = Result<T, WechatError>;
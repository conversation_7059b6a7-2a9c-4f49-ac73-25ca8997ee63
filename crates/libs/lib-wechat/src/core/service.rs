use crate::api::client::WechatClient;
use crate::api::response::{Code2SessionResponse, GetAccessTokenResponse, GetPhoneNumberResponse};
use crate::core::config::WechatConfig;
use crate::core::error::WechatResult;

/// 微信服务层
///
/// 提供更高层次的API调用封装，但不处理业务逻辑（如会话管理、用户管理等）
/// 这些业务逻辑应该由调用方（业务层）来处理
#[derive(Debug, Clone)]
pub struct WechatService {
    client: WechatClient,
}

impl WechatService {
    /// 创建新的微信服务实例
    pub fn new(config: WechatConfig) -> WechatResult<Self> {
        let client = WechatClient::new(config)?;
        Ok(Self { client })
    }

    /// 从现有客户端创建服务
    pub fn from_client(client: WechatClient) -> Self {
        Self { client }
    }

    /// 通过微信授权码获取用户信息
    ///
    /// 这是对 `WechatClient::code2session` 的简单封装
    /// 调用方需要自己处理返回的用户信息（如存储到数据库、创建会话等）
    pub async fn login(&self, js_code: String) -> WechatResult<Code2SessionResponse> {
        self.client.code2session(js_code).await
    }

    /// 获取微信小程序访问令牌
    ///
    /// 这是对 `WechatClient::get_access_token` 的简单封装
    pub async fn get_access_token(&self) -> WechatResult<GetAccessTokenResponse> {
        self.client.get_access_token().await
    }

    /// 获取用户手机号
    ///
    /// 这是对 `WechatClient::get_phone_number` 的封装，自动获取access_token
    /// 调用方需要自己处理返回的手机号信息（如存储到数据库、验证等）
    ///
    /// # 参数
    /// * `phone_code` - 手机号获取凭证，通过前端 wx.getPhoneNumber 获取
    ///
    /// # 返回
    /// 返回用户的手机号信息
    ///
    pub async fn get_phone_number(
        &self,
        phone_code: String,
    ) -> WechatResult<GetPhoneNumberResponse> {
        // 先获取access_token
        let token_response = self.get_access_token().await?;
        let access_token = token_response.access_token.ok_or_else(|| {
            crate::core::error::WechatError::OtherError("获取access_token失败".to_string())
        })?;

        // 使用access_token获取手机号
        self.client.get_phone_number(phone_code, access_token).await
    }

    /// 获取底层客户端的引用
    ///
    /// 如果需要调用更底层的API，可以通过这个方法获取客户端实例
    pub fn client(&self) -> &WechatClient {
        &self.client
    }

    /// 检查微信API是否可用（健康检查）
    ///
    /// 通过尝试获取access_token来验证配置是否正确
    pub async fn health_check(&self) -> bool {
        match self.get_access_token().await {
            Ok(response) => {
                // 检查响应是否包含access_token
                response.access_token.is_some()
            }
            Err(_) => false,
        }
    }
}

mod api;
mod core;

// 导出主要的公共接口
pub use api::{
    client::WechatClient,
    endpoints::{EndpointBuilder, MiniProgramEndpoints, PaymentEndpoints, MessageEndpoints},
    request::{Code2SessionRequest, GetAccessTokenRequest, GetPhoneNumberRequest},
    response::{Code2SessionResponse, GetAccessTokenResponse, GetPhoneNumberResponse, PhoneInfo, Watermark, WechatErrorResponse},
};

pub use core::{
    config::WechatConfig,
    error::{WechatError, WechatResult, WechatApiResponse},
    error_codes::*,
    service::WechatService,
};

use async_trait::async_trait;
use lib_core::app::app::{App, AppBuilder};
use lib_core::app::plugin::{ComponentRegistry, MutableComponentRegistry, Plugin};
use lib_core::config::ConfigRegistry;
use lib_core::error::Result;
use tracing::{info, warn};

/// 微信小程序插件
/// 负责微信客户端的初始化、配置和生命周期管理
#[derive(Debug)]
pub struct WechatPlugin;

#[async_trait]
impl Plugin for WechatPlugin {
    async fn build(&self, app: &mut AppBuilder) {
        // 1. 加载微信配置
        let config = app
            .get_config::<WechatConfig>()
            .expect("wechat plugin config load failed");

        // 2. 验证配置
        if let Err(e) = config.validate() {
            panic!("wechat plugin config validation failed: {}", e);
        }

        // 3. 创建微信服务
        let service = WechatService::new(config.clone())
            .expect("wechat service create failed");

        info!("微信小程序插件初始化成功，AppID: {}", config.app_id);

        // 4. 将微信服务注册为组件
        app.add_component(service)
            .add_shutdown_hook(|_app| Box::new(Self::shutdown()));
    }
}

impl WechatPlugin {
    /// 插件关闭时的清理工作
    async fn shutdown() -> Result<String> {
        info!("微信小程序插件正在关闭...");
        
        // 这里可以添加需要清理的资源
        // 例如：关闭连接、清理缓存等
        
        Ok("微信小程序插件关闭成功".to_string())
    }

    /// 获取微信服务实例
    /// 这是一个便利方法，用于从应用程序全局获取微信服务
    pub fn get_service() -> Option<WechatService> {
        App::global().get_component::<WechatService>().map(|s| s.clone())
    }

    /// 验证插件依赖
    /// 检查是否存在必要的依赖组件
    pub fn verify_dependencies(app: &App) -> Result<()> {
        // 检查是否有微信服务组件
        if app.get_component::<WechatService>().is_none() {
            return Err(lib_core::error::AppError::ComponentNotExist("微信服务组件未找到，请确保 WechatPlugin 已正确初始化").into());
        }

        Ok(())
    }

    /// 健康检查
    /// 验证微信服务是否可用
    pub async fn health_check() -> Result<bool> {
        match Self::get_service() {
            Some(service) => {
                // 使用服务的健康检查方法
                let is_healthy = service.health_check().await;
                if is_healthy {
                    info!("微信服务健康检查通过");
                } else {
                    warn!("微信服务健康检查失败");
                }
                Ok(is_healthy)
            }
            None => {
                warn!("微信服务未初始化");
                Ok(false)
            }
        }
    }
}
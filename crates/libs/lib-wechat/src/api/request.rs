use serde::{Deserialize, Serialize};

/// 微信小程序登录请求参数
/// 
/// 对应微信官方接口：GET https://api.weixin.qq.com/sns/jscode2session
/// 
/// 文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Code2SessionRequest {
    /// 小程序 appId
    pub appid: String,
    
    /// 小程序 appSecret
    pub secret: String,
    
    /// 登录时获取的 code，可通过wx.login获取
    pub js_code: String,
    
    /// 授权类型，此处只需填写 authorization_code
    pub grant_type: String,
}

impl Code2SessionRequest {
    /// 创建新的登录请求
    pub fn new(appid: String, secret: String, js_code: String) -> Self {
        Self {
            appid,
            secret,
            js_code,
            grant_type: "authorization_code".to_string(),
        }
    }
}

/// 微信小程序获取访问令牌请求参数
/// 
/// 对应微信官方接口：GET https://api.weixin.qq.com/cgi-bin/token
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetAccessTokenRequest {
    /// 填写 client_credential
    pub grant_type: String,
    
    /// 小程序唯一凭证，即 AppID
    pub appid: String,
    
    /// 小程序唯一凭证密钥，即 AppSecret
    pub secret: String,
}

impl GetAccessTokenRequest {
    /// 创建新的访问令牌请求
    pub fn new(appid: String, secret: String) -> Self {
        Self {
            grant_type: "client_credential".to_string(),
            appid,
            secret,
        }
    }
}

/// 微信小程序获取手机号请求参数
/// 
/// 对应微信官方接口：POST https://api.weixin.qq.com/wxa/business/getuserphonenumber
/// 
/// 文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetPhoneNumberRequest {
    /// 手机号获取凭证，通过前端 wx.getPhoneNumber 获取
    pub code: String,
}

impl GetPhoneNumberRequest {
    /// 创建新的获取手机号请求
    pub fn new(code: String) -> Self {
        Self { code }
    }
}
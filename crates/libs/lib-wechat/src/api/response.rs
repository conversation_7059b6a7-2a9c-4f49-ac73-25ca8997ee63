use serde::{Deserialize, Serialize};

/// 微信小程序登录成功响应
/// 
/// 对应微信官方接口：GET https://api.weixin.qq.com/sns/jscode2session
/// 文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-login/code2Session.html
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Code2SessionResponse {
    /// 用户唯一标识
    pub openid: String,
    
    /// 会话密钥
    pub session_key: String,
    
    /// 用户在开放平台的唯一标识符，若当前小程序已绑定到微信开放平台帐号下会返回
    #[serde(skip_serializing_if = "Option::is_none")]
    pub unionid: Option<String>,
    
    /// 错误码（仅在出错时返回）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub errcode: Option<i32>,
    
    /// 错误信息（仅在出错时返回）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub errmsg: Option<String>,
}

/// 微信小程序访问令牌响应
/// 
/// 对应微信官方接口：GET https://api.weixin.qq.com/cgi-bin/token
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetAccessTokenResponse {
    /// 获取到的凭证
    #[serde(skip_serializing_if = "Option::is_none")]
    pub access_token: Option<String>,
    
    /// 凭证有效时间，单位：秒。目前是7200秒之内的值
    #[serde(skip_serializing_if = "Option::is_none")]
    pub expires_in: Option<i64>,
    
    /// 错误码（仅在出错时返回）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub errcode: Option<i32>,
    
    /// 错误信息（仅在出错时返回）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub errmsg: Option<String>,
}

/// 手机号信息
/// 
/// 微信用户绑定的手机号信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PhoneInfo {
    /// 用户绑定的手机号（国外手机号会有区号）
    #[serde(rename = "phoneNumber")]
    pub phone_number: String,
    
    /// 没有区号的手机号
    #[serde(rename = "purePhoneNumber")]
    pub pure_phone_number: String,
    
    /// 区号
    #[serde(rename = "countryCode")]
    pub country_code: String,
    
    /// 数据水印
    #[serde(skip_serializing_if = "Option::is_none")]
    pub watermark: Option<Watermark>,
}

/// 数据水印
/// 
/// 用于验证数据的真实性
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Watermark {
    /// 小程序appid
    pub appid: String,
    
    /// 时间戳
    pub timestamp: i64,
}

/// 微信小程序获取手机号响应
/// 
/// 对应微信官方接口：POST https://api.weixin.qq.com/wxa/business/getuserphonenumber
/// 文档：https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/user-info/phone-number/getPhoneNumber.html
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetPhoneNumberResponse {
    /// 错误码，0表示成功
    #[serde(skip_serializing_if = "Option::is_none")]
    pub errcode: Option<i32>,
    
    /// 错误信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub errmsg: Option<String>,
    
    /// 用户手机号信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub phone_info: Option<PhoneInfo>,
}

/// 微信API通用错误响应
/// 
/// 微信API在出错时统一返回此格式
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WechatErrorResponse {
    /// 错误码
    pub errcode: i32,
    
    /// 错误信息
    pub errmsg: String,
}
/// 微信API端点管理
/// 
/// 统一管理微信各种服务的API地址，负责所有URL构建逻辑

/// 微信API基础域名
pub const WECHAT_API_BASE: &str = "https://api.weixin.qq.com";

/// 微信小程序相关API端点路径
pub struct MiniProgramEndpoints;

impl MiniProgramEndpoints {
    /// 小程序登录接口路径
    /// 通过临时登录凭证code获取用户openid和session_key
    pub const LOGIN: &'static str = "/sns/jscode2session";
    
    /// 获取小程序全局唯一后台接口调用凭据（access_token）路径
    pub const ACCESS_TOKEN: &'static str = "/cgi-bin/token";
    
    /// 获取用户手机号路径
    /// 通过code获取用户绑定的手机号
    pub const GET_PHONE_NUMBER: &'static str = "/wxa/business/getuserphonenumber";


    /// 检验数据的真实性，并且获取解密后的数据路径
    pub const DECRYPT_DATA: &'static str = "/wxa/business/checkencryptedmsg";
}

/// 微信支付相关API端点路径
pub struct PaymentEndpoints;

impl PaymentEndpoints {
    /// 统一下单路径
    pub const UNIFIED_ORDER: &'static str = "/pay/unifiedorder";
    
    /// 查询订单路径
    pub const ORDER_QUERY: &'static str = "/pay/orderquery";
    
    /// 关闭订单路径
    pub const CLOSE_ORDER: &'static str = "/pay/closeorder";
    
    /// 申请退款路径
    pub const REFUND: &'static str = "/secapi/pay/refund";
}

/// 微信消息推送相关API端点路径
pub struct MessageEndpoints;

impl MessageEndpoints {
    /// 发送模板消息路径
    pub const SEND_TEMPLATE: &'static str = "/cgi-bin/message/wxopen/template/send";
    
    /// 发送统一服务消息路径
    pub const SEND_UNIFORM: &'static str = "/cgi-bin/message/wxopen/template/uniform_send";
}

/// API端点构建器
/// 
/// 负责构建完整的微信API URL，支持自定义基础URL（用于测试环境等）
#[derive(Debug, Clone)]
pub struct EndpointBuilder {
    base_url: String,
}

impl EndpointBuilder {
    /// 创建新的端点构建器（使用默认的微信API基础URL）
    pub fn new() -> Self {
        Self {
            base_url: WECHAT_API_BASE.to_string(),
        }
    }

    /// 创建带有自定义基础URL的端点构建器
    pub fn with_base_url<S: Into<String>>(base_url: S) -> Self {
        Self {
            base_url: base_url.into(),
        }
    }

    /// 获取当前基础URL
    pub fn base_url(&self) -> &str {
        &self.base_url
    }

    // === 小程序相关API URL构建 ===
    
    /// 构建小程序登录URL
    /// GET https://api.weixin.qq.com/sns/jscode2session
    pub fn miniprogram_login(&self) -> String {
        format!("{}{}", self.base_url, MiniProgramEndpoints::LOGIN)
    }
    
    /// 构建访问令牌URL
    /// GET https://api.weixin.qq.com/cgi-bin/token
    pub fn access_token(&self) -> String {
        format!("{}{}", self.base_url, MiniProgramEndpoints::ACCESS_TOKEN)
    }
    
    /// 构建获取用户手机号URL
    /// POST https://api.weixin.qq.com/wxa/business/getuserphonenumber
    pub fn get_phone_number(&self) -> String {
        format!("{}{}", self.base_url, MiniProgramEndpoints::GET_PHONE_NUMBER)
    }
    
    
    /// 构建数据解密URL
    /// POST https://api.weixin.qq.com/wxa/business/checkencryptedmsg
    pub fn decrypt_data(&self) -> String {
        format!("{}{}", self.base_url, MiniProgramEndpoints::DECRYPT_DATA)
    }

    // === 支付相关API URL构建 ===
    
    /// 构建支付统一下单URL
    pub fn payment_unified_order(&self) -> String {
        format!("{}{}", self.base_url, PaymentEndpoints::UNIFIED_ORDER)
    }
    
    /// 构建支付查询订单URL
    pub fn payment_order_query(&self) -> String {
        format!("{}{}", self.base_url, PaymentEndpoints::ORDER_QUERY)
    }
    
    /// 构建支付关闭订单URL
    pub fn payment_close_order(&self) -> String {
        format!("{}{}", self.base_url, PaymentEndpoints::CLOSE_ORDER)
    }
    
    /// 构建支付退款URL
    pub fn payment_refund(&self) -> String {
        format!("{}{}", self.base_url, PaymentEndpoints::REFUND)
    }

    // === 消息推送相关API URL构建 ===
    
    /// 构建发送模板消息URL
    pub fn send_template_message(&self) -> String {
        format!("{}{}", self.base_url, MessageEndpoints::SEND_TEMPLATE)
    }
    
    /// 构建发送统一服务消息URL
    pub fn send_uniform_message(&self) -> String {
        format!("{}{}", self.base_url, MessageEndpoints::SEND_UNIFORM)
    }

    // === 通用URL构建 ===
    
    /// 构建自定义端点URL
    /// 
    /// # 参数
    /// * `endpoint` - API端点路径（可以带或不带前导斜杠）
    /// 
    pub fn custom<S: AsRef<str>>(&self, endpoint: S) -> String {
        let endpoint = endpoint.as_ref();
        let endpoint = if endpoint.starts_with('/') {
            endpoint
        } else {
            &format!("/{}", endpoint)
        };
        format!("{}{}", self.base_url, endpoint)
    }
}

impl Default for EndpointBuilder {
    fn default() -> Self {
        Self::new()
    }
}
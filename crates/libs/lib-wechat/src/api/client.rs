use crate::core::config::WechatConfig;
use crate::core::error::{WechatError, WechatResult};
use crate::api::endpoints::EndpointBuilder;
use crate::api::request::{Code2SessionRequest, GetAccessTokenRequest, GetPhoneNumberRequest};
use crate::api::response::{Code2SessionResponse, GetAccessTokenResponse, GetPhoneNumberResponse, WechatErrorResponse};
use reqwest::Client;
use std::time::Duration;
use tracing::{debug, error, warn};
use serde::de::DeserializeOwned;

/// 微信API客户端
/// 
/// 专注于封装微信API的HTTP调用，不处理业务逻辑
#[derive(Debug, Clone)]
pub struct WechatClient {
    /// HTTP客户端
    http_client: Client,
    /// 微信配置
    config: WechatConfig,
    /// API端点构建器
    endpoint_builder: EndpointBuilder,
}

impl WechatClient {
    /// 创建新的微信客户端
    pub fn new(config: WechatConfig) -> WechatResult<Self> {
        // 验证配置
        config.validate().map_err(WechatError::ConfigError)?;

        // 创建HTTP客户端
        let http_client = Client::builder()
            .timeout(Duration::from_millis(config.request_timeout_ms))
            .build()
            .map_err(WechatError::RequestError)?;

        // 创建端点构建器（使用默认的微信API基础URL）
        let endpoint_builder = EndpointBuilder::new();

        Ok(Self {
            http_client,
            config,
            endpoint_builder,
        })
    }
    /// 通过授权码获取用户会话信息
    /// 
    /// 对应微信API: GET https://api.weixin.qq.com/sns/jscode2session
    /// 
    /// # 参数
    /// * `js_code` - 微信小程序登录时获取的code
    /// 
    /// # 返回
    /// 返回微信用户的openid、session_key等信息
    pub async fn code2session(&self, js_code: String) -> WechatResult<Code2SessionResponse> {
        let request = Code2SessionRequest::new(
            self.config.app_id.clone(),
            self.config.app_secret.clone(),
            js_code,
        );

        let url = self.endpoint_builder.miniprogram_login();
        
        debug!("调用微信code2session接口: {}", url);
        
        // 微信登录直接调用，不使用重试机制
        self.send_get_request(&url, &request).await
    }

    /// 获取小程序全局唯一后台接口调用凭据
    /// 
    /// 对应微信API: GET https://api.weixin.qq.com/cgi-bin/token
    /// 
    /// # 返回
    /// 返回access_token和有效期信息
    pub async fn get_access_token(&self) -> WechatResult<GetAccessTokenResponse> {
        let request = GetAccessTokenRequest::new(
            self.config.app_id.clone(),
            self.config.app_secret.clone(),
        );

        let url = self.endpoint_builder.access_token();
        
        debug!("调用微信access_token接口: {}", url);
        
        self.execute_api_request(&url, &request).await
    }

    /// 获取用户手机号
    /// 
    /// 对应微信API: POST https://api.weixin.qq.com/wxa/business/getuserphonenumber
    /// 
    /// # 参数
    /// * `code` - 手机号获取凭证，通过前端 wx.getPhoneNumber 获取
    /// * `access_token` - 接口调用凭据，通过 get_access_token 获取
    /// 
    /// # 返回
    /// 返回用户的手机号信息
    pub async fn get_phone_number(&self, code: String, access_token: String) -> WechatResult<GetPhoneNumberResponse> {
        let request = GetPhoneNumberRequest::new(code);
        let url = self.endpoint_builder.get_phone_number();
        
        debug!("调用微信获取手机号接口: {}", url);
        
        self.execute_post_api_request(&url, &request, &access_token).await
    }

    /// 通用的微信API请求执行方法（GET请求）
    /// 
    /// 支持自动重试机制，根据配置进行错误重试（主要用于access_token等接口）
    async fn execute_api_request<T, R>(&self, url: &str, request: &T) -> WechatResult<R>
    where
        T: serde::Serialize + std::fmt::Debug,
        R: DeserializeOwned + std::fmt::Debug,
    {
        let mut last_error = None;
        
        // 重试机制
        for attempt in 1..=self.config.retry_count {
            match self.send_get_request(url, request).await {
                Ok(response) => {
                    debug!("微信API调用成功，尝试次数: {}", attempt);
                    return Ok(response);
                }
                Err(e) => {
                    warn!("微信API调用失败，尝试次数: {}, 错误: {}", attempt, e);
                    last_error = Some(e);
                    
                    // 如果不是最后一次尝试，等待一段时间后重试
                    if attempt < self.config.retry_count {
                        let delay = Duration::from_millis(1000 * attempt as u64);
                        debug!("等待 {:?} 后进行重试", delay);
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }

        // 所有重试都失败了
        Err(last_error.unwrap_or_else(|| WechatError::OtherError("未知的请求错误".to_string())))
    }

    /// 通用的微信API POST请求执行方法
    /// 
    /// 支持自动重试机制，用于需要access_token的POST接口
    async fn execute_post_api_request<T, R>(&self, url: &str, request: &T, access_token: &str) -> WechatResult<R>
    where
        T: serde::Serialize + std::fmt::Debug,
        R: DeserializeOwned + std::fmt::Debug,
    {
        let mut last_error = None;
        
        // 重试机制
        for attempt in 1..=self.config.retry_count {
            match self.send_post_request(url, request, access_token).await {
                Ok(response) => {
                    debug!("微信API POST调用成功，尝试次数: {}", attempt);
                    return Ok(response);
                }
                Err(e) => {
                    warn!("微信API POST调用失败，尝试次数: {}, 错误: {}", attempt, e);
                    last_error = Some(e);
                    
                    // 如果不是最后一次尝试，等待一段时间后重试
                    if attempt < self.config.retry_count {
                        let delay = Duration::from_millis(1000 * attempt as u64);
                        debug!("等待 {:?} 后进行重试", delay);
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }

        // 所有重试都失败了
        Err(last_error.unwrap_or_else(|| WechatError::OtherError("未知的请求错误".to_string())))
    }

    /// 发送HTTP GET请求到微信API
    /// 
    /// 处理HTTP请求的发送、状态码检查、响应解析等底层逻辑
    async fn send_get_request<T, R>(&self, url: &str, request: &T) -> WechatResult<R>
    where
        T: serde::Serialize + std::fmt::Debug,
        R: DeserializeOwned + std::fmt::Debug,
    {
        // 发送GET请求（微信API通常使用GET + query参数）
        let response = self
            .http_client
            .get(url)
            .query(request)
            .send()
            .await
            .map_err(WechatError::RequestError)?;

        self.handle_response(response).await
    }

    /// 发送HTTP POST请求到微信API
    /// 
    /// 处理需要access_token的POST接口
    async fn send_post_request<T, R>(&self, url: &str, request: &T, access_token: &str) -> WechatResult<R>
    where
        T: serde::Serialize + std::fmt::Debug,
        R: DeserializeOwned + std::fmt::Debug,
    {
        // 发送POST请求，access_token作为query参数，请求体为JSON
        let response = self
            .http_client
            .post(url)
            .query(&[("access_token", access_token)])
            .header("Content-Type", "application/json")
            .json(request)
            .send()
            .await
            .map_err(WechatError::RequestError)?;

        self.handle_response(response).await
    }

    /// 处理HTTP响应
    /// 
    /// 统一处理状态码检查和响应解析，支持微信错误响应处理
    async fn handle_response<R>(&self, response: reqwest::Response) -> WechatResult<R>
    where
        R: DeserializeOwned + std::fmt::Debug,
    {
        // 检查HTTP状态码
        if !response.status().is_success() {
            error!("微信API返回错误状态码: {}", response.status());
            return Err(WechatError::HttpError(response.status().as_u16()));
        }

        // 解析响应
        let response_text = response
            .text()
            .await
            .map_err(WechatError::RequestError)?;

        debug!("微信API响应: {}", response_text);

        // 先尝试解析为错误响应
        if let Ok(error_response) = serde_json::from_str::<WechatErrorResponse>(&response_text) {
            // 如果是微信错误响应，转换为WechatError
            return Err(WechatError::ApiError {
                code: error_response.errcode,
                message: error_response.errmsg,
            });
        }

        // 如果不是错误响应，尝试解析为目标类型
        let parsed_response: R = serde_json::from_str(&response_text)
            .map_err(|e| WechatError::ParseError(format!("解析响应失败: {}, 原始响应: {}", e, response_text)))?;

        debug!("微信API响应解析成功: {:?}", parsed_response);

        Ok(parsed_response)
    }

    /// 获取配置信息（只读）
    pub fn config(&self) -> &WechatConfig {
        &self.config
    }

    /// 获取端点构建器（只读）
    pub fn endpoint_builder(&self) -> &EndpointBuilder {
        &self.endpoint_builder
    }
}
//! 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
//! Generated by lib-codegen

use bon::Builder;
use chrono::{DateTime, FixedOffset};
use derive_more::{Constructor, From, Into};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use uuid::Uuid;

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema, From, Into, Constructor, Builder)]
pub struct MerchantCommissionRulesListResponse {
    /// 主键，自增UUID
    #[schema(example = 550e8400-e29b-41d4-a716-************)]
    pub id: Uuid,
    /// 创建时间
    #[schema(example = 2023-01-01T00:00:00+08:00)]
    pub created_date: DateTimeWithTimeZone,
}

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema, From, Into, Constructor, Builder)]
pub struct MerchantCommissionRulesDetailResponse {
    /// 主键，自增UUID
    #[schema(example = 550e8400-e29b-41d4-a716-************)]
    pub id: Uuid,
    /// 商户ID（关联business.merchants.id）
    pub merchant_id: i64,
    /// 规则名称，方便识别和管理
    pub rule_name: String,
    /// 基础佣金比例，精度为5位数字4位小数（如0.0500表示5%）
    pub base_commission_rate: Decimal,
    /// 阶梯佣金规则JSON配置，存储多组阶梯规则
    pub tier_rules: Option<serde_json::Value>,
    /// 创建时间
    #[schema(example = 2023-01-01T00:00:00+08:00)]
    pub created_date: DateTimeWithTimeZone,
    /// 更新时间
    #[schema(example = 2023-01-02T00:00:00+08:00)]
    pub updated_date: DateTimeWithTimeZone,
    /// 创建人ID
    pub created_by: Option<Uuid>,
    /// 更新人ID
    pub updated_by: Option<Uuid>,
    /// 备注信息
    pub remark: Option<String>,
}

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则选择项响应
#[derive(Debug, Serialize, Deserialize, ToSchema, From, Into, Constructor, Builder)]
pub struct MerchantCommissionRulesSelectItem {
    /// 主键，自增UUID
    #[schema(example = 550e8400-e29b-41d4-a716-************)]
    pub id: Uuid,
    /// 创建时间
    #[schema(example = 2023-01-01T00:00:00+08:00)]
    pub created_date: DateTimeWithTimeZone,
}

// ============================================================================
// 实体 <-> VO 转换
// ============================================================================

use crate::domain::business::entities::merchant_commission_rules::Model;

// Model -> VO
impl From<Model> for MerchantCommissionRulesListResponse {
    fn from(model: Model) -> Self {
        Self {
            id: model.id,
            created_date: model.created_date,
        }
    }
}

impl From<Model> for MerchantCommissionRulesDetailResponse {
    fn from(model: Model) -> Self {
        Self {
            id: model.id,
            merchant_id: model.merchant_id,
            rule_name: model.rule_name,
            base_commission_rate: model.base_commission_rate,
            tier_rules: model.tier_rules,
            created_date: model.created_date,
            updated_date: model.updated_date,
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
        }
    }
}

impl From<Model> for MerchantCommissionRulesSelectItem {
    fn from(model: Model) -> Self {
        Self {
            id: model.id,
            created_date: model.created_date,
        }
    }
}

// VO -> Model
impl From<MerchantCommissionRulesListResponse> for Model {
    fn from(vo: MerchantCommissionRulesListResponse) -> Self {
        Self {
            id: vo.id,
            created_date: vo.created_date,
            ..Default::default()
        }
    }
}

impl From<MerchantCommissionRulesDetailResponse> for Model {
    fn from(vo: MerchantCommissionRulesDetailResponse) -> Self {
        Self {
            id: vo.id,
            merchant_id: vo.merchant_id,
            rule_name: vo.rule_name,
            base_commission_rate: vo.base_commission_rate,
            tier_rules: vo.tier_rules,
            created_date: vo.created_date,
            updated_date: vo.updated_date,
            created_by: vo.created_by,
            updated_by: vo.updated_by,
            remark: vo.remark,
            ..Default::default()
        }
    }
}

impl From<MerchantCommissionRulesSelectItem> for Model {
    fn from(vo: MerchantCommissionRulesSelectItem) -> Self {
        Self {
            id: vo.id,
            created_date: vo.created_date,
            ..Default::default()
        }
    }
}

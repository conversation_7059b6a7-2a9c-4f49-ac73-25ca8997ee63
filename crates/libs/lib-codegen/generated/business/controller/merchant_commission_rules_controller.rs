//! 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
//! Generated by lib-codegen

use crate::domain::business::dto::merchant_commission_rules_request::{
    MerchantCommissionRulesPageRequest, MerchantCommissionRulesUpdateRequest,
};
use crate::domain::business::vo::merchant_commission_rules_vo::{
    MerchantCommissionRulesDetailResponse, MerchantCommissionRulesListResponse,
};
use crate::service::business::MerchantCommissionRulesService;
use crate::utils::custom_validator::{MyPath, MyQuery, ValidJson};
use axum::{
    Router, middleware,
    routing::{delete, get, put},
};
use lib_auth::require_permission;
use lib_core::response::{ApiResult, PageData};
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use tracing::info;
use utoipa::OpenApi;
use uuid::Uuid;

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则管理 相关接口
pub struct MerchantCommissionRulesController;

impl MerchantCommissionRulesController {
    /// 创建商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则管理公开路由
    pub fn public_routes() -> Router {
        Router::new()
        // 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则管理暂无公开路由
    }

    /// 创建商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则管理受保护路由
    pub fn protected_routes() -> Router {
        Router::new()
            // 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则详情查询
            .route(
                "/business/merchant_commission_rules/{id}",
                get(get_merchantcommissionrules_by_id).layer(middleware::from_fn(
                    require_permission("business:merchant_commission_rules:detail"),
                )),
            )
            // 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则更新
            .route(
                "/business/merchant_commission_rules/{id}",
                put(update_merchantcommissionrules).layer(middleware::from_fn(require_permission(
                    "business:merchant_commission_rules:update",
                ))),
            )
            // 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则删除
            .route(
                "/business/merchant_commission_rules/{id}",
                delete(delete_merchantcommissionrules).layer(middleware::from_fn(
                    require_permission("business:merchant_commission_rules:delete"),
                )),
            )
            // 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则列表查询
            .route(
                "/business/merchant_commission_rules",
                get(page_merchantcommissionrules).layer(middleware::from_fn(require_permission(
                    "business:merchant_commission_rules:list",
                ))),
            )
    }
}

/// 分页查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则列表
#[utoipa::path(
    get,
    path = "/business/merchant_commission_rules",
    summary = "分页查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则列表",
    description = "根据查询条件分页获取商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则信息",
    tags = ["商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则管理"],
    params(MerchantCommissionRulesPageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<MerchantCommissionRulesListResponse>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "items": [],
                 "total": 0,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 0
             }
         })
        )
    )
)]
pub async fn page_merchantcommissionrules(
    Component(merchant_commission_rules_service): Component<MerchantCommissionRulesService>,
    MyQuery(req): MyQuery<MerchantCommissionRulesPageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<MerchantCommissionRulesListResponse>> {
    info!(
        "分页查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则列表请求: {:?}，操作人: {}",
        req, current_user.account
    );
    match merchant_commission_rules_service
        .page_merchantcommissionrules(req)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 根据ID查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则详情
#[utoipa::path(
    get,
    path = "/business/merchant_commission_rules/{id}",
    summary = "查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则详情",
    description = "根据ID获取商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则详细信息",
    tags = ["商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则管理"],
    params(
        ("id" = Uuid, Path, description = "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<MerchantCommissionRulesDetailResponse>),
    )
)]
pub async fn get_merchantcommissionrules_by_id(
    Component(merchant_commission_rules_service): Component<MerchantCommissionRulesService>,
    MyPath(id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<MerchantCommissionRulesDetailResponse> {
    info!(
        "查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则详情请求，ID: {}，操作人: {}",
        id, current_user.account
    );

    match merchant_commission_rules_service
        .get_merchantcommissionrules_by_id(id)
        .await
    {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
#[utoipa::path(
    put,
    path = "/business/merchant_commission_rules/{id}",
    summary = "更新商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则",
    description = "更新商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则信息",
    tags = ["商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则管理"],
    params(
        ("id" = Uuid, Path, description = "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则ID")
    ),
    request_body = MerchantCommissionRulesUpdateRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则更新成功",
             "data": null
         })
        )
    )
)]
pub async fn update_merchantcommissionrules(
    Component(merchant_commission_rules_service): Component<MerchantCommissionRulesService>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(id): MyPath<Uuid>,
    ValidJson(req): ValidJson<MerchantCommissionRulesUpdateRequest>,
) -> ApiResult<String> {
    info!(
        "更新商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则请求，ID: {}，数据: {:?}，操作人: {}",
        id, req, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_commission_rules_service
        .update_merchantcommissionrules(id, req, userid)
        .await
    {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 删除商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
#[utoipa::path(
    delete,
    path = "/business/merchant_commission_rules/{id}",
    summary = "删除商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则",
    description = "删除指定的商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则",
    tags = ["商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则管理"],
    params(
        ("id" = Uuid, Path, description = "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则ID")
    ),
        responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则删除成功",
             "data": null
         })
        )
    )
    )]
pub async fn delete_merchantcommissionrules(
    Component(merchant_commission_rules_service): Component<MerchantCommissionRulesService>,
    MyPath(id): MyPath<Uuid>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!(
        "删除商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则请求，ID: {}，操作人: {}",
        id, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match merchant_commission_rules_service
        .delete_merchantcommissionrules(id, userid)
        .await
    {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则管理 API 文档
#[derive(OpenApi)]
#[openapi(
    paths(
        page_merchantcommissionrules,
        get_merchantcommissionrules_by_id,
        update_merchantcommissionrules,
        delete_merchantcommissionrules,
    ),
    components(schemas(
        MerchantCommissionRulesPageRequest,
        MerchantCommissionRulesUpdateRequest,
        MerchantCommissionRulesListResponse,
        MerchantCommissionRulesDetailResponse,
    ))
)]
pub struct MerchantCommissionRulesControllerApiDoc;

impl MerchantCommissionRulesControllerApiDoc {
    pub fn get_openapi_json() -> String {
        serde_json::to_string_pretty(&Self::openapi()).unwrap()
    }

    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&Self::openapi()).unwrap()
    }
}

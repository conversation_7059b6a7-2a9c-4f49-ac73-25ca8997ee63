//! 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
//! Generated by lib-codegen

use crate::domain::business::dto::merchant_commission_rules_request::MerchantCommissionRulesPageRequest;
use crate::domain::business::entities::merchant_commission_rules::{
    ActiveModel, Column, Entity as MerchantCommissionRules, Model as MerchantCommissionRulesModel,
};
use crate::utils::datetime::DateTimeUtils;
use anyhow::{Context, Result};
use lib_core::app::plugin::Service as ServiceTrait;
use lib_core::response::PageData;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, EntityTrait, PaginatorTrait,
    QueryFilter, QueryOrder,
};
use uuid::Uuid;

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则Repository实现
#[derive(Clone, Service)]
pub struct MerchantCommissionRulesRepository;

impl MerchantCommissionRulesRepository {
    /// 构建查询条件
    fn build_query_conditions(req: &MerchantCommissionRulesPageRequest) -> Condition {
        let mut condition = Condition::all();

        // ID过滤条件
        if let Some(id) = req.id {
            condition = condition.add(Column::ID.eq(id));
        }
        condition
    }

    /// 分页查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
    pub async fn page_by_condition<C>(
        &self,
        req: &MerchantCommissionRulesPageRequest,
        conn: &C,
    ) -> Result<PageData<MerchantCommissionRulesModel>, String>
    where
        C: ConnectionTrait,
    {
        let condition = Self::build_query_conditions(req);
        let pagination = Pagination::from_one_based(req.page, req.page_size);

        MerchantCommissionRules::find()
            .filter(condition)
            .order_by_desc(Column::CreatedDate)
            .page(conn, &pagination)
            .await
            .map_err(|e| e.to_string())
    }

    /// 根据ID查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
    pub async fn find_by_id<C>(
        &self,
        id: Uuid,
        conn: &C,
    ) -> Result<Option<MerchantCommissionRulesModel>>
    where
        C: ConnectionTrait,
    {
        MerchantCommissionRules::find_by_id(id)
            .one(conn)
            .await
            .context(
                "查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则失败",
            )
    }

    /// 根据IDs查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则列表
    pub async fn find_by_ids<C>(
        &self,
        ids: Vec<Uuid>,
        conn: &C,
    ) -> Result<Vec<MerchantCommissionRulesModel>>
    where
        C: ConnectionTrait,
    {
        if ids.is_empty() {
            return Ok(Vec::new());
        }

        MerchantCommissionRules::find()
            .filter(Column::Id.is_in(ids))
            .all(conn)
            .await
            .context("批量查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则失败")
    }

    /// 创建商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
    pub async fn create<C>(
        &self,
        model: ActiveModel,
        conn: &C,
    ) -> Result<MerchantCommissionRulesModel>
    where
        C: ConnectionTrait,
    {
        model.insert(conn).await.context(
            "创建商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则失败",
        )
    }

    /// 更新商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
    pub async fn update<C>(
        &self,
        model: ActiveModel,
        conn: &C,
    ) -> Result<MerchantCommissionRulesModel>
    where
        C: ConnectionTrait,
    {
        model.update(conn).await.context(
            "更新商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则失败",
        )
    }

    /// 根据ID删除商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
    pub async fn delete_by_id<C>(&self, id: Uuid, conn: &C) -> Result<()>
    where
        C: ConnectionTrait,
    {
        MerchantCommissionRules::delete_by_id(id)
            .exec(conn)
            .await
            .context(
                "删除商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则失败",
            )?;

        Ok(())
    }

    /// 批量删除商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
    pub async fn delete_by_ids<C>(&self, ids: Vec<Uuid>, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if ids.is_empty() {
            return Ok(0);
        }

        let result = MerchantCommissionRules::delete_many()
            .filter(Column::Id.is_in(ids))
            .exec(conn)
            .await
            .context("批量删除商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则失败")?;

        Ok(result.rows_affected)
    }

    /// 检查商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则是否可以删除
    pub async fn is_deletable<C>(&self, id: Uuid, conn: &C) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        Ok(true)
    }

    /// 统计商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则总数
    pub async fn count_all<C>(&self, conn: &C) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        MerchantCommissionRules::find().count(conn).await.context(
            "统计商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则总数失败",
        )
    }
}

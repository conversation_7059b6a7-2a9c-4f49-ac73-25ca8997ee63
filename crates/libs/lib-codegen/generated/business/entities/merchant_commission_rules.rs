//! 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则实体
//! Generated by lib-codegen
//! 对应表：business.merchant_commission_rules

use crate::utils::DateTimeUtils;
use async_trait::async_trait;
use derive_more::{Constructor, From, Into};
use rust_decimal::Decimal;
use sea_orm::entity::prelude::*;
use sea_orm::{ConnectionTrait, DbErr, Set};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则实体
///
/// 存储商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则的相关信息和业务数据
#[derive(
    Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Into, From, Constructor,
)]
#[sea_orm(schema_name = "business", table_name = "merchant_commission_rules")]
pub struct Model {
    /// 主键，自增UUID
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    /// 商户ID（关联business.merchants.id）
    pub merchant_id: i64,
    /// 规则名称，方便识别和管理
    pub rule_name: String,
    /// 基础佣金比例，精度为5位数字4位小数（如0.0500表示5%）
    pub base_commission_rate: Decimal,
    /// 阶梯佣金规则JSON配置，存储多组阶梯规则
    pub tier_rules: Option<serde_json::Value>,
    /// 创建时间
    pub created_date: DateTimeWithTimeZone,
    /// 更新时间
    pub updated_date: DateTimeWithTimeZone,
    /// 创建人ID
    pub created_by: Option<Uuid>,
    /// 更新人ID
    pub updated_by: Option<Uuid>,
    /// 备注信息
    pub remark: Option<String>,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    // 根据需要定义关系
}

#[async_trait]
impl ActiveModelBehavior for ActiveModel {
    /// 插入或更新前自动设置时间戳
    async fn before_save<C>(mut self, _db: &C, insert: bool) -> Result<Self, DbErr>
    where
        C: ConnectionTrait,
    {
        let now = DateTimeUtils::now_china_offset();
        if insert {
            self.created_date = Set(now);
        }
        self.updated_date = Set(now);
        Ok(self)
    }
}

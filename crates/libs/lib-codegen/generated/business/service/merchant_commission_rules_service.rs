//! 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
//! Generated by lib-codegen

use crate::constants::*;
use crate::domain::business::dto::merchant_commission_rules_request::{
    MerchantCommissionRulesPageRequest, MerchantCommissionRulesUpdateRequest,
};
use crate::domain::business::entities::merchant_commission_rules::{ActiveModel, Model};
use crate::domain::business::vo::merchant_commission_rules_vo::{
    MerchantCommissionRulesDetailResponse, MerchantCommissionRulesListResponse,
};
use crate::repository::MerchantCommissionRulesRepository;
use lib_core::BusinessError;
use lib_core::app::plugin::Service as ServiceTrait;
use lib_core::response::PageData;
use lib_macros::Service;
use sea_orm::{DatabaseConnection, Set};
use tracing::{info, warn};
use uuid::Uuid;

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则服务实现
#[derive(Clone, Service)]
pub struct MerchantCommissionRulesService {
    #[inject(component)]
    merchant_commission_rules_repository: MerchantCommissionRulesRepository,
    #[inject(component)]
    db: DatabaseConnection,
}

impl MerchantCommissionRulesService {
    /// 分页查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则列表
    pub async fn page_merchant_commission_rules(
        &self,
        req: MerchantCommissionRulesPageRequest,
    ) -> Result<PageData<MerchantCommissionRulesListResponse>, BusinessError> {
        info!(
            "分页查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则，参数: {:?}",
            req
        );

        // 使用Repository进行分页查询
        let page_data = self
            .merchant_commission_rules_repository
            .page_by_condition(&req, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("分页查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则失败: {}", e)))?;

        // 如果没有数据，直接返回空的响应结果
        if page_data.items.is_empty() {
            return Ok(PageData::new(
                Vec::new(),
                page_data.total,
                page_data.page,
                page_data.page_size,
            ));
        }

        // 转换为响应格式
        let response_items: Vec<MerchantCommissionRulesListResponse> = page_data
            .items
            .into_iter()
            .map(|model| MerchantCommissionRulesListResponse::from(model))
            .collect();

        // 构建最终的分页结果
        let final_page_data = PageData::new(
            response_items,
            page_data.total,
            page_data.page,
            page_data.page_size,
        );

        Ok(final_page_data)
    }

    /// 根据ID查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则详情
    pub async fn get_merchant_commission_rules_by_id(
        &self,
        merchant_commission_rules_id: Uuid,
    ) -> Result<MerchantCommissionRulesDetailResponse, BusinessError> {
        info!(
            "查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则详情，ID: {}",
            merchant_commission_rules_id
        );

        let merchant_commission_rules = self
            .merchant_commission_rules_repository
            .find_by_id(merchant_commission_rules_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则失败: {}", e)))?
            .ok_or_else(|| {
                warn!("商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则不存在: {}", merchant_commission_rules_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则不存在".to_string())
            })?;

        let detail = MerchantCommissionRulesDetailResponse::from(merchant_commission_rules);

        Ok(detail)
    }

    /// 更新商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
    pub async fn update_merchant_commission_rules(
        &self,
        merchant_commission_rules_id: Uuid,
        req: MerchantCommissionRulesUpdateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "更新商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则，ID: {}，请求: {:?}，操作人: {}",
            merchant_commission_rules_id, req, operator_id
        );

        let merchant_commission_rules = self
            .merchant_commission_rules_repository
            .find_by_id(merchant_commission_rules_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则失败: {}", e)))?
            .ok_or_else(|| {
                warn!("商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则不存在: {}", merchant_commission_rules_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则不存在".to_string())
            })?;

        let updated_merchant_commission_rules = req
            .update_active_model(merchant_commission_rules.into(), operator_id)
            .map_err(|_| BusinessError::new(INVALID_PARAMETER, "构建更新数据失败".to_string()))?;

        self.merchant_commission_rules_repository
            .update(updated_merchant_commission_rules, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        info!(
            "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则更新成功，ID: {}",
            merchant_commission_rules_id
        );
        Ok(
            "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则更新成功"
                .to_string(),
        )
    }

    /// 删除商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
    pub async fn delete_merchant_commission_rules(
        &self,
        merchant_commission_rules_id: Uuid,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "删除商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则，ID: {}，操作人: {}",
            merchant_commission_rules_id, operator_id
        );

        let merchant_commission_rules = self
            .merchant_commission_rules_repository
            .find_by_id(merchant_commission_rules_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则失败: {}", e)))?
            .ok_or_else(|| {
                warn!("商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则不存在: {}", merchant_commission_rules_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则不存在".to_string())
            })?;

        // 检查是否可以删除（可根据业务需求自定义）
        let deletable = self
            .merchant_commission_rules_repository
            .is_deletable(merchant_commission_rules_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        if !deletable {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "该商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则不能删除".to_string(),
            ));
        }

        // 删除商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
        self.merchant_commission_rules_repository
            .delete_by_id(merchant_commission_rules_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则失败: {}", e)))?;

        info!(
            "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则删除成功，ID: {}",
            merchant_commission_rules_id
        );
        Ok(
            "商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则删除成功"
                .to_string(),
        )
    }
}

//! 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则
//! Generated by lib-codegen

use bon::Builder;
use chrono::{DateTime, FixedOffset};
use derive_more::{Constructor, From, Into};
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则分页查询请求
#[derive(
    Debug, Deserialize, Serialize, Validate, ToSchema, IntoParams, Builder, From, Into, Constructor,
)]
#[into_params(parameter_in = Query)]
pub struct MerchantCommissionRulesPageRequest {
    /// 主键，自增UUID
    #[schema(example = 550e8400-e29b-41d4-a716-************)]
    pub id: Option<Uuid>,

    /// 当前页码
    #[validate(range(min = 1, message = "当前页码必须大于0"))]
    #[schema(example = 1)]
    pub page: Option<u64>,

    /// 每页大小
    #[validate(range(min = 1, max = 100, message = "每页大小必须在1-100之间"))]
    #[schema(example = 10)]
    pub page_size: Option<u64>,

    /// 创建时间-开始
    #[schema(example = "2023-01-01 00:00:00")]
    pub created_start: Option<String>,

    /// 创建时间-结束
    #[schema(example = "2023-12-31 23:59:59")]
    pub created_end: Option<String>,
}

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则创建请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder, From, Into, Constructor)]
#[schema(example = json!([object]))]
pub struct MerchantCommissionRulesCreateRequest {
    /// 商户ID（关联business.merchants.id）
    pub merchant_id: i64,
    /// 规则名称，方便识别和管理
    pub rule_name: String,
    /// 基础佣金比例，精度为5位数字4位小数（如0.0500表示5%）
    pub base_commission_rate: Decimal,
    /// 阶梯佣金规则JSON配置，存储多组阶梯规则
    pub tier_rules: Option<serde_json::Value>,
    /// 创建人ID
    pub created_by: Option<Uuid>,
    /// 更新人ID
    pub updated_by: Option<Uuid>,
    /// 备注信息
    pub remark: Option<String>,
}

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则更新请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder, From, Into, Constructor)]
#[schema(example = json!([object]))]
pub struct MerchantCommissionRulesUpdateRequest {
    /// 商户ID（关联business.merchants.id）
    pub merchant_id: Option<i64>,
    /// 规则名称，方便识别和管理
    pub rule_name: Option<String>,
    /// 基础佣金比例，精度为5位数字4位小数（如0.0500表示5%）
    pub base_commission_rate: Option<Decimal>,
    /// 阶梯佣金规则JSON配置，存储多组阶梯规则
    pub tier_rules: Option<serde_json::Value>,
    /// 创建人ID
    pub created_by: Option<Uuid>,
    /// 更新人ID
    pub updated_by: Option<Uuid>,
    /// 备注信息
    pub remark: Option<String>,
}

/// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则批量删除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder, From, Into, Constructor)]
pub struct MerchantCommissionRulesBatchDeleteRequest {
    /// 商户门店佣金规则：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则ID列表
    #[validate(length(min = 1, message = "ID列表不能为空"))]
    #[schema(example = json!(["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]))]
    pub ids: Vec<Uuid>,
}

// ============================================================================
// 实体 <-> DTO 转换
// ============================================================================
use crate::domain::merchants::business::entities::merchant_commission_rules::Model;

impl From<Model> for MerchantCommissionRulesCreateRequest {
    fn from(model: Model) -> Self {
        Self {
            merchant_id: model.merchant_id,
            rule_name: model.rule_name,
            base_commission_rate: model.base_commission_rate,
            tier_rules: model.tier_rules,
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
        }
    }
}

impl From<Model> for MerchantCommissionRulesUpdateRequest {
    fn from(model: Model) -> Self {
        Self {
            merchant_id: model.merchant_id,
            rule_name: model.rule_name,
            base_commission_rate: model.base_commission_rate,
            tier_rules: model.tier_rules,
            created_by: model.created_by,
            updated_by: model.updated_by,
            remark: model.remark,
        }
    }
}

// 反向转换（如有需要，可选）
impl From<MerchantCommissionRulesCreateRequest> for Model {
    fn from(dto: MerchantCommissionRulesCreateRequest) -> Self {
        Self {
            merchant_id: dto.merchant_id,
            rule_name: dto.rule_name,
            base_commission_rate: dto.base_commission_rate,
            tier_rules: dto.tier_rules,
            created_by: dto.created_by,
            updated_by: dto.updated_by,
            remark: dto.remark,
            ..Default::default()
        }
    }
}

impl From<MerchantCommissionRulesUpdateRequest> for Model {
    fn from(dto: MerchantCommissionRulesUpdateRequest) -> Self {
        Self {
            merchant_id: dto.merchant_id,
            rule_name: dto.rule_name,
            base_commission_rate: dto.base_commission_rate,
            tier_rules: dto.tier_rules,
            created_by: dto.created_by,
            updated_by: dto.updated_by,
            remark: dto.remark,
            ..Default::default()
        }
    }
}

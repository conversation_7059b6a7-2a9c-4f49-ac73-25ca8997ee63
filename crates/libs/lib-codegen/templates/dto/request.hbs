{{#if file_header}}{{file_header}}{{/if}}

use bon::Builder;
use derive_more::{From, Into, Constructor};
use serde::{Deserialize, Serialize};
#[allow(unused_imports)]
use serde_json::json;
use utoipa::{IntoParams, ToSchema};
use uuid::Uuid;
use validator::Validate;
{{#if has_decimal}}
use rust_decimal::Decimal;
{{/if}}
{{#if has_datetime}}
use chrono::{DateTime, FixedOffset};
{{/if}}
{{#each additional_imports}}
use {{this}};
{{/each}}

/// {{entity_comment}}分页查询请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, IntoParams, Builder, From, Into, Constructor)]
#[into_params(parameter_in = Query)]
pub struct {{entity_name}}PageRequest {
{{#each page_fields}}
    /// {{comment}}
    {{#if validation}}
    #[validate({{validation}})]
    {{/if}}
    {{#if schema_example}}
    #[schema(example = {{schema_example}})]
    {{/if}}
    pub {{name}}: {{rust_type}},
{{/each}}

    /// 当前页码
    #[validate(range(min = 1, message = "当前页码必须大于0"))]
    #[schema(example = 1)]
    pub page: Option<u64>,

    /// 每页大小
    #[validate(range(min = 1, max = 100, message = "每页大小必须在1-100之间"))]
    #[schema(example = 10)]
    pub page_size: Option<u64>,

    /// 创建时间-开始
    #[schema(example = "2023-01-01 00:00:00")]
    pub created_start: Option<String>,

    /// 创建时间-结束
    #[schema(example = "2023-12-31 23:59:59")]
    pub created_end: Option<String>,
}

/// {{entity_comment}}创建请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder, From, Into, Constructor)]
#[schema(example = json!({{create_example}}))]
pub struct {{entity_name}}CreateRequest {
{{#each create_fields}}
    /// {{comment}}
    {{#if validation}}
    #[validate({{validation}})]
    {{/if}}
    {{#if schema_example}}
    #[schema(example = {{schema_example}})]
    {{/if}}
    {{#if builder_default}}
    #[builder(default = {{builder_default}})]
    {{/if}}
    pub {{name}}: {{rust_type}},
{{/each}}
}

/// {{entity_comment}}更新请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder, From, Into, Constructor)]
#[schema(example = json!({{update_example}}))]
pub struct {{entity_name}}UpdateRequest {
{{#each update_fields}}
    /// {{comment}}
    {{#if validation}}
    #[validate({{validation}})]
    {{/if}}
    {{#if schema_example}}
    #[schema(example = {{schema_example}})]
    {{/if}}
    {{#if builder_default}}
    #[builder(default = {{builder_default}})]
    {{/if}}
    pub {{name}}: {{rust_type}},
{{/each}}
}

/// {{entity_comment}}批量删除请求
#[derive(Debug, Deserialize, Serialize, Validate, ToSchema, Builder, From, Into, Constructor)]
pub struct {{entity_name}}BatchDeleteRequest {
    /// {{entity_comment}}ID列表
    #[validate(length(min = 1, message = "ID列表不能为空"))]
    #[schema(example = json!(["123e4567-e89b-12d3-a456-426614174001", "123e4567-e89b-12d3-a456-426614174002"]))]
    pub ids: Vec<{{id_type}}>,
}

{{#if conversion_methods}}
// ============================================================================
// 转换方法
// ============================================================================

{{#each conversion_methods}}
/// {{comment}}
{{method_signature}} {
    {{method_body}}
}
{{/each}}
{{/if}}

{{#if has_entity_conversion}}

// ============================================================================
// 实体 <-> DTO 转换
// ============================================================================
use crate::domain::merchants::{{module_type}}::entities::{{entity_snake}}::Model;

impl From<Model> for {{entity_name}}CreateRequest {
    fn from(model: Model) -> Self {
    Self {
        {{#each create_fields}}
        {{name}}: model.{{name}},
        {{/each}}
        }
    }
}

impl From<Model> for {{entity_name}}UpdateRequest {
    fn from(model: Model) -> Self {
        Self {
            {{#each update_fields}}
            {{name}}: model.{{name}},
            {{/each}}
        }
    }
}

// 反向转换（如有需要，可选）
impl From<{{entity_name}}CreateRequest> for Model {
    fn from(dto: {{entity_name}}CreateRequest) -> Self {
        Self {
            {{#each create_fields}}
            {{name}}: dto.{{name}},
            {{/each}}
            ..Default::default()
        }
    }
}

impl From<{{entity_name}}UpdateRequest> for Model {
    fn from(dto: {{entity_name}}UpdateRequest) -> Self {
        Self {
            {{#each update_fields}}
                {{name}}: dto.{{name}},
            {{/each}}
            ..Default::default()
        }
    }
}
{{/if}}
{{#if file_header}}{{file_header}}{{/if}}

use crate::domain::{{module_type}}::dto::{{entity_snake}}_request::{
    {{entity_name}}PageRequest, {{entity_name}}UpdateRequest,
};
use crate::domain::{{module_type}}::vo::{{entity_snake}}_vo::{
    {{entity_name}}DetailResponse, {{entity_name}}ListResponse,
};
use crate::service::{{module_type}}::{{service_name}};
use crate::utils::custom_validator::{MyPath, MyQuery, ValidJson};
use axum::{
    middleware, routing::{delete, get, put},
    Router,
};
use lib_auth::require_permission;
use lib_core::response::{ApiResult, PageData};
use lib_web::extractor::Component;
#[allow(unused_imports)]
use serde_json::json;
use tracing::info;
use utoipa::OpenApi;
use uuid::Uuid;

/// {{tags}} 相关接口
pub struct {{controller_name}};

impl {{controller_name}} {
    /// 创建{{tags}}公开路由
    pub fn public_routes() -> Router {
        Router::new()
        // {{tags}}暂无公开路由
    }

    /// 创建{{tags}}受保护路由
    pub fn protected_routes() -> Router {
        Router::new()
            // {{entity_comment}}详情查询
            .route(
                "{{route_prefix}}/{{route_path}}/{id}",
                get(get_{{entity_lower}}_by_id).layer(middleware::from_fn(
                    require_permission("{{permission_prefix}}:detail"),
                )),
            )
            // {{entity_comment}}更新
            .route(
                "{{route_prefix}}/{{route_path}}/{id}",
                put(update_{{entity_lower}}).layer(middleware::from_fn(
                    require_permission("{{permission_prefix}}:update"),
                )),
            )
            // {{entity_comment}}删除
            .route(
                "{{route_prefix}}/{{route_path}}/{id}",
                delete(delete_{{entity_lower}}).layer(middleware::from_fn(
                    require_permission("{{permission_prefix}}:delete"),
                )),
            )
            // {{entity_comment}}列表查询
            .route(
                "{{route_prefix}}/{{route_path}}",
                get(page_{{entity_lower}}).layer(middleware::from_fn(
                    require_permission("{{permission_prefix}}:list"),
                )),
            )
    }
}

/// 分页查询{{entity_comment}}列表
#[utoipa::path(
    get,
    path = "{{route_prefix}}/{{route_path}}",
    summary = "分页查询{{entity_comment}}列表",
    description = "根据查询条件分页获取{{entity_comment}}信息",
    tags = ["{{tags}}"],
    params({{entity_name}}PageRequest),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<PageData<{{entity_name}}ListResponse>>,
         example = json!({
             "code": 200,
             "message": "操作成功",
             "data": {
                 "items": [],
                 "total": 0,
                 "page": 1,
                 "page_size": 10,
                 "total_pages": 0
             }
         })
        )
    )
)]
pub async fn page_{{entity_lower}}(
    Component({{entity_service}}): Component<{{service_name}}>,
    MyQuery(req): MyQuery<{{entity_name}}PageRequest>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<PageData<{{entity_name}}ListResponse>> {
    info!(
        "分页查询{{entity_comment}}列表请求: {:?}，操作人: {}",
        req, current_user.account
    );
    match {{entity_service}}.page_{{entity_lower}}(req).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 根据ID查询{{entity_comment}}详情
#[utoipa::path(
    get,
    path = "{{route_prefix}}/{{route_path}}/{id}",
    summary = "查询{{entity_comment}}详情",
    description = "根据ID获取{{entity_comment}}详细信息",
    tags = ["{{tags}}"],
    params(
        ("id" = {{id_type}}, Path, description = "{{entity_comment}}ID")
    ),
    responses(
        (status = 200, description = "查询成功", body = ApiResult<{{entity_name}}DetailResponse>),
    )
)]
pub async fn get_{{entity_lower}}_by_id(
    Component({{entity_service}}): Component<{{service_name}}>,
    MyPath(id): MyPath<{{id_type}}>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<{{entity_name}}DetailResponse> {
    info!(
        "查询{{entity_comment}}详情请求，ID: {}，操作人: {}",
        id, current_user.account
    );

    match {{entity_service}}.get_{{entity_lower}}_by_id(id).await {
        Ok(data) => ApiResult::success(data),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 更新{{entity_comment}}
#[utoipa::path(
    put,
    path = "{{route_prefix}}/{{route_path}}/{id}",
    summary = "更新{{entity_comment}}",
    description = "更新{{entity_comment}}信息",
    tags = ["{{tags}}"],
    params(
        ("id" = {{id_type}}, Path, description = "{{entity_comment}}ID")
    ),
    request_body = {{entity_name}}UpdateRequest,
    responses(
        (status = 200, description = "更新成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "{{entity_comment}}更新成功",
             "data": null
         })
        )
    )
)]
pub async fn update_{{entity_lower}}(
    Component({{entity_service}}): Component<{{service_name}}>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
    MyPath(id): MyPath<{{id_type}}>,
    ValidJson(req): ValidJson<{{entity_name}}UpdateRequest>,
) -> ApiResult<String> {
    info!(
        "更新{{entity_comment}}请求，ID: {}，数据: {:?}，操作人: {}",
        id, req, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match {{entity_service}}.update_{{entity_lower}}(id, req, userid).await {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// 删除{{entity_comment}}
    #[utoipa::path(
    delete,
    path = "{{route_prefix}}/{{route_path}}/{id}",
    summary = "删除{{entity_comment}}",
    description = "删除指定的{{entity_comment}}",
    tags = ["{{tags}}"],
    params(
        ("id" = {{id_type}}, Path, description = "{{entity_comment}}ID")
    ),
        responses(
        (status = 200, description = "删除成功", body = ApiResult<String>,
         example = json!({
             "code": 200,
             "message": "{{entity_comment}}删除成功",
             "data": null
         })
        )
    )
    )]
pub async fn delete_{{entity_lower}}(
    Component({{entity_service}}): Component<{{service_name}}>,
    MyPath(id): MyPath<{{id_type}}>,
    axum::extract::Extension(current_user): axum::extract::Extension<
        lib_auth::middleware::sys_jwt::AuthToken,
    >,
) -> ApiResult<String> {
    info!(
        "删除{{entity_comment}}请求，ID: {}，操作人: {}",
        id, current_user.account
    );
    let userid = Uuid::parse_str(&current_user.id).unwrap_or_else(|_| Uuid::nil());

    match {{entity_service}}.delete_{{entity_lower}}(id, userid).await {
        Ok(message) => ApiResult::success_with_message(message),
        Err(business) => ApiResult::error(business.code, business.message),
    }
}

/// {{tags}} API 文档
#[derive(OpenApi)]
#[openapi(
    paths(
        page_{{entity_lower}},
        get_{{entity_lower}}_by_id,
        update_{{entity_lower}},
        delete_{{entity_lower}},
    ),
    components(schemas(
        {{entity_name}}PageRequest,
        {{entity_name}}UpdateRequest,
        {{entity_name}}ListResponse,
        {{entity_name}}DetailResponse,
    ))
)]
pub struct {{controller_name}}ApiDoc;

impl {{controller_name}}ApiDoc {
    pub fn get_openapi_json() -> String {
        serde_json::to_string_pretty(&Self::openapi()).unwrap()
    }

    pub fn get_openapi_yaml() -> String {
        serde_yaml::to_string(&Self::openapi()).unwrap()
    }
} 
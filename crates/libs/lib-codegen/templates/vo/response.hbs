{{#if file_header}}{{file_header}}{{/if}}

use derive_more::{From, Into, Constructor};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use sea_orm::prelude::DateTimeWithTimeZone;
use uuid::Uuid;
{{#if has_decimal}}
    use rust_decimal::Decimal;
{{/if}}
{{#if has_datetime}}
    use chrono::{DateTime, FixedOffset};
{{/if}}
{{#each additional_imports}}
    use {{this}};
{{/each}}

/// {{entity_comment}}列表响应
#[derive(Debug, Serialize, Deserialize, ToSchema, From, Into, Constructor, Builder)]
pub struct {{entity_name}}ListResponse {
{{#each list_fields}}
    /// {{comment}}
    {{#if schema_example}}
        #[schema(example = {{schema_example}})]
    {{/if}}
    pub {{name}}: {{rust_type}},
{{/each}}
}

/// {{entity_comment}}详情响应
#[derive(Debug, Serialize, Deserialize, ToSchema, From, Into, Constructor, Builder)]
pub struct {{entity_name}}DetailResponse {
{{#each detail_fields}}
    /// {{comment}}
    {{#if schema_example}}
        #[schema(example = {{schema_example}})]
    {{/if}}
    pub {{name}}: {{rust_type}},
{{/each}}
}

/// {{entity_comment}}选择项响应
#[derive(Debug, Serialize, Deserialize, ToSchema, From, Into, Constructor, Builder)]
pub struct {{entity_name}}SelectItem {
{{#each select_fields}}
    /// {{comment}}
    {{#if schema_example}}
        #[schema(example = {{schema_example}})]
    {{/if}}
    pub {{name}}: {{rust_type}},
{{/each}}
}

{{#if conversion_methods}}
    // ============================================================================
    // 转换方法
    // ============================================================================

    {{#each conversion_methods}}
        /// {{comment}}
        {{method_signature}} {
        {{method_body}}
        }

    {{/each}}
{{/if}}

{{#if has_entity_conversion}}
    // ============================================================================
    // 实体 <-> VO 转换
    // ============================================================================

use crate::domain::{{module_type}}::entities::{{entity_snake}}::Model;

// Model -> VO
impl From <Model> for {{entity_name}}ListResponse {
    fn from(model: Model) -> Self {
        Self {
            {{#each list_fields}}
            {{name}}: model.{{name}},
            {{/each}}
        }
    }
}

impl From <Model> for {{entity_name}}DetailResponse {
    fn from(model: Model) -> Self {
        Self {
            {{#each detail_fields}}
            {{name}}: model.{{name}},
            {{/each}}
        }
    }
}

impl From <Model> for {{entity_name}}SelectItem {
    fn from(model: Model) -> Self {
        Self {
            {{#each select_fields}}
            {{name}}: model.{{name}},
            {{/each}}
        }
    }
}

// VO -> Model
impl From <{{entity_name}}ListResponse> for Model {
    fn from(vo: {{entity_name}}ListResponse) -> Self {
        Self {
            {{#each list_fields}}
            {{name}}: vo.{{name}},
            {{/each}}
            ..Default::default()
        }
    }
}

impl From <{{entity_name}}DetailResponse> for Model {
    fn from(vo: {{entity_name}}DetailResponse) -> Self {
        Self {
            {{#each detail_fields}}
            {{name}}: vo.{{name}},
            {{/each}}
            ..Default::default()
        }
    }
}

impl From <{{entity_name}}SelectItem> for Model {
    fn from(vo: {{entity_name}}SelectItem) -> Self {
        Self {
            {{#each select_fields}}
            {{name}}: vo.{{name}},
            {{/each}}
            ..Default::default()
        }
    }
}
{{/if}}
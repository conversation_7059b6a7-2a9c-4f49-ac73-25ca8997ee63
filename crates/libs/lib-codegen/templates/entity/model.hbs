//! {{entity_comment}}实体
//! Generated by lib-codegen
//! 对应表：{{schema_name}}.{{table_name}}

use crate::utils::DateTimeUtils;
use async_trait::async_trait;
use derive_more::{From, Into, Constructor};
use sea_orm::entity::prelude::*;
use sea_orm::{ConnectionTrait, DbErr, Set};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
{{#if has_decimal}}
use rust_decimal::Decimal;
{{/if}}
{{#if has_enum}}
use sea_orm::{DeriveActiveEnum, EnumIter};
{{/if}}
{{#each additional_imports}}
use {{this}};
{{/each}}

{{#each enums}}
/// {{comment}}
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, EnumIter, DeriveActiveEnum, Serialize, Deserialize)]
#[sea_orm(rs_type = "i32", db_type = "Integer")]
pub enum {{name}} {
    {{#each variants}}
    /// {{comment}}
    #[sea_orm(num_value = {{value}})]
    {{name}} = {{value}},
    {{/each}}
}

{{/each}}

/// {{entity_comment}}实体
///
/// 存储{{entity_comment}}的相关信息和业务数据
#[derive(Clone, Debug, PartialEq, DeriveEntityModel, Eq, Serialize, Deserialize, Into, From, Constructor)]
#[sea_orm({{#if schema_name}}schema_name = "{{schema_name}}", {{/if}}table_name = "{{table_name}}")]
pub struct Model {
{{#each fields}}
    /// {{comment}}
    {{#if is_primary_key}}
    #[sea_orm(primary_key{{#unless auto_increment}}, auto_increment = false{{/unless}})]
    {{/if}}
    pub {{name}}: {{rust_type}},
{{/each}}
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    // 根据需要定义关系
}

#[async_trait]
impl ActiveModelBehavior for ActiveModel {
    /// 插入或更新前自动设置时间戳
    async fn before_save<C>(mut self, _db: &C, insert: bool) -> Result<Self, DbErr>
    where
        C: ConnectionTrait,
    {
        let now = DateTimeUtils::now_china_offset();
        if insert {
            {{#each fields}}
            {{#if is_created_at}}
            self.{{name}} = Set(now);
            {{/if}}
            {{/each}}
        }
        {{#each fields}}
        {{#if is_updated_at}}
        self.{{name}} = Set(now);
        {{/if}}
        {{/each}}
        Ok(self)
    }
}

{{#if conversion_methods}}
// ============================================================================
// 转换方法
// ============================================================================

{{#each conversion_methods}}
/// {{comment}}
{{method_signature}} {
    {{method_body}}
}

{{/each}}
{{/if}} 
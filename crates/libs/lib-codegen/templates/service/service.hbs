{{#if file_header}}{{file_header}}{{/if}}

use crate::constants::*;
use crate::domain::{{module_type}}::dto::{{entity_snake}}_request::{
    {{entity_name}}PageRequest, {{entity_name}}UpdateRequest,
};
use crate::domain::{{module_type}}::vo::{{entity_snake}}_vo::{
    {{entity_name}}DetailResponse, {{entity_name}}ListResponse,
};
use crate::domain::{{module_type}}::entities::{{entity_snake}}::{ActiveModel, Model};
use crate::repository::{{repository_name}};
use lib_core::app::plugin::Service as ServiceTrait;
use lib_core::response::PageData;
use lib_core::BusinessError;
use lib_macros::Service;
use sea_orm::{DatabaseConnection, Set};
use tracing::{info, warn};
use uuid::Uuid;

/// {{entity_comment}}服务实现
#[derive(<PERSON><PERSON>, Service)]
pub struct {{service_name}} {
    #[inject(component)]
    {{repository_field}}: {{repository_name}},
    #[inject(component)]
    db: DatabaseConnection,
}

impl {{service_name}} {
    /// 分页查询{{entity_comment}}列表
    pub async fn page_{{entity_snake}}(
        &self,
        req: {{entity_name}}PageRequest,
    ) -> Result<PageData<{{entity_name}}ListResponse>, BusinessError> {
        info!("分页查询{{entity_comment}}，参数: {:?}", req);

        // 使用Repository进行分页查询
        let page_data = self
            .{{repository_field}}
            .page_by_condition(&req, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("分页查询{{entity_comment}}失败: {}", e)))?;

        // 如果没有数据，直接返回空的响应结果
        if page_data.items.is_empty() {
            return Ok(PageData::new(
                Vec::new(),
                page_data.total,
                page_data.page,
                page_data.page_size,
            ));
        }

        // 转换为响应格式
        let response_items: Vec<{{entity_name}}ListResponse> = page_data
            .items
            .into_iter()
            .map(|model| {{entity_name}}ListResponse::from(model))
            .collect();

        // 构建最终的分页结果
        let final_page_data = PageData::new(
            response_items,
            page_data.total,
            page_data.page,
            page_data.page_size,
        );

        Ok(final_page_data)
    }

    /// 根据ID查询{{entity_comment}}详情
    pub async fn get_{{entity_snake}}_by_id(
        &self,
        {{entity_snake}}_id: {{id_type}},
    ) -> Result<{{entity_name}}DetailResponse, BusinessError> {
        info!("查询{{entity_comment}}详情，ID: {}", {{entity_snake}}_id);

        let {{entity_snake}} = self
            .{{repository_field}}
            .find_by_id({{entity_snake}}_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询{{entity_comment}}失败: {}", e)))?
            .ok_or_else(|| {
                warn!("{{entity_comment}}不存在: {}", {{entity_snake}}_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "{{entity_comment}}不存在".to_string())
            })?;

        let detail = {{entity_name}}DetailResponse::from({{entity_snake}});

        Ok(detail)
    }

    /// 更新{{entity_comment}}
    pub async fn update_{{entity_snake}}(
        &self,
        {{entity_snake}}_id: {{id_type}},
        req: {{entity_name}}UpdateRequest,
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "更新{{entity_comment}}，ID: {}，请求: {:?}，操作人: {}",
            {{entity_snake}}_id, req, operator_id
        );

        let {{entity_snake}} = self
            .{{repository_field}}
            .find_by_id({{entity_snake}}_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询{{entity_comment}}失败: {}", e)))?
            .ok_or_else(|| {
                warn!("{{entity_comment}}不存在: {}", {{entity_snake}}_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "{{entity_comment}}不存在".to_string())
            })?;

        let updated_{{entity_snake}} = req
            .update_active_model({{entity_snake}}.into(), operator_id)
            .map_err(|_| BusinessError::new(INVALID_PARAMETER, "构建更新数据失败".to_string()))?;

        self.{{repository_field}}
            .update(updated_{{entity_snake}}, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        info!("{{entity_comment}}更新成功，ID: {}", {{entity_snake}}_id);
        Ok("{{entity_comment}}更新成功".to_string())
    }

    /// 删除{{entity_comment}}
    pub async fn delete_{{entity_snake}}(
        &self,
        {{entity_snake}}_id: {{id_type}},
        operator_id: Uuid,
    ) -> Result<String, BusinessError> {
        info!(
            "删除{{entity_comment}}，ID: {}，操作人: {}",
            {{entity_snake}}_id, operator_id
        );

        let {{entity_snake}} = self
            .{{repository_field}}
            .find_by_id({{entity_snake}}_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("查询{{entity_comment}}失败: {}", e)))?
            .ok_or_else(|| {
                warn!("{{entity_comment}}不存在: {}", {{entity_snake}}_id);
                BusinessError::new(RESOURCE_NOT_FOUND, "{{entity_comment}}不存在".to_string())
            })?;

        // 检查是否可以删除（可根据业务需求自定义）
        let deletable = self
            .{{repository_field}}
            .is_deletable({{entity_snake}}_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("数据库操作失败: {}", e)))?;

        if !deletable {
            return Err(BusinessError::new(
                OPERATION_CONFLICT,
                "该{{entity_comment}}不能删除".to_string(),
            ));
        }

        // 删除{{entity_comment}}
        self.{{repository_field}}
            .delete_by_id({{entity_snake}}_id, &self.db)
            .await
            .map_err(|e| BusinessError::new(DATABASE_ERROR, format!("删除{{entity_comment}}失败: {}", e)))?;

        info!("{{entity_comment}}删除成功，ID: {}", {{entity_snake}}_id);
        Ok("{{entity_comment}}删除成功".to_string())
    }
    {{#if additional_methods}}
    // ============================================================================
    // 附加业务方法
    // ============================================================================

    {{#each additional_methods}}
    /// {{comment}}
    {{method_signature}} {
        {{method_body}}
    }
    {{/each}}
    {{/if}}
} 
{{#if file_header}}{{file_header}}{{/if}}

use crate::domain::{{module_type}}::entities::{{entity_snake}}::{
    ActiveModel, Column, Entity as {{entity_name}}, Model as {{entity_name}}Model,
};
use crate::domain::{{module_type}}::dto::{{entity_snake}}_request::{{entity_name}}PageRequest;
{{#if has_datetime_utils}}
use crate::utils::datetime::DateTimeUtils;
{{/if}}
use lib_core::response::PageData;
use lib_data::pagination::{Pagination, PaginationExt};
use lib_macros::Service;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, EntityTrait, 
    PaginatorTrait, QueryFilter, QueryOrder,
};
{{#if use_uuid}}
use uuid::Uuid;
{{/if}}
use lib_core::app::plugin::Service as ServiceTrait;
use anyhow::{Context, Result};

/// {{entity_comment}}Repository实现
#[derive(Clone, Service)]
pub struct {{repository_name}};

impl {{repository_name}} {
    /// 构建查询条件
    fn build_query_conditions(req: &{{entity_name}}PageRequest) -> Condition {
        let mut condition = Condition::all();

        {{#each query_conditions}}
        {{#if is_string}}
        // 字符串搜索条件
        if let Some({{field_name}}) = &req.{{field_name}} {
            if !{{field_name}}.trim().is_empty() {
                condition = condition.add(Column::{{column_name}}.contains({{field_name}}.trim()));
            }
        }
        {{/if}}
        {{#if is_enum}}
        // 枚举匹配条件
        if let Some({{field_name}}) = req.{{field_name}} {
            condition = condition.add(Column::{{column_name}}.eq({{field_name}}));
        }
        {{/if}}
        {{#if is_id}}
        // ID过滤条件
        if let Some({{field_name}}) = req.{{field_name}} {
            condition = condition.add(Column::{{column_name}}.eq({{field_name}}));
        }
        {{/if}}
        {{#if is_date_range}}
        // {{comment}}时间范围过滤
        if let Some(ref {{field_name}}_start) = req.{{field_name}}_start {
            if !{{field_name}}_start.trim().is_empty() {
                if let Ok(start_datetime) = DateTimeUtils::parse_range_start({{field_name}}_start) {
                    condition = condition.add(Column::{{column_name}}.gte(start_datetime.fixed_offset()));
                }
            }
        }
        if let Some(ref {{field_name}}_end) = req.{{field_name}}_end {
            if !{{field_name}}_end.trim().is_empty() {
                if let Ok(end_datetime) = DateTimeUtils::parse_range_end({{field_name}}_end) {
                    condition = condition.add(Column::{{column_name}}.lte(end_datetime.fixed_offset()));
                }
            }
        }
        {{/if}}
        {{/each}}
        condition
    }

    /// 分页查询{{entity_comment}}
    pub async fn page_by_condition<C>(
        &self,
        req: &{{entity_name}}PageRequest,
        conn: &C,
    ) -> Result<PageData<{{entity_name}}Model>, String>
    where
        C: ConnectionTrait,
    {
        let condition = Self::build_query_conditions(req);
        let pagination = Pagination::from_one_based(req.page, req.page_size);

        {{entity_name}}::find()
            .filter(condition)
            .order_by_desc(Column::{{order_column}})
            .page(conn, &pagination)
            .await
            .map_err(|e| e.to_string())
    }

    /// 根据ID查询{{entity_comment}}
    pub async fn find_by_id<C>(
        &self,
        id: {{id_type}},
        conn: &C,
    ) -> Result<Option<{{entity_name}}Model>>
    where
        C: ConnectionTrait,
    {
        {{entity_name}}::find_by_id(id)
            .one(conn)
            .await
            .context("查询{{entity_comment}}失败")
    }

    /// 根据IDs查询{{entity_comment}}列表
    pub async fn find_by_ids<C>(
        &self,
        ids: Vec<{{id_type}}>,
        conn: &C,
    ) -> Result<Vec<{{entity_name}}Model>>
    where
        C: ConnectionTrait,
    {
        if ids.is_empty() {
            return Ok(Vec::new());
        }

        {{entity_name}}::find()
            .filter(Column::Id.is_in(ids))
            .all(conn)
            .await
            .context("批量查询{{entity_comment}}失败")
    }

    {{#if has_status_field}}
    /// 根据状态查询{{entity_comment}}
    pub async fn find_by_status<C>(
        &self,
        status: {{status_type}},
        conn: &C,
    ) -> Result<Vec<{{entity_name}}Model>>
    where
        C: ConnectionTrait,
    {
        {{entity_name}}::find()
            .filter(Column::Status.eq(status))
            .order_by_desc(Column::{{order_column}})
            .all(conn)
            .await
            .context("根据状态查询{{entity_comment}}失败")
    }

    /// 查询启用的{{entity_comment}}选择项
    pub async fn find_enabled_select_items<C>(
        &self,
        conn: &C,
    ) -> Result<Vec<{{entity_name}}Model>>
    where
        C: ConnectionTrait,
    {
        {{entity_name}}::find()
            .filter(Column::Status.eq({{enabled_status_value}}))
            .order_by_asc(Column::{{select_order_column}})
            .all(conn)
            .await
            .context("查询{{entity_comment}}选择项失败")
    }
    {{/if}}

    {{#if has_name_field}}
    /// 根据名称查询{{entity_comment}}
    pub async fn find_by_name<C>(
        &self,
        name: &str,
        conn: &C,
    ) -> Result<Option<{{entity_name}}Model>>
    where
        C: ConnectionTrait,
    {
        {{entity_name}}::find()
            .filter(Column::{{name_column}}.eq(name))
            .one(conn)
            .await
            .context("根据名称查询{{entity_comment}}失败")
    }
    {{/if}}

    {{#if has_code_field}}
    /// 根据编码查询{{entity_comment}}
    pub async fn find_by_code<C>(
        &self,
        code: &str,
        conn: &C,
    ) -> Result<Option<{{entity_name}}Model>>
    where
        C: ConnectionTrait,
    {
        {{entity_name}}::find()
            .filter(Column::{{code_column}}.eq(code))
            .one(conn)
            .await
            .context("根据编码查询{{entity_comment}}失败")
    }
    {{/if}}

    /// 创建{{entity_comment}}
    pub async fn create<C>(
        &self,
        model: ActiveModel,
        conn: &C,
    ) -> Result<{{entity_name}}Model>
    where
        C: ConnectionTrait,
    {
        model
            .insert(conn)
            .await
            .context("创建{{entity_comment}}失败")
    }

    /// 更新{{entity_comment}}
    pub async fn update<C>(
        &self,
        model: ActiveModel,
        conn: &C,
    ) -> Result<{{entity_name}}Model>
    where
        C: ConnectionTrait,
    {
        model
            .update(conn)
            .await
            .context("更新{{entity_comment}}失败")
    }

    /// 根据ID删除{{entity_comment}}
    pub async fn delete_by_id<C>(
        &self,
        id: {{id_type}},
        conn: &C,
    ) -> Result<()>
    where
        C: ConnectionTrait,
    {
        {{entity_name}}::delete_by_id(id)
            .exec(conn)
            .await
            .context("删除{{entity_comment}}失败")?;

        Ok(())
    }

    /// 批量删除{{entity_comment}}
    pub async fn delete_by_ids<C>(
        &self,
        ids: Vec<{{id_type}}>,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        if ids.is_empty() {
            return Ok(0);
        }

        let result = {{entity_name}}::delete_many()
            .filter(Column::Id.is_in(ids))
            .exec(conn)
            .await
            .context("批量删除{{entity_comment}}失败")?;

        Ok(result.rows_affected)
    }

    /// 检查{{entity_comment}}是否可以删除
    pub async fn is_deletable<C>(
        &self,
        id: {{id_type}},
        conn: &C,
    ) -> Result<bool>
    where
        C: ConnectionTrait,
    {
        {{#if has_deletable_check}}
        // 检查是否存在关联数据
        {{#each deletable_checks}}
        let {{relation_name}}_count = {{related_entity}}::find()
            .filter({{related_column}}.eq(id))
            .count(conn)
            .await
            .context("检查{{relation_comment}}关联失败")?;

        if {{relation_name}}_count > 0 {
            return Ok(false);
        }
        {{/each}}
        {{/if}}

        Ok(true)
    }

    /// 统计{{entity_comment}}总数
    pub async fn count_all<C>(
        &self,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        {{entity_name}}::find()
            .count(conn)
            .await
            .context("统计{{entity_comment}}总数失败")
    }

    {{#if has_status_field}}
    /// 统计启用的{{entity_comment}}数量
    pub async fn count_enabled<C>(
        &self,
        conn: &C,
    ) -> Result<u64>
    where
        C: ConnectionTrait,
    {
        {{entity_name}}::find()
            .filter(Column::Status.eq({{enabled_status_value}}))
            .count(conn)
            .await
            .context("统计启用{{entity_comment}}数量失败")
    }
    {{/if}}

    {{#if additional_methods}}
    // ============================================================================
    // 附加业务方法
    // ============================================================================

    {{#each additional_methods}}
    /// {{comment}}
    {{method_signature}} {
        {{method_body}}
    }

    {{/each}}
    {{/if}}
} 
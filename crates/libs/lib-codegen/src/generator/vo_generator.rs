use crate::analyzer::AnalyzedTable;
use crate::generator::base::{BaseGenerator, GeneratorType};
use crate::generator::context::{ContextBuilder, GeneratorContext};
use crate::generator::field_strategy::FieldStrategyFactory;
use crate::generator::template_strategy::{DefaultTemplateStrategy, TemplateStrategy};
use anyhow::Result;

/// VO生成器
pub struct VoGenerator {
    template_strategy: DefaultTemplateStrategy,
}

impl VoGenerator {
    pub fn new() -> Self {
        let template_strategy = DefaultTemplateStrategy::new("crates/libs/lib-codegen/templates/vo/response.hbs".to_string());
        Self { template_strategy }
    }
}

impl Default for VoGenerator {
    fn default() -> Self {
        Self::new()
    }
}

impl BaseGenerator for VoGenerator {
    fn generator_type(&self) -> GeneratorType {
        GeneratorType::Vo
    }

    fn build_context(&self, table: &AnalyzedTable) -> Result<GeneratorContext> {
        let list_strategy = FieldStrategyFactory::get_strategy("vo_list");
        let detail_strategy = FieldStrategyFactory::get_strategy("vo_detail");
        
        let list_fields = FieldStrategyFactory::apply_strategy(list_strategy.as_ref(), &table.fields);
        let detail_fields = FieldStrategyFactory::apply_strategy(detail_strategy.as_ref(), &table.fields);
        
        // select_fields: 默认取list_fields前两个字段，保证不为空
        let select_fields = if list_fields.clone().len() >= 2 {
            list_fields.iter().take(2).cloned().collect::<Vec<_>>()
        } else {
            list_fields.clone()
        };

        let context = ContextBuilder::from_table(table)
            .with_list_fields(list_fields)
            .with_detail_fields(detail_fields)
            .with_select_fields(select_fields)
            .add_import("bon::Builder".to_string())
            .build();
            
        Ok(context)
    }

    fn render_template(&self, context: &GeneratorContext) -> Result<String> {
        self.template_strategy.render(context)
    }
} 
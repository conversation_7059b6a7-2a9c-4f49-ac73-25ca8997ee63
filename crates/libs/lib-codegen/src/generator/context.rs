use crate::analyzer::{AnalyzedField, AnalyzedTable};
use crate::utils::StringUtils;
use serde::Serialize;
use serde_json::Value;
use std::collections::HashMap;

/// 通用生成器上下文
#[derive(Debug, Clone, Serialize)]
pub struct GeneratorContext {
    // 基础实体信息
    pub entity_name: String,
    pub entity_comment: String,
    pub entity_description: String,
    pub entity_snake: String,
    pub entity_lower: String,
    pub entity_plural: String,
    pub entity_plural_snake: String,

    // 模块信息
    pub module_type: String,
    pub module_name: String,

    // 表信息
    pub table_name: String,
    pub schema_name: Option<String>,
    pub id_type: String,
    pub primary_key: Option<String>,

    // 特性标志
    pub has_created_at: bool,
    pub has_updated_at: bool,
    pub has_deleted_at: bool,
    pub has_status: bool,
    pub is_soft_delete: bool,
    pub has_decimal: bool,
    pub has_datetime: bool,
    pub has_enum: bool,
    pub use_uuid: bool,
    pub has_datetime_utils: bool,

    // 字段集合
    pub fields: Vec<Value>,
    pub all_fields: Vec<Value>,

    // 类型特定字段
    pub page_fields: Vec<Value>,
    pub create_fields: Vec<Value>,
    pub update_fields: Vec<Value>,
    pub list_fields: Vec<Value>,
    pub select_fields: Vec<Value>,
    pub detail_fields: Vec<Value>,
    pub query_conditions: Vec<Value>,

    // 枚举定义
    pub enums: Vec<Value>,

    // 导入和依赖
    pub additional_imports: Vec<String>,
    pub file_header: Option<String>,

    // 命名相关
    pub controller_name: String,
    pub service_name: String,
    pub entity_service: String,
    pub repository_name: String,
    pub repository_field: String,

    // 路由相关
    pub route_prefix: String,
    pub route_path: String,
    pub permission_prefix: String,

    // 示例数据
    pub create_example: Value,
    pub update_example: Value,

    // 排序字段
    pub order_column: String,
    pub select_order_column: String,

    // 转换方法
    pub conversion_methods: Vec<Value>,
    pub has_entity_conversion: bool,

    // 扩展字段
    pub extra_data: HashMap<String, Value>,

    /// 文档标签
    pub tags: String,
}

/// 上下文构建器
pub struct ContextBuilder {
    context: GeneratorContext,
}

impl ContextBuilder {
    /// 从分析结果创建构建器
    pub fn from_table(table: &AnalyzedTable) -> Self {
        let module_type = table.schema_name.to_string();
        let id_type = Self::infer_id_type(table);

        let context = GeneratorContext {
            // 基础实体信息
            entity_name: table.entity_name.clone(),
            entity_comment: table.entity_comment.clone(),
            entity_description: format!("{}实体对象", table.entity_comment),
            entity_snake: table.entity_snake.clone(),
            entity_lower: table.entity_name.to_lowercase(),
            entity_plural: table.entity_plural.clone(),
            entity_plural_snake: table.entity_plural_snake.clone(),

            // 模块信息
            module_type: module_type.clone(),
            module_name: module_type.clone(),

            // 表信息
            table_name: table.table_name.clone(),
            schema_name: Some(table.schema_name.to_string()),
            id_type: id_type.clone(),
            primary_key: table.primary_key.clone(),

            // 特性标志
            has_created_at: table.has_created_at,
            has_updated_at: table.has_updated_at,
            has_deleted_at: table.has_deleted_at,
            has_status: table.has_status,
            is_soft_delete: table.is_soft_delete,
            has_decimal: Self::check_has_decimal(table),
            has_datetime: Self::check_has_datetime(table),
            has_enum: Self::check_has_enum(table),
            use_uuid: id_type == "Uuid",
            has_datetime_utils: table.has_created_at || table.has_updated_at,

            // 字段集合（初始为空，由具体生成器填充）
            fields: vec![],
            all_fields: Self::convert_all_fields(&table.fields),
            page_fields: vec![],
            create_fields: vec![],
            update_fields: vec![],
            list_fields: vec![],
            select_fields: vec![],
            detail_fields: vec![],
            query_conditions: vec![],
            enums: vec![],

            // 导入和依赖
            additional_imports: vec![],
            file_header: Some(format!(
                "//! {}\n//! Generated by lib-codegen",
                table.entity_comment
            )),

            // 命名相关
            controller_name: format!("{}Controller", table.entity_name),
            service_name: format!("{}Service", table.entity_name),
            entity_service: StringUtils::to_snake_case(&format!("{}_service", table.entity_name)),
            repository_name: format!("{}Repository", table.entity_name),
            repository_field: StringUtils::to_snake_case(&format!(
                "{}_repository",
                table.entity_name
            )),

            // 路由相关
            route_prefix: format!("{}{}", "/", table.schema_name.to_string()),
            route_path: table.entity_plural_snake.clone(),
            permission_prefix: format!("{}:{}", module_type, table.entity_snake),

            // 示例数据
            create_example: serde_json::json!({}),
            update_example: serde_json::json!({}),

            // 排序字段
            order_column: Self::infer_order_column(table),
            select_order_column: Self::infer_select_order_column(table),

            // 转换方法
            conversion_methods: vec![],
            has_entity_conversion: true,

            // 扩展字段
            extra_data: HashMap::new(),
            tags: table.tags.clone(),
        };

        Self { context }
    }
    

    /// 推断ID类型
    fn infer_id_type(table: &AnalyzedTable) -> String {
        table
            .primary_key
            .as_ref()
            .and_then(|pk| table.fields.iter().find(|f| f.column_name == *pk))
            .map(|field| field.rust_type.clone())
            .unwrap_or_else(|| "i32".to_string())
    }

    /// 检查是否包含Decimal类型
    fn check_has_decimal(table: &AnalyzedTable) -> bool {
        table.fields.iter().any(|f| f.rust_type.contains("Decimal"))
    }

    /// 检查是否包含DateTime类型
    fn check_has_datetime(table: &AnalyzedTable) -> bool {
        table
            .fields
            .iter()
            .any(|f| f.rust_type.contains("DateTime") || f.rust_type.contains("FixedOffset"))
    }

    /// 检查是否包含枚举类型
    fn check_has_enum(table: &AnalyzedTable) -> bool {
        // 假设状态字段为枚举类型
        table
            .fields
            .iter()
            .any(|f| f.column_name.contains("status") || f.column_name.contains("type"))
    }

    /// 推断默认排序字段
    fn infer_order_column(table: &AnalyzedTable) -> String {
        // 按优先级查找排序字段
        let preferred_columns = vec!["created_at", "created_date", "updated_at", "id"];

        for column in preferred_columns {
            if table
                .fields
                .iter()
                .any(|f| f.column_name.to_lowercase() == column)
            {
                return StringUtils::to_pascal_case(column);
            }
        }

        // 如果没有找到，使用第一个字段
        table
            .fields
            .first()
            .map(|f| StringUtils::to_pascal_case(&f.column_name))
            .unwrap_or_else(|| "Id".to_string())
    }

    /// 推断查询排序字段
    fn infer_select_order_column(table: &AnalyzedTable) -> String {
        // 对于查询排序，优先使用名称字段
        let preferred_columns = vec!["name", "title", "code", "id"];

        for column in preferred_columns {
            if table
                .fields
                .iter()
                .any(|f| f.column_name.to_lowercase() == column)
            {
                return StringUtils::to_pascal_case(column);
            }
        }

        // 默认使用ID
        "Id".to_string()
    }

    /// 转换所有字段为JSON值
    fn convert_all_fields(fields: &[AnalyzedField]) -> Vec<Value> {
        fields
            .iter()
            .map(|field| {
                serde_json::json!({
                    "name": field.column_name,
                    "rust_type": field.rust_type,
                    "comment": field.comment,
                    "is_primary_key": field.is_primary_key,
                    "is_nullable": field.is_nullable,
                    "purpose": format!("{:?}", field.purpose)
                })
            })
            .collect()
    }

    /// 设置字段
    pub fn with_fields(mut self, fields: Vec<Value>) -> Self {
        self.context.fields = fields;
        self
    }

    /// 设置页面查询字段
    pub fn with_page_fields(mut self, fields: Vec<Value>) -> Self {
        self.context.page_fields = fields;
        self
    }

    /// 设置创建字段
    pub fn with_create_fields(mut self, fields: Vec<Value>) -> Self {
        self.context.create_fields = fields;
        self
    }

    /// 设置更新字段
    pub fn with_update_fields(mut self, fields: Vec<Value>) -> Self {
        self.context.update_fields = fields;
        self
    }

    /// 设置列表字段
    pub fn with_list_fields(mut self, fields: Vec<Value>) -> Self {
        self.context.list_fields = fields;
        self
    }

    /// 设置详情字段
    pub fn with_detail_fields(mut self, fields: Vec<Value>) -> Self {
        self.context.detail_fields = fields;
        self
    }

    /// 设置选择字段
    pub fn with_select_fields(mut self, fields: Vec<Value>) -> Self {
        self.context.select_fields = fields;
        self
    }

    /// 设置查询条件
    pub fn with_query_conditions(mut self, conditions: Vec<Value>) -> Self {
        self.context.query_conditions = conditions;
        self
    }

    /// 设置枚举
    pub fn with_enums(mut self, enums: Vec<Value>) -> Self {
        self.context.enums = enums;
        self
    }

    /// 添加导入
    pub fn add_import(mut self, import: String) -> Self {
        self.context.additional_imports.push(import);
        self
    }

    /// 设置扩展数据
    pub fn with_extra_data(mut self, key: String, value: Value) -> Self {
        self.context.extra_data.insert(key, value);
        self
    }

    /// 构建上下文
    pub fn build(self) -> GeneratorContext {
        self.context
    }
}

use crate::analyzer::AnalyzedTable;
use crate::generator::base::{BaseGenerator, GeneratorType};
use crate::generator::context::{ContextBuilder, GeneratorContext};
use crate::generator::field_strategy::FieldStrategyFactory;
use crate::generator::template_strategy::{DefaultTemplateStrategy, TemplateStrategy};
use anyhow::Result;

/// Repository生成器
pub struct RepositoryGenerator {
    template_strategy: DefaultTemplateStrategy,
}

impl RepositoryGenerator {
    pub fn new() -> Self {
        let template_strategy = DefaultTemplateStrategy::new("crates/libs/lib-codegen/templates/repository/repository.hbs".to_string());
        Self { template_strategy }
    }
}

impl Default for RepositoryGenerator {
    fn default() -> Self {
        Self::new()
    }
}

impl BaseGenerator for RepositoryGenerator {
    fn generator_type(&self) -> GeneratorType {
        GeneratorType::Repository
    }

    fn build_context(&self, table: &AnalyzedTable) -> Result<GeneratorContext> {
        let query_strategy = FieldStrategyFactory::get_strategy("repository_query");
        let query_conditions = FieldStrategyFactory::apply_strategy(query_strategy.as_ref(), &table.fields);
        
        let mut context = ContextBuilder::from_table(table)
            .with_query_conditions(query_conditions)
            .add_import("sea_orm::{ActiveModelTrait, ColumnTrait, Condition, ConnectionTrait, EntityTrait, PaginatorTrait, QueryFilter, QueryOrder}".to_string())
            .add_import("lib_macros::Service".to_string())
            .add_import("anyhow::{Context, Result}".to_string())
            .build();
            
        // 设置额外的Repository特定字段
        context.has_datetime_utils = table.has_created_at || table.has_updated_at;
        
        Ok(context)
    }

    fn render_template(&self, context: &GeneratorContext) -> Result<String> {
        self.template_strategy.render(context)
    }
} 
use crate::analyzer::{AnalyzedTable, TableAnalyzer};
use crate::generator::base::{BaseGenerator, GeneratorType};
use crate::generator::controller_generator::ControllerGenerator;
use crate::generator::dto_generator::DtoGenerator;
use crate::generator::entity_generator::EntityGenerator;
use crate::generator::repository_generator::RepositoryGenerator;
use crate::generator::service_generator::ServiceGenerator;
use crate::generator::vo_generator::VoGenerator;
use anyhow::Result;
use sea_orm::Database;
use std::collections::HashMap;

/// 生成器管理器 - 门面模式
pub struct GeneratorManager {
    generators: HashMap<GeneratorType, Box<dyn BaseGenerator>>,
}

impl GeneratorManager {
    /// 创建新的生成器管理器
    pub fn new() -> Self {
        let mut generators: HashMap<GeneratorType, Box<dyn BaseGenerator>> = HashMap::new();

        // 注册所有生成器
        generators.insert(GeneratorType::Entity, Box::new(EntityGenerator::new()));
        generators.insert(GeneratorType::Dto, Box::new(DtoGenerator::new()));
        generators.insert(GeneratorType::Vo, Box::new(VoGenerator::new()));
        generators.insert(
            GeneratorType::Controller,
            Box::new(ControllerGenerator::new()),
        );
        generators.insert(GeneratorType::Service, Box::new(ServiceGenerator::new()));
        generators.insert(
            GeneratorType::Repository,
            Box::new(RepositoryGenerator::new()),
        );

        Self { generators }
    }

    /// 生成单个类型的代码
    pub fn generate_single(
        &self,
        generator_type: GeneratorType,
        table: &AnalyzedTable,
    ) -> Result<()> {
        if let Some(generator) = self.generators.get(&generator_type) {
            generator.generate(table)?;
            Ok(())
        } else {
            Err(anyhow::anyhow!("生成器类型 {:?} 未找到", generator_type))
        }
    }

    /// 生成指定类型的所有代码
    pub fn generate_by_types(
        &self,
        generator_types: &[GeneratorType],
        table: &AnalyzedTable,
    ) -> Result<()> {
        for generator_type in generator_types {
            self.generate_single(*generator_type, table)?;
        }
        Ok(())
    }

    /// 生成完整的CRUD代码（所有类型）
    pub fn generate_full_crud(&self, table: &AnalyzedTable) -> Result<()> {
        let all_types = vec![
            GeneratorType::Entity,
            GeneratorType::Dto,
            GeneratorType::Vo,
            GeneratorType::Repository,
            GeneratorType::Service,
            GeneratorType::Controller,
        ];

        self.generate_by_types(&all_types, table)
    }

    /// 从表名生成完整的CRUD代码
    pub async fn generate_from_table_name(
        &self,
        table_name: &str,
        schema: &str,
        database_url: &str,
    ) -> Result<()> {
        println!("🔍 开始分析表: {}", table_name);

        // 分析表结构
        let conn = Database::connect(database_url).await?;
        let analyzer = TableAnalyzer::new(conn, Some(schema)).await?;
        let table = analyzer.analyze_table(table_name).await?;

        println!(
            "📊 表分析完成: {} -> {}",
            table.table_name, table.entity_name
        );
        println!("   - 字段数量: {}", table.total_fields);
        println!("   - 主键: {:?}", table.primary_key);

        // 生成完整CRUD代码
        self.generate_full_crud(&table)?;

        println!("✅ 表 '{}' 的完整CRUD代码生成完成!", table_name);
        Ok(())
    }

    /// 批量生成多个表的代码
    pub async fn generate_from_table_names(
        &self,
        table_names: &[&str],
        schema: &str,
        database_url: &str,
    ) -> Result<()> {
        let conn = Database::connect(database_url).await?;
        for table_name in table_names {
            let analyzer = TableAnalyzer::new(conn.clone(), Some(schema)).await?;
            let table = analyzer.analyze_table(table_name).await?;
            self.generate_full_crud(&table)?;
        }
        Ok(())
    }

    /// 生成数据库中所有表的代码
    pub async fn generate_all_tables(&self, database_url: &str, schema: &str) -> Result<()> {
        let conn = Database::connect(database_url).await?;
        // 默认public schema批量生成
        let analyzer = TableAnalyzer::new(conn.clone(), Some("public")).await?;
        let table_names = analyzer.get_all_table_names().await?;

        println!("🔍 发现 {} 个表，开始批量生成...", table_names.len());

        for table_name in &table_names {
            let analyzer = TableAnalyzer::new(conn.clone(), Some(schema)).await?;
            let table = analyzer.analyze_table(table_name).await?;
            self.generate_full_crud(&table)?;
        }

        println!("🎉 所有表的代码生成完成! 共处理 {} 个表", table_names.len());
        Ok(())
    }

    /// 获取可用的生成器类型
    pub fn available_generator_types(&self) -> Vec<GeneratorType> {
        self.generators.keys().cloned().collect()
    }

    /// 检查生成器是否可用
    pub fn is_generator_available(&self, generator_type: GeneratorType) -> bool {
        self.generators.contains_key(&generator_type)
    }
}

impl Default for GeneratorManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 便捷的生成器工厂函数
pub struct GeneratorFactory;

impl GeneratorFactory {
    /// 创建默认的生成器管理器
    pub fn create_manager() -> GeneratorManager {
        GeneratorManager::new()
    }

    /// 快速生成单个表的代码
    pub async fn quick_generate(table_name: &str, database_url: &str, schema: &str) -> Result<()> {
        let manager = Self::create_manager();
        manager
            .generate_from_table_name(table_name, schema, database_url)
            .await
    }

    /// 快速生成指定类型的代码
    pub async fn quick_generate_types(
        table_name: &str,
        generator_types: &[GeneratorType],
        schema: &str,
        database_url: &str,
    ) -> Result<()> {
        let manager = Self::create_manager();
        let conn = Database::connect(database_url).await?;
        let analyzer = TableAnalyzer::new(conn, Some(schema)).await?;
        let table = analyzer.analyze_table(table_name).await?;

        manager.generate_by_types(generator_types, &table)
    }
}

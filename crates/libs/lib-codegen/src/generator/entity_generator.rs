use crate::analyzer::AnalyzedTable;
use crate::generator::base::{BaseGenerator, GeneratorType};
use crate::generator::context::{ContextBuilder, GeneratorContext};
use crate::generator::field_strategy::FieldStrategyFactory;
use crate::generator::template_strategy::{DefaultTemplateStrategy, TemplateStrategy};
use anyhow::Result;

/// 实体生成器
pub struct EntityGenerator {
    template_strategy: DefaultTemplateStrategy,
}

impl EntityGenerator {
    /// 创建新的实体生成器
    pub fn new() -> Self {
        let template_strategy = DefaultTemplateStrategy::new("crates/libs/lib-codegen/templates/entity/model.hbs".to_string());
        Self { template_strategy }
    }
}

impl Default for EntityGenerator {
    fn default() -> Self {
        Self::new()
    }
}

impl BaseGenerator for EntityGenerator {
    fn generator_type(&self) -> GeneratorType {
        GeneratorType::Entity
    }

    fn build_context(&self, table: &AnalyzedTable) -> Result<GeneratorContext> {
        // 使用字段策略生成实体字段
        let entity_strategy = FieldStrategyFactory::get_strategy("entity");
        let fields = FieldStrategyFactory::apply_strategy(entity_strategy.as_ref(), &table.fields);

        // 构建上下文
        let context = ContextBuilder::from_table(table)
            .with_fields(fields)
            .build();

        Ok(context)
    }

    fn render_template(&self, context: &GeneratorContext) -> Result<String> {
        self.template_strategy.render(context)
    }
}
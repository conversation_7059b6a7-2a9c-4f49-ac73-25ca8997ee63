use crate::analyzer::AnalyzedTable;
use crate::generator::base::{BaseGenerator, GeneratorType};
use crate::generator::context::{ContextBuilder, GeneratorContext};
use crate::generator::template_strategy::{DefaultTemplateStrategy, TemplateStrategy};
use anyhow::Result;

/// Controller生成器
pub struct ControllerGenerator {
    template_strategy: DefaultTemplateStrategy,
}

impl ControllerGenerator {
    pub fn new() -> Self {
        let template_strategy = DefaultTemplateStrategy::new("crates/libs/lib-codegen/templates/controller/controller.hbs".to_string());
        Self { template_strategy }
    }
}

impl Default for ControllerGenerator {
    fn default() -> Self {
        Self::new()
    }
}

impl BaseGenerator for ControllerGenerator {
    fn generator_type(&self) -> GeneratorType {
        GeneratorType::Controller
    }

    fn build_context(&self, table: &AnalyzedTable) -> Result<GeneratorContext> {
        let context = ContextBuilder::from_table(table)
            .add_import("axum::{middleware, routing::{delete, get, put}, Router}".to_string())
            .add_import("lib_auth::require_permission".to_string())
            .add_import("lib_core::response::{ApiResult, PageData}".to_string())
            .add_import("lib_web::extractor::Component".to_string())
            .add_import("tracing::info".to_string())
            .add_import("utoipa::OpenApi".to_string())
            .add_import("uuid::Uuid".to_string())
            .build();
            
        Ok(context)
    }

    fn render_template(&self, context: &GeneratorContext) -> Result<String> {
        self.template_strategy.render(context)
    }
} 
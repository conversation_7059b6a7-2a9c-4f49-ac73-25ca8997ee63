pub mod base;
pub mod context;
pub mod field_strategy;
pub mod template_strategy;

// 各种生成器
pub mod entity_generator;
pub mod dto_generator;
pub mod vo_generator;
pub mod controller_generator;
pub mod service_generator;
pub mod repository_generator;

// 统一管理器
pub mod generator_manager;

pub use base::{BaseGenerator, GeneratorType};
pub use context::{GeneratorContext, ContextBuilder};
pub use field_strategy::{FieldStrategy, FieldPurpose};
pub use template_strategy::TemplateStrategy;
pub use generator_manager::GeneratorManager; 
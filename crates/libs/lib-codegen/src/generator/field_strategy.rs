use crate::analyzer::{AnalyzedField, FieldPurpose as AnalyzerFieldPurpose};
use serde_json::{json, Value};

/// 字段用途枚举（重新导出分析器的枚举）
pub use crate::analyzer::FieldPurpose;

/// 字段策略trait - 策略模式核心
pub trait FieldStrategy {
    /// 策略名称
    fn name(&self) -> &str;
    
    /// 是否应该包含此字段
    fn should_include(&self, field: &AnalyzedField) -> bool;
    
    /// 转换字段为JSON值
    fn convert_field(&self, field: &AnalyzedField) -> Value;
    
    /// 获取字段验证规则
    fn get_validation(&self, _field: &AnalyzedField) -> Option<String> {
        None
    }
    
    /// 获取字段示例值
    fn get_example(&self, _field: &AnalyzedField) -> Option<Value> {
        None
    }
    
    /// 获取字段默认值
    fn get_default(&self, _field: &AnalyzedField) -> Option<String> {
        None
    }
}

/// 实体字段策略 - 包含所有字段
pub struct EntityFieldStrategy;

impl FieldStrategy for EntityFieldStrategy {
    fn name(&self) -> &str {
        "entity"
    }
    
    fn should_include(&self, _field: &AnalyzedField) -> bool {
        true // 实体包含所有字段
    }
    
    fn convert_field(&self, field: &AnalyzedField) -> Value {
        json!({
            "name": field.column_name,
            "rust_type": field.rust_type,
            "comment": field.comment,
            "is_primary_key": field.is_primary_key,
            "is_nullable": field.is_nullable,
            "auto_increment": field.is_primary_key && field.rust_type.contains("i32"),
            "is_created_at": matches!(field.purpose, AnalyzerFieldPurpose::CreatedAt),
            "is_updated_at": matches!(field.purpose, AnalyzerFieldPurpose::UpdatedAt),
            "is_deleted_at": matches!(field.purpose, AnalyzerFieldPurpose::DeletedAt),
            "purpose": format!("{:?}", field.purpose)
        })
    }
}

/// DTO页面查询字段策略 - 主要查询字段
pub struct DtoPageFieldStrategy;

impl FieldStrategy for DtoPageFieldStrategy {
    fn name(&self) -> &str {
        "dto_page"
    }
    
    fn should_include(&self, field: &AnalyzedField) -> bool {
        matches!(field.purpose, 
            AnalyzerFieldPurpose::Name |
            AnalyzerFieldPurpose::Status |
            AnalyzerFieldPurpose::Id |
            AnalyzerFieldPurpose::ForeignKey
        )
    }
    
    fn convert_field(&self, field: &AnalyzedField) -> Value {
        let optional_type = if field.rust_type.starts_with("Option<") {
            field.rust_type.clone()
        } else {
            format!("Option<{}>", field.rust_type)
        };
        
        json!({
            "name": field.column_name,
            "rust_type": optional_type,
            "comment": field.comment,
            "validation": self.get_validation(field),
            "schema_example": self.get_example(field)
        })
    }
    
    fn get_validation(&self, field: &AnalyzedField) -> Option<String> {
        match field.purpose {
            AnalyzerFieldPurpose::Name => Some(r#"length(max = 100, message = "名称长度不能超过100个字符")"#.to_string()),
            _ => None
        }
    }
    
    fn get_example(&self, field: &AnalyzedField) -> Option<Value> {
        match field.purpose {
            AnalyzerFieldPurpose::Name => Some(json!("示例名称")),
            AnalyzerFieldPurpose::Status => Some(json!(1)),
            AnalyzerFieldPurpose::Id => Some(json!("550e8400-e29b-41d4-a716-446655440000")),
            _ => None
        }
    }
}

/// DTO创建字段策略 - 排除自动生成字段
pub struct DtoCreateFieldStrategy;

impl FieldStrategy for DtoCreateFieldStrategy {
    fn name(&self) -> &str {
        "dto_create"
    }
    
    fn should_include(&self, field: &AnalyzedField) -> bool {
        !matches!(field.purpose, 
            AnalyzerFieldPurpose::Id |
            AnalyzerFieldPurpose::CreatedAt |
            AnalyzerFieldPurpose::UpdatedAt |
            AnalyzerFieldPurpose::DeletedAt
        )
    }
    
    fn convert_field(&self, field: &AnalyzedField) -> Value {
        json!({
            "name": field.column_name,
            "rust_type": field.rust_type,
            "comment": field.comment,
            "validation": self.get_validation(field),
            "schema_example": self.get_example(field),
            "builder_default": self.get_default(field)
        })
    }
    
    fn get_validation(&self, field: &AnalyzedField) -> Option<String> {
        match field.purpose {
            AnalyzerFieldPurpose::Name => Some(r#"length(min = 1, max = 100, message = "名称长度必须在1-100个字符之间")"#.to_string()),
            AnalyzerFieldPurpose::Status => Some(r#"range(min = 0, max = 3, message = "状态值必须在0-3之间")"#.to_string()),
            _ => None
        }
    }
    
    fn get_example(&self, field: &AnalyzedField) -> Option<Value> {
        match field.purpose {
            AnalyzerFieldPurpose::Name => Some(json!("新建项目")),
            AnalyzerFieldPurpose::Status => Some(json!(1)),
            _ => None
        }
    }
    
    fn get_default(&self, field: &AnalyzedField) -> Option<String> {
        match field.purpose {
            AnalyzerFieldPurpose::Status => Some("Some(1)".to_string()),
            _ => None
        }
    }
}

/// DTO更新字段策略 - 排除ID和时间戳
pub struct DtoUpdateFieldStrategy;

impl FieldStrategy for DtoUpdateFieldStrategy {
    fn name(&self) -> &str {
        "dto_update"
    }
    
    fn should_include(&self, field: &AnalyzedField) -> bool {
        !matches!(field.purpose, 
            AnalyzerFieldPurpose::Id |
            AnalyzerFieldPurpose::CreatedAt |
            AnalyzerFieldPurpose::UpdatedAt |
            AnalyzerFieldPurpose::DeletedAt
        )
    }
    
    fn convert_field(&self, field: &AnalyzedField) -> Value {
        let optional_type = if field.rust_type.starts_with("Option<") {
            field.rust_type.clone()
        } else {
            format!("Option<{}>", field.rust_type)
        };
        
        json!({
            "name": field.column_name,
            "rust_type": optional_type,
            "comment": field.comment,
            "validation": self.get_validation(field),
            "schema_example": self.get_example(field)
        })
    }
    
    fn get_validation(&self, field: &AnalyzedField) -> Option<String> {
        match field.purpose {
            AnalyzerFieldPurpose::Name => Some(r#"length(min = 1, max = 100, message = "名称长度必须在1-100个字符之间")"#.to_string()),
            _ => None
        }
    }
    
    fn get_example(&self, field: &AnalyzedField) -> Option<Value> {
        match field.purpose {
            AnalyzerFieldPurpose::Name => Some(json!("更新后的名称")),
            AnalyzerFieldPurpose::Status => Some(json!(2)),
            _ => None
        }
    }
}

/// VO列表字段策略 - 基本显示字段
pub struct VoListFieldStrategy;

impl FieldStrategy for VoListFieldStrategy {
    fn name(&self) -> &str {
        "vo_list"
    }
    
    fn should_include(&self, field: &AnalyzedField) -> bool {
        matches!(field.purpose, 
            AnalyzerFieldPurpose::Id |
            AnalyzerFieldPurpose::Name |
            AnalyzerFieldPurpose::Status |
            AnalyzerFieldPurpose::CreatedAt
        )
    }
    
    fn convert_field(&self, field: &AnalyzedField) -> Value {
        json!({
            "name": field.column_name,
            "rust_type": field.rust_type,
            "comment": field.comment,
            "schema_example": self.get_example(field)
        })
    }
    
    fn get_example(&self, field: &AnalyzedField) -> Option<Value> {
        match field.purpose {
            AnalyzerFieldPurpose::Id => Some(json!("550e8400-e29b-41d4-a716-446655440000")),
            AnalyzerFieldPurpose::Name => Some(json!("示例名称")),
            AnalyzerFieldPurpose::Status => Some(json!(1)),
            AnalyzerFieldPurpose::CreatedAt => Some(json!("2023-01-01T00:00:00+08:00")),
            _ => None
        }
    }
}

/// VO详情字段策略 - 所有字段除了删除时间
pub struct VoDetailFieldStrategy;

impl FieldStrategy for VoDetailFieldStrategy {
    fn name(&self) -> &str {
        "vo_detail"
    }
    
    fn should_include(&self, field: &AnalyzedField) -> bool {
        !matches!(field.purpose, AnalyzerFieldPurpose::DeletedAt)
    }
    
    fn convert_field(&self, field: &AnalyzedField) -> Value {
        json!({
            "name": field.column_name,
            "rust_type": field.rust_type,
            "comment": field.comment,
            "schema_example": self.get_example(field)
        })
    }
    
    fn get_example(&self, field: &AnalyzedField) -> Option<Value> {
        match field.purpose {
            AnalyzerFieldPurpose::Id => Some(json!("550e8400-e29b-41d4-a716-446655440000")),
            AnalyzerFieldPurpose::Name => Some(json!("详情示例名称")),
            AnalyzerFieldPurpose::Status => Some(json!(1)),
            AnalyzerFieldPurpose::CreatedAt => Some(json!("2023-01-01T00:00:00+08:00")),
            AnalyzerFieldPurpose::UpdatedAt => Some(json!("2023-01-02T00:00:00+08:00")),
            _ => None
        }
    }
}

/// Repository查询条件策略
pub struct RepositoryQueryFieldStrategy;

impl FieldStrategy for RepositoryQueryFieldStrategy {
    fn name(&self) -> &str {
        "repository_query"
    }
    
    fn should_include(&self, field: &AnalyzedField) -> bool {
        matches!(field.purpose, 
            AnalyzerFieldPurpose::Name |
            AnalyzerFieldPurpose::Status |
            AnalyzerFieldPurpose::Id |
            AnalyzerFieldPurpose::ForeignKey
        )
    }
    
    fn convert_field(&self, field: &AnalyzedField) -> Value {
        let (is_string, is_enum, is_id) = match field.purpose {
            AnalyzerFieldPurpose::Name => (true, false, false),
            AnalyzerFieldPurpose::Status => (false, true, false),
            AnalyzerFieldPurpose::Id | AnalyzerFieldPurpose::ForeignKey => (false, false, true),
            _ => (false, false, false)
        };
        
        json!({
            "field_name": field.column_name,
            "column_name": field.column_name.to_uppercase(),
            "is_string": is_string,
            "is_enum": is_enum,
            "is_id": is_id
        })
    }
}

/// 字段策略工厂
pub struct FieldStrategyFactory;

impl FieldStrategyFactory {
    /// 获取字段策略
    pub fn get_strategy(strategy_type: &str) -> Box<dyn FieldStrategy> {
        match strategy_type {
            "entity" => Box::new(EntityFieldStrategy),
            "dto_page" => Box::new(DtoPageFieldStrategy),
            "dto_create" => Box::new(DtoCreateFieldStrategy),
            "dto_update" => Box::new(DtoUpdateFieldStrategy),
            "vo_list" => Box::new(VoListFieldStrategy),
            "vo_detail" => Box::new(VoDetailFieldStrategy),
            "repository_query" => Box::new(RepositoryQueryFieldStrategy),
            _ => Box::new(EntityFieldStrategy) // 默认策略
        }
    }
    
    /// 应用策略到字段列表
    pub fn apply_strategy(strategy: &dyn FieldStrategy, fields: &[AnalyzedField]) -> Vec<Value> {
        fields.iter()
            .filter(|field| strategy.should_include(field))
            .map(|field| strategy.convert_field(field))
            .collect()
    }
} 
use crate::generator::context::GeneratorContext;
use anyhow::Result;
use handlebars::{
    Context, <PERSON><PERSON><PERSON><PERSON>, Helper, HelperResult, Output, RenderContext, RenderError,
    RenderErrorReason,
};

/// 模板策略trait
pub trait TemplateStrategy {
    /// 获取模板路径
    fn template_path(&self) -> String;

    /// 注册自定义助手
    fn register_helpers(&self, handlebars: &mut Handlebars) -> Result<()> {
        // 默认注册通用助手
        self.register_common_helpers(handlebars)?;

        // 子类可以重写此方法添加特定助手
        self.register_custom_helpers(handlebars)
    }

    /// 注册通用助手
    fn register_common_helpers(&self, handlebars: &mut Handlebars) -> Result<()> {
        // 字符串转换助手
        handlebars.register_helper("snake_case", Box::new(snake_case_helper));
        handlebars.register_helper("pascal_case", Box::new(pascal_case_helper));
        handlebars.register_helper("camel_case", Box::new(camel_case_helper));

        // 类型检查助手
        handlebars.register_helper("is_option", Box::new(is_option_helper));
        handlebars.register_helper("unwrap_option", Box::new(unwrap_option_helper));

        // 条件助手
        handlebars.register_helper("eq", Box::new(eq_helper));
        handlebars.register_helper("ne", Box::new(ne_helper));

        Ok(())
    }

    /// 注册自定义助手（由子类实现）
    fn register_custom_helpers(&self, _handlebars: &mut Handlebars) -> Result<()> {
        Ok(())
    }

    /// 渲染模板
    fn render(&self, context: &GeneratorContext) -> Result<String> {
        let mut handlebars = Handlebars::new();

        // 禁用HTML转义，因为我们生成的是Rust代码而不是HTML
        handlebars.register_escape_fn(handlebars::no_escape);

        // 注册助手
        self.register_helpers(&mut handlebars)?;

        println!("{:?}", &self.template_path());

        // 读取模板文件
        let template_content = std::fs::read_to_string(&self.template_path())
            .map_err(|e| anyhow::anyhow!("读取模板文件失败: {}", e))?;

        // 注册模板
        handlebars.register_template_string("main", template_content)?;

        // 渲染
        let rendered = handlebars.render("main", context)?;

        Ok(rendered)
    }
}

/// 默认模板策略
pub struct DefaultTemplateStrategy {
    template_path: String,
}

impl DefaultTemplateStrategy {
    pub fn new(template_path: String) -> Self {
        Self { template_path }
    }
}

impl TemplateStrategy for DefaultTemplateStrategy {
    fn template_path(&self) -> String {
        self.template_path.clone()
    }
}

// ============================================================================
// Handlebars 助手函数
// ============================================================================

/// 转换为snake_case
fn snake_case_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    if let Some(param) = h.param(0) {
        if let Some(value) = param.value().as_str() {
            use crate::utils::StringUtils;
            let snake_case = StringUtils::to_snake_case(value);
            out.write(&snake_case)?;
        }
    }
    Ok(())
}

/// 转换为PascalCase
fn pascal_case_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    if let Some(param) = h.param(0) {
        if let Some(value) = param.value().as_str() {
            use crate::utils::StringUtils;
            let pascal_case = StringUtils::to_pascal_case(value);
            out.write(&pascal_case)?;
        }
    }
    Ok(())
}

/// 转换为camelCase
fn camel_case_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    if let Some(param) = h.param(0) {
        if let Some(value) = param.value().as_str() {
            use crate::utils::StringUtils;
            let camel_case = StringUtils::to_camel_case(value);
            out.write(&camel_case)?;
        }
    }
    Ok(())
}

/// 检查是否为Option类型
fn is_option_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    _: &mut dyn Output,
) -> HelperResult {
    if let Some(param) = h.param(0) {
        if let Some(value) = param.value().as_str() {
            return if value.starts_with("Option<") {
                Ok(())
            } else {
                Err(RenderError::from(RenderErrorReason::Other(
                    "Not an Option type".to_string(),
                )))
            };
        }
    }
    Err(RenderError::from(RenderErrorReason::Other(
        "Invalid parameter".to_string(),
    )))
}

/// 解包Option类型
fn unwrap_option_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    out: &mut dyn Output,
) -> HelperResult {
    if let Some(param) = h.param(0) {
        if let Some(value) = param.value().as_str() {
            if value.starts_with("Option<") && value.ends_with(">") {
                let inner_type = &value[7..value.len() - 1];
                out.write(inner_type)?;
            } else {
                out.write(value)?;
            }
        }
    }
    Ok(())
}

/// 相等比较助手
fn eq_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    _: &mut dyn Output,
) -> HelperResult {
    if let (Some(left), Some(right)) = (h.param(0), h.param(1)) {
        return if left.value() == right.value() {
            Ok(())
        } else {
            Err(RenderError::from(RenderErrorReason::Other(
                "Values are not equal".to_string(),
            )))
        };
    }
    Err(RenderError::from(RenderErrorReason::Other(
        "Invalid parameters".to_string(),
    )))
}

/// 不相等比较助手
fn ne_helper(
    h: &Helper,
    _: &Handlebars,
    _: &Context,
    _: &mut RenderContext,
    _: &mut dyn Output,
) -> HelperResult {
    if let (Some(left), Some(right)) = (h.param(0), h.param(1)) {
        return if left.value() != right.value() {
            Ok(())
        } else {
            Err(RenderError::from(RenderErrorReason::Other(
                "Values are equal".to_string(),
            )))
        };
    }
    Err(RenderError::from(RenderErrorReason::Other(
        "Invalid parameters".to_string(),
    )))
}

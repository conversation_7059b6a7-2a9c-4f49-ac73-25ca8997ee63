use crate::analyzer::AnalyzedTable;
use crate::generator::base::{BaseGenerator, GeneratorType};
use crate::generator::context::{ContextBuilder, GeneratorContext};
use crate::generator::template_strategy::{DefaultTemplateStrategy, TemplateStrategy};
use anyhow::Result;

/// Service生成器
pub struct ServiceGenerator {
    template_strategy: DefaultTemplateStrategy,
}

impl ServiceGenerator {
    pub fn new() -> Self {
        let template_strategy = DefaultTemplateStrategy::new("crates/libs/lib-codegen/templates/service/service.hbs".to_string());
        Self { template_strategy }
    }
}

impl Default for ServiceGenerator {
    fn default() -> Self {
        Self::new()
    }
}

impl BaseGenerator for ServiceGenerator {
    fn generator_type(&self) -> GeneratorType {
        GeneratorType::Service
    }

    fn build_context(&self, table: &AnalyzedTable) -> Result<GeneratorContext> {
        let context = ContextBuilder::from_table(table)
            .add_import("lib_macros::Service".to_string())
            .add_import("lib_core::BusinessError".to_string())
            .add_import("sea_orm::{DatabaseConnection, Set}".to_string())
            .add_import("tracing::{info, warn}".to_string())
            .add_import("uuid::Uuid".to_string())
            .build();
            
        Ok(context)
    }

    fn render_template(&self, context: &GeneratorContext) -> Result<String> {
        self.template_strategy.render(context)
    }
} 
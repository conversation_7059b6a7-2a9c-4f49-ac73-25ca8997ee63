use crate::analyzer::AnalyzedTable;
use crate::generator::context::GeneratorContext;
use crate::utils::{CodeFormatter, FileUtils};
use anyhow::Result;
use std::fs;
use std::path::Path;

/// 生成器类型枚举
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub enum GeneratorType {
    Entity,
    Dto,
    Vo,
    Controller,
    Service,
    Repository,
}

impl GeneratorType {
    /// 获取生成器名称
    pub fn name(&self) -> &str {
        match self {
            GeneratorType::Entity => "Entity",
            GeneratorType::Dto => "DTO",
            GeneratorType::Vo => "VO",
            GeneratorType::Controller => "Controller",
            GeneratorType::Service => "Service",
            GeneratorType::Repository => "Repository",
        }
    }

    /// 获取模板名称
    pub fn template_name(&self) -> &str {
        match self {
            GeneratorType::Entity => "entity/model.hbs",
            GeneratorType::Dto => "dto/request.hbs",
            GeneratorType::Vo => "vo/response.hbs",
            GeneratorType::Controller => "controller/controller.hbs",
            GeneratorType::Service => "service/service.hbs",
            GeneratorType::Repository => "repository/repository.hbs",
        }
    }

    /// 获取输出目录
    pub fn output_dir(&self) -> &str {
        match self {
            GeneratorType::Entity => "entities",
            GeneratorType::Dto => "dto",
            GeneratorType::Vo => "vo",
            GeneratorType::Controller => "controller",
            GeneratorType::Service => "service",
            GeneratorType::Repository => "repository",
        }
    }

    /// 获取文件后缀
    pub fn file_suffix(&self) -> &str {
        match self {
            GeneratorType::Entity => ".rs",
            GeneratorType::Dto => "_request.rs",
            GeneratorType::Vo => "_vo.rs",
            GeneratorType::Controller => "_controller.rs",
            GeneratorType::Service => "_service.rs",
            GeneratorType::Repository => "_repository.rs",
        }
    }
}

/// 基础生成器trait - 使用模板方法模式
pub trait BaseGenerator {
    /// 获取生成器类型
    fn generator_type(&self) -> GeneratorType;

    /// 生成文件（模板方法）
    fn generate(&self, table: &AnalyzedTable) -> Result<()> {
        // 1. 构建上下文
        let context = self.build_context(table)?;
        
        // 2. 渲染模板
        let content = self.render_template(&context)?;
        
        // 3. 格式化代码
        let formatted_content = self.format_code(&content)?;
        
        // 4. 写入文件
        self.write_file(&context, &formatted_content)?;
        
        // 5. 输出成功信息
        self.log_success(table);
        
        Ok(())
    }

    /// 构建上下文（由子类实现）
    fn build_context(&self, table: &AnalyzedTable) -> Result<GeneratorContext>;

    /// 渲染模板（通用实现）
    fn render_template(&self, context: &GeneratorContext) -> Result<String> {
        use handlebars::Handlebars;
        
        let mut hbs = Handlebars::new();
        
        // 读取模板文件
        let template_path = format!("templates/{}", self.generator_type().template_name());
        let template_content = std::fs::read_to_string(&template_path)
            .map_err(|e| anyhow::anyhow!("读取模板文件失败: {}", e))?;
        
        // 注册模板
        hbs.register_template_string("template", template_content)?;
        
        // 渲染
        let rendered = hbs.render("template", context)?;
        
        Ok(rendered)
    }

    /// 格式化代码（通用实现）
    fn format_code(&self, content: &str) -> Result<String> {
        CodeFormatter::format_code(content)
    }

    /// 写入文件（通用实现）
    fn write_file(&self, context: &GeneratorContext, content: &str) -> Result<()> {
        let output_path = self.get_output_path(context);
        
        // 创建目录
        if let Some(parent) = Path::new(&output_path).parent() {
            fs::create_dir_all(parent)?;
        }
        
        // 写入文件
        FileUtils::write_file(&output_path, content)?;
        
        Ok(())
    }

    /// 获取输出路径（通用实现）
    fn get_output_path(&self, context: &GeneratorContext) -> String {
        let generator_type = self.generator_type();
        format!(
            "crates/libs/lib-codegen/generated/{}/{}/{}{}",
            context.module_type,
            generator_type.output_dir(),
            context.entity_snake,
            generator_type.file_suffix()
        )
    }

    /// 输出成功信息（通用实现）
    fn log_success(&self, table: &AnalyzedTable) {
        println!(
            "✅ Generated {} for table '{}': entity '{}'",
            self.generator_type().name(),
            table.table_name,
            table.entity_name
        );
    }
} 
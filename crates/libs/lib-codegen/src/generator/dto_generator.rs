use crate::analyzer::AnalyzedTable;
use crate::generator::base::{BaseGenerator, GeneratorType};
use crate::generator::context::{ContextBuilder, GeneratorContext};
use crate::generator::field_strategy::FieldStrategyFactory;
use crate::generator::template_strategy::{DefaultTemplateStrategy, TemplateStrategy};
use anyhow::Result;
use serde_json::json;

/// DTO生成器
pub struct DtoGenerator {
    template_strategy: DefaultTemplateStrategy,
}

impl DtoGenerator {
    /// 创建新的DTO生成器
    pub fn new() -> Self {
        let template_strategy = DefaultTemplateStrategy::new("crates/libs/lib-codegen/templates/dto/request.hbs".to_string());
        Self { template_strategy }
    }
    
    /// 生成示例数据
    fn generate_examples(&self, _table: &AnalyzedTable) -> (serde_json::Value, serde_json::Value) {
        let create_example = json!({
            "username": "新用户",
            "real_name": "真实姓名",
            "email": "<EMAIL>",
            "status": 1
        });
        
        let update_example = json!({
            "real_name": "更新后的姓名",
            "email": "<EMAIL>",
            "status": 2
        });
        
        (create_example, update_example)
    }
}

impl Default for DtoGenerator {
    fn default() -> Self {
        Self::new()
    }
}

impl BaseGenerator for DtoGenerator {
    fn generator_type(&self) -> GeneratorType {
        GeneratorType::Dto
    }

    fn build_context(&self, table: &AnalyzedTable) -> Result<GeneratorContext> {
        // 使用不同的字段策略生成不同类型的字段
        let page_strategy = FieldStrategyFactory::get_strategy("dto_page");
        let create_strategy = FieldStrategyFactory::get_strategy("dto_create");
        let update_strategy = FieldStrategyFactory::get_strategy("dto_update");
        
        let page_fields = FieldStrategyFactory::apply_strategy(page_strategy.as_ref(), &table.fields);
        let create_fields = FieldStrategyFactory::apply_strategy(create_strategy.as_ref(), &table.fields);
        let update_fields = FieldStrategyFactory::apply_strategy(update_strategy.as_ref(), &table.fields);
        
        // 生成示例数据
        let (create_example, update_example) = self.generate_examples(table);
        
        // 构建上下文
        let mut context = ContextBuilder::from_table(table)
            .with_page_fields(page_fields)
            .with_create_fields(create_fields)
            .with_update_fields(update_fields)
            .build();
            
        // 设置示例数据
        context.create_example = create_example;
        context.update_example = update_example;
            
        Ok(context)
    }

    fn render_template(&self, context: &GeneratorContext) -> Result<String> {
        self.template_strategy.render(context)
    }
}
use anyhow::Result;
use inflector::Inflector;
use sea_orm::{sea_query::Value, DatabaseConnection, Statement};
use sea_orm::ConnectionTrait;
use serde::{Deserialize, Serialize};


/// 字段用途枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum FieldPurpose {
    /// 主键ID字段
    Id,
    /// 名称字段
    Name,
    /// 状态字段
    Status,
    /// 创建时间
    CreatedAt,
    /// 更新时间
    UpdatedAt,
    /// 删除时间（软删除）
    DeletedAt,
    /// 外键字段
    ForeignKey,
    /// 其他字段
    Other,
}

/// 分析后的字段信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalyzedField {
    /// 字段名
    pub column_name: String,
    /// Rust类型
    pub rust_type: String,
    /// 字段注释
    pub comment: String,
    /// 是否为主键
    pub is_primary_key: bool,
    /// 是否可为空
    pub is_nullable: bool,
    /// 字段用途
    pub purpose: FieldPurpose,
}

/// 分析后的表信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalyzedTable {
    // 模式名
    pub schema_name: String,
    /// 表名
    pub table_name: String,
    /// 实体名（PascalCase）
    pub entity_name: String,
    /// 实体注释
    pub entity_comment: String,
    /// 实体蛇形命名
    pub entity_snake: String,
    /// 实体复数形式
    pub entity_plural: String,
    /// 实体复数蛇形命名
    pub entity_plural_snake: String,
    /// 主键字段名
    pub primary_key: Option<String>,
    /// 字段列表
    pub fields: Vec<AnalyzedField>,
    /// 是否有创建时间字段
    pub has_created_at: bool,
    /// 是否有更新时间字段
    pub has_updated_at: bool,
    /// 是否有删除时间字段
    pub has_deleted_at: bool,
    /// 是否有状态字段
    pub has_status: bool,
    /// 是否支持软删除
    pub is_soft_delete: bool,
    /// 统计信息
    pub total_fields: usize,
    /// tags 文档标签
    pub tags: String,
    pub id_fields: usize,
    pub name_fields: usize,
    pub status_fields: usize,
    pub foreign_key_fields: usize,
    pub datetime_fields: usize,
    pub other_fields: usize,
}

/// 表分析器
pub struct TableAnalyzer {
    pub conn: DatabaseConnection,
    pub schema: String,
}

impl TableAnalyzer {
    /// 创建新的表分析器，schema可选，默认public
    pub async fn new(conn: DatabaseConnection, schema: Option<&str>) -> Result<Self> {
        Ok(Self {
            conn,
            schema: schema.unwrap_or("public").to_string(),
        })
    }

    /// 获取所有表名（指定schema）
    pub async fn get_all_table_names(&self) -> Result<Vec<String>> {
        let sql = format!(
            "SELECT table_name FROM information_schema.tables WHERE table_schema = '{}' AND table_type = 'BASE TABLE' ORDER BY table_name",
            self.schema
        );
        let rows = self
            .conn
            .query_all(Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                sql,
            ))
            .await?;
        let mut names = Vec::new();
        for row in rows {
            if let Ok(name) = row.try_get_by_index::<String>(0) {
                names.push(name);
            }
        }
        Ok(names)
    }

    /// 分析单个表，获取真实结构
    pub async fn analyze_table(&self, table_name: &str) -> Result<AnalyzedTable> {
        // 查询字段信息
        let sql = format!(
            "SELECT c.column_name, c.data_type, c.is_nullable, c.column_default, pgd.description \
             FROM information_schema.columns c \
             LEFT JOIN pg_catalog.pg_class pgc on pgc.relname = c.table_name \
             LEFT JOIN pg_catalog.pg_namespace pgn on pgn.oid = pgc.relnamespace and pgn.nspname = c.table_schema \
             LEFT JOIN pg_catalog.pg_description pgd on pgd.objoid = pgc.oid and pgd.objsubid = c.ordinal_position \
             WHERE c.table_schema = '{}' AND c.table_name = $1 ORDER BY c.ordinal_position",
            self.schema
        );
        let rows = self
            .conn
            .query_all(Statement::from_sql_and_values(
                sea_orm::DatabaseBackend::Postgres,
                &sql,
                vec![Value::String(Some(Box::new(table_name.to_string())))],
            ))
            .await?;

        // 查询表注释
        let table_comment_sql = r#"
            SELECT pd.description
            FROM pg_catalog.pg_class c
            JOIN pg_catalog.pg_namespace n ON n.oid = c.relnamespace
            LEFT JOIN pg_catalog.pg_description pd ON pd.objoid = c.oid AND pd.objsubid = 0
            WHERE c.relname = $1 AND n.nspname = $2
        "#;
        let table_comment_rows = self
            .conn
            .query_all(Statement::from_sql_and_values(
                sea_orm::DatabaseBackend::Postgres,
                table_comment_sql,
                vec![
                    Value::String(Some(Box::new(table_name.to_string()))),
                    Value::String(Some(Box::new(self.schema.clone()))),
                ],
            ))
            .await?;
        let entity_comment = table_comment_rows
            .get(0)
            .and_then(|row| row.try_get_by_index::<String>(0).ok())
            .unwrap_or_else(|| {
                let entity_name = Self::to_pascal_case(table_name);
                entity_name
            });

        let entity_comment = Self::clean_table_comment(entity_comment.as_str());

        // 查询主键
        let pk_sql = format!(
            r#"
            SELECT a.attname
            FROM pg_index i
            JOIN pg_attribute a ON a.attrelid = i.indrelid AND a.attnum = ANY(i.indkey)
            WHERE i.indrelid = '"{}"."{}"'::regclass AND i.indisprimary
            "#,
            self.schema, table_name
        );
        let pk_rows = self
            .conn
            .query_all(Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                pk_sql,
            ))
            .await?;
        let primary_key = pk_rows
            .get(0)
            .and_then(|row| row.try_get_by_index::<String>(0).ok());

        // 字段分析
        let mut fields = Vec::new();
        let mut has_created_at = false;
        let mut has_updated_at = false;
        let mut has_deleted_at = false;
        let mut has_status = false;
        let mut is_soft_delete = false;
        let mut id_fields = 0;
        let mut name_fields = 0;
        let mut status_fields = 0;
        let mut foreign_key_fields = 0;
        let mut datetime_fields = 0;
        let mut other_fields = 0;

        for row in &rows {
            let column_name: String = row.try_get_by_index(0)?;
            let data_type: String = row.try_get_by_index(1)?;
            let is_nullable: String = row.try_get_by_index(2)?;
            let _default: Option<String> = row.try_get_by_index(3).ok();
            let comment: Option<String> = row.try_get_by_index(4).ok();

            // 类型推断
            let rust_type = map_pg_type_to_rust(&data_type, is_nullable == "YES");
            // 用途推断
            let (purpose, is_pk) =
                infer_field_purpose(&column_name, &data_type, primary_key.as_deref());
            match purpose {
                FieldPurpose::Id => id_fields += 1,
                FieldPurpose::Name => name_fields += 1,
                FieldPurpose::Status => {
                    has_status = true;
                    status_fields += 1;
                }
                FieldPurpose::CreatedAt => {
                    has_created_at = true;
                    datetime_fields += 1;
                }
                FieldPurpose::UpdatedAt => {
                    has_updated_at = true;
                    datetime_fields += 1;
                }
                FieldPurpose::DeletedAt => {
                    has_deleted_at = true;
                    is_soft_delete = true;
                    datetime_fields += 1;
                }
                FieldPurpose::ForeignKey => foreign_key_fields += 1,
                FieldPurpose::Other => other_fields += 1,
            }
            if matches!(
                purpose,
                FieldPurpose::CreatedAt | FieldPurpose::UpdatedAt | FieldPurpose::DeletedAt
            ) {
                datetime_fields += 1;
            }
            fields.push(AnalyzedField {
                column_name: column_name.clone(),
                rust_type,
                comment: comment.unwrap_or_default(),
                is_primary_key: is_pk,
                is_nullable: is_nullable == "YES",
                purpose,
            });
        }

        let entity_name = Self::to_pascal_case(table_name);
        let entity_snake = Self::to_snake_case(&entity_name);
        let entity_plural = entity_name.to_plural();
        let entity_plural_snake = Self::to_snake_case(&entity_plural);
        let tags = Self::clean_table_comment(entity_comment.as_str()) + "管理";

        Ok(AnalyzedTable {
            schema_name: self.schema.clone(),
            table_name: table_name.to_string(),
            entity_name,
            entity_comment,
            entity_snake,
            entity_plural,
            entity_plural_snake,
            primary_key,
            has_created_at,
            has_updated_at,
            has_deleted_at,
            has_status,
            is_soft_delete,
            total_fields: fields.len(),
            tags,
            id_fields,
            name_fields,
            status_fields,
            foreign_key_fields,
            datetime_fields,
            other_fields,
            fields,
        })
    }

    /// 转换为PascalCase
    fn to_pascal_case(input: &str) -> String {
        input
            .split('_')
            .map(|word| {
                let mut chars = word.chars();
                match chars.next() {
                    None => String::new(),
                    Some(first) => {
                        first.to_uppercase().collect::<String>() + &chars.as_str().to_lowercase()
                    }
                }
            })
            .collect()
    }

    /// 转换为snake_case
    fn to_snake_case(input: &str) -> String {
        let mut result = String::new();
        for (i, ch) in input.chars().enumerate() {
            if ch.is_uppercase() && i > 0 {
                result.push('_');
            }
            result.push(ch.to_lowercase().next().unwrap());
        }
        result
    }

    // 处理表注释，如中存在  ‘表’  去除
    fn clean_table_comment(comment: &str) -> String {
        comment.replace("表", "").trim().to_string()
    }
}

/// Postgres类型到Rust类型映射
fn map_pg_type_to_rust(pg_type: &str, is_nullable: bool) -> String {
    let base = match pg_type {
        "uuid" => "Uuid",
        "varchar" | "text" | "char" => "String",
        "int4" | "integer" => "i32",
        "int8" | "bigint" => "i64",
        "int2" | "smallint" => "i16",
        "bool" | "boolean" => "bool",
        "timestamp" | "timestamptz" | "timestamp with time zone" => "DateTimeWithTimeZone",
        "date" => "NaiveDate",
        "time" => "NaiveTime",
        "decimal" | "numeric" => "Decimal",
        "float4" | "real" => "f32",
        "float8" | "double precision" => "f64",
        "json" | "jsonb" => "serde_json::Value",
        _ => "String",
    };
    if is_nullable {
        format!("Option<{}>", base)
    } else {
        base.to_string()
    }
}

/// 字段用途推断
fn infer_field_purpose(
    column_name: &str,
    data_type: &str,
    pk: Option<&str>,
) -> (FieldPurpose, bool) {
    let name = column_name.to_lowercase();
    if let Some(pk_name) = pk {
        if name == pk_name.to_lowercase() {
            return (FieldPurpose::Id, true);
        }
    }
    if name == "name" {
        return (FieldPurpose::Name, false);
    }
    if name == "status" {
        return (FieldPurpose::Status, false);
    }
    if name == "created_at" || name == "created_date" {
        return (FieldPurpose::CreatedAt, false);
    }
    if name == "updated_at" || name == "updated_date" {
        return (FieldPurpose::UpdatedAt, false);
    }
    if name == "deleted_at" || name == "deleted_date" {
        return (FieldPurpose::DeletedAt, false);
    }
    if name.ends_with("_id") && data_type == "uuid" {
        return (FieldPurpose::ForeignKey, false);
    }
    (FieldPurpose::Other, false)
}

use lib_codegen::{GeneratorManager, GeneratorType};
use std::env;

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    println!("🎯 lib-codegen 重构架构演示");
    println!("========================================");

    // 从环境变量获取数据库连接
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgres://admin:123456@localhost:5432/invoice_book".to_string());
    println!("当前工作目录: {:?}", std::env::current_dir()?);
    println!("📦 数据库连接: {}", database_url);

    // 演示1: 创建生成器管理器
    println!("\n🏗️ 演示1: 创建生成器管理器");
    let manager = GeneratorManager::new();

    println!("✅ 可用的生成器类型:");
    for generator_type in manager.available_generator_types() {
        println!("   - {:?}", generator_type);
    }

    // 演示2: 使用工厂快速生成（如果有真实数据库连接）
    if database_url.contains("localhost") && database_url.contains("username") {
        println!("\n⚠️ 演示2: 数据库连接为示例地址，跳过实际生成");
        println!("   如需测试实际生成，请设置正确的 DATABASE_URL 环境变量");

        // 展示如何使用的代码示例
        println!("\n📝 代码示例 - 生成单个表:");
        println!("   GeneratorFactory::quick_generate(\"sys_users\", &database_url).await?;");

        println!("\n📝 代码示例 - 生成指定类型:");
        println!("   let types = vec![GeneratorType::Entity, GeneratorType::Dto];");
        println!(
            "   GeneratorFactory::quick_generate_types(\"sys_users\", &types, &database_url).await?;"
        );

        println!("\n📝 代码示例 - 生成所有表:");
        println!("   manager.generate_all_tables(&database_url).await?;");
    } else {
        println!("\n🔍 演示2: 尝试连接真实数据库...");

        // 如果数据库连接有效，展示实际生成过程
        match manager
            .generate_from_table_name("merchant_commission_rules", "business", &database_url)
            .await
        {
            Ok(_) => {
                println!("✅ 成功生成 sys_users 表的完整CRUD代码!");
            }
            Err(e) => {
                println!("❌ 数据库连接失败或表不存在: {}", e);
                println!("   这是正常的，演示仍在继续...");
            }
        }
    }

    // 演示3: 架构特点总结
    println!("\n🎨 演示3: 新架构特点");
    println!("========================================");

    println!("🔧 设计模式应用:");
    println!("   ✓ 策略模式   - FieldStrategy 处理不同字段需求");
    println!("   ✓ 模板方法   - BaseGenerator 统一生成流程");
    println!("   ✓ 工厂模式   - FieldStrategyFactory 创建策略");
    println!("   ✓ 门面模式   - GeneratorManager 统一接口");
    println!("   ✓ 建造者模式 - ContextBuilder 构建上下文");

    println!("\n🚀 架构优势:");
    println!("   ✓ 消除重复   - 统一的 GeneratorContext");
    println!("   ✓ 易于扩展   - 新增生成器只需实现 BaseGenerator");
    println!("   ✓ 灵活配置   - 字段策略可自由组合");
    println!("   ✓ 类型安全   - 强类型约束防止错误");
    println!("   ✓ 测试友好   - 每个组件都可独立测试");

    println!("\n📊 字段策略演示:");
    println!("   • EntityFieldStrategy      - 实体包含所有字段");
    println!("   • DtoPageFieldStrategy     - 查询字段 + 验证规则");
    println!("   • DtoCreateFieldStrategy   - 排除自动字段 + 默认值");
    println!("   • DtoUpdateFieldStrategy   - Optional类型包装");
    println!("   • VoListFieldStrategy      - 列表显示字段");
    println!("   • VoDetailFieldStrategy    - 详情显示字段");
    println!("   • RepositoryQueryFieldStrategy - 查询条件字段");

    println!("\n🎯 生成器类型:");
    for generator_type in &[
        GeneratorType::Entity,
        GeneratorType::Dto,
        GeneratorType::Vo,
        GeneratorType::Repository,
        GeneratorType::Service,
        GeneratorType::Controller,
    ] {
        println!(
            "   • {:?} - {}",
            generator_type,
            get_generator_description(*generator_type)
        );
    }

    println!("\n✨ 演示完成! 新架构已成功重构并测试通过!");
    println!("   🔗 所有组件都遵循SOLID原则，具有良好的可维护性和扩展性。");

    Ok(())
}

fn get_generator_description(generator_type: GeneratorType) -> &'static str {
    match generator_type {
        GeneratorType::Entity => "SeaORM 实体模型",
        GeneratorType::Dto => "请求数据传输对象",
        GeneratorType::Vo => "响应值对象",
        GeneratorType::Repository => "数据访问层",
        GeneratorType::Service => "业务逻辑层",
        GeneratorType::Controller => "控制器层",
    }
}

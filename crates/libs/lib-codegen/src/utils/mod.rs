use anyhow::Result;
use std::fs;
use std::path::Path;

/// 字符串处理工具
pub struct StringUtils;

impl StringUtils {
    /// 转换为snake_case
    pub fn to_snake_case(input: &str) -> String {
        let mut result = String::new();
        for (i, ch) in input.chars().enumerate() {
            if ch.is_uppercase() && i > 0 {
                result.push('_');
            }
            result.push(ch.to_lowercase().next().unwrap());
        }
        result
    }

    /// 转换为PascalCase
    pub fn to_pascal_case(input: &str) -> String {
        input
            .split('_')
            .map(|word| {
                let mut chars = word.chars();
                match chars.next() {
                    None => String::new(),
                    Some(first) => {
                        first.to_uppercase().collect::<String>() + &chars.as_str().to_lowercase()
                    }
                }
            })
            .collect()
    }

    /// 转换为camelCase
    pub fn to_camel_case(input: &str) -> String {
        let pascal = Self::to_pascal_case(input);
        let mut chars = pascal.chars();
        match chars.next() {
            None => String::new(),
            Some(first) => first.to_lowercase().collect::<String>() + chars.as_str(),
        }
    }

    /// 转换为复数形式（简单实现）
    pub fn to_plural(input: &str) -> String {
        if input.ends_with('s')
            || input.ends_with('x')
            || input.ends_with('z')
            || input.ends_with("ch")
            || input.ends_with("sh")
        {
            format!("{}es", input)
        } else if input.ends_with('y')
            && !input.ends_with("ay")
            && !input.ends_with("ey")
            && !input.ends_with("iy")
            && !input.ends_with("oy")
            && !input.ends_with("uy")
        {
            format!("{}ies", &input[..input.len() - 1])
        } else {
            format!("{}s", input)
        }
    }

    /// 转换为单数形式（简单实现）
    pub fn to_singular(input: &str) -> String {
        if input.ends_with("ies") {
            format!("{}y", &input[..input.len() - 3])
        } else if input.ends_with("es") && input.len() > 2 {
            input[..input.len() - 2].to_string()
        } else if input.ends_with('s') && input.len() > 1 {
            input[..input.len() - 1].to_string()
        } else {
            input.to_string()
        }
    }
}

/// 数据库工具
pub struct DatabaseUtils;

impl DatabaseUtils {
    /// PostgreSQL类型到Rust类型的映射
    pub fn pg_type_to_rust_type(pg_type: &str, is_nullable: bool) -> String {
        let base_type = match pg_type {
            "uuid" => "Uuid",
            "varchar" | "text" | "char" => "String",
            "int4" | "integer" => "i32",
            "int8" | "bigint" => "i64",
            "int2" | "smallint" => "i16",
            "bool" | "boolean" => "bool",
            "timestamp" | "timestamptz" => "DateTime<FixedOffset>",
            "date" => "NaiveDate",
            "time" => "NaiveTime",
            "decimal" | "numeric" => "Decimal",
            "float4" | "real" => "f32",
            "float8" | "double precision" => "f64",
            "json" | "jsonb" => "serde_json::Value",
            _ => "String", // 默认使用String
        };

        if is_nullable {
            format!("Option<{}>", base_type)
        } else {
            base_type.to_string()
        }
    }
}

/// 代码格式化工具
pub struct CodeFormatter;

impl CodeFormatter {
    /// 格式化Rust代码
    pub fn format_code(code: &str) -> Result<String> {
        // 尝试使用rustfmt格式化代码
        match Self::format_with_rustfmt(code) {
            Ok(formatted) => Ok(formatted),
            Err(_) => {
                // 如果rustfmt失败，返回原始代码
                println!("⚠️ rustfmt格式化失败，使用原始代码");
                Ok(code.to_string())
            }
        }
    }

    /// 使用rustfmt格式化代码
    fn format_with_rustfmt(code: &str) -> Result<String> {
        use std::io::Write;
        use std::process::{Command, Stdio};

        let mut child = Command::new("rustfmt")
            .arg("--edition")
            .arg("2024")
            .arg("--emit")
            .arg("stdout")
            .stdin(Stdio::piped())
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .spawn()?;

        if let Some(stdin) = child.stdin.take() {
            let mut stdin = stdin;
            stdin.write_all(code.as_bytes())?;
        }

        let output = child.wait_with_output()?;

        if output.status.success() {
            Ok(String::from_utf8(output.stdout)?)
        } else {
            Err(anyhow::anyhow!(
                "rustfmt failed: {}",
                String::from_utf8_lossy(&output.stderr)
            ))
        }
    }
}

/// 类型转换工具
pub struct TypeUtils;

impl TypeUtils {
    /// 检查是否为Option类型
    pub fn is_option_type(rust_type: &str) -> bool {
        rust_type.starts_with("Option<")
    }

    /// 获取Option内部类型
    pub fn get_inner_type(rust_type: &str) -> String {
        if Self::is_option_type(rust_type) && rust_type.ends_with('>') {
            rust_type[7..rust_type.len() - 1].to_string()
        } else {
            rust_type.to_string()
        }
    }

    /// 包装为Option类型
    pub fn wrap_option(rust_type: &str) -> String {
        if Self::is_option_type(rust_type) {
            rust_type.to_string()
        } else {
            format!("Option<{}>", rust_type)
        }
    }
}

/// 验证工具
pub struct ValidationUtils;

impl ValidationUtils {
    /// 验证Rust标识符
    pub fn is_valid_rust_identifier(name: &str) -> bool {
        if name.is_empty() {
            return false;
        }

        let mut chars = name.chars();
        let first = chars.next().unwrap();

        // 第一个字符必须是字母或下划线
        if !first.is_alphabetic() && first != '_' {
            return false;
        }

        // 其余字符必须是字母、数字或下划线
        chars.all(|c| c.is_alphanumeric() || c == '_')
    }

    /// 验证数据库URL
    pub fn is_valid_database_url(url: &str) -> bool {
        url.starts_with("postgresql://") || url.starts_with("postgres://")
    }
}

/// 文件操作工具
pub struct FileUtils;

impl FileUtils {
    /// 写入文件
    pub fn write_file(path: &str, content: &str) -> Result<()> {
        // 创建目录
        if let Some(parent) = Path::new(path).parent() {
            fs::create_dir_all(parent)?;
        }

        // 写入文件
        fs::write(path, content)?;
        Ok(())
    }

    /// 读取文件
    pub fn read_file(path: &str) -> Result<String> {
        let content = fs::read_to_string(path)?;
        Ok(content)
    }

    /// 检查文件是否存在
    pub fn file_exists(path: &str) -> bool {
        Path::new(path).exists()
    }

    /// 确保目录存在
    pub fn ensure_dir_exists(path: &str) -> Result<()> {
        fs::create_dir_all(path)?;
        Ok(())
    }
}

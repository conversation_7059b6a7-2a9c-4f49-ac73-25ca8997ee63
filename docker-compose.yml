version: "3"
services:
  postgres:
    image: postgres:16
    container_name: postgres
    environment:
      POSTGRES_PASSWORD: 123456
      POSTGRES_USER: admin
      POSTGRES_DB: invoice_book
    ports:
      - "5432:5432"
    volumes:
      - ./pg-data:/var/lib/postgresql/data
      - ./postgresql.conf:/etc/postgresql/postgresql.conf
      - ./pg_hba.conf:/etc/postgresql/pg_hba.conf
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    restart: always
    networks:
      - invoice-network

  redis:
    image: redis:7.4.2
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - ./redis-data:/data
    command: redis-server --appendonly yes
    restart: always
    networks:
      - invoice-network

  app:
    image: invoice-book-app:latest
    environment:
      - APP_ENV=prod
    container_name: rust-app
    ports:
      - "9002:9002"
    volumes:
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    restart: always
    networks:
      - invoice-network

  nginx:
    image: nginx:latest
    container_name: nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: always
    networks:
      - invoice-network

networks:
  invoice-network:
    driver: bridge

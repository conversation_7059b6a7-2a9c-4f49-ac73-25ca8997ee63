-- =============================================
-- 商户门店佣金规则表
-- Schema: business
-- Description: 存储商户的员工开单佣金规则配置，支持基础佣金和阶梯佣金
-- =============================================

-- 创建商户门店佣金规则表
CREATE TABLE IF NOT EXISTS business.merchant_commission_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),              -- 主键，自增UUID
    merchant_id BIGINT NOT NULL,                               -- 商户ID（关联business.merchants.id）
    rule_name VARCHAR(100) NOT NULL,                           -- 规则名称
    base_commission_rate DECIMAL(5,4) NOT NULL DEFAULT 0.0000, -- 基础佣金比例（如0.0500表示5%）
    tier_rules JSONB,                                          -- 阶梯佣金规则JSON配置
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    created_by UUID,                                           -- 创建人ID
    updated_by UUID,                                           -- 更新人ID
    remark TEXT                                                -- 备注
);

-- 表注释
COMMENT ON TABLE business.merchant_commission_rules IS '商户门店佣金规则表：存储商户的员工开单佣金配置，支持基础佣金和阶梯佣金规则';

-- 字段注释
COMMENT ON COLUMN business.merchant_commission_rules.id IS '主键，自增UUID';
COMMENT ON COLUMN business.merchant_commission_rules.merchant_id IS '商户ID（关联business.merchants.id）';
COMMENT ON COLUMN business.merchant_commission_rules.rule_name IS '规则名称，方便识别和管理';
COMMENT ON COLUMN business.merchant_commission_rules.base_commission_rate IS '基础佣金比例，精度为5位数字4位小数（如0.0500表示5%）';
COMMENT ON COLUMN business.merchant_commission_rules.tier_rules IS '阶梯佣金规则JSON配置，存储多组阶梯规则';
COMMENT ON COLUMN business.merchant_commission_rules.created_date IS '创建时间';
COMMENT ON COLUMN business.merchant_commission_rules.updated_date IS '更新时间';
COMMENT ON COLUMN business.merchant_commission_rules.created_by IS '创建人ID';
COMMENT ON COLUMN business.merchant_commission_rules.updated_by IS '更新人ID';
COMMENT ON COLUMN business.merchant_commission_rules.remark IS '备注信息';

-- 创建索引优化查询性能
CREATE INDEX idx_merchant_commission_rules_merchant_id ON business.merchant_commission_rules (merchant_id);



-- =============================================
-- 阶梯佣金规则JSON格式说明
-- =============================================
/*
tier_rules JSON 格式示例：

方案1：简单OR关系（符合当前需求文档）
{
  "tiers": [
    {
      "tier_name": "初级阶梯",
      "logic_type": "OR",              // 逻辑关系：OR（或）/ AND（且）
      "conditions": [
        {
          "type": "sales_amount",      // 条件类型：sales_amount（销售额）/ customer_count（客户数）
          "threshold": 50000,          // 阈值
          "operator": ">="             // 操作符：>=, >, <, <=, =
        },
        {
          "type": "customer_count",
          "threshold": 20,
          "operator": ">="
        }
      ],
      "commission_rate": 0.0600,       // 佣金比例（6%）
      "description": "年销售额达到5万或客户数达到20个"
    },
    {
      "tier_name": "中级阶梯",
      "logic_type": "OR",
      "conditions": [
        {
          "type": "sales_amount",
          "threshold": 100000,
          "operator": ">="
        },
        {
          "type": "customer_count", 
          "threshold": 50,
          "operator": ">="
        }
      ],
      "commission_rate": 0.0800,
      "description": "年销售额达到10万或客户数达到50个"
    }
  ]
}

方案2：支持复杂逻辑关系（扩展支持）
{
  "tiers": [
    {
      "tier_name": "高级阶梯",
      "logic_type": "AND",             // AND关系：必须同时满足
      "conditions": [
        {
          "type": "sales_amount",
          "threshold": 200000,
          "operator": ">="
        },
        {
          "type": "customer_count",
          "threshold": 100,
          "operator": ">="
        }
      ],
      "commission_rate": 0.1000,
      "description": "年销售额达到20万且客户数达到100个"
    }
  ]
}

说明：
1. logic_type 指定条件间的逻辑关系：OR（满足任一条件）/ AND（必须全部满足）
2. conditions 数组包含具体的判断条件
3. 系统按阶梯从高到低匹配，匹配到第一个满足条件的阶梯即停止
4. 如果都不满足阶梯条件，则使用 base_commission_rate 基础佣金比例
5. 每个阶梯可以灵活配置OR或AND关系
*/

-- =============================================
-- 测试数据插入
-- =============================================

-- 商户1001：基础佣金规则（无阶梯）
INSERT INTO business.merchant_commission_rules (
    merchant_id, 
    rule_name, 
    base_commission_rate, 
    tier_rules,
    remark
) VALUES (
    1001,
    '基础佣金规则',
    0.0300,  -- 基础3%佣金
    NULL,    -- 无阶梯规则
    '简单的基础佣金配置，无阶梯奖励'
);

-- 商户1002：两级阶梯佣金规则（OR逻辑）
INSERT INTO business.merchant_commission_rules (
    merchant_id, 
    rule_name, 
    base_commission_rate, 
    tier_rules,
    remark
) VALUES (
    1002,
    '标准阶梯佣金规则',
    0.0400,  -- 基础4%佣金
    '{
      "tiers": [
        {
          "tier_name": "初级阶梯",
          "logic_type": "OR",
          "conditions": [
            {
              "type": "sales_amount",
              "threshold": 50000,
              "operator": ">="
            },
            {
              "type": "customer_count",
              "threshold": 20,
              "operator": ">="
            }
          ],
          "commission_rate": 0.0600,
          "description": "年销售额达到5万或客户数达到20个"
        },
        {
          "tier_name": "中级阶梯",
          "logic_type": "OR",
          "conditions": [
            {
              "type": "sales_amount",
              "threshold": 100000,
              "operator": ">="
            },
            {
              "type": "customer_count",
              "threshold": 50,
              "operator": ">="
            }
          ],
          "commission_rate": 0.0800,
          "description": "年销售额达到10万或客户数达到50个"
        }
      ]
    }'::jsonb,
    '标准的两级阶梯佣金配置'
);

-- 商户1003：三级阶梯佣金规则（OR逻辑）
INSERT INTO business.merchant_commission_rules (
    merchant_id, 
    rule_name, 
    base_commission_rate, 
    tier_rules,
    remark
) VALUES (
    1003,
    '高级阶梯佣金规则',
    0.0350,  -- 基础3.5%佣金
    '{
      "tiers": [
        {
          "tier_name": "青铜阶梯",
          "logic_type": "OR",
          "conditions": [
            {
              "type": "sales_amount",
              "threshold": 30000,
              "operator": ">="
            },
            {
              "type": "customer_count",
              "threshold": 15,
              "operator": ">="
            }
          ],
          "commission_rate": 0.0500,
          "description": "年销售额达到3万或客户数达到15个"
        },
        {
          "tier_name": "白银阶梯",
          "logic_type": "OR",
          "conditions": [
            {
              "type": "sales_amount",
              "threshold": 80000,
              "operator": ">="
            },
            {
              "type": "customer_count",
              "threshold": 40,
              "operator": ">="
            }
          ],
          "commission_rate": 0.0700,
          "description": "年销售额达到8万或客户数达到40个"
        },
        {
          "tier_name": "黄金阶梯",
          "logic_type": "OR",
          "conditions": [
            {
              "type": "sales_amount",
              "threshold": 150000,
              "operator": ">="
            },
            {
              "type": "customer_count",
              "threshold": 80,
              "operator": ">="
            }
          ],
          "commission_rate": 0.1000,
          "description": "年销售额达到15万或客户数达到80个"
        }
      ]
    }'::jsonb,
    '三级阶梯佣金配置，青铜-白银-黄金'
);
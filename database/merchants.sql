-- 商户管理相关表结构

-- 创建业务模式
CREATE SCHEMA IF NOT EXISTS business;
COMMENT
ON SCHEMA business IS '业务数据模式，包含商户、订单、产品等业务数据';

-- 启用必要的扩展（用于模糊搜索）
CREATE
EXTENSION IF NOT EXISTS pg_trgm;

-- ============================================================================
-- 商户分类表
-- ============================================================================
CREATE TABLE business.merchant_categories
(
    id            UUID PRIMARY KEY                  DEFAULT gen_random_uuid(),
    category_name VARCHAR(100)             NOT NULL UNIQUE,
    category_code VARCHAR(50),
    description   TEXT,
    sort_order    INTEGER                           DEFAULT 0,
    status        INTEGER                  NOT NULL DEFAULT 1 CHECK (status IN (1, 2)),
    created_date  TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date  TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by    UUID,
    updated_by    UUID,
    remark        TEXT
);

-- 添加列注释
COMMENT
ON TABLE business.merchant_categories IS '商户分类表';
COMMENT
ON COLUMN business.merchant_categories.id IS '分类ID';
COMMENT
ON COLUMN business.merchant_categories.category_name IS '分类名称';
COMMENT
ON COLUMN business.merchant_categories.category_code IS '分类编码';
COMMENT
ON COLUMN business.merchant_categories.description IS '分类描述';
COMMENT
ON COLUMN business.merchant_categories.sort_order IS '排序字段';
COMMENT
ON COLUMN business.merchant_categories.status IS '状态：1-启用，2-禁用';
COMMENT
ON COLUMN business.merchant_categories.created_date IS '创建时间';
COMMENT
ON COLUMN business.merchant_categories.updated_date IS '修改时间';
COMMENT
ON COLUMN business.merchant_categories.created_by IS '创建人ID';
COMMENT
ON COLUMN business.merchant_categories.updated_by IS '修改人ID';
COMMENT
ON COLUMN business.merchant_categories.remark IS '备注信息';

-- 索引优化
CREATE INDEX idx_business_merchant_categories_status_sort ON business.merchant_categories (status, sort_order);
CREATE INDEX idx_business_merchant_categories_name ON business.merchant_categories (category_name) WHERE status = 1;

-- ============================================================================
-- 商户表
-- ============================================================================
CREATE TABLE business.merchants
(
    id                       BIGSERIAL PRIMARY KEY,
    merchant_name            VARCHAR(200)             NOT NULL,
    merchant_code            VARCHAR(50)              NOT NULL UNIQUE,
    category_id              UUID,
    phone                    VARCHAR(20),
    email                    VARCHAR(100),
    address                  TEXT,
    location                 JSONB,
    business_license         VARCHAR(100),
    license_photo            TEXT,
    avatar                   TEXT,
    description              TEXT,
    platform_commission_rate DECIMAL(5, 4)                     DEFAULT 0.0000 CHECK (platform_commission_rate >= 0 AND platform_commission_rate <= 1),
    status                   INTEGER                  NOT NULL DEFAULT 1 CHECK (status IN (1, 2, 3)),
    auto_clear_date          DATE,
    sort_order               INTEGER                           DEFAULT 0,
    created_date             TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date             TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by               UUID,
    updated_by               UUID,
    remark                   TEXT
);

-- 添加列注释
COMMENT
ON TABLE business.merchants IS '商户表';
COMMENT
ON COLUMN business.merchants.id IS '商户ID - 自增主键';
COMMENT
ON COLUMN business.merchants.merchant_name IS '商户名称';
COMMENT
ON COLUMN business.merchants.merchant_code IS '商户编码，全局唯一，业务友好标识';
COMMENT
ON COLUMN business.merchants.category_id IS '商户分类ID';
COMMENT
ON COLUMN business.merchants.phone IS '联系电话';
COMMENT
ON COLUMN business.merchants.email IS '邮箱地址';
COMMENT
ON COLUMN business.merchants.address IS '经营地址';
COMMENT
ON COLUMN business.merchants.location IS '经营位置坐标，JSONB格式存储经纬度：{"longitude": 116.4074, "latitude": 39.9042}，用于地图定位和距离计算';
COMMENT
ON COLUMN business.merchants.business_license IS '营业执照号';
COMMENT
ON COLUMN business.merchants.license_photo IS '营业执照照片URL';
COMMENT
ON COLUMN business.merchants.avatar IS '商户头像URL';
COMMENT
ON COLUMN business.merchants.description IS '商户描述';
COMMENT
ON COLUMN business.merchants.platform_commission_rate IS '平台抽成比例(0-1)';
COMMENT
ON COLUMN business.merchants.status IS '商户状态：1-正常营业，2-临时关闭，3-永久关闭';
COMMENT
ON COLUMN business.merchants.auto_clear_date IS '账单自动清零日期(YYYY-MM-DD格式)，每年此日期自动清零账单数据';
COMMENT
ON COLUMN business.merchants.sort_order IS '排序字段';
COMMENT
ON COLUMN business.merchants.created_date IS '创建时间';
COMMENT
ON COLUMN business.merchants.updated_date IS '修改时间';
COMMENT
ON COLUMN business.merchants.created_by IS '创建人ID';
COMMENT
ON COLUMN business.merchants.updated_by IS '修改人ID';
COMMENT
ON COLUMN business.merchants.remark IS '备注信息';

-- 索引优化
-- 1. 保留重要的单列索引
CREATE INDEX idx_business_merchants_category_id ON business.merchants (category_id);
CREATE INDEX idx_business_merchants_code ON business.merchants (merchant_code);
CREATE INDEX idx_business_merchants_location ON business.merchants USING gin (location);

-- 2. 添加常用查询的复合索引
CREATE INDEX idx_business_merchants_status_sort ON business.merchants (status, sort_order);
CREATE INDEX idx_business_merchants_category_status ON business.merchants (category_id, status);

-- 3. 添加模糊搜索支持
CREATE INDEX idx_business_merchants_name_gin ON business.merchants USING gin(merchant_name gin_trgm_ops);
CREATE INDEX idx_business_merchants_phone ON business.merchants (phone) WHERE phone IS NOT NULL;


-- ============================================================================
-- 商户用户表 (统一用户账户)
-- ============================================================================

CREATE TABLE business.merchant_users
(
    id              UUID PRIMARY KEY                  DEFAULT gen_random_uuid(),
    username        VARCHAR(50)              NOT NULL,
    password        VARCHAR(255)             NOT NULL,
    real_name       VARCHAR(100)             NOT NULL UNIQUE,
    phone           VARCHAR(20)              NOT NULL,
    email           VARCHAR(100),
    avatar          TEXT,
    gender          INTEGER                           DEFAULT 3 CHECK (gender IN (1, 2, 3)),
    id_card         VARCHAR(18),
    status          INTEGER                  NOT NULL DEFAULT 1 CHECK (status IN (1, 2, 3)),
    last_login_date TIMESTAMP WITH TIME ZONE,
    last_login_ip   VARCHAR(45),
    created_date    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by      UUID,
    updated_by      UUID,
    remark          TEXT
);

-- 添加列注释
COMMENT
ON TABLE business.merchant_users IS '商户用户表';
COMMENT
ON COLUMN business.merchant_users.id IS '用户ID';
COMMENT
ON COLUMN business.merchant_users.username IS '用户名（非唯一）';
COMMENT
ON COLUMN business.merchant_users.password IS '密码';
COMMENT
ON COLUMN business.merchant_users.real_name IS '真实姓名（全局唯一）';
COMMENT
ON COLUMN business.merchant_users.phone IS '手机号码';
COMMENT
ON COLUMN business.merchant_users.email IS '邮箱地址';
COMMENT
ON COLUMN business.merchant_users.avatar IS '头像URL';
COMMENT
ON COLUMN business.merchant_users.gender IS '性别：1-男，2-女，3-未知';
COMMENT
ON COLUMN business.merchant_users.id_card IS '身份证号码';
COMMENT
ON COLUMN business.merchant_users.status IS '用户状态：1-启用，2-禁用，3-锁定';
COMMENT
ON COLUMN business.merchant_users.last_login_date IS '最后登录时间';
COMMENT
ON COLUMN business.merchant_users.last_login_ip IS '最后登录IP地址';
COMMENT
ON COLUMN business.merchant_users.created_date IS '创建时间';
COMMENT
ON COLUMN business.merchant_users.updated_date IS '修改时间';
COMMENT
ON COLUMN business.merchant_users.created_by IS '创建人ID';
COMMENT
ON COLUMN business.merchant_users.updated_by IS '修改人ID';
COMMENT
ON COLUMN business.merchant_users.remark IS '备注信息';

-- 索引
CREATE INDEX idx_business_merchant_users_username ON business.merchant_users (username);
CREATE UNIQUE INDEX idx_business_merchant_users_real_name ON business.merchant_users (real_name);
CREATE INDEX idx_business_merchant_users_phone ON business.merchant_users (phone);
CREATE INDEX idx_business_merchant_users_status ON business.merchant_users (status);

-- ============================================================================
-- 商户角色表 (统一角色表 - 支持管理员角色和自定义角色)
-- ============================================================================

CREATE TABLE business.merchant_roles
(
    id               UUID PRIMARY KEY                  DEFAULT gen_random_uuid(),
    merchant_id      BIGINT                   NOT NULL,
    role_code        VARCHAR(50),
    role_name        VARCHAR(100)             NOT NULL,
    role_type        INTEGER                  NOT NULL DEFAULT 2 CHECK (role_type IN (1, 2)),
    is_default       BOOLEAN                  NOT NULL DEFAULT FALSE,
    data_scope       INTEGER                  NOT NULL DEFAULT 1 CHECK (data_scope IN (1, 2)),
    role_description TEXT,
    status           INTEGER                  NOT NULL DEFAULT 1 CHECK (status IN (0, 1)),
    created_date     TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date     TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by       UUID,
    updated_by       UUID,
    remark           TEXT
);

-- 添加列注释
COMMENT
ON TABLE business.merchant_roles IS '商户角色表 - 统一管理员角色和自定义角色';
COMMENT
ON COLUMN business.merchant_roles.id IS '角色ID';
COMMENT
ON COLUMN business.merchant_roles.merchant_id IS '商户ID';
COMMENT
ON COLUMN business.merchant_roles.role_code IS '角色编码，如 MERCHANT_ADMIN、CUSTOM_ROLE_001';
COMMENT
ON COLUMN business.merchant_roles.role_name IS '角色名称';
COMMENT
ON COLUMN business.merchant_roles.role_type IS '角色类型：1-商户管理员角色，2-商户自定义角色';
COMMENT
ON COLUMN business.merchant_roles.is_default IS '是否默认角色（新用户默认分配）';
COMMENT
ON COLUMN business.merchant_roles.data_scope IS '数据范围：1-商户全部数据，2-个人数据';
COMMENT
ON COLUMN business.merchant_roles.role_description IS '角色描述';
COMMENT
ON COLUMN business.merchant_roles.status IS '角色状态：0-禁用，1-启用';
COMMENT
ON COLUMN business.merchant_roles.created_date IS '创建时间';
COMMENT
ON COLUMN business.merchant_roles.updated_date IS '修改时间';
COMMENT
ON COLUMN business.merchant_roles.created_by IS '创建人ID';
COMMENT
ON COLUMN business.merchant_roles.updated_by IS '修改人ID';
COMMENT
ON COLUMN business.merchant_roles.remark IS '备注信息';

-- 索引
CREATE INDEX idx_business_merchant_roles_merchant_id ON business.merchant_roles (merchant_id);
CREATE INDEX idx_business_merchant_roles_status ON business.merchant_roles (status);
CREATE INDEX idx_business_merchant_roles_type ON business.merchant_roles (role_type);
CREATE INDEX idx_business_merchant_roles_code ON business.merchant_roles (role_code);
CREATE INDEX idx_business_merchant_roles_data_scope ON business.merchant_roles (data_scope);
CREATE UNIQUE INDEX idx_business_merchant_roles_merchant_name ON business.merchant_roles (merchant_id, role_name);
-- 管理员角色专用索引
CREATE INDEX idx_business_merchant_roles_admin ON business.merchant_roles (merchant_id, role_type) WHERE role_type = 1;

-- ============================================================================
-- 商户用户角色关联表
-- ============================================================================

CREATE TABLE business.merchant_user_roles
(
    id              UUID PRIMARY KEY                  DEFAULT gen_random_uuid(),
    merchant_id     BIGINT                   NOT NULL,
    user_id         UUID                     NOT NULL,
    role_id         UUID                     NOT NULL,
    commission_rate DECIMAL(5, 4)                     DEFAULT 0.0000 CHECK (commission_rate >= 0 AND commission_rate <= 1),
    status          INTEGER                  NOT NULL DEFAULT 1 CHECK (status IN (0, 1)),
    created_date    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by      UUID,
    updated_by      UUID,
    remark          TEXT
);

-- 添加列注释
COMMENT
ON TABLE business.merchant_user_roles IS '商户用户角色关联表 - 简化版本，直接关联merchant_roles表';
COMMENT
ON COLUMN business.merchant_user_roles.id IS '关联ID';
COMMENT
ON COLUMN business.merchant_user_roles.merchant_id IS '商户ID，关联business.merchants.id';
COMMENT
ON COLUMN business.merchant_user_roles.user_id IS '用户ID';
COMMENT
ON COLUMN business.merchant_user_roles.role_id IS '角色ID，关联business.merchant_roles.id';
COMMENT
ON COLUMN business.merchant_user_roles.commission_rate IS '佣金比例(0-1)';
COMMENT
ON COLUMN business.merchant_user_roles.status IS '状态：0-禁用，1-启用';
COMMENT
ON COLUMN business.merchant_user_roles.created_date IS '创建时间';
COMMENT
ON COLUMN business.merchant_user_roles.updated_date IS '修改时间';
COMMENT
ON COLUMN business.merchant_user_roles.created_by IS '创建人ID';
COMMENT
ON COLUMN business.merchant_user_roles.updated_by IS '修改人ID';
COMMENT
ON COLUMN business.merchant_user_roles.remark IS '备注信息';

-- 索引优化
CREATE INDEX idx_business_merchant_user_roles_merchant_user ON business.merchant_user_roles (merchant_id, user_id);
CREATE INDEX idx_business_merchant_user_roles_user_status ON business.merchant_user_roles (user_id, status);
CREATE INDEX idx_business_merchant_user_roles_role_id ON business.merchant_user_roles (role_id);
CREATE INDEX idx_business_merchant_user_roles_merchant_role ON business.merchant_user_roles (merchant_id, role_id);

-- 防止重复分配相同角色的唯一约束
CREATE UNIQUE INDEX idx_business_merchant_user_roles_unique ON business.merchant_user_roles (merchant_id, user_id, role_id);


-- 商户权限管理系统数据库设计（三层权限体系）

-- =============================================
-- 第一层：系统权限模板表（系统预置，商户不可修改）
-- =============================================
CREATE TABLE business.system_permission_templates
(
    id              UUID PRIMARY KEY                  DEFAULT gen_random_uuid(),
    permission_name VARCHAR(100)             NOT NULL,
    permission_code VARCHAR(100)             NOT NULL UNIQUE,
    parent_id       UUID,
    order_num       INTEGER                           DEFAULT 0,
    path            VARCHAR(200),
    component       VARCHAR(255),
    query           VARCHAR(255),
    is_frame        INTEGER                           DEFAULT 1,
    is_cache        INTEGER                           DEFAULT 0,
    permission_type INTEGER                           DEFAULT 1,
    visible         INTEGER                           DEFAULT 0,
    icon            VARCHAR(100)                      DEFAULT '#',
    description     TEXT,
    created_date    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by      UUID,
    updated_by      UUID,
    remark          TEXT
);

-- 添加列注释
COMMENT
ON TABLE business.system_permission_templates IS '系统权限模板表 - 系统预置的所有可用权限项，商户不可修改';
COMMENT
ON COLUMN business.system_permission_templates.id IS '权限模板ID';
COMMENT
ON COLUMN business.system_permission_templates.permission_name IS '权限名称';
COMMENT
ON COLUMN business.system_permission_templates.permission_code IS '权限编码，全局唯一，如：order:view、product:create';
COMMENT
ON COLUMN business.system_permission_templates.parent_id IS '父权限ID，支持权限层级结构';
COMMENT
ON COLUMN business.system_permission_templates.order_num IS '显示顺序';
COMMENT
ON COLUMN business.system_permission_templates.path IS '路由地址';
COMMENT
ON COLUMN business.system_permission_templates.component IS '组件路径';
COMMENT
ON COLUMN business.system_permission_templates.query IS '路由参数';
COMMENT
ON COLUMN business.system_permission_templates.is_frame IS '是否为外链：0-是，1-否';
COMMENT
ON COLUMN business.system_permission_templates.is_cache IS '是否缓存：0-缓存，1-不缓存';
COMMENT
ON COLUMN business.system_permission_templates.permission_type IS '权限类型：1-目录，2-菜单，3-按钮';
COMMENT
ON COLUMN business.system_permission_templates.visible IS '菜单状态：0-显示，1-隐藏';
COMMENT
ON COLUMN business.system_permission_templates.icon IS '权限图标';
COMMENT
ON COLUMN business.system_permission_templates.description IS '权限描述';
COMMENT
ON COLUMN business.system_permission_templates.created_date IS '创建时间';
COMMENT
ON COLUMN business.system_permission_templates.updated_date IS '修改时间';
COMMENT
ON COLUMN business.system_permission_templates.created_by IS '创建人ID';
COMMENT
ON COLUMN business.system_permission_templates.updated_by IS '修改人ID';
COMMENT
ON COLUMN business.system_permission_templates.remark IS '备注信息';

-- 索引优化
CREATE INDEX idx_system_permission_templates_parent_id ON business.system_permission_templates (parent_id);
CREATE INDEX idx_system_permission_templates_type ON business.system_permission_templates (permission_type);

-- =============================================
-- 第二层：商户授权权限表（系统管理员给商户的权限）
-- =============================================
CREATE TABLE business.merchant_authorized_permissions
(
    id                     UUID PRIMARY KEY                  DEFAULT gen_random_uuid(),
    merchant_id            BIGINT                   NOT NULL,
    permission_template_id UUID                     NOT NULL,
    status                 INTEGER                  NOT NULL DEFAULT 0 CHECK (status IN (0, 1)),
    authorized_date        TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by             UUID                     NOT NULL,
    updated_by             UUID,
    remark                 TEXT
);

-- 添加列注释
COMMENT
ON TABLE business.merchant_authorized_permissions IS '商户授权权限表 - 系统管理员授权给商户的权限清单';
COMMENT
ON COLUMN business.merchant_authorized_permissions.id IS '授权记录ID';
COMMENT
ON COLUMN business.merchant_authorized_permissions.merchant_id IS '商户ID';
COMMENT
ON COLUMN business.merchant_authorized_permissions.permission_template_id IS '系统权限模板ID';
COMMENT
ON COLUMN business.merchant_authorized_permissions.status IS '授权状态：0-正常，1-停用';
COMMENT
ON COLUMN business.merchant_authorized_permissions.authorized_date IS '授权时间';
COMMENT
ON COLUMN business.merchant_authorized_permissions.created_by IS '授权人ID（系统管理员）';
COMMENT
ON COLUMN business.merchant_authorized_permissions.updated_by IS '修改人ID';
COMMENT
ON COLUMN business.merchant_authorized_permissions.remark IS '授权备注';

-- 索引优化
CREATE INDEX idx_merchant_authorized_permissions_merchant_id ON business.merchant_authorized_permissions (merchant_id);
CREATE INDEX idx_merchant_authorized_permissions_template_id ON business.merchant_authorized_permissions (permission_template_id);
CREATE INDEX idx_merchant_authorized_permissions_status ON business.merchant_authorized_permissions (status);
CREATE INDEX idx_merchant_authorized_permissions_merchant_status ON business.merchant_authorized_permissions (merchant_id, status);

-- 确保同一商户不重复授权相同权限
CREATE UNIQUE INDEX idx_merchant_authorized_permissions_unique ON business.merchant_authorized_permissions (merchant_id, permission_template_id);

-- =============================================
-- 第三层：商户角色权限表（商户在授权范围内分配给角色）
-- =============================================
CREATE TABLE business.merchant_role_permissions
(
    id                       UUID PRIMARY KEY                  DEFAULT gen_random_uuid(),
    merchant_id              BIGINT                   NOT NULL,
    role_id                  UUID                     NOT NULL,
    authorized_permission_id UUID                     NOT NULL,
    created_date             TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by               UUID,
    remark                   TEXT
);

-- 添加列注释
COMMENT
ON TABLE business.merchant_role_permissions IS '商户角色权限表 - 商户在授权范围内分配给角色的权限';
COMMENT
ON COLUMN business.merchant_role_permissions.id IS '分配记录ID';
COMMENT
ON COLUMN business.merchant_role_permissions.merchant_id IS '商户ID';
COMMENT
ON COLUMN business.merchant_role_permissions.role_id IS '商户角色ID';
COMMENT
ON COLUMN business.merchant_role_permissions.authorized_permission_id IS '商户授权权限ID';
COMMENT
ON COLUMN business.merchant_role_permissions.created_date IS '分配时间';
COMMENT
ON COLUMN business.merchant_role_permissions.created_by IS '分配人ID（商户管理员）';
COMMENT
ON COLUMN business.merchant_role_permissions.remark IS '分配备注';

-- 索引优化
CREATE INDEX idx_merchant_role_permissions_merchant_id ON business.merchant_role_permissions (merchant_id);
CREATE INDEX idx_merchant_role_permissions_role_id ON business.merchant_role_permissions (role_id);
CREATE INDEX idx_merchant_role_permissions_authorized_id ON business.merchant_role_permissions (authorized_permission_id);
CREATE INDEX idx_merchant_role_permissions_merchant_role ON business.merchant_role_permissions (merchant_id, role_id);

-- 确保同一商户、同一角色下不重复分配相同权限
CREATE UNIQUE INDEX idx_merchant_role_permissions_unique ON business.merchant_role_permissions (merchant_id, role_id, authorized_permission_id);
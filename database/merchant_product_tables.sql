--
-- 商户级商品相关表创建脚本
-- 这些表按merchant_id分表分schema，每个商户有独立的数据空间
-- 注意：不使用外键约束，采用应用层控制数据完整性
-- 使用 {schema} 和 {table_name} 占位符，由lib-sharding自动替换
--

-- 1. 商户商品分类表（一级分类）
CREATE TABLE {schema}.product_categories (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    name VARCHAR(50) NOT NULL,
    description TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL,
    updated_by BIGINT NOT NULL
);

-- 商户商品分类表注释
COMMENT ON TABLE {schema}.product_categories IS '商户商品分类表 - 一级分类，由商户自定义维护，用于组织店铺商品';
COMMENT ON COLUMN {schema}.product_categories.id IS '主键ID';
COMMENT ON COLUMN {schema}.product_categories.merchant_id IS '商户ID，数据隔离字段';
COMMENT ON COLUMN {schema}.product_categories.name IS '分类名称，最大50字符';
COMMENT ON COLUMN {schema}.product_categories.description IS '分类描述';
COMMENT ON COLUMN {schema}.product_categories.sort_order IS '排序字段，数值越小越靠前';
COMMENT ON COLUMN {schema}.product_categories.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN {schema}.product_categories.created_at IS '创建时间';
COMMENT ON COLUMN {schema}.product_categories.updated_at IS '更新时间';
COMMENT ON COLUMN {schema}.product_categories.created_by IS '创建人ID';
COMMENT ON COLUMN {schema}.product_categories.updated_by IS '更新人ID';

-- 商户商品分类索引
CREATE INDEX idx_product_categories_merchant_id ON {schema}.product_categories(merchant_id);
CREATE INDEX idx_product_categories_status ON {schema}.product_categories(status);
CREATE INDEX idx_product_categories_sort ON {schema}.product_categories(sort_order);

-- 2. 库区分类管理表
CREATE TABLE {schema}.warehouse_areas (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    name VARCHAR(50) NOT NULL,
    description TEXT,
    location VARCHAR(100),
    sort_order INTEGER NOT NULL DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL,
    updated_by BIGINT NOT NULL
);

-- 库区分类表注释
COMMENT ON TABLE {schema}.warehouse_areas IS '库区分类管理表 - 商户自定义库区分类，用于商品规格选择时的分组';
COMMENT ON COLUMN {schema}.warehouse_areas.id IS '主键ID';
COMMENT ON COLUMN {schema}.warehouse_areas.merchant_id IS '商户ID，数据隔离字段';
COMMENT ON COLUMN {schema}.warehouse_areas.name IS '库区名称，最大50字符';
COMMENT ON COLUMN {schema}.warehouse_areas.description IS '库区描述';
COMMENT ON COLUMN {schema}.warehouse_areas.location IS '库区位置描述';
COMMENT ON COLUMN {schema}.warehouse_areas.sort_order IS '排序字段，数值越小越靠前';
COMMENT ON COLUMN {schema}.warehouse_areas.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN {schema}.warehouse_areas.created_at IS '创建时间';
COMMENT ON COLUMN {schema}.warehouse_areas.updated_at IS '更新时间';
COMMENT ON COLUMN {schema}.warehouse_areas.created_by IS '创建人ID';
COMMENT ON COLUMN {schema}.warehouse_areas.updated_by IS '更新人ID';

-- 库区分类索引
CREATE INDEX idx_warehouse_areas_merchant_id ON {schema}.warehouse_areas(merchant_id);
CREATE INDEX idx_warehouse_areas_status ON {schema}.warehouse_areas(status);
CREATE INDEX idx_warehouse_areas_sort ON {schema}.warehouse_areas(sort_order);

-- 3. 基础商品信息表
CREATE TABLE {schema}.products (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    category_id BIGINT NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    brand VARCHAR(50),
    supplier VARCHAR(100),
    shelf_life_days INTEGER,
    main_image_url TEXT,
    images_json TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL,
    updated_by BIGINT NOT NULL
);

-- 基础商品信息表注释
COMMENT ON TABLE {schema}.products IS '基础商品信息表 - 存储商品基本信息，与商品规格分离';
COMMENT ON COLUMN {schema}.products.id IS '主键ID';
COMMENT ON COLUMN {schema}.products.merchant_id IS '商户ID，数据隔离字段';
COMMENT ON COLUMN {schema}.products.category_id IS '商户商品分类ID';
COMMENT ON COLUMN {schema}.products.name IS '商品名称，最大100字符';
COMMENT ON COLUMN {schema}.products.description IS '商品描述';
COMMENT ON COLUMN {schema}.products.brand IS '品牌名称';
COMMENT ON COLUMN {schema}.products.supplier IS '供应商名称';
COMMENT ON COLUMN {schema}.products.shelf_life_days IS '保质期天数';
COMMENT ON COLUMN {schema}.products.main_image_url IS '主图片URL';
COMMENT ON COLUMN {schema}.products.images_json IS '商品图片JSON数组';
COMMENT ON COLUMN {schema}.products.status IS '商品状态：active-正常，inactive-下架，draft-草稿';
COMMENT ON COLUMN {schema}.products.sort_order IS '排序字段，数值越小越靠前';
COMMENT ON COLUMN {schema}.products.created_at IS '创建时间';
COMMENT ON COLUMN {schema}.products.updated_at IS '更新时间';
COMMENT ON COLUMN {schema}.products.created_by IS '创建人ID';
COMMENT ON COLUMN {schema}.products.updated_by IS '更新人ID';

-- 基础商品信息索引
CREATE INDEX idx_products_merchant_id ON {schema}.products(merchant_id);
CREATE INDEX idx_products_category_id ON {schema}.products(category_id);
CREATE INDEX idx_products_status ON {schema}.products(status);
CREATE INDEX idx_products_brand ON {schema}.products(brand);
CREATE INDEX idx_products_sort ON {schema}.products(sort_order);

-- 4. 商品预警设置表
CREATE TABLE {schema}.alert_settings (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    product_id BIGINT NOT NULL,
    stock_warning_threshold INTEGER DEFAULT 10,
    expiry_warning_days INTEGER DEFAULT 7,
    stagnant_warning_days INTEGER DEFAULT 30,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL,
    updated_by BIGINT NOT NULL
);

-- 商品预警设置表注释
COMMENT ON TABLE {schema}.alert_settings IS '商品预警设置表 - 在商品发布时配置预警规则，智能预警系统使用';
COMMENT ON COLUMN {schema}.alert_settings.id IS '主键ID';
COMMENT ON COLUMN {schema}.alert_settings.merchant_id IS '商户ID，数据隔离字段';
COMMENT ON COLUMN {schema}.alert_settings.product_id IS '商品ID';
COMMENT ON COLUMN {schema}.alert_settings.stock_warning_threshold IS '库存预警阈值，商品数量不足此值时预警';
COMMENT ON COLUMN {schema}.alert_settings.expiry_warning_days IS '过期预警天数，商品离过期时间不到此天数时预警';
COMMENT ON COLUMN {schema}.alert_settings.stagnant_warning_days IS '滞销预警天数，商品进库时间超过此天数时预警';
COMMENT ON COLUMN {schema}.alert_settings.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN {schema}.alert_settings.created_at IS '创建时间';
COMMENT ON COLUMN {schema}.alert_settings.updated_at IS '更新时间';
COMMENT ON COLUMN {schema}.alert_settings.created_by IS '创建人ID';
COMMENT ON COLUMN {schema}.alert_settings.updated_by IS '更新人ID';

-- 商品预警设置索引
CREATE INDEX idx_alert_settings_merchant_id ON {schema}.alert_settings(merchant_id);
CREATE INDEX idx_alert_settings_product_id ON {schema}.alert_settings(product_id);
CREATE INDEX idx_alert_settings_status ON {schema}.alert_settings(status);

-- 5. 商品规格管理表
CREATE TABLE {schema}.product_specs (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    product_id BIGINT NOT NULL,
    warehouse_area_id BIGINT,
    spec_name VARCHAR(100) NOT NULL,
    sale_price DECIMAL(15,2) NOT NULL,
    cost_price DECIMAL(15,2) NOT NULL,
    total_stock INTEGER NOT NULL DEFAULT 0,
    available_stock INTEGER NOT NULL DEFAULT 0,
    location_code VARCHAR(50),
    production_date DATE,
    stock_in_quantity INTEGER NOT NULL DEFAULT 0,
    stock_in_time TIMESTAMP WITH TIME ZONE,
    stock_in_user BIGINT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL,
    updated_by BIGINT NOT NULL
);

-- 商品规格管理表注释
COMMENT ON TABLE {schema}.product_specs IS '商品规格管理表 - 存储商品规格信息，支持先进先出库存管理';
COMMENT ON COLUMN {schema}.product_specs.id IS '主键ID';
COMMENT ON COLUMN {schema}.product_specs.merchant_id IS '商户ID，数据隔离字段';
COMMENT ON COLUMN {schema}.product_specs.product_id IS '商品ID';
COMMENT ON COLUMN {schema}.product_specs.warehouse_area_id IS '所属库区ID';
COMMENT ON COLUMN {schema}.product_specs.spec_name IS '规格名称';
COMMENT ON COLUMN {schema}.product_specs.sale_price IS '销售价格';
COMMENT ON COLUMN {schema}.product_specs.cost_price IS '进货价（角色员工不可查看）';
COMMENT ON COLUMN {schema}.product_specs.total_stock IS '总库存数量';
COMMENT ON COLUMN {schema}.product_specs.available_stock IS '可用库存数量';
COMMENT ON COLUMN {schema}.product_specs.location_code IS '位置编号';
COMMENT ON COLUMN {schema}.product_specs.production_date IS '生产日期';
COMMENT ON COLUMN {schema}.product_specs.stock_in_quantity IS '入库数量';
COMMENT ON COLUMN {schema}.product_specs.stock_in_time IS '入库时间，填写入库时自动生成';
COMMENT ON COLUMN {schema}.product_specs.stock_in_user IS '入库人，自动识别当前操作员工';
COMMENT ON COLUMN {schema}.product_specs.status IS '规格状态：active-正常，inactive-停用，sold_out-售罄';
COMMENT ON COLUMN {schema}.product_specs.created_at IS '创建时间';
COMMENT ON COLUMN {schema}.product_specs.updated_at IS '更新时间';
COMMENT ON COLUMN {schema}.product_specs.created_by IS '创建人ID';
COMMENT ON COLUMN {schema}.product_specs.updated_by IS '更新人ID';

-- 商品规格管理索引
CREATE INDEX idx_product_specs_merchant_id ON {schema}.product_specs(merchant_id);
CREATE INDEX idx_product_specs_product_id ON {schema}.product_specs(product_id);
CREATE INDEX idx_product_specs_warehouse_area_id ON {schema}.product_specs(warehouse_area_id);
CREATE INDEX idx_product_specs_production_date ON {schema}.product_specs(production_date);
CREATE INDEX idx_product_specs_status ON {schema}.product_specs(status);
CREATE INDEX idx_product_specs_stock_in_time ON {schema}.product_specs(stock_in_time);

-- 6. 商品与系统分类关联表
CREATE TABLE {schema}.product_system_categories (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    product_id BIGINT NOT NULL,
    system_category_id BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL
);

-- 商品与系统分类关联表注释
COMMENT ON TABLE {schema}.product_system_categories IS '商品与系统分类关联表 - 支持商品多选系统分类';
COMMENT ON COLUMN {schema}.product_system_categories.id IS '主键ID';
COMMENT ON COLUMN {schema}.product_system_categories.merchant_id IS '商户ID，数据隔离字段';
COMMENT ON COLUMN {schema}.product_system_categories.product_id IS '商品ID';
COMMENT ON COLUMN {schema}.product_system_categories.system_category_id IS '系统商品分类ID';
COMMENT ON COLUMN {schema}.product_system_categories.created_at IS '创建时间';
COMMENT ON COLUMN {schema}.product_system_categories.created_by IS '创建人ID';

-- 商品与系统分类关联索引
CREATE INDEX idx_product_system_categories_merchant_id ON {schema}.product_system_categories(merchant_id);
CREATE INDEX idx_product_system_categories_product_id ON {schema}.product_system_categories(product_id);
CREATE INDEX idx_product_system_categories_system_category_id ON {schema}.product_system_categories(system_category_id);
CREATE UNIQUE INDEX idx_product_system_categories_unique ON {schema}.product_system_categories(product_id, system_category_id);

-- 7. 商品与标签关联表
CREATE TABLE {schema}.product_tags (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    product_id BIGINT NOT NULL,
    system_tag_id BIGINT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL
);

-- 商品与标签关联表注释
COMMENT ON TABLE {schema}.product_tags IS '商品与标签关联表 - 支持商品多选系统标签';
COMMENT ON COLUMN {schema}.product_tags.id IS '主键ID';
COMMENT ON COLUMN {schema}.product_tags.merchant_id IS '商户ID，数据隔离字段';
COMMENT ON COLUMN {schema}.product_tags.product_id IS '商品ID';
COMMENT ON COLUMN {schema}.product_tags.system_tag_id IS '系统商品标签ID';
COMMENT ON COLUMN {schema}.product_tags.created_at IS '创建时间';
COMMENT ON COLUMN {schema}.product_tags.created_by IS '创建人ID';

-- 商品与标签关联索引
CREATE INDEX idx_product_tags_merchant_id ON {schema}.product_tags(merchant_id);
CREATE INDEX idx_product_tags_product_id ON {schema}.product_tags(product_id);
CREATE INDEX idx_product_tags_system_tag_id ON {schema}.product_tags(system_tag_id);
CREATE UNIQUE INDEX idx_product_tags_unique ON {schema}.product_tags(product_id, system_tag_id);

-- 8. 库存管理表
CREATE TABLE {schema}.inventory (
    id BIGSERIAL PRIMARY KEY,
    merchant_id BIGINT NOT NULL DEFAULT {merchant_id},
    product_id BIGINT NOT NULL,
    product_spec_id BIGINT NOT NULL,
    warehouse_area_id BIGINT,
    current_stock INTEGER NOT NULL DEFAULT 0,
    reserved_stock INTEGER NOT NULL DEFAULT 0,
    total_in_stock INTEGER NOT NULL DEFAULT 0,
    total_out_stock INTEGER NOT NULL DEFAULT 0,
    last_in_time TIMESTAMP WITH TIME ZONE,
    last_out_time TIMESTAMP WITH TIME ZONE,
    production_date DATE,
    expiry_date DATE,
    location_code VARCHAR(50),
    batch_no VARCHAR(50),
    lot_no VARCHAR(50),
    status VARCHAR(20) NOT NULL DEFAULT 'normal',
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 库存管理表注释
COMMENT ON TABLE {schema}.inventory IS '库存管理表 - 实时库存数据，支持先进先出原则';
COMMENT ON COLUMN {schema}.inventory.id IS '主键ID';
COMMENT ON COLUMN {schema}.inventory.merchant_id IS '商户ID，数据隔离字段';
COMMENT ON COLUMN {schema}.inventory.product_id IS '商品ID';
COMMENT ON COLUMN {schema}.inventory.product_spec_id IS '商品规格ID';
COMMENT ON COLUMN {schema}.inventory.warehouse_area_id IS '库区ID';
COMMENT ON COLUMN {schema}.inventory.current_stock IS '当前库存数量';
COMMENT ON COLUMN {schema}.inventory.reserved_stock IS '预留库存数量';
COMMENT ON COLUMN {schema}.inventory.total_in_stock IS '累计入库数量';
COMMENT ON COLUMN {schema}.inventory.total_out_stock IS '累计出库数量';
COMMENT ON COLUMN {schema}.inventory.last_in_time IS '最后入库时间';
COMMENT ON COLUMN {schema}.inventory.last_out_time IS '最后出库时间';
COMMENT ON COLUMN {schema}.inventory.production_date IS '生产日期';
COMMENT ON COLUMN {schema}.inventory.expiry_date IS '过期日期';
COMMENT ON COLUMN {schema}.inventory.location_code IS '位置编号';
COMMENT ON COLUMN {schema}.inventory.batch_no IS '批次号';
COMMENT ON COLUMN {schema}.inventory.lot_no IS '批号';
COMMENT ON COLUMN {schema}.inventory.status IS '库存状态：normal-正常，reserved-预留，damaged-损坏，expired-过期';
COMMENT ON COLUMN {schema}.inventory.created_at IS '创建时间';
COMMENT ON COLUMN {schema}.inventory.updated_at IS '更新时间';

-- 库存管理索引
CREATE INDEX idx_inventory_merchant_id ON {schema}.inventory(merchant_id);
CREATE INDEX idx_inventory_product_id ON {schema}.inventory(product_id);
CREATE INDEX idx_inventory_product_spec_id ON {schema}.inventory(product_spec_id);
CREATE INDEX idx_inventory_warehouse_area_id ON {schema}.inventory(warehouse_area_id);
CREATE INDEX idx_inventory_production_date ON {schema}.inventory(production_date);
CREATE INDEX idx_inventory_expiry_date ON {schema}.inventory(expiry_date);
CREATE INDEX idx_inventory_status ON {schema}.inventory(status);
CREATE INDEX idx_inventory_current_stock ON {schema}.inventory(current_stock);
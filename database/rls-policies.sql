-- 行级安全策略配置
-- Database: invoice-book
-- PostgreSQL 版本: 14+
-- 说明: 为多层用户系统配置行级安全策略，确保数据隔离
-- 
-- 用户体系：
-- 1. 系统管理员 - 可查看所有数据，不受任何限制
-- 2. 商户管理员/员工 - 只能查看自己商户的数据
-- 3. 普通用户 - 只能查看自己的数据（未来）

-- ============================================================================
-- 前置准备：创建RSL相关函数
-- ============================================================================

-- 获取当前用户类型
CREATE OR REPLACE FUNCTION current_user_type() RETURNS TEXT AS $$
BEGIN
    -- 从会话变量获取当前用户类型
    -- 可能的值：'system_admin', 'merchant_user', 'regular_user'
    RETURN COALESCE(current_setting('app.current_user_type', true), NULL);
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 获取当前系统用户ID
CREATE OR REPLACE FUNCTION current_system_user_id() RETURNS UUID AS $$
BEGIN
    -- 只有当用户类型为system_admin时才返回系统用户ID
    IF current_user_type() = 'system_admin' THEN
        RETURN COALESCE(current_setting('app.current_system_user_id', true)::UUID, NULL);
    END IF;
    RETURN NULL;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 获取当前商户用户ID
CREATE OR REPLACE FUNCTION current_merchant_user_id() RETURNS UUID AS $$
BEGIN
    -- 只有当用户类型为merchant_user时才返回商户用户ID
    IF current_user_type() = 'merchant_user' THEN
        RETURN COALESCE(current_setting('app.current_merchant_user_id', true)::UUID, NULL);
    END IF;
    RETURN NULL;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 获取当前商户ID
CREATE OR REPLACE FUNCTION current_merchant_id() RETURNS BIGINT AS $$
BEGIN
    -- 只有当用户类型为merchant_user时才返回商户ID
    IF current_user_type() = 'merchant_user' THEN
        RETURN COALESCE(current_setting('app.current_merchant_id', true)::BIGINT, NULL);
    END IF;
    RETURN NULL;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 获取当前普通用户ID（未来使用）
CREATE OR REPLACE FUNCTION current_regular_user_id() RETURNS UUID AS $$
BEGIN
    -- 只有当用户类型为regular_user时才返回普通用户ID
    IF current_user_type() = 'regular_user' THEN
        RETURN COALESCE(current_setting('app.current_regular_user_id', true)::UUID, NULL);
    END IF;
    RETURN NULL;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 检查是否为系统管理员
CREATE OR REPLACE FUNCTION is_system_admin() RETURNS BOOLEAN AS $$
BEGIN
    RETURN current_user_type() = 'system_admin' AND current_system_user_id() IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 检查是否为商户用户（管理员或员工）
CREATE OR REPLACE FUNCTION is_merchant_user() RETURNS BOOLEAN AS $$
BEGIN
    RETURN current_user_type() = 'merchant_user' AND current_merchant_user_id() IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 检查是否为普通用户（未来使用）
CREATE OR REPLACE FUNCTION is_regular_user() RETURNS BOOLEAN AS $$
BEGIN
    RETURN current_user_type() = 'regular_user' AND current_regular_user_id() IS NOT NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 检查商户用户是否可以访问指定商户
CREATE OR REPLACE FUNCTION can_access_merchant(target_merchant_id BIGINT) RETURNS BOOLEAN AS $$
DECLARE
    user_id UUID;
BEGIN
    -- 系统管理员可以访问所有商户
    IF is_system_admin() THEN
        RETURN TRUE;
    END IF;
    
    -- 非商户用户不能访问商户数据
    IF NOT is_merchant_user() THEN
        RETURN FALSE;
    END IF;
    
    user_id := current_merchant_user_id();
    
    -- 检查用户是否在该商户下有角色
    RETURN EXISTS (
        SELECT 1 
        FROM business.merchant_user_roles mur
        WHERE mur.user_id = user_id 
          AND mur.merchant_id = target_merchant_id 
          AND mur.status = 1
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 检查商户用户是否为指定商户的管理员
CREATE OR REPLACE FUNCTION is_merchant_admin(target_merchant_id BIGINT) RETURNS BOOLEAN AS $$
DECLARE
    user_id UUID;
    admin_role_id UUID;
BEGIN
    -- 系统管理员具有所有商户的管理权限
    IF is_system_admin() THEN
        RETURN TRUE;
    END IF;
    
    -- 非商户用户不具有商户管理权限
    IF NOT is_merchant_user() THEN
        RETURN FALSE;
    END IF;
    
    user_id := current_merchant_user_id();
    
    -- 获取管理员角色ID
    SELECT id INTO admin_role_id 
    FROM business.merchant_roles 
    WHERE role_code = 'MERCHANT_ADMIN' AND status = 1;
    
    -- 检查用户是否为该商户的管理员
    RETURN EXISTS (
        SELECT 1 
        FROM business.merchant_user_roles mur
        WHERE mur.user_id = user_id 
          AND mur.merchant_id = target_merchant_id 
          AND mur.role_id = admin_role_id
          AND mur.status = 1
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 系统表RSL策略（系统管理员专用）
-- ============================================================================

-- 系统用户表：只有系统管理员可以访问
ALTER TABLE system.users ENABLE ROW LEVEL SECURITY;

-- 系统管理员可以查看所有系统用户
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'system_users_admin_policy') THEN
        EXECUTE 'CREATE POLICY system_users_admin_policy ON system.users
                FOR ALL
                USING (is_system_admin())';
    END IF;
END $$;

-- 系统用户可以查看和修改自己的信息
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'system_users_self_policy') THEN
        EXECUTE 'CREATE POLICY system_users_self_policy ON system.users
                FOR ALL
                USING (id = current_system_user_id())';
    END IF;
END $$;

-- 系统角色表：系统管理员管理，商户用户可查看
ALTER TABLE system.roles ENABLE ROW LEVEL SECURITY;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'system_roles_admin_policy') THEN
        EXECUTE 'CREATE POLICY system_roles_admin_policy ON system.roles
                FOR ALL
                USING (is_system_admin())';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'system_roles_merchant_view_policy') THEN
        EXECUTE 'CREATE POLICY system_roles_merchant_view_policy ON system.roles
                FOR SELECT
                USING (is_merchant_user())';
    END IF;
END $$;

-- 权限表：系统管理员管理，其他用户可查看
ALTER TABLE system.permissions ENABLE ROW LEVEL SECURITY;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'system_permissions_admin_policy') THEN
        EXECUTE 'CREATE POLICY system_permissions_admin_policy ON system.permissions
                FOR ALL
                USING (is_system_admin())';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'system_permissions_view_policy') THEN
        EXECUTE 'CREATE POLICY system_permissions_view_policy ON system.permissions
                FOR SELECT
                USING (is_merchant_user() OR is_regular_user())';
    END IF;
END $$;

-- 系统用户角色关联表：只有系统管理员可以访问
ALTER TABLE system.user_roles ENABLE ROW LEVEL SECURITY;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'system_user_roles_admin_policy') THEN
        EXECUTE 'CREATE POLICY system_user_roles_admin_policy ON system.user_roles
                FOR ALL
                USING (is_system_admin())';
    END IF;
END $$;

-- 系统角色权限关联表：只有系统管理员可以访问
ALTER TABLE system.role_permissions ENABLE ROW LEVEL SECURITY;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'system_role_permissions_admin_policy') THEN
        EXECUTE 'CREATE POLICY system_role_permissions_admin_policy ON system.role_permissions
                FOR ALL
                USING (is_system_admin())';
    END IF;
END $$;

-- ============================================================================
-- 商户相关表RSL策略
-- ============================================================================

-- 商户表：系统管理员查看所有，商户用户只能查看有权限的商户
ALTER TABLE business.merchants ENABLE ROW LEVEL SECURITY;

-- 系统管理员可以管理所有商户
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchants_admin_policy') THEN
        EXECUTE 'CREATE POLICY merchants_admin_policy ON business.merchants
                FOR ALL
                USING (is_system_admin())';
    END IF;
END $$;

-- 商户用户只能查看有权限的商户
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchants_user_policy') THEN
        EXECUTE 'CREATE POLICY merchants_user_policy ON business.merchants
                FOR SELECT
                USING (is_merchant_user() AND can_access_merchant(id))';
    END IF;
END $$;

-- 商户分类表：系统管理员管理，所有用户可查看
ALTER TABLE business.merchant_categories ENABLE ROW LEVEL SECURITY;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchant_categories_admin_policy') THEN
        EXECUTE 'CREATE POLICY merchant_categories_admin_policy ON business.merchant_categories
                FOR ALL
                USING (is_system_admin())';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchant_categories_view_policy') THEN
        EXECUTE 'CREATE POLICY merchant_categories_view_policy ON business.merchant_categories
                FOR SELECT
                USING (is_merchant_user() OR is_regular_user())';
    END IF;
END $$;

-- ============================================================================
-- 商户用户相关表RSL策略
-- ============================================================================

-- 商户用户表：系统管理员查看所有，商户管理员查看本商户员工，用户查看自己
ALTER TABLE business.merchant_users ENABLE ROW LEVEL SECURITY;

-- 系统管理员可以管理所有商户用户
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchant_users_admin_policy') THEN
        EXECUTE 'CREATE POLICY merchant_users_admin_policy ON business.merchant_users
                FOR ALL
                USING (is_system_admin())';
    END IF;
END $$;

-- 商户用户可以查看和修改自己的信息
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchant_users_self_policy') THEN
        EXECUTE 'CREATE POLICY merchant_users_self_policy ON business.merchant_users
                FOR ALL
                USING (id = current_merchant_user_id())';
    END IF;
END $$;

-- 商户管理员可以查看本商户的员工
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchant_users_manager_policy') THEN
        EXECUTE 'CREATE POLICY merchant_users_manager_policy ON business.merchant_users
                FOR SELECT
                USING (
                    is_merchant_user() AND
                    EXISTS (
                        SELECT 1 
                        FROM business.merchant_user_roles mur
                        WHERE mur.user_id = current_merchant_user_id()
                          AND is_merchant_admin(mur.merchant_id)
                          AND EXISTS (
                              SELECT 1 
                              FROM business.merchant_user_roles mur2
                              WHERE mur2.user_id = business.merchant_users.id
                                AND mur2.merchant_id = mur.merchant_id
                          )
                    )
                )';
    END IF;
END $$;

-- 商户角色表：系统管理员查看所有，商户用户只能查看自己商户的角色
ALTER TABLE business.merchant_roles ENABLE ROW LEVEL SECURITY;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchant_roles_admin_policy') THEN
        EXECUTE 'CREATE POLICY merchant_roles_admin_policy ON business.merchant_roles
                FOR ALL
                USING (is_system_admin())';
    END IF;
END $$;

DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchant_roles_merchant_policy') THEN
        EXECUTE 'CREATE POLICY merchant_roles_merchant_policy ON business.merchant_roles
                FOR ALL
                USING (is_merchant_user() AND can_access_merchant(merchant_id))';
    END IF;
END $$;

-- 商户用户角色关联表：系统管理员查看所有，商户管理员管理本商户的用户角色
ALTER TABLE business.merchant_user_roles ENABLE ROW LEVEL SECURITY;

-- 系统管理员可以管理所有用户角色关联
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchant_user_roles_admin_policy') THEN
        EXECUTE 'CREATE POLICY merchant_user_roles_admin_policy ON business.merchant_user_roles
                FOR ALL
                USING (is_system_admin())';
    END IF;
END $$;

-- 商户用户可以查看自己的角色
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchant_user_roles_self_policy') THEN
        EXECUTE 'CREATE POLICY merchant_user_roles_self_policy ON business.merchant_user_roles
                FOR SELECT
                USING (user_id = current_merchant_user_id())';
    END IF;
END $$;

-- 商户管理员可以管理本商户的用户角色
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE policyname = 'merchant_user_roles_manager_policy') THEN
        EXECUTE 'CREATE POLICY merchant_user_roles_manager_policy ON business.merchant_user_roles
                FOR ALL
                USING (is_merchant_user() AND is_merchant_admin(merchant_id))';
    END IF;
END $$;

-- ============================================================================
-- 会话管理函数
-- ============================================================================

-- 设置系统管理员上下文
CREATE OR REPLACE FUNCTION set_system_admin_context(user_id UUID) RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_user_type', 'system_admin', false);
    PERFORM set_config('app.current_system_user_id', user_id::TEXT, false);
    -- 清除其他用户上下文
    PERFORM set_config('app.current_merchant_user_id', NULL, false);
    PERFORM set_config('app.current_merchant_id', NULL, false);
    PERFORM set_config('app.current_regular_user_id', NULL, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 设置商户用户上下文
CREATE OR REPLACE FUNCTION set_merchant_user_context(user_id UUID, merchant_id BIGINT) RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_user_type', 'merchant_user', false);
    PERFORM set_config('app.current_merchant_user_id', user_id::TEXT, false);
    PERFORM set_config('app.current_merchant_id', merchant_id::TEXT, false);
    -- 清除其他用户上下文
    PERFORM set_config('app.current_system_user_id', NULL, false);
    PERFORM set_config('app.current_regular_user_id', NULL, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 设置普通用户上下文（未来使用）
CREATE OR REPLACE FUNCTION set_regular_user_context(user_id UUID) RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_user_type', 'regular_user', false);
    PERFORM set_config('app.current_regular_user_id', user_id::TEXT, false);
    -- 清除其他用户上下文
    PERFORM set_config('app.current_system_user_id', NULL, false);
    PERFORM set_config('app.current_merchant_user_id', NULL, false);
    PERFORM set_config('app.current_merchant_id', NULL, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 清除所有会话上下文
CREATE OR REPLACE FUNCTION clear_session_context() RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_user_type', NULL, false);
    PERFORM set_config('app.current_system_user_id', NULL, false);
    PERFORM set_config('app.current_merchant_user_id', NULL, false);
    PERFORM set_config('app.current_merchant_id', NULL, false);
    PERFORM set_config('app.current_regular_user_id', NULL, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 创建数据库角色
-- ============================================================================

-- 创建管理员角色（绕过RSL）
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'rls_admin') THEN
        CREATE ROLE rls_admin;
    END IF;
END $$;

-- 赋予管理员角色绕过RSL的权限
ALTER ROLE rls_admin BYPASSRLS;

-- 赋予必要的权限
GRANT USAGE ON SCHEMA system TO rls_admin;
GRANT USAGE ON SCHEMA business TO rls_admin;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA system TO rls_admin;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA business TO rls_admin;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA business TO rls_admin;

-- 创建应用程序连接角色
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'app_user') THEN
        CREATE ROLE app_user;
    END IF;
END $$;

-- 赋予应用程序角色必要的权限
GRANT USAGE ON SCHEMA system TO app_user;
GRANT USAGE ON SCHEMA business TO app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA system TO app_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA business TO app_user;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA business TO app_user;

-- 赋予执行RSL函数的权限
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO app_user; 
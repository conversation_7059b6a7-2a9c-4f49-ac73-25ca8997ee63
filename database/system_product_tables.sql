--
-- 系统级商品相关表创建脚本
-- 这些表由系统超级管理员维护，为所有商户提供基础分类和标签数据
-- 注意：不使用外键约束，采用应用层控制数据完整性
--

-- 1. 系统商品分类表（二级分类结构）
CREATE TABLE system.system_product_categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    parent_id BIGINT,
    level INTEGER NOT NULL DEFAULT 1,
    sort_order INTEGER NOT NULL DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    icon_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL,
    updated_by BIGINT NOT NULL
);

-- 系统商品分类表注释
COMMENT ON TABLE system.system_product_categories IS '系统商品分类表 - 商户发布商品时可多选';
COMMENT ON COLUMN system.system_product_categories.id IS '主键ID';
COMMENT ON COLUMN system.system_product_categories.name IS '分类名称，最大50字符';
COMMENT ON COLUMN system.system_product_categories.description IS '分类描述';
COMMENT ON COLUMN system.system_product_categories.parent_id IS '父分类ID，一级分类为NULL，二级分类关联一级分类ID';
COMMENT ON COLUMN system.system_product_categories.level IS '分类层级：1-一级分类，2-二级分类';
COMMENT ON COLUMN system.system_product_categories.sort_order IS '排序字段，数值越小越靠前';
COMMENT ON COLUMN system.system_product_categories.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN system.system_product_categories.icon_url IS '分类图标URL';
COMMENT ON COLUMN system.system_product_categories.created_at IS '创建时间';
COMMENT ON COLUMN system.system_product_categories.updated_at IS '更新时间';
COMMENT ON COLUMN system.system_product_categories.created_by IS '创建人ID';
COMMENT ON COLUMN system.system_product_categories.updated_by IS '更新人ID';

-- 系统商品分类索引（仅保留必需的）
CREATE INDEX idx_system_product_categories_parent_id ON system.system_product_categories(parent_id);

-- 2. 系统商品标签表
CREATE TABLE system.system_product_tags (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(30) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7) DEFAULT '#1890ff',
    sort_order INTEGER NOT NULL DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL,
    updated_by BIGINT NOT NULL
);

-- 系统商品标签表注释
COMMENT ON TABLE system.system_product_tags IS '系统商品标签表 - 由系统超级管理员维护，商户发布商品时选择标签，方便用户快速筛选';
COMMENT ON COLUMN system.system_product_tags.id IS '主键ID';
COMMENT ON COLUMN system.system_product_tags.name IS '标签名称，最大30字符，全局唯一';
COMMENT ON COLUMN system.system_product_tags.description IS '标签描述';
COMMENT ON COLUMN system.system_product_tags.color IS '标签颜色，十六进制格式如#1890ff';
COMMENT ON COLUMN system.system_product_tags.sort_order IS '排序字段，数值越小越靠前';
COMMENT ON COLUMN system.system_product_tags.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN system.system_product_tags.created_at IS '创建时间';
COMMENT ON COLUMN system.system_product_tags.updated_at IS '更新时间';
COMMENT ON COLUMN system.system_product_tags.created_by IS '创建人ID';
COMMENT ON COLUMN system.system_product_tags.updated_by IS '更新人ID';

-- 系统商品标签索引（UNIQUE约束自动创建name索引，无需额外索引）

-- 3. 系统客户分类表
CREATE TABLE system.system_customer_categories (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    status INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT NOT NULL,
    updated_by BIGINT NOT NULL
);

-- 系统客户分类表注释
COMMENT ON TABLE system.system_customer_categories IS '系统客户分类表 - 由系统超级管理员统一维护，商户使用该分类对客户进行分类管理';
COMMENT ON COLUMN system.system_customer_categories.id IS '主键ID';
COMMENT ON COLUMN system.system_customer_categories.name IS '客户分类名称，最大50字符，全局唯一';
COMMENT ON COLUMN system.system_customer_categories.description IS '分类描述';
COMMENT ON COLUMN system.system_customer_categories.sort_order IS '排序字段，数值越小越靠前';
COMMENT ON COLUMN system.system_customer_categories.status IS '状态：0-禁用，1-启用';
COMMENT ON COLUMN system.system_customer_categories.created_at IS '创建时间';
COMMENT ON COLUMN system.system_customer_categories.updated_at IS '更新时间';
COMMENT ON COLUMN system.system_customer_categories.created_by IS '创建人ID';
COMMENT ON COLUMN system.system_customer_categories.updated_by IS '更新人ID';

-- 系统客户分类索引（UNIQUE约束自动创建name索引，无需额外索引）

-- 注意：系统商户分类表已存在于 business.merchant_categories

-- 插入初始数据示例
-- 系统商品分类：一级分类
INSERT INTO system.system_product_categories (name, description, level, sort_order, created_by, updated_by) VALUES
('食品饮料', '各类食品和饮料商品', 1, 1, 1, 1),
('日用百货', '日常生活用品', 1, 2, 1, 1),
('电子产品', '各类电子设备和配件', 1, 3, 1, 1);

-- 系统商品分类：二级分类
INSERT INTO system.system_product_categories (name, description, parent_id, level, sort_order, created_by, updated_by) VALUES
('新鲜水果', '各类新鲜水果', 1, 2, 1, 1, 1),
('休闲零食', '各类休闲食品', 1, 2, 2, 1, 1),
('饮料饮品', '各类饮料饮品', 1, 2, 3, 1, 1),
('清洁用品', '家庭清洁用品', 2, 2, 1, 1, 1),
('个人护理', '个人护理用品', 2, 2, 2, 1, 1),
('手机数码', '手机及数码产品', 3, 2, 1, 1, 1);

-- 系统商品标签
INSERT INTO system.system_product_tags (name, description, color, sort_order, created_by, updated_by) VALUES
('热销', '热销商品标签', '#ff4d4f', 1, 1, 1),
('新品', '新品上市标签', '#52c41a', 2, 1, 1),
('促销', '促销活动标签', '#faad14', 3, 1, 1),
('精选', '精选推荐标签', '#1890ff', 4, 1, 1),
('特价', '特价商品标签', '#fa541c', 5, 1, 1);

-- 系统客户分类
INSERT INTO system.system_customer_categories (name, description, sort_order, created_by, updated_by) VALUES
('普通客户', '一般消费客户', 1, 1, 1),
('VIP客户', '重要客户', 2, 1, 1),
('企业客户', '企业采购客户', 3, 1, 1),
('批发客户', '批发商客户', 4, 1, 1);

-- 系统商户分类数据已存在于 business.merchant_categories 表中
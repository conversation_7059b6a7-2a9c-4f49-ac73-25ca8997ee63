-- 开单记账系统数据库设计
-- Database: invoice-book
-- PostgreSQL 版本: 14+

-- 创建系统模式
CREATE SCHEMA IF NOT EXISTS system;
COMMENT ON SCHEMA system IS '系统管理模式，包含用户、角色、权限等系统级数据';

-- 系统用户表
CREATE TABLE system.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    avatar TEXT,
    gender INTEGER,
    status INTEGER NOT NULL DEFAULT 1 CHECK (status IN (1, 2, 3)),
    last_login_date TIMESTAMP WITH TIME ZONE,
    last_login_ip VARCHAR(45),
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUI<PERSON>,
    updated_by UUID,
    remark TEXT
);

-- 添加列注释
COMMENT ON TABLE system.users IS '系统用户表';
COMMENT ON COLUMN system.users.id IS '用户ID';
COMMENT ON COLUMN system.users.username IS '用户名';
COMMENT ON COLUMN system.users.password IS '密码';
COMMENT ON COLUMN system.users.real_name IS '真实姓名';
COMMENT ON COLUMN system.users.phone IS '手机号码';
COMMENT ON COLUMN system.users.email IS '邮箱地址';
COMMENT ON COLUMN system.users.avatar IS '头像URL';
COMMENT ON COLUMN system.users.gender IS '性别：1-男，2-女，3-未知';
COMMENT ON COLUMN system.users.status IS '用户状态：1-启用，2-禁用，3-锁定';
COMMENT ON COLUMN system.users.last_login_date IS '最后登录时间';
COMMENT ON COLUMN system.users.last_login_ip IS '最后登录IP地址';
COMMENT ON COLUMN system.users.created_date IS '创建时间';
COMMENT ON COLUMN system.users.updated_date IS '修改时间';
COMMENT ON COLUMN system.users.created_by IS '创建人ID';
COMMENT ON COLUMN system.users.updated_by IS '修改人ID';
COMMENT ON COLUMN system.users.remark IS '备注信息';

-- 索引
CREATE INDEX idx_system_users_status ON system.users (status);

-- 系统角色表
CREATE TABLE system.roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    remark TEXT
);

-- 添加列注释
COMMENT ON TABLE system.roles IS '系统角色表';
COMMENT ON COLUMN system.roles.id IS '角色ID';
COMMENT ON COLUMN system.roles.name IS '角色名称';
COMMENT ON COLUMN system.roles.description IS '角色描述';
COMMENT ON COLUMN system.roles.created_date IS '创建时间';
COMMENT ON COLUMN system.roles.updated_date IS '修改时间';
COMMENT ON COLUMN system.roles.created_by IS '创建人ID';
COMMENT ON COLUMN system.roles.updated_by IS '修改人ID';
COMMENT ON COLUMN system.roles.remark IS '备注信息';

-- 系统用户角色关联表
CREATE TABLE system.user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    role_id UUID NOT NULL,
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    remark TEXT
);

-- 添加列注释
COMMENT ON TABLE system.user_roles IS '系统用户角色关联表';
COMMENT ON COLUMN system.user_roles.id IS '关联ID';
COMMENT ON COLUMN system.user_roles.user_id IS '系统用户ID';
COMMENT ON COLUMN system.user_roles.role_id IS '系统角色ID';
COMMENT ON COLUMN system.user_roles.created_date IS '创建时间';
COMMENT ON COLUMN system.user_roles.created_by IS '创建人ID';
COMMENT ON COLUMN system.user_roles.remark IS '备注信息';

-- 索引
CREATE INDEX idx_system_user_roles_user_id ON system.user_roles (user_id);
CREATE INDEX idx_system_user_roles_role_id ON system.user_roles (role_id);
CREATE UNIQUE INDEX idx_system_user_roles_user_role ON system.user_roles (user_id, role_id);

-- 权限表
CREATE TABLE system.permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    menu_name VARCHAR(50) NOT NULL,
    parent_id UUID,
    order_num INTEGER DEFAULT 0,
    path VARCHAR(200),
    component VARCHAR(255),
    query VARCHAR(255),
    is_frame INTEGER DEFAULT 1,
    is_cache INTEGER DEFAULT 0,
    menu_type INTEGER DEFAULT 1,
    visible INTEGER DEFAULT 0,
    status INTEGER DEFAULT 0,
    perms VARCHAR(100),
    icon VARCHAR(100) DEFAULT '#',
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    remark TEXT
);

-- 添加列注释
COMMENT ON TABLE system.permissions IS '权限表';
COMMENT ON COLUMN system.permissions.id IS '权限ID';
COMMENT ON COLUMN system.permissions.menu_name IS '菜单名称';
COMMENT ON COLUMN system.permissions.parent_id IS '父菜单ID';
COMMENT ON COLUMN system.permissions.order_num IS '显示顺序';
COMMENT ON COLUMN system.permissions.path IS '路由地址';
COMMENT ON COLUMN system.permissions.component IS '组件路径';
COMMENT ON COLUMN system.permissions.query IS '路由参数';
COMMENT ON COLUMN system.permissions.is_frame IS '是否为外链：0-是，1-否';
COMMENT ON COLUMN system.permissions.is_cache IS '是否缓存：0-缓存，1-不缓存';
COMMENT ON COLUMN system.permissions.menu_type IS '菜单类型：1-目录，2-菜单，3-按钮';
COMMENT ON COLUMN system.permissions.visible IS '菜单状态：0-显示，1-隐藏';
COMMENT ON COLUMN system.permissions.status IS '菜单状态：0-正常，1-停用';
COMMENT ON COLUMN system.permissions.perms IS '权限标识';
COMMENT ON COLUMN system.permissions.icon IS '菜单图标';
COMMENT ON COLUMN system.permissions.created_date IS '创建时间';
COMMENT ON COLUMN system.permissions.updated_date IS '修改时间';
COMMENT ON COLUMN system.permissions.created_by IS '创建人ID';
COMMENT ON COLUMN system.permissions.updated_by IS '修改人ID';
COMMENT ON COLUMN system.permissions.remark IS '备注信息';

-- 索引
CREATE INDEX idx_system_permissions_parent_id ON system.permissions (parent_id);
CREATE INDEX idx_system_permissions_menu_type ON system.permissions (menu_type);
CREATE INDEX idx_system_permissions_status ON system.permissions (status);

-- 系统角色权限关联表
CREATE TABLE system.role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL,
    permission_id UUID NOT NULL,
    created_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    remark TEXT
);

-- 添加列注释
COMMENT ON TABLE system.role_permissions IS '系统角色权限关联表';
COMMENT ON COLUMN system.role_permissions.id IS '关联ID';
COMMENT ON COLUMN system.role_permissions.role_id IS '系统角色ID';
COMMENT ON COLUMN system.role_permissions.permission_id IS '系统权限ID';
COMMENT ON COLUMN system.role_permissions.created_date IS '创建时间';
COMMENT ON COLUMN system.role_permissions.created_by IS '创建人ID';
COMMENT ON COLUMN system.role_permissions.remark IS '备注信息';

-- 索引
CREATE INDEX idx_system_role_permissions_role_id ON system.role_permissions (role_id);
CREATE INDEX idx_system_role_permissions_permission_id ON system.role_permissions (permission_id);
CREATE UNIQUE INDEX idx_system_role_permissions_role_permission ON system.role_permissions (role_id, permission_id);
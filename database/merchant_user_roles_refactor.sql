-- =====================================================
-- 用户角色关系表重构
-- =====================================================
-- 重构目标：
-- 1. 将用户角色关系从直接关联用户改为关联用户商户关系
-- 2. 支持用户在不同商户中拥有不同角色
-- 3. 保持角色权限管理的完整性
-- 4. 提供平滑的数据迁移方案
-- =====================================================

-- 备份原表（可选，生产环境建议执行）
-- CREATE TABLE business.merchant_user_roles_backup AS SELECT * FROM business.merchant_user_roles;

-- =====================================================
-- 第一步：创建新的用户角色关系表
-- =====================================================

-- 如果表已存在，需要先备份或删除（谨慎操作）
-- SELECT business.backup_old_merchant_user_roles(); -- 备份原表
-- DROP TABLE IF EXISTS business.merchant_user_roles CASCADE; -- 删除原表

-- 重构后的用户角色关系表
CREATE TABLE business.merchant_user_roles (
    -- 主键
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 关联用户商户关系（核心变更：从user_id改为user_merchant_id，不使用数据库外键）
    user_merchant_id UUID NOT NULL,
    
    -- 角色信息（不使用数据库外键，由应用层保证数据完整性）
    role_id UUID NOT NULL,
    
    -- 角色分配状态（由应用层控制有效值）
    status INTEGER NOT NULL DEFAULT 1 CHECK (status >= 0),
    -- 状态说明（代码层枚举控制）：
    -- 0=禁用(Disabled) 1=启用(Enabled)
    -- 后续可扩展：2=暂停(Suspended) 3=过期(Expired) 等
    
    -- 审计字段
    created_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    
    -- 备注
    remark TEXT,
    
    -- 唯一约束：用户在商户中的同一个角色只能分配一次
    CONSTRAINT uk_merchant_user_roles_user_merchant_role UNIQUE(user_merchant_id, role_id)
);

-- 创建索引
CREATE INDEX idx_merchant_user_roles_user_merchant_id ON business.merchant_user_roles(user_merchant_id);
CREATE INDEX idx_merchant_user_roles_role_id ON business.merchant_user_roles(role_id);
CREATE INDEX idx_merchant_user_roles_status ON business.merchant_user_roles(status);

-- 表注释
COMMENT ON TABLE business.merchant_user_roles IS '用户角色关系表 - 管理用户在不同商户中的角色分配';
COMMENT ON COLUMN business.merchant_user_roles.id IS '关系ID - 主键';
COMMENT ON COLUMN business.merchant_user_roles.user_merchant_id IS '用户商户关系ID - 关联merchant_user_merchants表';
COMMENT ON COLUMN business.merchant_user_roles.role_id IS '角色ID - 关联merchant_roles表';
COMMENT ON COLUMN business.merchant_user_roles.status IS '状态 - 0=禁用 1=启用';
COMMENT ON COLUMN business.merchant_user_roles.created_date IS '创建时间';
COMMENT ON COLUMN business.merchant_user_roles.updated_date IS '更新时间';
COMMENT ON COLUMN business.merchant_user_roles.created_by IS '创建人ID';
COMMENT ON COLUMN business.merchant_user_roles.updated_by IS '更新人ID';
COMMENT ON COLUMN business.merchant_user_roles.remark IS '备注信息';

-- =====================================================
-- 第二步：数据迁移脚本
-- =====================================================

-- 数据迁移函数
CREATE OR REPLACE FUNCTION business.migrate_merchant_user_roles()
RETURNS VOID AS $$
DECLARE
    migration_count INTEGER := 0;
    error_count INTEGER := 0;
    rec RECORD;
BEGIN
    -- 记录迁移开始
    RAISE NOTICE '开始迁移merchant_user_roles数据...';
    
    -- 迁移数据：从旧表到新表
    FOR rec IN 
        SELECT 
            old_mur.id as old_id,
            old_mur.user_id,
            old_mur.merchant_id,
            old_mur.role_id,
            old_mur.status,
            old_mur.created_date,
            old_mur.updated_date,
            old_mur.created_by,
            old_mur.updated_by,
            old_mur.remark,
            mum.id as user_merchant_id
        FROM business.merchant_user_roles old_mur
        JOIN business.merchant_user_merchants mum 
            ON old_mur.user_id = mum.user_id 
            AND old_mur.merchant_id = mum.merchant_id
        WHERE mum.status = 1 -- 只迁移active状态的用户商户关系
    LOOP
        BEGIN
            -- 插入到新表
            INSERT INTO business.merchant_user_roles (
                user_merchant_id,
                role_id,
                status,
                created_date,
                updated_date,
                created_by,
                updated_by,
                remark
            ) VALUES (
                rec.user_merchant_id,
                rec.role_id,
                rec.status,
                rec.created_date,
                rec.updated_date,
                rec.created_by,
                rec.updated_by,
                COALESCE(rec.remark, '数据迁移')
            ) ON CONFLICT (user_merchant_id, role_id) DO NOTHING;
            
            migration_count := migration_count + 1;
            
        EXCEPTION WHEN OTHERS THEN
            error_count := error_count + 1;
            RAISE NOTICE '迁移记录失败: user_id=%, merchant_id=%, role_id=%, error=%', 
                rec.user_id, rec.merchant_id, rec.role_id, SQLERRM;
        END;
    END LOOP;
    
    -- 输出迁移结果
    RAISE NOTICE '数据迁移完成: 成功=%条, 失败=%条', migration_count, error_count;
END;
$$ LANGUAGE plpgsql;

-- 执行数据迁移（注释掉，需要手动执行）
-- SELECT business.migrate_merchant_user_roles();

-- =====================================================
-- 第三步：表初始化脚本（如果需要清理旧表）
-- =====================================================

-- 如果需要替换现有表，可以使用以下脚本（谨慎执行）
-- DROP TABLE IF EXISTS business.merchant_user_roles CASCADE;

-- 创建备份的函数（可选）
CREATE OR REPLACE FUNCTION business.backup_old_merchant_user_roles()
RETURNS VOID AS $$
BEGIN
    -- 备份原表（如果存在）
    CREATE TABLE business.merchant_user_roles_backup AS 
    SELECT * FROM business.merchant_user_roles;
    
    RAISE NOTICE '原表已备份到 merchant_user_roles_backup';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 清理和优化
-- =====================================================

-- 清理旧的备份表函数（谨慎执行）
CREATE OR REPLACE FUNCTION business.cleanup_merchant_user_roles_migration()
RETURNS VOID AS $$
BEGIN
    -- 删除备份表（确认迁移成功后执行）
    -- DROP TABLE IF EXISTS business.merchant_user_roles_backup;
    -- DROP TABLE IF EXISTS business.merchant_user_roles_old;
    
    -- 删除迁移函数（确认不再需要后执行）
    -- DROP FUNCTION IF EXISTS business.migrate_merchant_user_roles();
    -- DROP FUNCTION IF EXISTS business.backup_old_merchant_user_roles();
    
    RAISE NOTICE '清理函数准备就绪，请手动执行需要的清理操作';
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION business.cleanup_merchant_user_roles_migration() IS '清理用户角色关系表迁移相关的临时对象';

-- =====================================================
-- 使用说明
-- =====================================================

/*
使用步骤：

1. 确保merchant_user_merchants表已经创建并有数据
2. 如果需要保留原表数据，先执行备份：SELECT business.backup_old_merchant_user_roles();
3. 如果存在旧表，可选择删除：DROP TABLE IF EXISTS business.merchant_user_roles CASCADE;
4. 执行本脚本创建新表结构
5. 如果需要迁移原有数据，运行：SELECT business.migrate_merchant_user_roles();
6. 验证数据完整性
7. 测试所有相关功能
8. 确认稳定后，执行清理：SELECT business.cleanup_merchant_user_roles_migration();

注意事项：
- 生产环境执行前务必备份数据
- 建议在维护窗口期间执行
- 迁移过程中可能需要停止相关服务
- 仔细测试所有使用用户角色的功能

表结构说明：
- user_merchant_id: 关联用户商户关系表的ID，实现用户在不同商户中的角色分配
- role_id: 角色ID，由应用层保证数据完整性
- status: 角色状态，支持启用/禁用切换（0=禁用 1=启用）
- 移除了角色分配时间管理字段，简化表结构
- 状态值由应用层枚举控制，新增状态值时只需修改代码层枚举，无需修改数据库结构
*/ 
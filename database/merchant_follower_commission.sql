-- =============================================
-- 商户跟进人佣金表
-- Schema: business
-- =============================================

CREATE TABLE IF NOT EXISTS business.merchant_follower_commission (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(), -- 主键，自增UUID
    merchant_id BIGINT NOT NULL,                    -- 商户ID（关联business.merchants.id）
    user_id UUID NOT NULL,                        -- 跟进人ID（关联system.users.id）
    commission_type INT NOT NULL DEFAULT 1,       -- 佣金类型：1=比例，2=固定金额
    commission_value DECIMAL(18,4) NOT NULL,      -- 佣金值：比例（如0.05）或金额（如100.00）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), -- 创建时间
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(), -- 更新时间
    remark TEXT                                   -- 备注
);

-- 表注释
COMMENT ON TABLE business.merchant_follower_commission IS '商户跟进人佣金表：记录每个商户与跟进人之间的佣金关系';

-- 字段注释
COMMENT ON COLUMN business.merchant_follower_commission.id IS '主键，自增UUID';
COMMENT ON COLUMN business.merchant_follower_commission.merchant_id IS '商户ID（关联business.merchants.id）';
COMMENT ON COLUMN business.merchant_follower_commission.user_id IS '跟进人ID（关联system.users.id）';
COMMENT ON COLUMN business.merchant_follower_commission.commission_type IS '佣金类型：1=比例，2=固定金额';
COMMENT ON COLUMN business.merchant_follower_commission.commission_value IS '佣金值：比例（如0.05）或金额（如100.00）';
COMMENT ON COLUMN business.merchant_follower_commission.created_at IS '创建时间';
COMMENT ON COLUMN business.merchant_follower_commission.updated_at IS '更新时间';
COMMENT ON COLUMN business.merchant_follower_commission.remark IS '备注'; 
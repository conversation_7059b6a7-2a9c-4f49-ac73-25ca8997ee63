-- =====================================================
-- 用户商户关系表设计
-- =====================================================
-- 解决问题：
-- 1. 用户可以加入多个商户（多对多关系）
-- 2. 用户加入商户和角色分配分离
-- 3. 支持用户申请/邀请加入商户的业务流程
-- 4. 支持用户在商户中的生命周期管理
-- =====================================================

-- 用户商户关系表
CREATE TABLE business.merchant_user_merchants (
    -- 主键
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 关联字段（不使用数据库外键，由应用层保证数据完整性）
    user_id UUID NOT NULL,
    merchant_id BIGINT NOT NULL,
    
    -- 用户在该商户的状态（由应用层控制有效值）
    status INTEGER NOT NULL DEFAULT 1 CHECK (status > 0),
    -- 1=正常(Active) 2=待审核(Pending) 3=已离开(Left) 4=被移除(Removed)
    
    -- 加入类型（由应用层控制有效值）
    join_type INTEGER NOT NULL DEFAULT 1 CHECK (join_type > 0),
    -- 1=邀请加入(Invited) 2=申请加入(Applied) 3=系统添加(System)
    
    -- 时间字段
    join_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 加入时间
    leave_date TIMESTAMPTZ, -- 离开时间（可为空）
    
    -- 审计字段
    created_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by UUID,
    updated_by UUID,
    
    -- 备注
    remark TEXT,
    
    -- 唯一约束：一个用户在一个商户中只能有一条记录
    CONSTRAINT uk_merchant_user_merchants_user_merchant UNIQUE(user_id, merchant_id)
);

-- 创建索引
CREATE INDEX idx_merchant_user_merchants_user_id ON business.merchant_user_merchants(user_id);
CREATE INDEX idx_merchant_user_merchants_merchant_id ON business.merchant_user_merchants(merchant_id);
CREATE INDEX idx_merchant_user_merchants_status ON business.merchant_user_merchants(status);
CREATE INDEX idx_merchant_user_merchants_join_type ON business.merchant_user_merchants(join_type);
CREATE INDEX idx_merchant_user_merchants_join_date ON business.merchant_user_merchants(join_date);

-- 表注释
COMMENT ON TABLE business.merchant_user_merchants IS '用户商户关系表 - 管理用户与商户之间的多对多关系';
COMMENT ON COLUMN business.merchant_user_merchants.id IS '关系ID - 主键';
COMMENT ON COLUMN business.merchant_user_merchants.user_id IS '用户ID - 关联merchant_users表';
COMMENT ON COLUMN business.merchant_user_merchants.merchant_id IS '商户ID - 关联merchants表';
COMMENT ON COLUMN business.merchant_user_merchants.status IS '状态 - 1=正常 2=待审核 3=已离开 4=被移除';
COMMENT ON COLUMN business.merchant_user_merchants.join_type IS '加入类型 - 1=邀请加入 2=申请加入 3=系统添加';
COMMENT ON COLUMN business.merchant_user_merchants.join_date IS '加入时间';
COMMENT ON COLUMN business.merchant_user_merchants.leave_date IS '离开时间';
COMMENT ON COLUMN business.merchant_user_merchants.created_date IS '创建时间';
COMMENT ON COLUMN business.merchant_user_merchants.updated_date IS '更新时间';
COMMENT ON COLUMN business.merchant_user_merchants.created_by IS '创建人ID';
COMMENT ON COLUMN business.merchant_user_merchants.updated_by IS '更新人ID';
COMMENT ON COLUMN business.merchant_user_merchants.remark IS '备注信息'; 
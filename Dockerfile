# rust builder
# FROM rust:1.87-slim AS builder
FROM rust:1.87-slim AS builder


# 使用国内镜像源加速依赖安装
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources

# 安装构建必要依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 配置 Rust 国内镜像源
RUN mkdir -p ~/.cargo && \
    echo '[source.crates-io]' > ~/.cargo/config.toml && \
    echo 'replace-with = "rsproxy-sparse"' >> ~/.cargo/config.toml && \
    echo '[source.rsproxy]' >> ~/.cargo/config.toml && \
    echo 'registry = "https://rsproxy.cn/crates.io-index"' >> ~/.cargo/config.toml && \
    echo '[source.rsproxy-sparse]' >> ~/.cargo/config.toml && \
    echo 'registry = "sparse+https://rsproxy.cn/index/"' >> ~/.cargo/config.toml

WORKDIR /build

# 先复制 Cargo.toml 和 Cargo.lock 文件
COPY Cargo.toml Cargo.lock ./
COPY crates/libs/lib-auth/Cargo.toml ./crates/libs/lib-auth/
COPY crates/libs/lib-core/Cargo.toml ./crates/libs/lib-core/
COPY crates/libs/lib-web/Cargo.toml ./crates/libs/lib-web/
COPY crates/libs/lib-cache/Cargo.toml ./crates/libs/lib-cache/
COPY crates/libs/lib-data/Cargo.toml ./crates/libs/lib-data/
COPY crates/libs/lib-job/Cargo.toml ./crates/libs/lib-job/
COPY crates/libs/lib-macros/Cargo.toml ./crates/libs/lib-macros/
COPY crates/libs/lib-store/Cargo.toml ./crates/libs/lib-store/
COPY crates/services/app-service/Cargo.toml ./crates/services/app-service/


# 复制所有源代码
COPY crates ./crates

# 构建项目
RUN cargo build --release

# runner container
# FROM debian:bookworm-slim
FROM docker.1ms.run/library/debian:bookworm-slim

# 使用国内镜像源
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources && \
    sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    libssl-dev \
    libpq5 \
    ca-certificates \
    tzdata \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 设置时区为中国上海时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置环境变量
ENV RUST_LOG=info
ENV APP_ENV=prod
ENV CONFIG_FILE=/app/config/app-prod.toml

# 创建工作目录
WORKDIR /app

# 复制可执行文件
COPY --from=builder /build/target/release/app-service ./app-service

# 创建配置目录
RUN mkdir -p config logs/app-service crates/services/app-service/config

# 复制配置文件到多个位置确保能被找到
COPY crates/services/app-service/config/app-prod.toml ./config/app-prod.toml
COPY crates/services/app-service/config/app-prod.toml ./config/app.toml
COPY crates/services/app-service/config/app-prod.toml ./crates/services/app-service/config/app.toml
COPY crates/services/app-service/config/app-prod.toml ./crates/services/app-service/config/app-prod.toml

# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

# 暴露应用端口
EXPOSE 9002

# 启动应用
CMD ["./app-service"]
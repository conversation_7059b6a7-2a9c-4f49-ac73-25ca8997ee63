[workspace]
resolver = "2"
members = [
    "crates/libs/lib-auth",
    "crates/libs/lib-cache",
    "crates/libs/lib-codegen",
    "crates/libs/lib-core",
    "crates/libs/lib-data",
    "crates/libs/lib-job",
    "crates/libs/lib-macros",
    "crates/libs/lib-sharding",
    "crates/libs/lib-store",
    "crates/libs/lib-web", "crates/libs/lib-wechat",
    "crates/services/app-service",
]

[workspace.package]
version = "0.0.1"
edition = "2024"


[workspace.dependencies]
# 数据库相关
sea-orm = { version = "1.1.11", features = [
    "sqlx-postgres",
    "runtime-tokio-rustls",
    "macros",
    "with-uuid",
    "with-time",
    "with-chrono",
    "debug-print",
    "uuid",
] }
sea-query = { version = "0.32.5" }
redis = { version = "0.31.0", features = ["tokio-comp", "json"] }

# Web框架相关
axum = { version = "0.8.4", features = ["tokio"] }
http = { version = "1.0" }
# 注意：app-service 使用 http = "0.2.11" 因为兼容性要求
tower = { version = "0.4", features = ["timeout", "util"] }
tower-http = { version = "0.5", features = ["trace"] }

# 序列化相关
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0"

# 异步运行时
tokio = { version = "1.45.0", features = ["rt", "rt-multi-thread", "macros"] }
async-trait = "0.1.88"

# 日志和追踪
tracing = "0.1.41"
tracing-subscriber = { version = "0.3", features = [
    "env-filter",
    "json",
    "registry",
    "fmt",
] }
tracing-appender = "0.2"
tracing-error = "0.2"

# 时间处理
chrono = "0.4.31"

# 错误处理
thiserror = "2.0.12"
anyhow = "1.0.98"

# 配置管理
config = { version = "0.15.11" }

# 密码处理
argon2 = { version = "0.5" }
password-hash = { version = "0.5" }

# 验证
validator = { version = "0.20.0" }


# 数值处理
rust_decimal = { version = "1.35", features = ["serde"] }

# 宏相关
proc-macro2 = "1.0"
quote = "1.0"
syn = "2.0"

# UUID支持
uuid = { version = "1.17.0", features = ["v4", "v7"] }

# 其他常用依赖
log = "0.4.27"
once_cell = "1.21.3"
dashmap = "7.0.0-rc2"
toml = "0.8.22"
nu-ansi-term = "0.46.0"
serde-toml-merge = "0.3.9"
dotenvy = "0.15.7"
tempfile = "3.20.0"
inventory = "0.3.20"
futures = "0.3"
jsonwebtoken = "8"
serde_yaml = "0.9"
bon = "3.6"
hmac = "0.12.1"
base64 = "0.22.1"
reqwest = { version = "0.12.15", features = ["json"] }
urlencoding = "2.1.3"
lazy_static = "1.5.0"
regex = "1.11.1"
utoipa = { version = "5.3.1" }
form_urlencoded = "1.2.1"
serde_urlencoded = "0.7.1"
serde_path_to_error = "0.1.17"
schemars = { version = "1.0.0-alpha.17", features = ["derive"] }
byte-unit = "5.1"
tokio-cron-scheduler = "0.14.0"
derive_more = { version = "2.0.1", features = ["default"] }
Inflector = "0.11.4"
handlebars = { version = "6.3.2" }

# ========================================
# 🚀 编译性能优化配置
# ========================================

# 🔧 发布版本优化 - 追求极致性能
[profile.release]
opt-level = 3              # 最高优化级别
lto = "thin"               # 启用链接时优化 (推荐thin，比full编译快)
codegen-units = 1          # 减少代码生成单元，提高优化效果
panic = "abort"            # panic时直接终止，减少二进制大小
strip = true               # 剥离调试信息，减少二进制大小
debug = false              # 禁用调试信息

# 🔥 生产环境超级优化版本
[profile.production]
inherits = "release"       # 继承release配置
opt-level = 3
lto = "fat"                # 完整LTO优化，编译慢但性能最佳
codegen-units = 1
panic = "abort"
strip = true
debug = false
overflow-checks = false    # 禁用溢出检查，提升性能

# 🛠️ 开发版本优化 - 平衡编译速度和调试体验
[profile.dev]
opt-level = 0              # 快速编译
debug = true               # 保留调试信息
split-debuginfo = "unpacked"  # macOS/Linux上加速链接
incremental = true         # 启用增量编译

# 🧪 测试版本优化
[profile.test]
opt-level = 1              # 轻微优化，加速测试
debug = true
incremental = true

# ========================================
# 📦 依赖优化 - 即使在debug模式下也优化依赖
# ========================================
[profile.dev.package."*"]
opt-level = 2              # 依赖库始终使用优化级别2

[profile.dev.package.sqlx-macros]
opt-level = 3              # SQL宏编译优化

[profile.dev.package.sea-orm-macros]
opt-level = 3              # ORM宏编译优化

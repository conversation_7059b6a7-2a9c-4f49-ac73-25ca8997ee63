#!/bin/bash

# Invoice Book Docker 镜像构建脚本
# 用途：一键构建Linux AMD64平台的Docker镜像并保存到本地

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置变量
IMAGE_NAME="invoice-book-app"
IMAGE_TAG="latest"
DOCKERFILE_PATH="Dockerfile"
OUTPUT_FILE="invoice-book-app-amd64.tar"
PLATFORM="linux/amd64"

# 函数：打印带颜色的信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 函数：检查必要条件
check_prerequisites() {
    print_info "检查必要条件..."
    
    # 检查Docker是否安装
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        print_error "Docker 未运行，请启动Docker"
        exit 1
    fi
    
    # 检查Dockerfile是否存在
    if [ ! -f "$DOCKERFILE_PATH" ]; then
        print_error "Dockerfile 不存在: $DOCKERFILE_PATH"
        exit 1
    fi
    
    # 检查是否在项目根目录
    if [ ! -f "Cargo.toml" ]; then
        print_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    print_success "必要条件检查通过"
}

# 函数：清理旧镜像
cleanup_old_images() {
    print_info "清理旧镜像..."
    
    # 删除同名镜像（如果存在）
    if docker images | grep -q "$IMAGE_NAME.*$IMAGE_TAG"; then
        print_warning "发现同名镜像，正在删除..."
        docker rmi "$IMAGE_NAME:$IMAGE_TAG" 2>/dev/null || true
    fi
    
    # 删除旧的tar文件（如果存在）
    if [ -f "$OUTPUT_FILE" ]; then
        print_warning "删除旧的镜像文件: $OUTPUT_FILE"
        rm -f "$OUTPUT_FILE"
    fi
}

# 函数：构建Docker镜像
build_image() {
    print_info "开始构建Docker镜像..."
    print_info "平台: $PLATFORM"
    print_info "镜像名: $IMAGE_NAME:$IMAGE_TAG"
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 构建镜像
    if docker build \
        --platform "$PLATFORM" \
        -t "$IMAGE_NAME:$IMAGE_TAG" \
        -f "$DOCKERFILE_PATH" \
        .; then
        
        # 计算构建时间
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        print_success "镜像构建完成，耗时: ${duration}秒"
    else
        print_error "镜像构建失败"
        exit 1
    fi
}

# 函数：验证镜像
verify_image() {
    print_info "验证镜像..."
    
    # 检查镜像是否存在
    if ! docker images | grep -q "$IMAGE_NAME.*$IMAGE_TAG"; then
        print_error "镜像验证失败：镜像不存在"
        exit 1
    fi
    
    # 检查镜像架构
    architecture=$(docker inspect "$IMAGE_NAME:$IMAGE_TAG" | grep -o '"Architecture":"[^"]*"' | cut -d'"' -f4)
    if [ "$architecture" != "amd64" ]; then
        print_warning "镜像架构不是amd64: $architecture"
    else
        print_success "镜像架构验证通过: $architecture"
    fi
    
    # 显示镜像信息
    image_id=$(docker images --format "table {{.ID}}\t{{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "$IMAGE_NAME" | grep "$IMAGE_TAG" | awk '{print $1}')
    image_size=$(docker images --format "table {{.ID}}\t{{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "$IMAGE_NAME" | grep "$IMAGE_TAG" | awk '{print $4}')
    
    print_info "镜像ID: $image_id"
    print_info "镜像大小: $image_size"
}

# 函数：保存镜像
save_image() {
    print_info "保存镜像到本地文件..."
    print_info "输出文件: $OUTPUT_FILE"
    
    # 记录开始时间
    start_time=$(date +%s)
    
    # 保存镜像
    if docker save -o "$OUTPUT_FILE" "$IMAGE_NAME:$IMAGE_TAG"; then
        # 计算保存时间
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        
        # 显示文件信息
        file_size=$(ls -lh "$OUTPUT_FILE" | awk '{print $5}')
        print_success "镜像保存完成，耗时: ${duration}秒"
        print_info "文件大小: $file_size"
        print_info "文件路径: $(pwd)/$OUTPUT_FILE"
    else
        print_error "镜像保存失败"
        exit 1
    fi
}


# 主函数
main() {
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}  Invoice Book Docker 镜像构建脚本${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo ""
    
    check_prerequisites
    cleanup_old_images
    build_image
    verify_image
    save_image
    
    echo ""
    echo -e "${GREEN}========================================${NC}"
    print_success "所有步骤完成！"
    echo -e "${GREEN}========================================${NC}"
}

# 运行主函数
main "$@" 
#!/bin/bash

# 本地数据库导出脚本（Docker版本）
# 用途：导出Docker容器中的PostgreSQL数据库数据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 数据库配置
CONTAINER_NAME="postgres"
DB_USER="admin"
DB_NAME="invoice_book"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="invoice_book_backup_${TIMESTAMP}.dump"

echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}  本地数据库导出脚本 (Docker版本)${NC}"
echo -e "${GREEN}========================================${NC}"

# 函数：打印信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查Docker是否运行
if ! command -v docker &> /dev/null; then
    print_error "Docker 未安装，请安装Docker"
    exit 1
fi

if ! docker info &> /dev/null; then
    print_error "Docker 未运行，请启动Docker"
    exit 1
fi

# 检查PostgreSQL容器是否运行
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    print_error "PostgreSQL容器 '$CONTAINER_NAME' 未运行"
    print_info "当前运行的容器："
    docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}"
    exit 1
fi

print_info "找到PostgreSQL容器: $CONTAINER_NAME"
print_info "开始导出数据库..."
print_info "数据库: $DB_NAME"
print_info "导出文件: $BACKUP_FILE"

# 使用Docker容器导出数据库
if docker exec "$CONTAINER_NAME" pg_dump -U "$DB_USER" -d "$DB_NAME" -Fc > "$BACKUP_FILE"; then
    print_success "数据库导出成功！"
    print_info "文件大小: $(ls -lh "$BACKUP_FILE" | awk '{print $5}')"
    print_info "文件路径: $(pwd)/$BACKUP_FILE"
else
    print_error "数据库导出失败"
    exit 1
fi

echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}  导出完成！${NC}"
echo -e "${GREEN}========================================${NC}" 
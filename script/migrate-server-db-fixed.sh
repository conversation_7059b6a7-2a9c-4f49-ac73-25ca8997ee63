#!/bin/bash

# 服务器数据库完整迁移脚本（修复版）
# 用途：清空服务器数据库并导入本地数据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 数据库配置
CONTAINER_NAME="postgres"
DB_USER="admin"
DB_SUPERUSER="123456"
DB_NAME="invoice_book"
BACKUP_DIR="/opt/invoice-book"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}  服务器数据库完整迁移脚本（修复版）${NC}"
echo -e "${GREEN}========================================${NC}"

# 函数：打印信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查Docker容器是否运行
if ! docker ps | grep -q "$CONTAINER_NAME"; then
    print_error "PostgreSQL容器 '$CONTAINER_NAME' 未运行"
    print_info "可用容器："
    docker ps --format "table {{.Names}}\t{{.Status}}"
    exit 1
fi

# 查找备份文件
BACKUP_FILE=$(ls -t $BACKUP_DIR/invoice_book_backup_*.dump 2>/dev/null | head -n1)
if [ -z "$BACKUP_FILE" ]; then
    print_error "在 $BACKUP_DIR 中未找到备份文件 (invoice_book_backup_*.dump)"
    print_info "请确保已上传备份文件到服务器"
    exit 1
fi

print_info "找到备份文件: $BACKUP_FILE"
print_info "文件大小: $(ls -lh "$BACKUP_FILE" | awk '{print $5}')"

# 确认操作
echo ""
print_warning "⚠️  警告：此操作将会："
echo "   1. 备份当前服务器数据库数据"
echo "   2. 终止所有数据库连接"
echo "   3. 完全删除并重建数据库"
echo "   4. 导入本地数据"
echo "   5. 重启应用服务"
echo ""
read -p "确认继续操作？(输入 'YES' 确认): " confirm

if [ "$confirm" != "YES" ]; then
    print_info "操作已取消"
    exit 0
fi

echo ""
print_info "开始数据库完整迁移操作..."

# 1. 备份当前服务器数据
print_info "步骤 1/6: 备份当前服务器数据..."
SERVER_BACKUP_FILE="$BACKUP_DIR/server_backup_${TIMESTAMP}.sql"
if docker exec "$CONTAINER_NAME" pg_dump -U "$DB_USER" -d "$DB_NAME" > "$SERVER_BACKUP_FILE" 2>/dev/null; then
    print_success "服务器数据备份完成: $SERVER_BACKUP_FILE"
else
    print_warning "服务器数据备份失败或数据库不存在，继续迁移..."
fi

# 2. 停止应用服务（避免数据写入）
print_info "步骤 2/6: 停止应用服务..."
if docker stop rust-app 2>/dev/null; then
    print_success "应用服务已停止"
else
    print_warning "应用服务停止失败或未运行"
fi

# 3. 终止所有数据库连接
print_info "步骤 3/6: 终止所有数据库连接..."
docker exec "$CONTAINER_NAME" psql -U "$DB_SUPERUSER" -d postgres -c "
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity 
WHERE datname = '$DB_NAME' AND pid <> pg_backend_pid();" > /dev/null 2>&1 || true
print_success "数据库连接已终止"

# 4. 删除并重建数据库
print_info "步骤 4/6: 删除并重建数据库..."
docker exec -i "$CONTAINER_NAME" psql -U "$DB_SUPERUSER" -c "DROP DATABASE IF EXISTS $DB_NAME;" postgres
docker exec -i "$CONTAINER_NAME" psql -U "$DB_SUPERUSER" -c "CREATE DATABASE $DB_NAME;" postgres  
docker exec -i "$CONTAINER_NAME" psql -U "$DB_SUPERUSER" -c "ALTER DATABASE $DB_NAME OWNER TO $DB_USER;" postgres
print_success "数据库已重建"

# 5. 导入本地数据
print_info "步骤 5/6: 导入本地数据..."
if cat "$BACKUP_FILE" | docker exec -i "$CONTAINER_NAME" pg_restore -U "$DB_USER" -d "$DB_NAME" --no-owner --no-privileges 2>/dev/null; then
    print_success "数据导入完成"
else
    print_error "数据导入失败"
    print_info "正在恢复服务器备份数据..."
    if [ -f "$SERVER_BACKUP_FILE" ]; then
        docker exec -i "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" < "$SERVER_BACKUP_FILE"
        print_info "服务器数据已恢复"
    fi
    exit 1
fi

# 6. 重启应用服务并验证
print_info "步骤 6/6: 重启应用服务并验证..."
docker start rust-app
print_success "应用服务已重启"

# 验证数据
print_info "验证数据库..."
sleep 3

# 检查schema
print_info "检查数据库schema..."
docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -c "\dn" | grep -E "(system|business)" && print_success "Schema验证通过" || print_warning "Schema验证失败"

# 检查表
print_info "检查数据表..."
SYSTEM_TABLES=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'system';" | tr -d ' ')
BUSINESS_TABLES=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'business';" | tr -d ' ')

print_info "System schema表数量: $SYSTEM_TABLES"
print_info "Business schema表数量: $BUSINESS_TABLES"

# 检查用户数据
if USER_COUNT=$(docker exec "$CONTAINER_NAME" psql -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM system.users;" 2>/dev/null | tr -d ' '); then
    print_success "用户数据验证通过，用户数量: $USER_COUNT"
else
    print_warning "用户数据验证失败"
fi

# 检查应用状态
APP_STATUS=$(docker ps --format "{{.Status}}" --filter "name=rust-app")
print_info "应用服务状态: $APP_STATUS"

echo ""
print_success "数据库迁移完成！"
print_info "服务器原数据备份: $SERVER_BACKUP_FILE"
print_info "导入的数据文件: $BACKUP_FILE"

echo ""
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}  迁移操作完成！${NC}"
echo -e "${GREEN}========================================${NC}"

# 显示迁移总结
echo ""
print_info "迁移总结："
echo "   ✅ 数据库已重建"
echo "   ✅ 本地数据已导入" 
echo "   ✅ 应用服务已重启"
echo "   📊 System表数量: $SYSTEM_TABLES"
echo "   📊 Business表数量: $BUSINESS_TABLES"
echo "   👥 用户数量: ${USER_COUNT:-'未知'}" 